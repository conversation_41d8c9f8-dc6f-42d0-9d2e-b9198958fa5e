import {
  createWalletClient,
  http,
  Address,
  createPublic<PERSON>lient,
  getAddress,
} from "viem";
import { privateKeyToAccount, nonceManager } from "viem/accounts";
import { env } from "../env";
import { ronin, saigon } from "viem/chains";
import Withdraw from "../models/withdraw";
import GameItemsAbi from "../abi/GameItems.abi.json";
import { getRedisStore } from "../services/redis-store";

const account = privateKeyToAccount(env.MINTER_PK as `0x${string}`, {
  nonceManager,
});

const walletClient = createWalletClient({
  account,
  chain: env.ENV === "production" ? ronin : saigon,
  transport: http(env.RONIN_RPC),
});

const publicClient = createPublicClient({
  chain: env.ENV === "production" ? ronin : saigon,
  transport: http(env.RONIN_RPC),
});
const MAX_BATCH_SIZE = 50;
const MAX_RETRIES = 100; // Maximum retry attempts
const RETRY_DELAY_MS = 2000; // Delay between retries in milliseconds

const distributeRewards = async () => {
  const redisStore = getRedisStore();
  try {
    const lockAcquired = await redisStore.acquireLock("processrequest", 300000);

    if (!lockAcquired) {
      console.log("Multiple requests detected in short time.");
      return;
    }
    const withdrawRequest = await Withdraw.find({ distributed: false }).limit(
      MAX_BATCH_SIZE
    );

    if (withdrawRequest.length === 0) {
      await redisStore.releaseLock("processrequest");
      return;
    }

    // Prepare arrays for batch minting
    const addresses: Address[] = [];
    const amounts: number[] = [];
    const bytes: string[] = [];

    // Populate arrays from withdraw requests
    withdrawRequest.forEach((request) => {
      addresses.push(getAddress(request.address) as Address);
      amounts.push(request.feathers);
      bytes.push("0x");
    });

    if (addresses.length > MAX_BATCH_SIZE) {
      throw new Error(`Batch size exceeds maximum limit of ${MAX_BATCH_SIZE}`);
    }

    // Prepare the transaction
    const { request } = await publicClient.simulateContract({
      account,
      address: env.GAMEITEMS_CONTRACT as Address,
      abi: GameItemsAbi,
      functionName: "bulkMint",
      args: [0, addresses, amounts, bytes],
    });

    // Send the transaction
    const hash = await walletClient.writeContract(request);

    // Wait for transaction confirmation
    const receipt = await publicClient.waitForTransactionReceipt({ hash });

    if (receipt.status === "success") {
      // Retry mechanism for database write (update withdraw requests)
      let attempts = 0;
      let success = false;

      while (attempts < MAX_RETRIES && !success) {
        try {
          // Update all processed withdraw requests
          const updatePromises = withdrawRequest.map((request) =>
            Withdraw.findByIdAndUpdate(request._id, {
              distributed: true,
              transactionHash: hash,
            })
          );

          await Promise.all(updatePromises);
          success = true;
          console.log(
            `Successfully distributed tokens. Transaction hash: ${hash}`
          );
        } catch (error) {
          attempts++;
          if (attempts < MAX_RETRIES) {
            console.log(`Retrying database update... Attempt ${attempts}`);
            await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY_MS)); // Wait before retrying
          } else {
            console.error(
              "Max retries reached. Failed to update withdraw requests."
            );
            throw error; // Throw error after max retries
          }
        }
      }
    } else {
      throw new Error("Transaction failed");
    }
  } catch (error) {
    console.error("Error in distributeRewards:", error);
    throw error;
  } finally {
    await redisStore.releaseLock("processrequest");
  }
};

export default distributeRewards;
