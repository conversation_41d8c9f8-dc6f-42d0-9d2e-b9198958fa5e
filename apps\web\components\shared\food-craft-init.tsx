"use client";

import useFoodCraftingStore from "@/store/food-crafting";
import { useStateContext } from "@/providers/app/state";
import { PropsWithChildren, useEffect } from "react";

interface Props extends PropsWithChildren {
  jwt?: string;
}

export default function FoodCraftInit(props: Props) {
  // Make sure we have access to the state context
  const stateContext = useStateContext();
  const { fetchRandomCookiePrice } = useFoodCraftingStore();

  useEffect(() => {
    // Ensure window.stateContext is set before calling store methods
    if (typeof window !== "undefined" && stateContext) {
      window.stateContext = {
        address: stateContext.address,
        isConnected: stateContext.isConnected,
        publicClient: stateContext.publicClient,
        walletClient: stateContext.walletClient,
        Disconnect: stateContext.Disconnect,
        ConnectRecentWallet: stateContext.ConnectRecentWallet,
      };

      // Now it's safe to call the store method
      fetchRandomCookiePrice();
    }
  }, [stateContext, fetchRandomCookiePrice]);

  return props.children;
}
