{"name": "marketplace", "type": "module", "main": "./dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "rm -rf dist && tsup", "start": "node ."}, "dependencies": {"@hono/node-server": "^1.13.7", "axios": "^1.7.9", "croner": "^9.0.0", "dotenv": "^16.4.7", "hono": "^4.6.17", "mongoose": "^8.9.3", "viem": "^2.21.57", "zod": "^3.24.1"}, "devDependencies": {"@sabongsaga/eslint-config": "workspace:*", "@sabongsaga/typescript-config": "workspace:*", "@types/node": "^20.11.17", "terser": "^5.36.0", "tsup": "^8.3.5", "tsx": "^4.7.1"}}