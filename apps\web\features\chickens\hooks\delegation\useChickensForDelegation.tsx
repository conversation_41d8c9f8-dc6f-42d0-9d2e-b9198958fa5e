"use client";

import { useConnectedWalletChickenInfo } from "@/features/chickens/hooks/useChickenInfo";
import { IChickenInfo } from "@/features/chickens/types/chicken-info.types";
import { IChickenMetadata } from "@/lib/types/chicken.types";
import { useStateContext } from "@/providers/app/state";
import { useMemo } from "react";

// Interface for chicken rental status
export interface IChickenRentalStatus {
  isAvailable: boolean;
  rentalStatus?:
    | "available"
    | "listed"
    | "rented"
    | "delegated"
    | "expired"
    | "cancelled";
  statusLabel?: string;
}

// Interface for chicken data suitable for delegation
export interface IChickenForDelegation {
  tokenId: number;
  image: string;
  metadata?: IChickenMetadata;
  dailyFeathers?: number;
  breedCount?: number;
  type?: string;
  isAvailable?: boolean;
  rentalStatus?: IChickenRentalStatus;
  cooldownEndsAt?: Date;
  level?: number;
  winRate?: number;
  battleStats?: IBattleStats;
}

// Interface for battle stats
interface IBattleStats {
  wins: number;
  losses: number;
  draws?: number;
  level?: number;
  state?: string; // "normal", "faint", "dead", "breeding"
  recoverDate?: string;
  stats?: {
    attack?: number;
    defense?: number;
    speed?: number;
    currentHp?: number;
    hp?: number;
    cockrage?: number;
    ferocity?: number;
    evasion?: number;
    instinct?: string;
  };
}

// Helper function to determine rental status from individual rental data
const getChickenRentalStatusFromData = (
  rental: any | null
): IChickenRentalStatus => {
  if (!rental) {
    // No rental data means chicken is available
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  }

  // Check the rental status
  if (rental.status === 0) {
    // Status 0 = AVAILABLE (listed in rental market)
    return {
      isAvailable: false,
      rentalStatus: "listed",
      statusLabel: "Listed in Rental Market",
    };
  } else if (rental.status === 1) {
    // Status 1 = RENTED (actually rented by someone)
    // Check if it's a paid rental or free delegation
    const roninPrice = rental.roninPrice || rental.ronin_price;
    const isDelegation = roninPrice === "0" || roninPrice === 0;

    if (isDelegation) {
      return {
        isAvailable: false,
        rentalStatus: "delegated",
        statusLabel: "Already Delegated",
      };
    } else {
      return {
        isAvailable: false,
        rentalStatus: "rented",
        statusLabel: "Already Rented",
      };
    }
  } else if (rental.status === 2) {
    // Status 2 = EXPIRED
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  } else if (rental.status === 3) {
    // Status 3 = CANCELLED
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  }

  // Default to available for unknown statuses
  return {
    isAvailable: true,
    rentalStatus: "available",
    statusLabel: "Available",
  };
};

// Individual battle stats function removed - now using batch API only

// Helper functions to check chicken states
const isChickenFaint = (battleStats: IBattleStats): boolean => {
  return battleStats.state === "faint";
};

const isChickenDead = (battleStats: IBattleStats): boolean => {
  return battleStats.state === "dead";
};

const isChickenBreeding = (battleStats: IBattleStats): boolean => {
  return battleStats.state === "breeding";
};

const isChickenOnCooldown = (battleStats: IBattleStats): boolean => {
  return (
    isChickenFaint(battleStats) ||
    isChickenDead(battleStats) ||
    isChickenBreeding(battleStats)
  );
};

export function useChickensForDelegation() {
  const { address, isConnected } = useStateContext();

  // Use the centralized chicken info fetcher
  const chickenInfoResponse = useConnectedWalletChickenInfo({
    includeMetadata: true,
    includeGenes: false, // Not needed for delegation
    includeBattleStats: true,
    includeBreedCount: true,
    includeCooldown: true,
    includeRentalStatus: true,
  });

  // Transform chicken info to delegation format
  const chickensForDelegation: IChickenForDelegation[] = useMemo(() => {
    return chickenInfoResponse.chickens.map((chicken: IChickenInfo) => {
      // Calculate win rate from battle stats
      const totalBattles =
        (chicken.battleStats?.wins || 0) + (chicken.battleStats?.losses || 0);
      const winRate =
        totalBattles > 0
          ? Math.round(((chicken.battleStats?.wins || 0) / totalBattles) * 100)
          : 0;

      return {
        tokenId: chicken.tokenId,
        image: chicken.image,
        metadata: chicken.metadata,
        dailyFeathers: chicken.dailyFeathers,
        breedCount: chicken.breedCount,
        type: chicken.type,
        isAvailable: chicken.isAvailable,
        rentalStatus: chicken.rentalStatus,
        cooldownEndsAt: chicken.cooldownInfo?.remainingTime
          ? new Date(Date.now() + chicken.cooldownInfo.remainingTime)
          : undefined,
        level: chicken.level,
        winRate,
        battleStats: chicken.battleStats,
      };
    });
  }, [chickenInfoResponse.chickens]);

  // Filter chickens by type using the centralized data
  const chickensByType = useMemo(() => {
    const ordinary = chickensForDelegation.filter(
      (chicken) => chicken.type?.toLowerCase() === "ordinary"
    );
    const legacy = chickensForDelegation.filter(
      (chicken) => chicken.type?.toLowerCase() === "legacy"
    );
    const genesis = chickensForDelegation.filter(
      (chicken) => chicken.type?.toLowerCase() === "genesis"
    );

    return { ordinary, legacy, genesis };
  }, [chickensForDelegation]);

  return {
    chickens: chickensForDelegation,
    chickensByType,
    isLoading: chickenInfoResponse.isLoading,
    error: chickenInfoResponse.error,
    isConnected,
    address,
  };
}
