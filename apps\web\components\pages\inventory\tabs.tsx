"use client";

import { InventoryType } from "@/types/inventory.type";
import { ChickX, Feathers } from "@/components/shared/icons";
import { Cookie, Home, Users } from "lucide-react";
import { useInventoryData } from "@/hooks/useInventoryData";
import { useRouter } from "next/navigation";

interface InventoryTabsProps {
  activeTab: InventoryType;
  onTabChange: (tab: InventoryType) => void;
}

export default function InventoryTabs({
  activeTab,
  onTabChange,
}: InventoryTabsProps) {
  const { myChickensCount, delegatedOutCount, featherCount, itemCount } =
    useInventoryData();
  const router = useRouter();

  const handleTabChange = (tab: InventoryType) => {
    onTabChange(tab);
    // Update URL with tab parameter
    router.push(`/inventory?tab=${tab}`, { scroll: false });
  };

  const tabs = [
    {
      id: "my-chickens" as InventoryType,
      label: "My Chickens",
      icon: <ChickX size={20} />,
      count: myChickensCount,
    },
    {
      id: "delegated-out" as InventoryType,
      label: "Delegated Chickens",
      icon: <Users className="w-5 h-5" />,
      count: delegatedOutCount,
    },
    {
      id: "feathers" as InventoryType,
      label: "Feathers",
      icon: <Feathers size={20} />,
      count: featherCount,
    },
    {
      id: "items" as InventoryType,
      label: "Items",
      icon: <Cookie className="w-5 h-5" />,
      count: itemCount,
    },
  ];

  return (
    <div className="border-b border-stone-600">
      {/* Mobile: Grid layout for better spacing */}
      <div className="grid grid-cols-2 gap-2 sm:hidden">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => handleTabChange(tab.id)}
            className={`flex items-center justify-center gap-2 px-3 py-3 rounded-t-lg font-medium transition-all text-sm ${
              activeTab === tab.id
                ? "bg-stone-800 text-primary border-b-2 border-primary"
                : "bg-stone-900 text-stone-300 hover:bg-stone-800 hover:text-white"
            }`}
          >
            {tab.icon}
            <span className="truncate">{tab.label}</span>
            <span className="bg-stone-700 text-xs px-1.5 py-0.5 rounded-full min-w-[20px] text-center">
              {tab.count}
            </span>
          </button>
        ))}
      </div>

      {/* Desktop: Flex layout */}
      <div className="hidden sm:flex flex-wrap gap-2">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => handleTabChange(tab.id)}
            className={`flex items-center gap-2 px-4 py-3 rounded-t-lg font-medium transition-all ${
              activeTab === tab.id
                ? "bg-stone-800 text-primary border-b-2 border-primary"
                : "bg-stone-900 text-stone-300 hover:bg-stone-800 hover:text-white"
            }`}
          >
            {tab.icon}
            <span>{tab.label}</span>
            <span className="bg-stone-700 text-xs px-2 py-1 rounded-full">
              {tab.count}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
}
