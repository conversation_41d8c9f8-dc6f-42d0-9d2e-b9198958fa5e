import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Plus, Swords } from "lucide-react";
import { useState, useRef, useEffect } from "react";

interface ChickenActionsProps {
  tokenId: string;
  isEgg: boolean;
  isFaint: boolean;
  isDead: boolean;
  isBreeding: boolean;
  isListed: boolean;
  wasTransferred: boolean;
  hp?: number;
  maxHp?: number;
  chickenType?:
    | "owned"
    | "delegated-to-me"
    | "rented-to-me"
    | "delegated-out"
    | "rented-out"
    | "listed-in-market";
  isDisabled?: boolean;
  onBattle: (e: React.MouseEvent, tokenId: string) => void;
  onHeal: (e: React.MouseEvent, tokenId: string) => void;
  onFeed: (e: React.MouseEvent, tokenId: string) => void;
  onBreed: (e: React.MouseEvent, tokenId: string) => void;
  onMenuAction: (e: React.MouseEvent, action: string, tokenId: string) => void;
}

export function ChickenActions({
  tokenId,
  isEgg,
  isFaint,
  isDead,
  isBreeding,
  isListed,
  wasTransferred,
  hp,
  maxHp,
  chickenType,
  isDisabled: chickenIsDisabled,
  onBattle,
  onHeal,
  onFeed,
  onBreed,
  onMenuAction,
}: ChickenActionsProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<"bottom" | "top">(
    "bottom"
  );
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Priority order: dead → faint → transferred → listed → breeding
  const isDisabled =
    isDead ||
    isFaint ||
    wasTransferred ||
    isListed ||
    isBreeding ||
    chickenIsDisabled;
  const isHpNotFull = hp !== undefined && maxHp !== undefined && hp < maxHp;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDropdownOpen]);

  // Handle menu action and close dropdown
  const handleMenuAction = (
    e: React.MouseEvent,
    action: string,
    tokenId: string
  ) => {
    setIsDropdownOpen(false);
    onMenuAction(e, action, tokenId);
  };

  // Handle breed action and close dropdown
  const handleBreed = (e: React.MouseEvent, tokenId: string) => {
    setIsDropdownOpen(false);
    onBreed(e, tokenId);
  };

  // Calculate dropdown position based on available space
  const calculateDropdownPosition = () => {
    if (!buttonRef.current) return;

    const buttonRect = buttonRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const dropdownHeight = 200; // Approximate height of dropdown
    const spaceBelow = viewportHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top;

    // If there's not enough space below but enough space above, position dropdown above
    if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
      setDropdownPosition("top");
    } else {
      setDropdownPosition("bottom");
    }
  };

  // Action restrictions based on chicken type
  const getActionRestrictions = () => {
    switch (chickenType) {
      case "delegated-to-me":
      case "rented-to-me":
        return {
          canBattle: true, // depends on access (TODO: check delegation access)
          canHeal: true, // depends on access (TODO: check delegation access)
          canFeed: true,
          canBreed: false, // NO breed for delegated/rented
          canRelease: false, // NO release for delegated/rented
          canDelegate: false, // NO delegate for delegated/rented
          canCancelDelegation: false, // Cannot cancel someone else's delegation
          canUnlistFromMarket: false, // Cannot unlist someone else's listing
          showDelegationDetails: true,
        };
      case "delegated-out":
        return {
          canBattle: false,
          canHeal: false,
          canFeed: true, // Owner/delegator can feed delegated out chickens
          canBreed: false,
          canRelease: false,
          canDelegate: false,
          canCancelDelegation: true, // Owner can cancel delegation
          canUnlistFromMarket: false, // Not a marketplace listing
          showDelegationDetails: true,
        };
      case "rented-out":
        return {
          canBattle: false,
          canHeal: false,
          canFeed: false,
          canBreed: false,
          canRelease: false,
          canDelegate: false,
          canCancelDelegation: false, // Cannot cancel rental (different from delegation)
          canUnlistFromMarket: false, // Not a marketplace listing
          showDelegationDetails: true, // Show rental details
        };
      case "listed-in-market":
        return {
          canBattle: false,
          canHeal: false,
          canFeed: false,
          canBreed: false,
          canRelease: false,
          canDelegate: false,
          canCancelDelegation: false, // Not delegated
          canUnlistFromMarket: true, // Owner can unlist from marketplace
          showDelegationDetails: true,
        };
      case "owned":
      default:
        return {
          canBattle: true,
          canHeal: true,
          canFeed: true,
          canBreed: true,
          canRelease: true,
          canDelegate: true,
          canCancelDelegation: false, // Not delegated
          canUnlistFromMarket: false, // Not listed
          showDelegationDetails: false,
        };
    }
  };

  const restrictions = getActionRestrictions();

  if (isEgg) {
    return null;
  }

  return (
    <div className="flex items-center gap-1">
      {/* Battle Button */}
      {restrictions.canBattle && (
        <button
          className={`p-1.5 rounded-full text-white transition-colors ${
            isDisabled || isHpNotFull
              ? "bg-stone-600 cursor-not-allowed opacity-50"
              : "bg-stone-700 hover:bg-red-700"
          }`}
          onClick={(e) => onBattle(e, tokenId)}
          aria-label="Battle"
          title={
            isDead
              ? "Cannot battle - chicken is dead"
              : isFaint
                ? "Cannot battle - chicken is faint"
                : wasTransferred
                  ? "Cannot battle - chicken was recently transferred"
                  : isListed
                    ? "Cannot battle - chicken is listed on marketplace"
                    : isBreeding
                      ? "Cannot battle - chicken is breeding"
                      : isHpNotFull
                        ? "Cannot battle - chicken HP must be full"
                        : "Battle"
          }
          disabled={isDisabled || isHpNotFull}
        >
          <Swords className="h-4 w-4" />
        </button>
      )}

      {/* Heal Button */}
      {restrictions.canHeal && (
        <button
          className={`p-1.5 rounded-full text-white transition-colors ${
            isDisabled
              ? "bg-stone-600 cursor-not-allowed opacity-50"
              : "bg-stone-700 hover:bg-green-700"
          }`}
          onClick={(e) => onHeal(e, tokenId)}
          aria-label="Heal"
          title={
            isDead
              ? "Cannot heal - chicken is dead"
              : isFaint
                ? "Cannot heal - chicken is faint"
                : wasTransferred
                  ? "Cannot heal - chicken was recently transferred"
                  : isListed
                    ? "Cannot heal - chicken is listed on marketplace"
                    : isBreeding
                      ? "Cannot heal - chicken is breeding"
                      : "Heal"
          }
          disabled={isDisabled}
        >
          <Plus className="h-4 w-4" />
        </button>
      )}

      {/* Feed Button */}
      {restrictions.canFeed && (
        <button
          className={`p-1.5 rounded-full text-white transition-colors ${
            isDisabled
              ? "bg-stone-600 cursor-not-allowed opacity-50"
              : "bg-stone-700 hover:bg-blue-700"
          }`}
          onClick={(e) => onFeed(e, tokenId)}
          aria-label="Feed"
          title={
            isDead
              ? "Cannot feed - chicken is dead"
              : isFaint
                ? "Cannot feed - chicken is faint"
                : wasTransferred
                  ? "Cannot feed - chicken was recently transferred"
                  : isListed
                    ? "Cannot feed - chicken is listed on marketplace"
                    : isBreeding
                      ? "Cannot feed - chicken is breeding"
                      : "Feed"
          }
          disabled={isDisabled}
        >
          <Cookie className="h-4 w-4" />
        </button>
      )}

      {/* More Options Button */}
      <div className="relative" ref={dropdownRef}>
        <button
          ref={buttonRef}
          className={`p-1.5 rounded-full text-white menu-button transition-colors ${
            isDisabled && !restrictions.showDelegationDetails
              ? "bg-stone-600 cursor-not-allowed opacity-50"
              : "bg-stone-700 hover:bg-stone-600"
          }`}
          aria-label="More options"
          title={
            isDead
              ? "Options disabled - chicken is dead"
              : isFaint
                ? "Options disabled - chicken is faint"
                : wasTransferred
                  ? "Options disabled - chicken was recently transferred"
                  : isListed
                    ? "Options disabled - chicken is listed on marketplace"
                    : isBreeding
                      ? "Options disabled - chicken is breeding"
                      : "More options"
          }
          disabled={isDisabled && !restrictions.showDelegationDetails}
          onClick={(e) => {
            e.stopPropagation();
            if (!isDropdownOpen) {
              calculateDropdownPosition();
            }
            setIsDropdownOpen(!isDropdownOpen);
          }}
        >
          <MoreHorizontal className="h-4 w-4" />
        </button>

        {isDropdownOpen && (
          <div
            className={`absolute right-0 w-48 bg-stone-800 border border-stone-600 rounded-lg shadow-lg overflow-hidden z-[100] ${
              dropdownPosition === "top" ? "bottom-full mb-1" : "top-full mt-1"
            }`}
            style={{ zIndex: 100 }}
          >
            {/* Only show available actions when chicken is not disabled */}
            {!isDisabled && restrictions.canBreed && (
              <button
                className="w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors"
                onClick={(e) => handleBreed(e, tokenId)}
              >
                Breed
              </button>
            )}
            {!isDisabled && restrictions.canDelegate && (
              <button
                className="w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors"
                onClick={(e) => handleMenuAction(e, "delegate", tokenId)}
              >
                Delegate
              </button>
            )}
            {/* Cancel delegation button - for delegated out chickens */}
            {!isDisabled && restrictions.canCancelDelegation && (
              <button
                className="w-full text-left px-4 py-2 text-orange-400 hover:bg-stone-700 transition-colors"
                onClick={(e) =>
                  handleMenuAction(e, "cancel-delegation", tokenId)
                }
              >
                Cancel Delegation
              </button>
            )}
            {/* Unlist from marketplace button - for listed chickens */}
            {!isDisabled && restrictions.canUnlistFromMarket && (
              <button
                className="w-full text-left px-4 py-2 text-red-400 hover:bg-stone-700 transition-colors"
                onClick={(e) =>
                  handleMenuAction(e, "unlist-from-market", tokenId)
                }
              >
                Unlist from Market
              </button>
            )}
            {/* Always show delegation/rental details if available, even when chicken is disabled */}
            {restrictions.showDelegationDetails && (
              <button
                className="w-full text-left px-4 py-2 text-blue-400 hover:bg-stone-700 transition-colors"
                onClick={(e) =>
                  handleMenuAction(e, "delegation-details", tokenId)
                }
              >
                {chickenType === "rented-out"
                  ? "Rental Details"
                  : "Delegation Details"}
              </button>
            )}
            {!isDisabled && restrictions.canRelease && (
              <button
                className="w-full text-left px-4 py-2 text-red-400 hover:bg-stone-700 transition-colors"
                onClick={(e) => handleMenuAction(e, "release", tokenId)}
              >
                Release
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
