# Maintenance Mode Components

This directory contains maintenance mode components for different sections of the application. Each maintenance mode can be independently controlled via environment variables.

## Available Maintenance Modes

### 1. Breeding Maintenance Mode

The breeding page supports a maintenance mode that completely replaces the breeding interface.

**Environment Variable:**

```bash
NEXT_PUBLIC_BREEDING_MAINTENANCE_MODE=true
```

**Behavior:**

- When enabled: Entire breeding page shows maintenance message
- When disabled: Normal breeding functionality
- Scope: Full page replacement

**Usage:**

- Set `NEXT_PUBLIC_BREEDING_MAINTENANCE_MODE=true` to enable maintenance mode
- Set `NEXT_PUBLIC_BREEDING_MAINTENANCE_MODE=false` or omit the variable to disable maintenance mode

### 2. Ninuno Rewards Maintenance Mode

The ninuno rewards tab supports a maintenance mode that only affects the specific tab within the rewards page.

**Environment Variable:**

```bash
NEXT_PUBLIC_NINUNO_REWARDS_MAINTENANCE_MODE=true
```

**Behavior:**

- When enabled: Only the Ninuno Rewards tab shows maintenance message
- When disabled: Normal ninuno rewards functionality
- Scope: Tab-specific (Rewards Inventory tab remains functional)

**Usage:**

- Set `NEXT_PUBLIC_NINUNO_REWARDS_MAINTENANCE_MODE=true` to enable maintenance mode
- Set `NEXT_PUBLIC_NINUNO_REWARDS_MAINTENANCE_MODE=false` or omit the variable to disable maintenance mode

**Important:** This maintenance mode only affects the Ninuno Rewards tab. Users can still access the Rewards Inventory tab normally.

## Common Features

- ✅ **Environment variable controlled** - Easy to toggle via configuration
- ✅ **Responsive design** - Works on mobile and desktop
- ✅ **Consistent with app theme** - Matches the application's visual design
- ✅ **User-friendly messaging** - Clear communication about maintenance status
- ✅ **No data loss** - Chickens and rewards remain safe during maintenance
- ✅ **Independent control** - Each maintenance mode can be enabled/disabled separately

## Quick Setup

Add both environment variables to your `.env` file:

```bash
# Maintenance Mode Configuration
NEXT_PUBLIC_BREEDING_MAINTENANCE_MODE=false
NEXT_PUBLIC_NINUNO_REWARDS_MAINTENANCE_MODE=false
```

## Implementation Details

### File Structure

```
apps/web/components/maintenance/
├── index.ts                           # Export file
├── breeding-maintenance.tsx           # Breeding maintenance component
├── ninuno-rewards-maintenance.tsx     # Ninuno rewards maintenance component
└── README.md                          # This documentation
```

### Integration Points

- **Breeding Page**: `apps/web/app/breeding/page.tsx`
  - Full page replacement when maintenance is enabled
- **Ninuno Rewards Tab**: `apps/web/features/rewards/ninuno/components/ninuno-rewards-tab.tsx`
  - Tab-specific replacement when maintenance is enabled
- **Environment Variables**: `apps/web/env.example`
  - Configuration documentation and defaults

### Usage Examples

**Enable breeding maintenance only:**

```bash
NEXT_PUBLIC_BREEDING_MAINTENANCE_MODE=true
NEXT_PUBLIC_NINUNO_REWARDS_MAINTENANCE_MODE=false
```

**Enable ninuno rewards maintenance only:**

```bash
NEXT_PUBLIC_BREEDING_MAINTENANCE_MODE=false
NEXT_PUBLIC_NINUNO_REWARDS_MAINTENANCE_MODE=true
```

**Enable both maintenance modes:**

```bash
NEXT_PUBLIC_BREEDING_MAINTENANCE_MODE=true
NEXT_PUBLIC_NINUNO_REWARDS_MAINTENANCE_MODE=true
```
