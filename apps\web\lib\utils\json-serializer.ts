/**
 * Custom JSON replacer function to handle BigInt serialization
 * 
 * @param _key - The key in the object being stringified
 * @param value - The value being stringified
 * @returns The serialized value
 */
export function bigIntReplacer(_key: string, value: any): any {
  // Convert BigInt values to strings
  if (typeof value === 'bigint') {
    return value.toString();
  }
  return value;
}

/**
 * Safely stringify an object that may contain BigInt values
 * 
 * @param data - The data to stringify
 * @returns A JSON string with BigInt values converted to strings
 */
export function safeJsonStringify(data: any): string {
  return JSON.stringify(data, bigIntReplacer);
}
