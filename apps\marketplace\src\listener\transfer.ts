import {
  Address,
  createPublicClient,
  http,
  Log,
  decodeEventLog,
} from "viem";
import { env } from "../utils/env";
import { ronin } from "viem/chains";
import GenesisAbi from "../abi/genesis.json";
import Legacy<PERSON>bi from "../abi/legacy.json"; // Add your second contract ABI
import { UTCTimestamp } from "../utils/utc-timestamp";
import Transfer from "../models/events";


// Define event argument types
interface BaseEventArgs {
  tokenId: bigint;
}

interface TransferEventArgs extends BaseEventArgs {
  from: Address;
  to: Address;
}

// Initialize client
const client = createPublicClient({
  chain: ronin,
  transport: http(`${env.RPC}`),
});

// Generic log processor
const processLogs = async <T extends BaseEventArgs>(
  logs: Log[],
  eventName: string,
  transformArgs: (args: T) => Record<string, any> | null,
  contractName: string
) => {
  if (!logs?.length) return;

  const epoch = UTCTimestamp();

  const insertLogs = logs
    .map((log) => {
      try {
        // Select the appropriate ABI based on contract name
        const abi = contractName === "Genesis" ? GenesisAbi : LegacyAbi;

        const decodedLog = decodeEventLog({
          abi: abi,
          data: log.data,
          topics: log.topics,
        });

        const args = decodedLog.args as unknown as T;
        if (!args) {
          console.log(`Decoded log has no args: ${JSON.stringify(log)}`);
          return null;
        }
        const cleanArgs = transformArgs(args);
        if (cleanArgs) {
          return {
            epoch,
            data: {
              blockNumber: Number(log.blockNumber),
              transactionHash: log.transactionHash,
              tokenId: Number(args.tokenId),
              ...cleanArgs,
            },
          };
        }
        return null;
      } catch (error) {
        console.log(`Failed to decode log: ${error}`);
        return null;
      }
    })
    .filter(Boolean);

  if (insertLogs.length) {
    await Transfer.insertMany(insertLogs);
    console.log(
      `Done processed new ${eventName} event from ${contractName}: ${JSON.stringify(insertLogs)}`
    );
  }
};

// Define event configurations
interface EventConfig<T extends BaseEventArgs> {
  transformArgs: (args: T) => Record<string, any> | null;
}

// Contract configurations
interface ContractConfig {
  address: Address;
  abi: any;
  events: {
    [eventName: string]: EventConfig<any>;
  };
  name: string;
}

// Define configurations for each contract
const contractConfigs: ContractConfig[] = [
  {
    address: env.CHICKEN_CONTRACT as Address,
    abi: GenesisAbi,
    name: "Genesis",
    events: {
      Transfer: {
        transformArgs: (args: TransferEventArgs) => ({
          from: args.from,
          to: args.to,
        }),
      },
    },
  },
  {
    address: env.LEGACY_CONTRACT as Address, // Add your second contract address
    abi: LegacyAbi,
    name: "Legacy",
    events: {
      Transfer: {
        transformArgs: (args: TransferEventArgs) => ({
          from: args.from,
          to: args.to,
        }),
      },
      // Add other events for this contract if needed
    },
  },
];

export const startTransferEventListener = async () => {
  // Set up listeners for each contract and its events
  contractConfigs.forEach((contractConfig) => {
    Object.entries(contractConfig.events).forEach(([eventName, config]) => {
      client.watchContractEvent({
        address: contractConfig.address,
        abi: contractConfig.abi,
        eventName,
        onLogs: async (logs) => {
          try {
            await processLogs(
              logs,
              eventName,
              config.transformArgs,
              contractConfig.name
            );
          } catch (error) {
            console.log(
              `Can't process new ${eventName} event from ${contractConfig.name}: ${error}`
            );
          }
        },
      });
    });
  });

  console.log("Event listeners started for all contracts");
};
