import { Context, Next } from "hono";
import { sign, verify } from "jsonwebtoken";
import { BigNumber, utils } from "ethers";

import { AuthConfig, SignMessage, NonceData, TokenPayload } from "../types";
import { getRedisStore } from "../services/redis-store";
import { randomBytes } from "node:crypto";
import { ResponseHelper, sendResponse } from "../utils/response-helper";

import { createSiweMessage } from "viem/siwe";
import { Address, createPublicClient, http } from "viem";
import { saigon } from "viem/chains";
import { env } from "../env";
import me from "../models/me";
import { getCookie } from "hono/cookie";

const DEFAULT_CONFIG: AuthConfig = {
  jwtSecret: env.JWT_SECRET,
  refreshJwtSecret: env.REFRESH_JWT_SECRET,
  signMessage: {
    statement:
      "Please sign this message to verify your ownership of this Ethereum address. This signature will not trigger a blockchain transaction or cost any gas fees.",
    version: "1",
    nonce: "0",
    chainId: env.SIWE.chainId,
    domain: env.SIWE.domain,
    address: "0x",
    uri: env.SIWE.uri,
  },
  expiresIn: 24 * 60 * 60, // 24 hours
  refreshTokenExpiresIn: 7 * 24 * 60 * 60,
};

function generateNonce(): string {
  // Generate 32 random bytes (256 bits)
  const randomBuffer = randomBytes(32);
  // Convert to BigNumber and ensure it's positive
  const randomBigNumber = BigNumber.from(randomBuffer).abs();
  // Convert to string
  return randomBigNumber.toString();
}

const generateTokenPair = async (address: string) => {
  const store = getRedisStore();

  const accessToken = sign(
    {
      address,
      exp: Math.floor(Date.now() / 1000) + DEFAULT_CONFIG.expiresIn!,
      tokenType: "access",
    },
    DEFAULT_CONFIG.jwtSecret
  );

  const refreshToken = sign(
    {
      address,
      exp:
        Math.floor(Date.now() / 1000) + DEFAULT_CONFIG.refreshTokenExpiresIn!,
      tokenType: "refresh",
    },
    DEFAULT_CONFIG.refreshJwtSecret
  );

  // Store the refresh token
  await store.store(
    `refreshtoken:${address}`,
    refreshToken,
    DEFAULT_CONFIG.refreshTokenExpiresIn
  );

  return { accessToken, refreshToken };
};

export const createAuthMiddleware = (config: Partial<AuthConfig> = {}) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const store = getRedisStore();
  const publicClient = createPublicClient({
    chain: saigon,
    transport: http(),
  });

  const generateSignMessage = (
    nonce: string,
    address: Address
  ): SignMessage => {
    return {
      ...DEFAULT_CONFIG.signMessage!,
      nonce,
      address,
    };
  };

  return {
    protect: async (c: Context, next: Next) => {
      // First check Authorization header
      const authHeader = c.req.header("Authorization");
      let token: string | undefined;

      if (authHeader && authHeader.startsWith("Bearer ")) {
        token = authHeader.split(" ")[1];
      } else {
        // If no Authorization header, check cookies
        token = getCookie(c, "jwt");
      }

      if (!token) {
        return sendResponse(
          c,
          ResponseHelper.unauthorized("No authorization token provided")
        );
      }

      try {
        const payload = verify(token, finalConfig.jwtSecret);
        c.set("user", payload);
        await next();
      } catch (error) {
        return sendResponse(
          c,
          ResponseHelper.forbidden("Invalid or expired token")
        );
      }
    },

    refreshToken: async (c: Context) => {
      try {
        const { refreshToken } = await c.req.json();

        if (!refreshToken) {
          return sendResponse(
            c,
            ResponseHelper.badRequest("Refresh token is required")
          );
        }

        const payload = verify(
          refreshToken,
          finalConfig.refreshJwtSecret
        ) as TokenPayload;

        if (payload.tokenType !== "refresh") {
          return sendResponse(
            c,
            ResponseHelper.badRequest("Invalid token type")
          );
        }

        const oldRT = await store.get(`refreshtoken:${payload.address}`);

        if (oldRT != refreshToken) {
          return sendResponse(
            c,
            ResponseHelper.badRequest("Invalid refresh token")
          );
        }

        const newTokens = await generateTokenPair(payload.address);
        return sendResponse(
          c,
          ResponseHelper.success({
            token: newTokens.accessToken,
            refreshToken: newTokens.refreshToken,
            accessTokenExpiresIn: finalConfig.expiresIn,
            refreshTokenExpiresIn: finalConfig.refreshTokenExpiresIn,
          })
        );
      } catch (error) {
        return sendResponse(
          c,
          ResponseHelper.unauthorized("Invalid or expired refresh token")
        );
      }
    },

    getNonce: async (c: Context) => {
      const address = c.req.param("address")?.toLowerCase();

      if (!address || !utils.isAddress(address)) {
        return c.json({ error: "Invalid Ethereum address" }, 400);
      }

      try {
        const nonce = generateNonce();
        const timestamp = Date.now();
        const nonceData: NonceData = { nonce, timestamp };

        await store.storeNonce(address, nonceData);
        const message = generateSignMessage(nonce, address as Address);

        return sendResponse(
          c,
          ResponseHelper.success(
            message,
            "Nonce generated valid for 5 minutes."
          )
        );
      } catch (error) {
        return sendResponse(
          c,
          ResponseHelper.serverError("Failed to generate nonce")
        );
      }
    },

    verifySignature: async (c: Context) => {
      try {
        const { address, signature, issuedAt } = await c.req.json();

        if (!address || !signature || !issuedAt) {
          return sendResponse(
            c,
            ResponseHelper.badRequest("Missing address, issuedAt or signature")
          );
        }

        const lowercaseAddress = address.toLowerCase();
        const nonceData = await store.getNonce(lowercaseAddress);

        if (!nonceData) {
          return sendResponse(
            c,
            ResponseHelper.badRequest("Invalid or expired nonce")
          );
        }

        const message = generateSignMessage(nonceData.nonce, address);

        const siweMsg = createSiweMessage({
          domain: message.domain,
          address,
          chainId: message.chainId,
          nonce: nonceData.nonce,
          uri: message.uri,
          issuedAt: new Date(issuedAt),
          version: "1",
          statement: message.statement,
        });

        const valid = await publicClient.verifySiweMessage({
          message: siweMsg,
          signature,
        });

        if (!valid) {
          return sendResponse(
            c,
            ResponseHelper.badRequest("Invalid signature")
          );
        }

        const userFound = await me.findOne({ address: lowercaseAddress });

        if (!userFound) {
          const newUser = new me({ address: lowercaseAddress });
          await newUser.save();
        }

        const { accessToken, refreshToken } =
          await generateTokenPair(lowercaseAddress);

        await store.removeNonce(lowercaseAddress);

        return sendResponse(
          c,
          ResponseHelper.success({
            token: accessToken,
            refreshToken,
            expiresIn: finalConfig.expiresIn,
          })
        );
      } catch (error) {
        const errors: Record<string, string[]> = {};
        errors.validations = ["Validation failed"];
        return sendResponse(c, ResponseHelper.validationError(errors));
      }
    },

    cleanup: async () => {
      await store.close();
    },
  };
};
