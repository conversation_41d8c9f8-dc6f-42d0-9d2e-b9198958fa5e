import { ChevronDown, Search, CheckSquare } from "lucide-react";

type SortableAttribute =
  | "id"
  | "hp"
  | "level"
  | "attack"
  | "defense"
  | "speed"
  | "ferocity"
  | "cockrage"
  | "evasion"
  | "mmr";

interface HealInfo {
  healsRemaining: number;
  maxHeals: number;
  resetTime: string;
}

interface InventoryControlsProps {
  sortAttribute: SortableAttribute;
  sortOrder: "asc" | "desc";
  showSortDropdown: boolean;
  searchQuery: string;
  healInfo: HealInfo | null;
  healInfoLoading: boolean;
  onSortAttributeChange: (attribute: SortableAttribute) => void;
  onSortOrderChange: () => void;
  onShowSortDropdownChange: (show: boolean) => void;
  onSearchQueryChange: (query: string) => void;
  onRefreshHealInfo: () => void;
  // Bulk actions props
  isBulkMode?: boolean;
  bulkActionSummary?: {
    totalSelected: number;
    canCancelDelegation: number;
    canUnlistFromMarket: number;
  };
  onToggleBulkMode?: () => void;
}

export function InventoryControls({
  sortAttribute,
  sortOrder,
  showSortDropdown,
  searchQuery,
  healInfo,
  healInfoLoading,
  onSortAttributeChange,
  onSortOrderChange,
  onShowSortDropdownChange,
  onSearchQueryChange,
  onRefreshHealInfo,
  isBulkMode = false,
  bulkActionSummary,
  onToggleBulkMode,
}: InventoryControlsProps) {
  const getSortDisplayName = (attribute: SortableAttribute) => {
    switch (attribute) {
      case "id":
        return "ID";
      case "hp":
        return "HP";
      case "mmr":
        return "MMR";
      default:
        return attribute.charAt(0).toUpperCase() + attribute.slice(1);
    }
  };

  return (
    <div className="flex flex-col md:flex-row gap-4 items-center justify-between my-6">
      {/* Sort Control */}
      <div className="relative">
        <button
          className="flex items-center gap-2 bg-stone-700 text-white px-4 py-2 rounded-lg sort-button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onShowSortDropdownChange(!showSortDropdown);
          }}
        >
          Sort by {getSortDisplayName(sortAttribute)}{" "}
          {sortOrder === "desc" ? "↓" : "↑"}
          <ChevronDown className="h-4 w-4" />
        </button>

        {showSortDropdown && (
          <div className="absolute right-0 mt-1 w-40 bg-stone-800 border border-stone-600 rounded-lg shadow-lg overflow-hidden z-40 sort-dropdown">
            {(["id"] as SortableAttribute[]).map((attr) => (
              <button
                key={attr}
                className={`w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors ${
                  sortAttribute === attr ? "bg-stone-700" : ""
                }`}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onSortAttributeChange(attr);
                  onShowSortDropdownChange(false);
                }}
              >
                {getSortDisplayName(attr)}
              </button>
            ))}
            <div className="border-t border-stone-600 my-1"></div>
            <button
              className="w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onSortOrderChange();
                onShowSortDropdownChange(false);
              }}
            >
              {sortOrder === "asc" ? "Descending ↓" : "Ascending ↑"}
            </button>
          </div>
        )}
      </div>

      {/* Bulk Actions Toggle */}
      {onToggleBulkMode && (
        <div className="flex items-center">
          <button
            onClick={onToggleBulkMode}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
              isBulkMode
                ? "bg-blue-600 hover:bg-blue-700 text-white"
                : "bg-stone-700 hover:bg-stone-600 text-white"
            }`}
          >
            <CheckSquare className="h-4 w-4" />
            {isBulkMode ? (
              <span>
                Bulk Actions
                {bulkActionSummary && bulkActionSummary.totalSelected > 0 && (
                  <span className="ml-1 bg-blue-800 px-2 py-0.5 rounded text-xs">
                    {bulkActionSummary.totalSelected}
                  </span>
                )}
              </span>
            ) : (
              "Bulk Actions"
            )}
          </button>
        </div>
      )}

      {/* Search */}
      <div className="relative w-full md:w-64">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
          <Search className="h-5 w-5" />
        </div>
        <input
          type="text"
          className="bg-stone-700 text-white pl-10 pr-4 py-2 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Search by ID..."
          value={searchQuery}
          onChange={(e) => onSearchQueryChange(e.target.value)}
        />
      </div>

      {/* Heal Info Display */}
      <div className="flex items-center gap-2">
        <div
          className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
            healInfoLoading
              ? "bg-stone-700"
              : "bg-stone-800 border border-stone-700"
          }`}
        >
          {healInfoLoading ? (
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 rounded-full border-2 border-t-transparent border-blue-500 animate-spin"></div>
              <span className="text-stone-400">Loading...</span>
            </div>
          ) : healInfo ? (
            <div className="flex flex-col">
              <span className="text-white font-medium">
                {healInfo.healsRemaining} / {healInfo.maxHeals} Free Heals
              </span>
              <span className="text-xs text-stone-400">
                Resets at {healInfo.resetTime}
              </span>
            </div>
          ) : (
            <span className="text-stone-400">No heal info</span>
          )}
        </div>

        {/* Refresh button */}
        <button
          onClick={onRefreshHealInfo}
          disabled={healInfoLoading}
          className="p-2 bg-stone-700 hover:bg-stone-600 rounded-lg text-white transition-colors"
          title="Refresh heal count"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={`${healInfoLoading ? "animate-spin" : ""}`}
          >
            <path d="M21 12a9 9 0 0 1-9 9c-4.97 0-9-4.03-9-9s4.03-9 9-9h3"></path>
            <path d="M21 3v6h-6"></path>
            <path d="M21 9a9 9 0 0 0-9 3"></path>
          </svg>
        </button>
      </div>
    </div>
  );
}
