export enum CollectionType {
  Genesis = 0,
  Legacy = 1,
}

export enum StatType {
  ATTACK = 0,
  DEFENSE = 1,
  SPEED = 2,
  COCKRAGE = 3,
  FEROCITY = 4,
  EVASION = 5,
  HEALTH = 6,
}

export enum ItemType {
  NORMAL_FEED = 0,
  IMMORTAL_ITEM = 1,
  DEBUFF_ITEM = 2,
  ULTIMATE_COOKIE = 3,
}

export enum DebuffType {
  RANDOM_SELECT = 0,
  USER_CHOICE = 1,
}

export interface StatBoost {
  statType: StatType;
  amount: bigint;
  duration: bigint;
}

export interface FeedItemConfig {
  itemType: ItemType;
  debuffType: DebuffType;
  immortalDuration: bigint;
  isActive: boolean;
  statBoosts: StatBoost[];
}

export interface ActiveBoost {
  amount: bigint;
  expiryTime: bigint;
}

export interface NFTStatus {
  immortalExpiryTime: bigint;
  activeBoostedStatsCount: number;
  activeBoostedStats: StatType[];
}

export interface FeedParams {
  collectionType: CollectionType;
  nftTokenId: number;
  feedItemId: number;
  amount: number;
  affectionPoints: number;
  debuffStatChoice: StatType;
}

export interface NFTStat {
  statType: StatType;
  totalValue: bigint;
  expiryTime: bigint;
}

export interface NFTStatsResponse {
  stats: NFTStat[];
  isImmortal: boolean;
  immortalExpiryTime: bigint;
}

export interface SignatureRequest {
  feederAddress: string;
  collectionType: CollectionType;
  nftTokenId: number;
  feedItemId: number;
  amount: number;
  debuffStatChoice: StatType;
  nonce: number;
}

export interface SignatureResponse {
  signature: string;
  deadline: number;
  affectionPoints: number;
  messageHash: string;
}
