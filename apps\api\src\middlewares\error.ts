import { StatusCode } from "hono/utils/http-status";
import { ResponseHelper } from "../utils/response-helper";
import { Context, Next } from "hono";
import { AppError } from "../types";

export const errorHandler = async (c: Context, next: Next) => {
  try {
    await next();
  } catch (error) {
    console.error("Error:", error);

    if (error instanceof AppError) {
      const response = {
        status: false,
        responseCode: error.statusCode,
        message: error.message,
        errors: error.errors,
      };
      return c.json(response, error.statusCode as StatusCode);
    }

    // Handle JWT/Token errors
    if (
      error instanceof Error &&
      error.message.toLowerCase().includes("token")
    ) {
      const response = ResponseHelper.invalidToken();
      return c.json(response, response.responseCode as StatusCode);
    }

    // Default server error
    const response = ResponseHelper.serverError();
    return c.json(response, response.responseCode as StatusCode);
  }
};
