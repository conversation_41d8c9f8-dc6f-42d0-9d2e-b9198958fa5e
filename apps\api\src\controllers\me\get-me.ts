import { Context } from "hono";
import { Response<PERSON><PERSON>per, sendResponse } from "../../utils/response-helper";
import ME from "../../models/me";
import {
  getYesterdayUTCTimestamp,
  UTCTimestamp,
} from "../../utils/timestamp-utc";
import Withdraw from "../../models/withdraw";
import { Address } from "viem";
import Rub from "../../models/rub";
import legendaryFeathers from "../../models/legendary-feathers";
import LegendaryClaim from "../../models/legWithdraw";

export const getMe = async (c: Context) => {
  const user = c.get("user");
  const address = user.address;
  const normalizeAddr = address.toLowerCase();

  // Run all DB queries in parallel
  const [
    userFound,
    userLegFound,
    todayWithdrawReq,
    todayWithdrawLegReq,
    pendingWithdrawReq,
    pendingWithdrawLegReq,
    streak,
  ] = await Promise.all([
    ME.findOne({ address: normalizeAddr }),
    legendaryFeathers.findOne({ address: normalizeAddr }),
    Withdraw.findOne({
      address: normalizeAddr,
      createdAt: { $gte: new Date(new Date().setUTCHours(0, 0, 0, 0)) },
    }),
    LegendaryClaim.findOne({
      address: normalizeAddr,
      createdAt: { $gte: new Date(new Date().setUTCHours(0, 0, 0, 0)) },
    }),
    Withdraw.findOne({
      address: normalizeAddr,
      distributed: false,
    }),
    LegendaryClaim.findOne({
      address: normalizeAddr,
      distributed: false,
    }),
    checkDailyStreak(normalizeAddr),
  ]);

  if (!userFound) {
    return sendResponse(c, ResponseHelper.notFound("User not found"));
  }

  const legendaryClaimableFeathers = userLegFound
    ? userLegFound.legendaryFeathers
    : 0;

  // Convert userFound to a plain object if it's a mongoose document
  const userData = userFound.toObject ? userFound.toObject() : { ...userFound };

  // Only able to claim if no pending or today's requests in either collection
  const ableToClaim =
    !todayWithdrawReq &&
    !todayWithdrawLegReq &&
    !pendingWithdrawReq &&
    !pendingWithdrawLegReq;

  const data = {
    ...userData,
    legendaryClaimableFeathers,
    streak,
    ableToClaim,
  };

  return sendResponse(c, ResponseHelper.success(data));
};

async function checkDailyStreak(address: Address) {
  try {
    // Get all records for the address
    let rub = await Rub.find({
      address,
    });

    const epochToday = UTCTimestamp();
    const yesterdayEpoch = getYesterdayUTCTimestamp();

    // Extract unique epochs
    const records = [...new Set(rub.map((r) => r.epoch))];

    if (!records || !records.length) return 0;

    // Filter out any records with undefined epoch
    const validRecords = records.filter((record) => record !== undefined);

    // Sort in ascending order (oldest to newest)
    const sortedData = [...validRecords].sort((a, b) => a - b);

    if (sortedData.length === 0) return 0;

    const ONE_DAY = 86400; // seconds in a day

    // Check if the user has activity today or yesterday
    const hasToday = sortedData.includes(epochToday);
    const hasYesterday = sortedData.includes(yesterdayEpoch);

    // If no activity yesterday or today, streak is 0
    if (!hasToday && !hasYesterday) {
      return 0;
    }

    // Start counting from the most recent day (today or yesterday)
    const startDay = hasToday ? epochToday : yesterdayEpoch;
    let currentStreak = 1; // Always start with 1 if we have either today or yesterday
    let currentDay = startDay;

    // Count backwards day by day
    while (true) {
      const previousDay = currentDay - ONE_DAY;

      // If the previous day exists in our records, increment streak
      if (sortedData.includes(previousDay)) {
        currentStreak++;
        currentDay = previousDay;
      } else {
        // Break the chain - streak ends
        break;
      }
    }

    return currentStreak;
  } catch (error) {
    console.error("Error checking daily streak:", error);
    return 0;
  }
}
