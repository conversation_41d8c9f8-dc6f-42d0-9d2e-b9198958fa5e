"use client";

import { useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";
import { chain } from "@/providers/web3/web3-provider";
import { chickenGenesisAbi } from "@/providers/web3/abi/chicken-genesis-abi";
import { chickenLegacyAbi } from "@/providers/web3/abi/chicken-legacy-abi";

/**
 * Interface for chicken approval status
 */
export interface IChickenApprovalStatus {
  isApproved: boolean;
  isLoading: boolean;
  error?: Error;
}

/**
 * Hook for managing chicken NFT approvals for the rental contract
 * Handles both Genesis and Legacy chickens based on tokenId
 */
export const useChickenApproval = () => {
  const { publicClient, walletClient, address, Disconnect } = useStateContext();
  const { blockchainQuery } = useBlockchain();
  const queryClient = useQueryClient();

  const [isApproving, setIsApproving] = useState(false);

  // Get contract addresses from blockchain config
  const chickenGenesisAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.chicken_genesis_address
    : ("" as Address);

  const chickenLegacyAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.chicken_legacy_address
    : ("" as Address);

  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  const chickenGenesisThreshold = blockchainQuery.isSuccess
    ? blockchainQuery.data?.chicken_genesis_threshold || 2222
    : 2222;

  /**
   * Determine which chicken contract to use based on tokenId
   */
  const getChickenContractInfo = (tokenId: number) => {
    const isGenesis = tokenId <= chickenGenesisThreshold;
    return {
      isGenesis,
      contractAddress: isGenesis ? chickenGenesisAddress : chickenLegacyAddress,
      contractAbi: isGenesis ? chickenGenesisAbi : chickenLegacyAbi,
      contractName: isGenesis ? "Genesis" : "Legacy",
    };
  };

  /**
   * Check if a specific chicken contract is approved for the rental contract
   */
  const checkApprovalStatus = async (tokenId: number): Promise<boolean> => {
    if (!address || !publicClient || !rentalAddress) {
      return false;
    }

    try {
      const { contractAddress, contractAbi } = getChickenContractInfo(tokenId);

      if (!contractAddress) {
        throw new Error("Chicken contract address not configured");
      }

      const isApproved = await publicClient.readContract({
        address: contractAddress,
        abi: contractAbi,
        functionName: "isApprovedForAll",
        args: [address, rentalAddress],
      });

      return Boolean(isApproved);
    } catch (error) {
      console.error("Failed to check approval status:", error);
      return false;
    }
  };

  /**
   * Query hook for checking approval status
   */
  const useApprovalStatus = (tokenId: number) => {
    return useQuery({
      queryKey: ["chickenApproval", tokenId, address, rentalAddress],
      queryFn: () => checkApprovalStatus(tokenId),
      enabled: !!address && !!rentalAddress && !!tokenId,
      staleTime: 30000, // 30 seconds
      refetchOnWindowFocus: false,
    });
  };

  /**
   * Execute approval transaction for a specific chicken
   */
  const executeApproval = async (tokenId: number) => {
    try {
      if (!address || !walletClient) {
        toast.error("Cannot approve chicken", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return { success: false };
      }

      if (!rentalAddress) {
        toast.error("Cannot approve chicken", {
          description: "Rental contract not configured",
          position: "top-right",
        });
        return { success: false };
      }

      const { contractAddress, contractAbi, contractName } = getChickenContractInfo(tokenId);

      if (!contractAddress) {
        toast.error("Cannot approve chicken", {
          description: "Chicken contract not configured",
          position: "top-right",
        });
        return { success: false };
      }

      setIsApproving(true);

      // Check if already approved
      const isAlreadyApproved = await checkApprovalStatus(tokenId);
      if (isAlreadyApproved) {
        toast.success("Chicken already approved", {
          description: `${contractName} chickens are already approved for rental`,
          position: "top-center",
        });
        return { success: true };
      }

      // Step 1: Simulate the approval transaction
      toast.info("Simulating approval...", {
        description: `Checking ${contractName} chicken approval`,
        position: "top-center",
      });

      await publicClient.simulateContract({
        address: contractAddress,
        abi: contractAbi,
        functionName: "setApprovalForAll",
        args: [rentalAddress, true],
        chain,
        account: address,
      });

      // Step 2: Estimate gas
      const gasEstimate = await publicClient.estimateContractGas({
        address: contractAddress,
        abi: contractAbi,
        functionName: "setApprovalForAll",
        args: [rentalAddress, true],
        account: address,
      });

      // Step 3: Execute the approval transaction
      toast.info("Approving chicken...", {
        description: `Please confirm the approval for ${contractName} chickens`,
        position: "top-center",
      });

      const hash = await walletClient.writeContract({
        address: contractAddress,
        abi: contractAbi,
        functionName: "setApprovalForAll",
        args: [rentalAddress, true],
        gas: gasEstimate + BigInt(10000), // Add small buffer
        chain,
        account: address,
      });

      // Step 4: Wait for confirmation
      toast.info("Confirming approval...", {
        description: "Waiting for blockchain confirmation",
        position: "top-center",
      });

      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        confirmations: 1,
      });

      if (receipt.status === "success") {
        toast.success("Chicken approved successfully!", {
          description: `${contractName} chickens are now approved for rental`,
          position: "top-center",
        });

        // Invalidate approval queries to refresh status
        setTimeout(() => {
          queryClient.invalidateQueries({ 
            queryKey: ["chickenApproval"] 
          });
        }, 500);

        return { success: true, hash, receipt };
      } else {
        throw new Error("Approval transaction failed");
      }
    } catch (error) {
      console.error("Chicken approval failed:", error);

      let errorMessage = "Failed to approve chicken";
      let errorDescription = "An unexpected error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;

        if (error.message.includes("User rejected")) {
          errorDescription = "Transaction was cancelled by user";
        } else if (error.message.includes("insufficient funds")) {
          errorDescription = "Insufficient funds for gas fees";
        } else {
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        position: "top-center",
      });

      return { success: false, error };
    } finally {
      setIsApproving(false);
    }
  };

  // Mutation for React Query integration
  const approvalMutation = useMutation({
    mutationFn: executeApproval,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Approval mutation error:", error);
    },
  });

  return {
    // Functions
    executeApproval,
    checkApprovalStatus,
    getChickenContractInfo,
    useApprovalStatus,

    // Mutation
    approvalMutation,

    // State
    isApproving,

    // Contract info
    chickenGenesisAddress,
    chickenLegacyAddress,
    rentalAddress,
    chickenGenesisThreshold,
  };
};
