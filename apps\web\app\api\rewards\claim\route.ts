import { NextRequest, NextResponse } from "next/server";

const CHICKEN_API_BASE_URL = "https://chicken-api-ivory.vercel.app/api/game";
const CHICKEN_API_KEY = process.env.CHICKEN_API_KEY;

export async function POST(request: NextRequest) {
  try {
    // Check if API key is configured
    if (!CHICKEN_API_KEY) {
      return NextResponse.json(
        { error: "API key not configured" },
        { status: 500 }
      );
    }

    // Validate CSRF token
    const csrfToken = request.headers.get("X-CSRF-Token");
    if (!csrfToken) {
      return NextResponse.json(
        { error: "CSRF token required" },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { address, claims, tokenId, amount } = body;

    // Validate request
    if (!address) {
      return NextResponse.json(
        { error: "Invalid request. Address is required." },
        { status: 400 }
      );
    }

    // Support both single token claim and batch claims
    let claimRequest;
    if (claims && Array.isArray(claims)) {
      // Batch claim
      claimRequest = { address, claims };
    } else if (tokenId !== undefined && amount !== undefined) {
      // Single token claim
      claimRequest = { 
        address, 
        claims: [{ tokenId, amount }] 
      };
    } else {
      return NextResponse.json(
        { error: "Invalid request. Either claims array or tokenId/amount is required." },
        { status: 400 }
      );
    }

    console.log("🎯 Proxying claim request to chicken-api-ivory:", claimRequest);

    // Make request to chicken-api-ivory
    const response = await fetch(`${CHICKEN_API_BASE_URL}/claim`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": CHICKEN_API_KEY,
      },
      body: JSON.stringify(claimRequest),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error("❌ Claim failed:", data);
      return NextResponse.json(
        { 
          error: data.error || data.message || "Claim failed",
          details: data
        },
        { status: response.status }
      );
    }

    console.log("✅ Claim successful:", data);
    return NextResponse.json(data);

  } catch (error: any) {
    console.error("❌ Claim API error:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    );
  }
}