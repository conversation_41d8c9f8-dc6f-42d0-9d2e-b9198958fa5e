import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const cookie = await cookies();
    cookie.delete("jwt");
    cookie.delete("refresh_token");

    return NextResponse.json(
      {
        status: true,
        responseCode: 200,
        message: "Successuflly logout",
      },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json(
      {
        status: false,
        responseCode: 500,
        message: "Internal Server Error",
        errors: [(error as Error).message],
      },
      { status: 500 }
    );
  }
}
