"use client";

import React from "react";
import { <PERSON><PERSON>, Badge, Checkbox } from "ui";
import { cn } from "@/utils/classes";
import Image from "next/image";
import { 
  IChickenWithRewards, 
  IChickenSelection, 
  ERewardType 
} from "../types/inventory.types";

interface IChickenRewardsGridProps {
  chickens: IChickenWithRewards[];
  selectedChickens: IChickenSelection;
  onToggleChickenSelection: (chickenId: string) => void;
  onSelectAllChickens: () => void;
  onDeselectAllChickens: () => void;
  onChickenClick: (chickenId: string) => void;
  selectedChickenId: string | null;
  className?: string;
}

/**
 * ChickenRewardsGrid Component
 * 
 * Displays chickens with their rewards in a grid layout
 * Supports selection similar to ninuno rewards pattern
 */
export const ChickenRewardsGrid: React.FC<IChickenRewardsGridProps> = ({
  chickens,
  selectedChickens,
  onToggleChickenSelection,
  onSelectAllChickens,
  onDeselect<PERSON><PERSON><PERSON>hic<PERSON>s,
  onChickenClick,
  selectedChickenId,
  className,
}) => {
  const selectedCount = Object.values(selectedChickens).filter(Boolean).length;
  const totalCount = chickens.length;

  // Get reward type badge color - improved contrast
  const getTypeColor = (type: ERewardType) => {
    switch (type) {
      case ERewardType.CRYSTAL:
        return "bg-blue-600/30 text-blue-200 border border-blue-400/40";
      case ERewardType.SHARD:
        return "bg-purple-600/30 text-purple-200 border border-purple-400/40";
      case ERewardType.CORN:
        return "bg-amber-600/30 text-amber-200 border border-amber-400/40";
      default:
        return "bg-stone-600/30 text-stone-200 border border-stone-400/40";
    }
  };

  return (
    <div className={cn("bg-stone-900/50 rounded-lg border border-primary/5 p-4", className)}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-amber-200">
          Chickens with Rewards ({chickens.length})
        </h2>

        <div className="flex items-center gap-3">
          <span className="text-sm font-medium text-stone-300">
            {selectedCount} selected
          </span>
          <Button
            size="small"
            appearance="plain"
            onPress={onSelectAllChickens}
            isDisabled={totalCount === 0}
            className="text-amber-300 hover:text-amber-200 hover:bg-amber-600/20"
          >
            Select All
          </Button>
          <Button
            size="small"
            appearance="plain"
            onPress={onDeselectAllChickens}
            isDisabled={selectedCount === 0}
            className="text-stone-300 hover:text-stone-200 hover:bg-stone-600/20"
          >
            Clear
          </Button>
        </div>
      </div>

      <div className="h-[450px] overflow-y-auto pr-2 custom-scrollbar">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-2">
          {chickens.map((chicken) => (
            <div
              key={chicken.chickenId}
              className={cn(
                "relative group rounded-lg overflow-hidden border transition-all duration-200 cursor-pointer",
                selectedChickenId === chicken.chickenId
                  ? "border-amber-400 bg-amber-500/15 shadow-lg shadow-amber-400/20 ring-1 ring-amber-400/30"
                  : "border-stone-600/40 bg-stone-800/60 hover:border-amber-400/60 hover:bg-stone-700/80 hover:shadow-md"
              )}
              onClick={() => onChickenClick(chicken.chickenId)}
            >
              {/* Selection Checkbox */}
              <div className="absolute top-3 left-3 z-10">
                <div className="bg-stone-900/80 rounded p-1 backdrop-blur-sm">
                  <Checkbox
                    isSelected={selectedChickens[chicken.chickenId] || false}
                    onChange={() => onToggleChickenSelection(chicken.chickenId)}
                    className="scale-90"
                  />
                </div>
              </div>

              {/* Chicken Image */}
              <div className="aspect-square bg-gradient-to-br from-stone-800/60 to-stone-900/80 p-6 flex items-center justify-center relative">
                <Image
                  src={chicken.image}
                  alt={chicken.name}
                  width={120}
                  height={120}
                  quality={100}
                  unoptimized
                  className="w-full h-full object-contain drop-shadow-lg"
                />
                
                {/* Total Rewards Badge */}
                <div className="absolute top-3 right-3">
                  <div className="bg-amber-500/90 text-amber-900 px-2 py-1 rounded-full text-xs font-bold shadow-lg">
                    {chicken.totalRewards}
                  </div>
                </div>
              </div>

              {/* Chicken Info */}
              <div className="p-4 space-y-3 bg-stone-800/40">
                <div className="flex justify-between items-start">
                  <h3 className="font-semibold text-amber-200 text-base truncate">
                    {chicken.name}
                  </h3>
                  <span className="text-xs text-stone-400 bg-stone-700/50 px-2 py-1 rounded">
                    #{chicken.chickenId}
                  </span>
                </div>

                {/* Rewards by Type */}
                <div className="flex flex-wrap gap-2">
                  {Object.entries(chicken.rewardsByType).map(([type, quantity]) => (
                    <span
                      key={type}
                      className={cn(
                        "px-3 py-1.5 rounded-full text-xs font-semibold",
                        getTypeColor(type as ERewardType)
                      )}
                    >
                      {type}: {quantity}
                    </span>
                  ))}
                </div>

                {/* Rewards Count */}
                <div className="text-xs text-stone-300 font-medium">
                  {chicken.rewards.length} unclaimed rewards
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {chickens.length === 0 && (
          <div className="text-center py-12">
            <span className="text-4xl mb-3 block">🐔</span>
            <h3 className="text-lg font-medium text-primary/70 mb-2">
              No chickens with rewards
            </h3>
            <p className="text-sm text-muted-fg">
              Your chickens haven't earned any rewards yet.
            </p>
            <p className="text-sm text-muted-fg mt-1">
              Play battles to earn rewards for your chickens!
            </p>
          </div>
        )}
      </div>
    </div>
  );
};