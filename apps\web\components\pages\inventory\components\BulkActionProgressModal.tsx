"use client";

import { Modal } from "@/components/ui/modal";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, XCircle, Clock, Loader2 } from "lucide-react";

interface BulkActionProgressModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  actionType: "cancel-delegation" | "unlist-market";
  progress: {
    current: number;
    total: number;
    status: string;
  };
  onClose?: () => void;
}

export function BulkActionProgressModal({
  isOpen,
  onOpenChange,
  actionType,
  progress,
  onClose,
}: BulkActionProgressModalProps) {
  const actionConfig = {
    "cancel-delegation": {
      title: "Cancelling Delegations",
      icon: "🔄",
      color: "orange",
    },
    "unlist-market": {
      title: "Unlisting from Market",
      icon: "🏪",
      color: "red",
    },
  };

  const config = actionConfig[actionType];
  const progressPercentage =
    progress.total > 0 ? (progress.current / progress.total) * 100 : 0;
  const isComplete = progress.current >= progress.total && progress.total > 0;

  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
      <Modal.Content>
        <Modal.Header>
          <Modal.Title className="flex items-center gap-2">
            <span className="text-2xl">{config.icon}</span>
            {config.title}
          </Modal.Title>
        </Modal.Header>

        <Modal.Body className="space-y-6">
          {/* Progress Bar */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-white font-medium">Progress</span>
              <span className="text-stone-300 text-sm">
                {progress.current} of {progress.total}
              </span>
            </div>

            <div className="w-full bg-stone-700 rounded-full h-3 overflow-hidden">
              <div
                className={`h-full transition-all duration-500 ease-out ${
                  config.color === "orange"
                    ? "bg-gradient-to-r from-orange-500 to-orange-400"
                    : "bg-gradient-to-r from-red-500 to-red-400"
                }`}
                style={{ width: `${progressPercentage}%` }}
              />
            </div>

            <div className="text-center">
              <span className="text-2xl font-bold text-white">
                {Math.round(progressPercentage)}%
              </span>
            </div>
          </div>

          {/* Status */}
          <div className="bg-stone-800/50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              {!isComplete ? (
                <Loader2 className="h-5 w-5 text-blue-400 animate-spin" />
              ) : (
                <CheckCircle className="h-5 w-5 text-green-400" />
              )}
              <div>
                <p className="text-white font-medium">
                  {isComplete ? "Complete!" : "Processing..."}
                </p>
                <p className="text-stone-300 text-sm">{progress.status}</p>
              </div>
            </div>
          </div>

          {/* Steps */}
          <div className="space-y-3">
            <h3 className="text-white font-medium">Process Steps</h3>
            <div className="space-y-2">
              <ProcessStep
                title="Preparing signatures"
                status={
                  progress.current >= 1
                    ? "complete"
                    : progress.current === 0
                      ? "current"
                      : "pending"
                }
              />
              <ProcessStep
                title="Simulating transaction"
                status={
                  progress.current >= 2
                    ? "complete"
                    : progress.current === 1
                      ? "current"
                      : "pending"
                }
              />
              <ProcessStep
                title="Executing blockchain transaction"
                status={
                  progress.current >= 3
                    ? "complete"
                    : progress.current === 2
                      ? "current"
                      : "pending"
                }
              />
              <ProcessStep
                title="Confirming transaction"
                status={
                  isComplete
                    ? "complete"
                    : progress.current === 3
                      ? "current"
                      : "pending"
                }
              />
            </div>
          </div>

          {/* Important Note */}
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <p className="text-blue-200 text-sm">
              <strong>Please wait:</strong> Do not close this window or refresh
              the page while the transaction is being processed. This may take a
              few minutes depending on network conditions.
            </p>
          </div>
        </Modal.Body>

        {isComplete && (
          <Modal.Footer>
            <Button
              className="w-full bg-green-600 hover:bg-green-700 text-white"
              onPress={onClose || (() => onOpenChange(false))}
            >
              Done
            </Button>
          </Modal.Footer>
        )}
      </Modal.Content>
    </Modal>
  );
}

interface ProcessStepProps {
  title: string;
  status: "pending" | "current" | "complete";
}

function ProcessStep({ title, status }: ProcessStepProps) {
  const getIcon = () => {
    switch (status) {
      case "complete":
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case "current":
        return <Loader2 className="h-4 w-4 text-blue-400 animate-spin" />;
      case "pending":
        return <Clock className="h-4 w-4 text-stone-500" />;
    }
  };

  const getTextColor = () => {
    switch (status) {
      case "complete":
        return "text-green-400";
      case "current":
        return "text-blue-400";
      case "pending":
        return "text-stone-500";
    }
  };

  return (
    <div className="flex items-center gap-3">
      {getIcon()}
      <span className={`text-sm ${getTextColor()}`}>{title}</span>
    </div>
  );
}
