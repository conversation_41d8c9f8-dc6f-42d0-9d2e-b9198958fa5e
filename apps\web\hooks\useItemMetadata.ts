"use client";

import { useState, useEffect, useMemo } from "react";

export interface ItemMetadata {
  tokenId: number;
  name: string;
  description: string;
  image: string;
  edition?: number;
  type: "Food" | "Material" | "Unknown";
}

interface MetadataCache {
  [tokenId: number]: ItemMetadata;
}

// Global cache to persist across component unmounts
const metadataCache: MetadataCache = {};

export function useItemMetadata(tokenIds: number[]) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get cached and missing items
  const { cached, missing } = useMemo(() => {
    const cached: ItemMetadata[] = [];
    const missing: number[] = [];

    tokenIds.forEach(tokenId => {
      if (metadataCache[tokenId]) {
        cached.push(metadataCache[tokenId]);
      } else {
        missing.push(tokenId);
      }
    });

    return { cached, missing };
  }, [tokenIds]);

  // Fetch missing metadata
  useEffect(() => {
    if (missing.length === 0) return;

    const fetchMetadata = async () => {
      setLoading(true);
      setError(null);

      try {
        // Batch fetch with Promise.allSettled to handle individual failures
        const promises = missing.map(async (tokenId) => {
          try {
            const response = await fetch(`/api/metadata/${tokenId}`);
            
            if (!response.ok) {
              console.error(`API response not ok for item ${tokenId}:`, response.status, response.statusText);
              throw new Error(`Failed to fetch metadata for item ${tokenId}: ${response.status}`);
            }

            const data = await response.json();
            console.log(`Raw API response for item ${tokenId}:`, data);
            
            // Extract type from attributes
            const typeAttribute = data.attributes?.find(
              (attr: any) => attr.trait_type === "Type"
            );
            
            console.log(`Type attribute for item ${tokenId}:`, typeAttribute);
            
            const metadata: ItemMetadata = {
              tokenId,
              name: data.name || `Item #${tokenId}`,
              description: data.description || "No description available",
              image: data.image || `https://sabong-saga-resources.s3.ap-southeast-1.amazonaws.com/${tokenId}.png`,
              edition: data.edition,
              type: typeAttribute?.value === "Food" ? "Food" : 
                    typeAttribute?.value === "Material" ? "Material" : "Unknown"
            };

            console.log(`Processed metadata for item ${tokenId}:`, metadata);

            // Cache the result
            metadataCache[tokenId] = metadata;
            
            return metadata;
          } catch (err) {
            console.error(`Error fetching metadata for item ${tokenId}:`, err);
            
            // Create fallback metadata
            const fallbackMetadata: ItemMetadata = {
              tokenId,
              name: `Item #${tokenId}`,
              description: "Failed to load description",
              image: `https://sabong-saga-resources.s3.ap-southeast-1.amazonaws.com/${tokenId}.png`,
              type: "Unknown"
            };

            // Cache the fallback to avoid repeated failed requests
            metadataCache[tokenId] = fallbackMetadata;
            
            return fallbackMetadata;
          }
        });

        await Promise.allSettled(promises);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch metadata");
        console.error("Error fetching item metadata:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchMetadata();
  }, [missing]);

  // Return all metadata (cached + newly fetched)
  const metadata = useMemo(() => {
    return tokenIds.map(tokenId => metadataCache[tokenId]).filter(Boolean);
  }, [tokenIds, loading]); // Include loading to trigger updates

  return {
    metadata,
    loading,
    error,
    isComplete: metadata.length === tokenIds.length
  };
}

// Helper hook for a single item
export function useItemMetadataSingle(tokenId: number) {
  const { metadata, loading, error } = useItemMetadata([tokenId]);
  
  return {
    metadata: metadata[0] || null,
    loading,
    error
  };
}

// Clear cache utility (useful for development/testing)
export function clearMetadataCache() {
  Object.keys(metadataCache).forEach(key => {
    delete metadataCache[Number(key)];
  });
}