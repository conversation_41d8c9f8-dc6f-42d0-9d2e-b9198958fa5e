"use client";

import { Input } from "@/components/ui/field";
import React from "react";

interface ISearchBarProps {
  searchTokenId: string;
  setSearchTokenId: (value: string) => void;
  totalItems: number;
  filteredItems: number;
}

export default function SearchBar({
  searchTokenId,
  setSearchTokenId,
  totalItems,
  filteredItems,
}: ISearchBarProps): React.ReactNode {
  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6">
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="flex-1">
          <div className="flex justify-between items-center mb-2">
            <label
              htmlFor="tokenId"
              className="block text-sm font-medium text-gray-700"
            >
              Search by Token ID
            </label>
            {searchTokenId.trim() && (
              <button
                type="button"
                onClick={() => setSearchTokenId("")}
                className="text-xs text-primary hover:text-primary/80 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3 w-3 mr-1"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
                Clear search
              </button>
            )}
          </div>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <Input
              id="tokenId"
              type="text"
              value={searchTokenId}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setSearchTokenId(e.target.value)
              }
              placeholder="Enter token ID to filter..."
              className="w-full pl-10 border border-gray-300 rounded-lg text-gray-700 focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all"
            />
          </div>
          {searchTokenId.trim() ? (
            <div className="mt-2 flex items-center">
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                Showing {filteredItems} of {totalItems} total items
              </span>
            </div>
          ) : (
            <p className="mt-1 text-xs text-gray-500">
              Enter a token ID to filter the results
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
