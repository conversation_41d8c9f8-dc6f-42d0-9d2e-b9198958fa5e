import { MvpLeaderboardApi } from "@/services/mvp-leaderboard";
import { LeaderboardType } from "@/types/mvp-leaderboard.type";
import { useQuery } from "@tanstack/react-query";

export function useMvpLeaderboard(
  type: LeaderboardType,
  page: number,
  limit: number
) {
  return useQuery({
    queryKey: ["leaderboard", type, page, limit],
    queryFn: () => MvpLeaderboardApi.getMvpLeaderBoard(type, page, limit),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
}
