import axios from "axios";
import { env } from "../env";
import { NFTMetadata } from "../types/nfts";

export const getMetadata = async ({
  tokenId,
}: {
  tokenId: number;
}): Promise<NFTMetadata | null> => {
  try {
    const { data } = await axios.get(
      `${env.IPFS_ENDPOINT}/${env.METADATA_CID}/${tokenId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return data;
  } catch (error) {
    return null;
  }
};
