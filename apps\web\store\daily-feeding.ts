import { ConnectorErrorType } from "@sky-mavis/tanto-connect";
import { toast } from "sonner";
import {
  UserRejectedRequestError,
  ContractFunctionExecutionError,
  Address,
} from "viem";
import { create } from "zustand";
import erc1155Abi from "@/abi/Erc1155.abi.json";
import { ronin, saigon } from "viem/chains";
import dailyFeedAbi from "@/abi/DailyFeed.abi.json";
import useFoodCraftingStore from "./food-crafting";
import { delay } from "@/utils/delay";
import {
  StatType,
  CollectionType,
  SignatureResponse,
  FeedParams,
} from "@/types/daily-feed.type";

type DailyFeedState = {
  isPending: boolean;
  approvingResources: boolean;
  approvedResources: boolean;
  fetchingSignature: boolean;
};

type Actions = {
  feedNFT: (
    collectionId: 0 | 1,
    tokenId: number,
    itemId: number,
    amount: number,
    debuffStatChoice: StatType
  ) => Promise<void>;
  approveResources: () => Promise<void>;
  checkApproval: () => Promise<boolean>;
  getNonce: (address: string) => Promise<number>;
  clearStore: () => void;
};

type StoreState = DailyFeedState & Actions;

const ADDRESSES = {
  RESOURCES_TOKEN: process.env.NEXT_PUBLIC_GAMEITEMS_CONTRACT as Address,
  DAILY_FEED: process.env.NEXT_PUBLIC_DAILY_FEED_CONTRACT as Address,
};

const CHAIN_ID = Number(process.env.NEXT_PUBLIC_CHAINDID || "2021");
const chain = CHAIN_ID === 2020 ? ronin : saigon;

// Custom error mappings for V2 contract
const CONTRACT_ERRORS = {
  ErrInvalidItemConfig: "Invalid item configuration",
  ErrInvalidSignature: "Invalid signature - please try again",
  ErrSignatureExpired: "Signature expired - please try again",
  ErrInvalidNonce: "Invalid nonce - please refresh and try again",
  ErrMaxStatsReached: "Maximum stat boosts reached (4 limit)",
  ErrStatAlreadyBoosted: "This stat is already boosted",
  ErrNoActiveBoostedStats: "No active boosted stats to debuff",
  ErrInvalidStatChoice: "Invalid stat choice for debuff",
  ErrNotAuthorized: "Not authorized to perform this action",
  ErrInvalidItemType: "Invalid item type",
  ErrItemNotActive: "This item is not active",
  ErrInsufficientBalance: "Insufficient balance",
  ErrInvalidCollection: "Invalid collection type",
  ErrInvalidAmount: "Invalid amount specified",
};

const handleError = (
  error: unknown,
  defaultMessage: string,
  operation: string
) => {
  if (error instanceof UserRejectedRequestError) {
    toast.error("Transaction rejected", {
      description: "You rejected the transaction in your wallet",
      position: "top-right",
    });
    throw error;
  }

  if (error instanceof ContractFunctionExecutionError) {
    // Check for custom contract errors
    const errorMessage = error.shortMessage || error.message;
    let customMessage = errorMessage;

    // Look for custom error patterns
    for (const [errorKey, friendlyMessage] of Object.entries(CONTRACT_ERRORS)) {
      if (errorMessage.includes(errorKey)) {
        customMessage = friendlyMessage;
        break;
      }
    }

    toast.error("Contract Error", {
      description: customMessage,
      position: "top-right",
    });
    throw error;
  }

  // Handle specific error types
  if (error instanceof Error) {
    if (error.name === ConnectorErrorType.PROVIDER_NOT_FOUND) {
      window.open("https://wallet.roninchain.com", "_blank");
      toast.error("Wallet not found", {
        description: "Please install Ronin Wallet to continue",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("user rejected")) {
      toast.error("Transaction rejected", {
        description: "You rejected the transaction in your wallet",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("insufficient funds")) {
      toast.error("Insufficient funds", {
        description: "You don't have enough funds to complete this transaction",
        position: "top-right",
      });
      throw error;
    }

    // Handle API errors
    if (error.message.includes("signature")) {
      toast.error("Signature Error", {
        description: "Failed to generate valid signature. Please try again.",
        position: "top-right",
      });
      throw error;
    }
  }

  const err = error as Error;
  toast.error(err.name || "Error", {
    description: err.message || defaultMessage,
    position: "top-right",
  });
  throw err;
};

/**
 * Get signature from API for feeding
 */
async function getFeedSignature(
  feederAddress: string,
  collectionType: CollectionType,
  nftTokenId: number,
  feedItemId: number,
  amount: number,
  debuffStatChoice: StatType,
  nonce: number
): Promise<SignatureResponse> {
  const res = await fetch("/csrf-token");
  if (!res.ok) {
    throw new Error(`API call failed: ${res.statusText}`);
  }
  const { csrfToken } = await res.json();
  const response = await fetch("/api/daily-feed/single", {
    method: "POST",
    headers: {
      "X-CSRF-Token": csrfToken,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      feederAddress,
      collectionType,
      nftTokenId,
      feedItemId,
      amount,
      debuffStatChoice,
      nonce,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.error || `HTTP ${response.status}: ${response.statusText}`
    );
  }

  return response.json();
}

const initialState = {
  isPending: false,
  approvingResources: false,
  approvedResources: false,
  fetchingSignature: false,
};

const useDailyFeedStore = create<StoreState>()((set, get) => ({
  ...initialState,

  feedNFT: async (collectionId, tokenId, itemId, amount, debuffStatChoice) => {
    const stateContext = window.stateContext;
    if (!stateContext) {
      toast.error("Cannot feed NFT", {
        description: "State context not available",
        position: "top-right",
      });
      return;
    }

    const { address, publicClient, walletClient, ConnectRecentWallet } =
      stateContext;
    const { fetchFoodBalance } = useFoodCraftingStore.getState();
    await ConnectRecentWallet();
    const { approvedResources, approveResources, getNonce } = get();

    try {
      set({ isPending: true });

      if (!approvedResources) {
        await approveResources();
      }

      if (!address || !walletClient) {
        throw new Error("Wallet not connected");
      }

      // Get current nonce
      set({ fetchingSignature: true });
      const nonce = await getNonce(address);

      // Get signature from API
      const signatureData = await getFeedSignature(
        address,
        collectionId as CollectionType,
        tokenId,
        itemId,
        amount,
        debuffStatChoice,
        nonce
      );

      set({ fetchingSignature: false });

      // Prepare feed parameters
      const feedParams: FeedParams = {
        collectionType: collectionId as CollectionType,
        nftTokenId: tokenId,
        feedItemId: itemId,
        amount,
        affectionPoints: signatureData.affectionPoints,
        debuffStatChoice,
      };

      // Simulate the contract call
      const simulateReq = await publicClient.simulateContract({
        address: ADDRESSES.DAILY_FEED,
        abi: dailyFeedAbi,
        functionName: "feedNFT",
        args: [feedParams, signatureData.deadline, signatureData.signature],
        chain,
        account: address,
      });

      // Execute the transaction
      const hash = await walletClient.writeContract(simulateReq.request);

      toast.info("Feed transaction sent", {
        description: `Transaction hash: ${hash}`,
        position: "top-right",
      });

      await delay(3000);

      // Wait for transaction to be mined
      const receipt = await publicClient.waitForTransactionReceipt({ hash });

      if (receipt.status === "success") {
        // Refresh balances after successful feed
        await Promise.all([fetchFoodBalance(), get().checkApproval()]);
      } else {
        toast.error("Feed transaction failed", {
          description: "The transaction was processed but failed on-chain",
          position: "top-center",
        });
      }
    } catch (error) {
      console.log(error);

      handleError(error, "Failed to feed NFT", "NFT feeding");
      throw error;
    } finally {
      set({ isPending: false, fetchingSignature: false });
    }
  },

  getNonce: async (address: string) => {
    const stateContext = window.stateContext;
    if (!stateContext) {
      throw new Error("State context not available");
    }

    const { publicClient } = stateContext;

    try {
      const nonce = await publicClient.readContract({
        address: ADDRESSES.DAILY_FEED,
        abi: dailyFeedAbi,
        functionName: "getAddressNonce",
        args: [address as Address],
      });

      return Number(nonce);
    } catch (error) {
      handleError(error, "Failed to fetch nonce", "nonce fetching");
      throw error;
    }
  },

  approveResources: async () => {
    const stateContext = window.stateContext;
    if (!stateContext) {
      toast.error("Cannot approve ERC1155", {
        description: "State context not available",
        position: "top-right",
      });
      return;
    }

    const { address, publicClient, walletClient, ConnectRecentWallet } =
      stateContext;

    try {
      await ConnectRecentWallet();

      if (!address || !walletClient) {
        toast.error("Cannot approve ERC1155", {
          description: "Wallet not connected",
          position: "top-right",
        });
        return;
      }

      set({ approvingResources: true });
      toast.info("Preparing approval transaction...", {
        description: "Setting approval for all Resources tokens",
        position: "top-right",
      });

      // Set approval for all for ERC1155 token
      const hash = await walletClient.writeContract({
        address: ADDRESSES.RESOURCES_TOKEN,
        abi: erc1155Abi,
        functionName: "setApprovalForAll",
        args: [ADDRESSES.DAILY_FEED, true],
        chain,
        account: address,
      });

      toast.info("Approval transaction sent", {
        description: `Transaction hash: ${hash}`,
        position: "top-right",
      });

      // Wait for transaction to be mined
      const receipt = await publicClient.waitForTransactionReceipt({ hash });

      if (receipt.status === "success") {
        toast.success("ERC1155 approval successful", {
          description:
            "Successfully approved all Resources tokens for spending",
          position: "top-right",
        });

        set({ approvedResources: true });
      } else {
        toast.error("ERC1155 approval failed", {
          description: "The transaction was processed but failed on-chain",
          position: "top-right",
        });
      }
    } catch (error) {
      handleError(error, "Failed to approve ERC1155 token", "ERC1155 approval");
      throw error;
    } finally {
      set({ approvingResources: false });
    }
  },

  checkApproval: async () => {
    const stateContext = window.stateContext;
    if (!stateContext || !stateContext.address) {
      return false;
    }
    const { address, publicClient } = stateContext;

    try {
      const isApprove = await publicClient?.readContract({
        address: ADDRESSES.RESOURCES_TOKEN,
        abi: erc1155Abi,
        functionName: "isApprovedForAll",
        args: [address as Address, ADDRESSES.DAILY_FEED],
      });
      set({ approvedResources: Boolean(isApprove) });
      return Boolean(isApprove);
    } catch (error) {
      handleError(error, "Failed to fetch approval", "approval check");
      set({ approvedResources: false });
      return false;
    }
  },

  clearStore: () => {
    set({ ...initialState });
  },
}));

export default useDailyFeedStore;
