import type { Metadata } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";
import localFont from "next/font/local";

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  display: "swap",
});

const arcadia = localFont({
  src: "../public/fonts/Arcadia.woff",
  display: "swap",
  variable: "--font-arcadia",
});

export const metadata: Metadata = {
  title: "Sabong Saga",
  description:
    "Welcome to Sabong Saga, a blockchain game that merges the excitement of traditional cockfighting with the innovation of Web3. In Sabong Saga, players can collect, breed, battle, and trade digital roosters.Real-life cockfighting is harmful and unnecessary, causing animal suffering for sport. Sabong Saga offers a humane alternative, channeling the competitive spirit of cockfighting into a digital, cruelty-free game that preserves the thrill—without harming any chickens. Save chicken lives, play Sabong Saga! ",
  openGraph: {
    images: "/images/sabongsaga-poster.jpg",
  },
  twitter: {
    card: "summary_large_image",
    title: "Sabong Saga",
    description:
      "Welcome to Sabong Saga, a blockchain game that merges the excitement of traditional cockfighting with the innovation of Web3. In Sabong Saga, players can collect, breed, battle, and trade digital roosters.Real-life cockfighting is harmful and unnecessary, causing animal suffering for sport. Sabong Saga offers a humane alternative, channeling the competitive spirit of cockfighting into a digital, cruelty-free game that preserves the thrill—without harming any chickens. Save chicken lives, play Sabong Saga! ",
    images: ["/images/sabongsaga-poster.jpg"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${arcadia.variable} ${poppins.variable} antialiased`}>
        {children}
      </body>
    </html>
  );
}
