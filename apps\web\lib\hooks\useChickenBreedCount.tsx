"use client";

import { useQuery } from "@tanstack/react-query";
import React from "react";
import { Address } from "viem";
import { readContract } from "@/lib/blockchain/contractReader";
import { breedingAbi } from "@/providers/web3/abi/breeding-abi";
import useBlockchain from "./useBlockchain";

/**
 * Hook to fetch the actual breed count from the breeding contract
 * @param breedingAddress - Address of the breeding contract
 * @param tokenIds - Array of chicken token IDs to get breed counts for
 * @returns Query result with breed counts
 */
export function useChickenBreedCount(
  breedingAddress: Address | undefined,
  tokenIds: number[] | undefined
) {
  const fetchBreedCounts = async () => {
    if (!breedingAddress || !tokenIds || tokenIds.length === 0) {
      return [];
    }

    try {
      // Convert tokenIds to BigInt array for contract call
      const bigIntTokenIds = tokenIds.map((id) => BigInt(id));

      // Call the contract function to get breed counts
      const result = await readContract<bigint[]>({
        address: breedingAddress,
        abi: breedingAbi,
        functionName: "getChickenBreedCountBatch",
        args: [bigIntTokenIds],
      });

      // Convert BigInt results to numbers
      return result.map((count) => Number(count));
    } catch (error) {
      console.error("Error fetching chicken breed counts:", error);
      throw error;
    }
  };

  return useQuery({
    queryKey: ["chickenBreedCounts", breedingAddress, tokenIds],
    queryFn: fetchBreedCounts,
    enabled: !!breedingAddress && !!tokenIds && tokenIds.length > 0,
  });
}

/**
 * Convenience hook that gets the blockchain config first, then fetches breed counts
 * @param tokenIds - Array of chicken token IDs to get breed counts for
 * @returns Query result with breed counts and a mapping of tokenId to breed count
 */
export function useChickenBreedCountWithConfig(tokenIds: number[] | undefined) {
  const { blockchainQuery } = useBlockchain();
  const breedingAddress = blockchainQuery.data?.breeding_address as
    | Address
    | undefined;

  const breedCountQuery = useChickenBreedCount(breedingAddress, tokenIds);

  // Create a mapping of tokenId to breed count for easier lookup
  const breedCountMap = React.useMemo(() => {
    if (!tokenIds || !breedCountQuery.data) {
      return {};
    }

    return tokenIds.reduce(
      (map, tokenId, index) => {
        map[tokenId] = breedCountQuery.data[index] || 0;
        return map;
      },
      {} as Record<number, number>
    );
  }, [tokenIds, breedCountQuery.data]);

  return {
    breedCountQuery,
    breedCountMap,
    isLoading: blockchainQuery.isLoading || breedCountQuery.isLoading,
    isError: blockchainQuery.isError || breedCountQuery.isError,
  };
}
