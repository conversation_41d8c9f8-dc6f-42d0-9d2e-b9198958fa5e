"use client";
import { useState } from "react";

export default function Banner() {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-[#1c3d0b] text-white py-3 px-4 flex justify-between items-center z-50">
      <div className="flex w-full items-center justify-center lg:hidden">
        <div className="flex w-full items-center justify-center">
          <span className="animate-pulse mr-2">🪶</span>
          <p className="font-medium text-center text-lg">
            Feather generation is currently paused due to{" "}
            <a
              href="https://x.com/SabongSaga/status/1892768522310472187"
              target="_blank"
              rel="noopener noreferrer"
              className="underline hover:text-green-300 transition-colors"
            >
              Featherfall
            </a>
            .
          </p>
        </div>
        <div className="flex justify-end">
          <button
            onClick={() => setIsVisible(false)}
            className="text-white hover:text-green-300 transition-colors"
            aria-label="Close banner"
          >
            ✕
          </button>
        </div>
      </div>
      <div className="hidden lg:flex w-full">
        <div className="flex-1" /> {/* This empty div helps with centering */}
        <div className="flex items-center justify-center flex-1">
          <span className="animate-pulse mr-2">🪶</span>
          <p className="font-medium text-center text-lg">
            Feather generation is currently paused due to{" "}
            <a
              href="https://x.com/SabongSaga/status/1892768522310472187"
              target="_blank"
              rel="noopener noreferrer"
              className="underline hover:text-green-300 transition-colors"
            >
              Featherfall
            </a>
            .
          </p>
        </div>
        <div className="flex-1 flex justify-end">
          <button
            onClick={() => setIsVisible(false)}
            className="text-white hover:text-green-300 transition-colors"
            aria-label="Close banner"
          >
            ✕
          </button>
        </div>
      </div>
    </div>
  );
}
