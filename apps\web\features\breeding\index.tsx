"use client";

import { Tabs } from "@/components/ui";
import { useGlobalState } from "@/lib/store";
import { useHookstate } from "@hookstate/core";
import { BreedingHeader } from "./components/breeding-header";
import Breeding from "./tab/breeding";
import OptimizedHatching from "./tab/hatching/optimized-hatching-index";
import { useEffect } from "react";
import { useOptimizedBreeding } from "./tab/breeding/hooks/useBreeding";

export function BreedingTab() {
  const gState = useGlobalState();
  const activeTab = useHookstate(gState.breeding.tab);
  const { state } = useOptimizedBreeding();

  const handleTabChange = (id: "breeding" | "hatching") => {
    activeTab.set(id);
  };

  // Load referral code from localStorage if available
  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedReferralCode = localStorage.getItem("referralCode");
      if (savedReferralCode) {
        state.savedReferralCode.set(savedReferralCode);
      }
    }
  }, []);

  return (
    <div className="flex flex-col w-full">
      {/* Header above tabs */}
      <BreedingHeader
        title={activeTab.value === "breeding" ? "Breeding" : "Hatching"}
      />

      <Tabs
        aria-label="Breeding Tabs"
        className="w-full"
        selectedKey={activeTab.value}
        onSelectionChange={(key) =>
          handleTabChange(key as "breeding" | "hatching")
        }
      >
        <Tabs.List className="flex justify-center w-full mb-8 border-none">
          <Tabs.Tab
            id="breeding"
            className={({ isSelected }) =>
              isSelected ? "text-primary hover:text-primary px-12" : "px-12"
            }
            indicatorClassName="bg-primary"
          >
            Breeding
          </Tabs.Tab>
          <Tabs.Tab
            id="hatching"
            className={({ isSelected }) =>
              isSelected ? "text-primary hover:text-primary px-12" : "px-12"
            }
            indicatorClassName="bg-primary"
          >
            Hatching
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel id="breeding" className="mt-0">
          <Breeding />
        </Tabs.Panel>

        <Tabs.Panel id="hatching" className="mt-0">
          <OptimizedHatching />
        </Tabs.Panel>
      </Tabs>
    </div>
  );
}
