import axios from "axios";
import { env } from "../env";
import { NFTMetadata } from "../types/nfts";

export const getRevealMetadata = async ({
  tokenId,
}: {
  tokenId: number | string;
}): Promise<NFTMetadata | null> => {
  try {
    const { data } = await axios.get(
      `${env.REVEAL_METADATA_API_ENDPOINT}/${tokenId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return data;
  } catch (error) {
    console.log(error);

    throw error;
  }
};
