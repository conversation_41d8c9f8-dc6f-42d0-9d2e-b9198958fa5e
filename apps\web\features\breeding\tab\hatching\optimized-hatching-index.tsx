"use client";

import LoadingMessage from "@/components/common/loading/loading-message";
import {
  useOptimizedBreedingPhaseEggs,
  useOptimizedHatchingPhaseEggs,
} from "./hooks/useOptimizedHatching";
import { EggCard } from "./components/egg-card";
import { IEggInfo } from "./types/egg-info.types";
import { useState } from "react";
import LoadingScreenHatching from "@/components/common/loading/loading-screen-hatching";
import { Button, Modal } from "@/components/ui";
import { IconCheck, IconChevronLeft, IconChevronRight } from "justd-icons";
import { useEggHatching } from "./hooks/useEggHatching";

interface ISuccessDialogState {
  isOpen: boolean;
  eggId?: number;
  parentIds?: { parent1: number; parent2: number };
}

interface IHatchingAnimationState {
  isShowing: boolean;
  eggId?: number;
  parentIds?: { parent1: number; parent2: number };
}

export default function OptimizedHatching() {
  // Use separate hooks for breeding and hatching phase eggs with pagination
  const breedingEggsQuery = useOptimizedBreedingPhaseEggs();
  const hatchingEggsQuery = useOptimizedHatchingPhaseEggs();

  // Use the existing egg hatching functionality
  const { hatchEgg, isHatching } = useEggHatching({
    ...breedingEggsQuery.eggInfoMap,
    ...hatchingEggsQuery.eggInfoMap,
  });

  const [successDialog, setSuccessDialog] = useState<ISuccessDialogState>({
    isOpen: false,
    eggId: undefined,
    parentIds: undefined,
  });

  const [hatchingAnimation, setHatchingAnimation] =
    useState<IHatchingAnimationState>({
      isShowing: false,
      eggId: undefined,
      parentIds: undefined,
    });

  const isLoading = breedingEggsQuery.isLoading || hatchingEggsQuery.isLoading;
  const totalEggs =
    breedingEggsQuery.stats.total + hatchingEggsQuery.stats.total;

  return (
    <div className="space-y-4">
      {/* Egg Count Display */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0">
        <h1 className="text-xl sm:text-2xl font-bold text-white">Hatching</h1>
        <div className="text-sm text-stone-400">
          {isLoading ? (
            <span>Loading eggs...</span>
          ) : (
            <span>
              Total eggs: {totalEggs} (Breeding:{" "}
              {breedingEggsQuery.stats.breeding}, Hatching:{" "}
              {hatchingEggsQuery.stats.hatching})
            </span>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
        {/* Left Side: Breeding Phase */}
        <div className="bg-stone-900/50 border border-stone-800 rounded-lg p-3 sm:p-4 md:p-6">
          <div className="flex justify-between items-center mb-2 sm:mb-4">
            <h2 className="text-lg sm:text-xl font-bold text-white">
              Breeding Phase
            </h2>
            {breedingEggsQuery.totalPages > 1 && (
              <div className="flex items-center gap-2 text-sm text-stone-400">
                <Button
                  intent="secondary"
                  size="small"
                  onPress={breedingEggsQuery.goToPreviousPage}
                  isDisabled={breedingEggsQuery.currentPage === 1}
                >
                  <IconChevronLeft className="w-4 h-4" />
                </Button>
                <span>
                  {breedingEggsQuery.currentPage} /{" "}
                  {breedingEggsQuery.totalPages}
                </span>
                <Button
                  intent="secondary"
                  size="small"
                  onPress={breedingEggsQuery.goToNextPage}
                  isDisabled={!breedingEggsQuery.hasMore}
                >
                  <IconChevronRight className="w-4 h-4" />
                </Button>
              </div>
            )}
          </div>

          <p className="text-stone-400 text-sm sm:text-base mb-3 sm:mb-6">
            These eggs are still being, uhm, fertilized. The parent chickens are
            still at Brokebeak Mountain doing… we don&apos;t even wanna know
            what.
          </p>

          {breedingEggsQuery.isLoading ? (
            <LoadingMessage message="Loading Breeding Phase Eggs" />
          ) : breedingEggsQuery.currentPageEggTokenIds.length > 0 ? (
            <div className="grid grid-cols-1 gap-6">
              {breedingEggsQuery.currentPageEggTokenIds.map((egg) => (
                <div key={egg}>
                  <EggCard
                    tokenId={egg}
                    phase="breeding"
                    eggInfo={breedingEggsQuery.eggInfoMap[egg] as IEggInfo}
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-40 sm:h-64 text-center">
              <p className="text-stone-500 mb-2">No eggs in breeding phase</p>
              <p className="text-stone-400 text-sm">
                Breed some chickens to create eggs
              </p>
            </div>
          )}
        </div>

        {/* Right Side: Hatching Phase */}
        <div className="bg-stone-900/50 border border-stone-800 rounded-lg p-3 sm:p-4 md:p-6">
          <div className="flex justify-between items-center mb-2 sm:mb-4">
            <h2 className="text-lg sm:text-xl font-bold text-white">
              Hatching Phase
            </h2>
            {hatchingEggsQuery.totalPages > 1 && (
              <div className="flex items-center gap-2 text-sm text-stone-400">
                <Button
                  intent="secondary"
                  size="small"
                  onPress={hatchingEggsQuery.goToPreviousPage}
                  isDisabled={hatchingEggsQuery.currentPage === 1}
                >
                  <IconChevronLeft className="w-4 h-4" />
                </Button>
                <span>
                  {hatchingEggsQuery.currentPage} /{" "}
                  {hatchingEggsQuery.totalPages}
                </span>
                <Button
                  intent="secondary"
                  size="small"
                  onPress={hatchingEggsQuery.goToNextPage}
                  isDisabled={!hatchingEggsQuery.hasMore}
                >
                  <IconChevronRight className="w-4 h-4" />
                </Button>
              </div>
            )}
          </div>

          <p className="text-stone-400 text-sm sm:text-base mb-3 sm:mb-6">
            These eggs are now being incubated. Once they&apos;re ready, you can
            hatch them to reveal your new chicken!
          </p>

          {hatchingEggsQuery.isLoading ? (
            <LoadingMessage message="Loading Hatching Phase Eggs" />
          ) : hatchingEggsQuery.currentPageEggTokenIds.length > 0 ? (
            <div className="grid grid-cols-1 gap-6">
              {hatchingEggsQuery.currentPageEggTokenIds.map((egg) => (
                <div key={egg}>
                  <EggCard
                    tokenId={egg}
                    phase="hatching"
                    eggInfo={hatchingEggsQuery.eggInfoMap[egg] as IEggInfo}
                    onHatch={() =>
                      hatchEgg(egg, (eggId, parentIds) => {
                        // First show the hatching animation
                        setHatchingAnimation({
                          isShowing: true,
                          eggId,
                          parentIds,
                        });

                        // After a delay, hide the animation and show the success dialog
                        setTimeout(() => {
                          setHatchingAnimation((prev) => ({
                            ...prev,
                            isShowing: false,
                          }));
                          setSuccessDialog({
                            isOpen: true,
                            eggId,
                            parentIds,
                          });
                        }, 3000); // Show animation for 3 seconds
                      })
                    }
                    isHatching={isHatching === egg}
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-40 sm:h-64 text-center">
              <p className="text-stone-500 mb-2">No eggs ready to hatch</p>
              <p className="text-stone-400 text-sm">
                Wait for your breeding phase eggs to develop
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Hatching Animation */}
      {hatchingAnimation.isShowing && (
        <LoadingScreenHatching
          message={`Hatching egg #${hatchingAnimation.eggId}`}
        />
      )}

      {/* Success Dialog */}
      <Modal
        isOpen={successDialog.isOpen}
        onOpenChange={(isOpen) =>
          setSuccessDialog((prev) => ({ ...prev, isOpen }))
        }
      >
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>Hatching Successful!</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
                <IconCheck className="w-8 h-8 text-white" />
              </div>
              <p className="text-lg font-semibold">
                Congratulations! Your egg #{successDialog.eggId} has hatched!
              </p>
              <p className="text-stone-400">
                Your new chicken is now available in your inventory.
              </p>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button
              intent="primary"
              onPress={() =>
                setSuccessDialog((prev) => ({ ...prev, isOpen: false }))
              }
            >
              Continue
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>
    </div>
  );
}
