import { ConnectorErrorType } from "@sky-mavis/tanto-connect";
import { toast } from "sonner";
import {
  UserRejectedRequestError,
  ContractFunctionExecutionError,
  Address,
} from "viem";
import { create } from "zustand";
import { ronin, saigon } from "viem/chains";
import ClaimAbi from "../abi/ClaimReward.abi.json";
import { delay } from "@/utils/delay";

type SignatureWithData = {
  signature: string;
  contractAddress: string;
  claimant: string;
  itemContracts: string[];
  tokenids: number[];
  amounts: number[];
  deadline: number;
};

type ClaimRewardState = {
  isPending: boolean;
  canClaim: boolean;
  ableToClaimIn: bigint;
};

type Actions = {
  clearStore: () => void;
  getGameClaimSignature: (
    contracts: string[],
    tokenIds: number[],
    amounts: number[]
  ) => Promise<SignatureWithData>;
  claimItems: (data: SignatureWithData) => Promise<`0x${string}`>;
  canClaimFunc: () => Promise<void>;
  ableToClaimInFunc: () => Promise<void>;
};

type StoreState = ClaimRewardState & Actions;

const CHAIN_ID = Number(process.env.NEXT_PUBLIC_CHAINDID || "2021");
const chain = CHAIN_ID === 2020 ? ronin : saigon;

const handleError = (
  error: unknown,
  defaultMessage: string,
  operation: string
) => {
  if (error instanceof UserRejectedRequestError) {
    toast.error("Transaction rejected", {
      description: "You rejected the transaction in your wallet",
      position: "top-right",
    });
    throw error;
  }
  if (error instanceof ContractFunctionExecutionError) {
    toast.error(error.name, {
      description: error.shortMessage,
      position: "top-right",
    });
    throw error;
  }

  // Handle specific error types
  if (error instanceof Error) {
    if (error.name === ConnectorErrorType.PROVIDER_NOT_FOUND) {
      window.open("https://wallet.roninchain.com", "_blank");
      toast.error("Wallet not found", {
        description: "Please install Ronin Wallet to continue",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("user rejected")) {
      toast.error("Transaction rejected", {
        description: "You rejected the transaction in your wallet",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("insufficient funds")) {
      toast.error("Insufficient funds", {
        description: "You don't have enough funds to complete this transaction",
        position: "top-right",
      });
      throw error;
    }
  } else {
    const err = error as Error;

    // Fallback for non-Error objects
    toast.error(err.name, {
      description: err.message,
      position: "top-right",
    });
    throw err;
  }
};

const initialState = {
  isPending: false,
  canClaim: false,
  ableToClaimIn: 0n,
};

const useClaimRewardStore = create<StoreState>()((set, get) => ({
  ...initialState,
  getGameClaimSignature: async (contracts, tokenIds, amounts) => {
    try {
      set({ isPending: true });
      const res = await fetch("/csrf-token");
      if (!res.ok) {
        throw new Error(`API call failed: ${res.statusText}`);
      }
      const { csrfToken } = await res.json();

      const response = await fetch("/api/rewards/claim/signature", {
        method: "POST",
        headers: {
          "X-CSRF-Token": csrfToken,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          tokenIds,
          contracts,
          amounts,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Purchase API failed: ${response.status} ${errorText}`);
      }

      const data: { data: SignatureWithData } = await response.json();
      return data.data;
    } catch (error) {
      handleError(error, "Failed to get signature", "getSignature");
      throw error; // rethrow after handling
    } finally {
      set({ isPending: false });
    }
  },
  canClaimFunc: async () => {
    const stateContext = window.stateContext;
    if (!stateContext) {
      return;
    }
    const { address, publicClient } = stateContext;
    const canClaim = (await publicClient.readContract({
      address: process.env.NEXT_PUBLIC_REWARDS_CONTRACT as Address,
      abi: ClaimAbi,
      functionName: "canClaim",
      args: [address],
    })) as boolean;

    set({ canClaim });
  },
  ableToClaimInFunc: async () => {
    const stateContext = window.stateContext;
    if (!stateContext) {
      return;
    }
    const { address, publicClient } = stateContext;
    const ableToClaimIn = (await publicClient.readContract({
      address: process.env.NEXT_PUBLIC_REWARDS_CONTRACT as Address,
      abi: ClaimAbi,
      functionName: "getRemainingCooldown",
      args: [address],
    })) as bigint;
    set({ ableToClaimIn });
  },

  claimItems: async (data) => {
    set({ isPending: true });
    const stateContext = window.stateContext;
    if (!stateContext) {
      toast.error("Session need to relogin", {
        description: "State context not available",
        position: "top-right",
      });
      throw new Error("State context not available");
    }

    const { address, publicClient, walletClient, ConnectRecentWallet } =
      stateContext;

    try {
      await ConnectRecentWallet();
      if (!address || !walletClient) {
        toast.error("Cannot process claim", {
          position: "top-right",
          description: "Wallet not connected",
        });
        throw new Error("Wallet not connected");
      }

      // Simulate contract call to check if it will succeed
      const simulateReq = await publicClient.simulateContract({
        address: data.contractAddress as Address,
        abi: ClaimAbi,
        functionName: "claimItems",
        args: [
          data.itemContracts,
          data.tokenids,
          data.amounts,
          data.deadline,
          data.signature,
        ],
        chain,
        account: address,
      });

      // Send the transaction
      const hash = await walletClient.writeContract(simulateReq.request);

      toast.info("Claim transaction sent", {
        description: `Transaction hash: ${hash}`,
        position: "top-right",
      });

      // Wait for transaction receipt
      const receipt = await publicClient.waitForTransactionReceipt({ hash });

      if (receipt.status === "success") {
        await get().canClaimFunc();
        await get().ableToClaimInFunc();

        return hash;
      } else {
        toast.error("Claim transaction failed", {
          description: "The transaction was processed but failed on-chain",
          position: "top-center",
        });
        throw new Error("Claime transaction failed");
      }
    } catch (error) {
      handleError(error, "Failed to process claim", "processPurchase");
      throw error; // rethrow so caller knows it failed
    } finally {
      set({ isPending: false });
    }
  },

  clearStore: () => {
    set({ ...initialState });
  },
}));

export default useClaimRewardStore;
