import SABONG_CONFIG from 'Config/sabong'
import { createPublicClient, getContract, http } from 'viem'
import { ronin, saigon } from 'viem/chains'

export const initializeContracts = () => {
  const client = createPublicClient({
    chain: SABONG_CONFIG.ENV === 'production' ? ronin : saigon,
    transport: http(SABONG_CONFIG.RONIN_RPC),
  })

  const chickenLegacyContract = getContract({
    address: SABONG_CONFIG.CONTRACTS.CHICKEN_LEGACY_ADDRESS,
    abi: SABONG_CONFIG.ABIS.CHICKEN_LEGACY_ABI,
    client: client,
  })

  const chickenGenesisContract = getContract({
    address: SABONG_CONFIG.CONTRACTS.CHICKEN_GENESIS_ADDRESS,
    abi: SABONG_CONFIG.ABIS.CHICKEN_GENESIS_ABI,
    client: client,
  })

  const itemContract = getContract({
    address: SABONG_CONFIG.CONTRACTS.ITEMS_ADDRESS,
    abi: SABONG_CONFIG.ABIS.ITEMS_ABI,
    client: client,
  })

  const cockContract = getContract({
    address: SABONG_CONFIG.CONTRACTS.COCK_ADDRESS,
    abi: SABONG_CONFIG.ABIS.COCK_ABI,
    client: client,
  })

  const breedingContract = getContract({
    address: SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS,
    abi: SABONG_CONFIG.ABIS.BREEDING_ABI,
    client: client,
  })

  const resourcesContract = getContract({
    address: SABONG_CONFIG.CONTRACTS.RESOURCES_ADDRESS,
    abi: SABONG_CONFIG.ABIS.RESOURCES_ABI,
    client: client,
  })

  const rentalContract = getContract({
    address: SABONG_CONFIG.CONTRACTS.RENTAL_ADDRESS,
    abi: SABONG_CONFIG.ABIS.RENTAL_ABI,
    client: client,
  })

  const bulkFetcherContract = getContract({
    address: SABONG_CONFIG.CONTRACTS.BULK_FETCHER_ADDRESS,
    abi: SABONG_CONFIG.ABIS.BULK_FETCHER_ABI,
    client: client,
  })

  return {
    chickenLegacyContract,
    chickenGenesisContract,
    itemContract,
    cockContract,
    breedingContract,
    resourcesContract,
    rentalContract,
    bulkFetcherContract,
  }
}
