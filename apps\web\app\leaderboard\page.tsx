// app/leaderboard/page.tsx
import CardsLoaders from "@/components/shared/cards-loaders";
import AppNavbar from "@/components/shared/navbar";
import AppInitializer from "@/providers/app/initializer";
// import PrizePoolHeader from "@/components/pages/leaderboard/prize-pool"; // Temporarily disabled for Preseason 0
import dynamic from "next/dynamic";

const DynamicLeaderboard = dynamic(
  () => import("@/components/pages/leaderboard"),
  {
    ssr: true,
    loading: () => <CardsLoaders />,
  }
);

export default async function LeaderboardPage() {
  return (
    <AppInitializer>
      <div className="min-h-screen relative bg-gradient-to-b from-stone-900 via-black to-stone-900 bg-opacity-85 overflow-hidden">
        <div className="relative z-10">
          <AppNavbar />
        </div>

        {/* <PrizePoolHeader /> */}

        <div className="container mx-auto px-4 py-8">
          <DynamicLeaderboard />
        </div>
      </div>
    </AppInitializer>
  );
}
