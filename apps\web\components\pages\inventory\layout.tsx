"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { InventoryType } from "@/types/inventory.type";
import InventoryTabs from "./tabs";
import EnhancedChickens from "./enhanced-chickens";
import FeatherInventory from "./feather-inventory";
import ItemInventory from "./item-inventory";

export default function InventoryLayout() {
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState<InventoryType>("my-chickens");

  // Set initial tab based on URL parameter
  useEffect(() => {
    const tabParam = searchParams.get("tab") as InventoryType;
    if (
      tabParam &&
      ["my-chickens", "delegated-out", "feathers", "items"].includes(tabParam)
    ) {
      setActiveTab(tabParam);
    }
  }, [searchParams]);

  const renderTabContent = () => {
    switch (activeTab) {
      case "my-chickens":
        return <EnhancedChickens initialTab="owned" />;
      case "delegated-out":
        return <EnhancedChickens initialTab="delegated-out" />;
      case "feathers":
        return <FeatherInventory />;
      case "items":
        return <ItemInventory />;
      default:
        return <EnhancedChickens initialTab="owned" />;
    }
  };

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-4 sm:mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold text-center font-Arcadia text-primary mb-4 md:mb-0">
          My Inventory
        </h1>
      </div>

      <InventoryTabs activeTab={activeTab} onTabChange={setActiveTab} />

      <div className="mt-4 sm:mt-6">{renderTabContent()}</div>
    </div>
  );
}
