import axios from "axios";
import { env } from "../utils/env";
import { delay } from "../utils/delay";

const API_BASE_URL = "https://api.opensea.io/api/v2/listings/collection";

const DELAY_MS = 1000; // Delay between requests in milliseconds

// Define types for the API response and the desired output
interface Offer {
  identifierOrCriteria: string; // Token ID
}

interface Parameters {
  offerer: string; // Owner address
  offer: Offer[];
  startTime: string; // Start time
}

interface ProtocolData {
  parameters: Parameters;
}

interface Listing {
  protocol_data?: ProtocolData; // Optional property
}

interface ApiResponse {
  listings: Listing[];
  next?: string; // Cursor for pagination
}

interface ProcessedListing {
  id: string; // Token ID
  address: string; // Owner address
  startedAt: number; // Start time
}

// Type guard to ensure protocol_data is defined
function hasProtocolData(
  listing: Listing
): listing is Listing & { protocol_data: ProtocolData } {
  return listing.protocol_data !== undefined;
}

// Function to fetch all listings for a single collection
async function fetchCollectionListings(
  collectionSlug: string
): Promise<ProcessedListing[]> {
  let allListings: ProcessedListing[] = [];
  let cursor: string | null = null;
  let hasMoreData = true;
  let requestCount = 0;

  try {
    while (hasMoreData) {
      // Add delay after the first request
      if (requestCount > 0) {
        await delay(DELAY_MS);
      }
      requestCount++;

      const url: string = cursor
        ? `${API_BASE_URL}/${collectionSlug}/all?limit=100&cursor=${cursor}`
        : `${API_BASE_URL}/${collectionSlug}/all?limit=100`;

      console.log(`Making request #${requestCount} to: ${url}`);
      const response = await axios.get<ApiResponse>(url, {
        headers: {
          accept: "application/json",
          "x-api-key": env.OPENSEA_API_KEY,
        },
      });

      const data = response.data;

      // Filter listings using the type guard and then map them
      const mappedListings = (data.listings || [])
        .filter(hasProtocolData)
        .map((item) => {
          const offer = item.protocol_data.parameters.offer[0];
          if (offer) {
            return {
              id: offer.identifierOrCriteria,
              address: item.protocol_data.parameters.offerer,
              startedAt: parseInt(item.protocol_data.parameters.startTime),
            };
          }

          return null; // Return null instead of undefined
        })
        .filter((item): item is ProcessedListing => item !== null); // Filter out null values

      allListings = allListings.concat(mappedListings);

      cursor = data.next || null;
      hasMoreData = !!cursor;
    }

    return allListings;
  } catch (error: any) {
    console.error(
      `Error fetching listings for ${collectionSlug}:`,
      error.message
    );
    return [];
  }
}

// Function to fetch listings for multiple collections
export async function fetchOpAllCollections(): Promise<ProcessedListing[]> {
  const collections = ["sabong-saga-chickens", "sabong-saga-genesis"];
  let allData: ProcessedListing[] = [];

  for (const collection of collections) {
    console.log(`Fetching data for collection: ${collection}`);
    const collectionData = await fetchCollectionListings(collection);
    allData = allData.concat(collectionData);

    // Add delay between collections
    if (collections.indexOf(collection) < collections.length - 1) {
      await delay(DELAY_MS);
    }
  }

  return allData;
}
