import { ApiResponse } from "@/types/api.types";
import { NextRequest, NextResponse } from "next/server";

class ApiError extends Error {
  constructor(
    public statusCode: number,
    public message: string,
    public errors?: string[]
  ) {
    super(message);
    this.name = "ApiError";
  }
}

const API_BASE_URL = process.env.HONO_API_ENDPOINT!;

export async function proxyApi<T>(
  request: NextRequest,
  path: string,
  init?: RequestInit
): Promise<NextResponse<ApiResponse<T>>> {
  try {
    const url = new URL(path, API_BASE_URL);
    const headers = new Headers(request.headers);
    headers.delete("host");

    if (init?.headers) {
      const customHeaders = new Headers(init.headers);
      customHeaders.forEach((value, key) => {
        headers.set(key, value);
      });
    }

    // Create new request to read the body
    const requestBody = request.body
      ? await readRequestBody(request)
      : undefined;

    const response = await fetch(url, {
      ...init,
      headers,
      body: requestBody,
    });

    if (!response.ok) {
      throw new ApiError(response.status, response.statusText, [
        `API returned ${response.status}`,
      ]);
    }

    const data = await response.json();

    if (!isValidApiResponse(data)) {
      throw new ApiError(500, "Invalid API response structure");
    }

    return NextResponse.json(data as ApiResponse<T>, {
      status: response.status,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("Proxy API Error:", error);

    if (error instanceof ApiError) {
      return NextResponse.json(
        {
          status: false,
          responseCode: error.statusCode,
          message: error.message,
          errors: error.errors,
        } as ApiResponse<T>,
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      {
        status: false,
        responseCode: 500,
        message: "Internal Server Error",
        errors: [(error as Error).message || "Failed to proxy request"],
      } as ApiResponse<T>,
      { status: 500 }
    );
  }
}

async function readRequestBody(
  request: NextRequest
): Promise<string | undefined> {
  const contentType = request.headers.get("content-type");

  if (contentType?.includes("application/json")) {
    // Create a new ReadableStream from the request body
    const reader = request.body?.getReader();
    if (!reader) return undefined;

    const chunks: Uint8Array[] = [];

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      chunks.push(value);
    }

    // Combine chunks and convert to string
    const concatenated = new Uint8Array(
      chunks.reduce((acc, chunk) => acc + chunk.length, 0)
    );

    let position = 0;
    for (const chunk of chunks) {
      concatenated.set(chunk, position);
      position += chunk.length;
    }

    return new TextDecoder().decode(concatenated);
  }

  return undefined;
}

function isValidApiResponse(response: any): response is ApiResponse<unknown> {
  return (
    typeof response === "object" &&
    response !== null &&
    typeof response.status === "boolean" &&
    typeof response.responseCode === "number" &&
    typeof response.message === "string"
  );
}
