import Home from "@/components/pages/home";
import CardsLoaders from "@/components/shared/cards-loaders";
import AppNavbar from "@/components/shared/navbar";
import AppInitializer from "@/providers/app/initializer";
import dynamic from "next/dynamic";

const DynamicRub = dynamic(() => import("../components/pages/home/<USER>"), {
  ssr: true,
  loading: () => <CardsLoaders />,
});

const DynamicLeaderboard = dynamic(
  () => import("../components/pages/home/<USER>"),
  {
    ssr: true,
    loading: () => <CardsLoaders />,
  }
);

const DynamicClaim = dynamic(() => import("../components/pages/home/<USER>"), {
  ssr: true,
  loading: () => <CardsLoaders />,
});

const DynamicBanner = dynamic(() => import("../components/pages/home/<USER>"), {
  ssr: true,
  loading: () => <CardsLoaders />,
});

export default async function Page() {
  return (
    <AppInitializer>
      <div className="min-h-screen relative bg-gradient-to-b from-stone-900 via-black to-stone-900 bg-opacity-85 overflow-hidden">
        {/* Header */}
        <div className="relative z-10">
          <AppNavbar />
        </div>

        {/* Main Content */}
        <Home>
          <DynamicRub />

          <DynamicLeaderboard />
          <DynamicClaim />
        </Home>
      </div>
    </AppInitializer>
  );
}
