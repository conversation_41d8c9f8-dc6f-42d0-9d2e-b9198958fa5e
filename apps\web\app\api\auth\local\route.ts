import { VerifyResponse } from "@/types/api.types";

import { NextRequest, NextResponse } from "next/server";

type Payload = {
  address: string;
  issuedAt: string;
  signature: string;
};

export async function POST(request: NextRequest) {
  try {
    const data: Payload = await request.clone().json();
    const { address, issuedAt, signature } = data;

    if (!address || !issuedAt || !signature) {
      return NextResponse.json(
        {
          status: false,
          responseCode: 400,
          message: "Missing required fields",
          errors: ["address, issuedAt, and signature are required"],
        },
        { status: 400 }
      );
    }

    const response = await fetch(
      `${process.env.HONO_API_ENDPOINT}/api/auth/verify`,
      {
        method: "POST",
        headers: {
          "content-type": "application/json",
        },
        body: JSON.stringify({
          address,
          signature,
          issuedAt,
        }),
      }
    );

    if (response.ok) {
      const returnedData: VerifyResponse = await response.json();
      const { data } = returnedData;

      // Create a response and set the cookie
      const res = NextResponse.json(
        {
          status: true,
          responseCode: 200,
          message: "Verification successful",
          data,
        },
        { status: 200 }
      );

      // Set the cookie using NextResponse
      res.cookies.set("jwt", data.token, {
        maxAge: 24 * 60 * 60, // 1 day
        sameSite: "strict",
        path: "/",
        secure: process.env.NODE_ENV === "production",
        httpOnly: false,
      });

      res.cookies.set("refresh_token", data.refreshToken, {
        maxAge: 7 * 24 * 60 * 60, // 7 day
        sameSite: "strict",
        path: "/",
        secure: process.env.NODE_ENV === "production",
        httpOnly: false,
      });

      return res;
    } else {
      const errorResponse = await response.json();
      return NextResponse.json(
        {
          status: false,
          responseCode: response.status,
          message: "Verification failed",
          errors: errorResponse.errors || ["Unknown error"],
        },
        { status: response.status }
      );
    }
  } catch (error) {
    return NextResponse.json(
      {
        status: false,
        responseCode: 400,
        message: "Bad request",
        errors: [(error as Error).message],
      },
      { status: 500 }
    );
  }
}
