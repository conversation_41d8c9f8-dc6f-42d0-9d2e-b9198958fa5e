"use client";

import { Tabs } from "@/components/ui/tabs";
import React from "react";
import DataTabs from "./data-tabs";
import TokenIdForm from "./token-id-form";
import TokenInfoDisplay from "./token-info-display";
import { Address } from "viem";
import useTokenOwner from "../hooks/useTokenOwner";
import useEggTokenInfo from "../hooks/useEggTokenInfo";
import AddressForm from "./address-form";
import SearchBar from "./search-bar";
import StatsDashboard, { IStatsData } from "./stats-dashboard";
import Loading from "./loading";
import Error from "./error";

interface IDebuggerTabsProps {
  // Address-based debugger props
  addressData: {
    chickens: any[];
    eggs: any[];
    nullAttributes: any[];
    searchTokenId: string;
    chickensTotal: number;
    eggsTotal: number;
    nullAttributesTotal: number;
    address: string;
  } | null;

  // Token ID-based debugger props
  tokenIdData: {
    inputTokenId: string;
    setInputTokenId: (value: string) => void;
    handleTokenIdSubmit: (e: React.FormEvent) => void;
    submittedTokenId: string | null;
  };

  // Navigation function
  onViewOwnerData: (address: string) => void;

  // Form props
  inputAddress: string;
  setInputAddress: (value: string) => void;
  handleAddressSubmit: (e: React.FormEvent) => void;

  // Search props
  searchTokenId: string;
  setSearchTokenId: (value: string) => void;
  totalItems: number;
  filteredItems: number;

  // Stats props
  chickenStats: IStatsData | null;

  // State props
  isLoading: boolean;
  error: string | null;

  // Tab selection
  selectedTab: string;
  setSelectedTab: (tab: string | number) => void;
}

export default function DebuggerTabs({
  addressData,
  tokenIdData,
  onViewOwnerData,
  inputAddress,
  setInputAddress,
  handleAddressSubmit,
  searchTokenId,
  setSearchTokenId,
  totalItems,
  filteredItems,
  chickenStats,
  isLoading: isLoadingData,
  error: errorMessage,
  selectedTab,
  setSelectedTab,
}: IDebuggerTabsProps): React.ReactNode {
  const {
    owner,
    contractType,
    isLoading: isLoadingToken,
    isError,
    error: tokenError,
  } = useTokenOwner(tokenIdData.submittedTokenId);

  // Check if the token is an egg and get egg information
  const {
    isEgg,
    eggStatus,
    isLoading: isLoadingEggInfo,
  } = useEggTokenInfo(tokenIdData.submittedTokenId);

  return (
    <div className="w-full">
      <Tabs
        aria-label="Debugger Mode"
        selectedKey={selectedTab}
        onSelectionChange={setSelectedTab}
      >
        <Tabs.List className="max-w-max flex bg-gray-100 p-1 rounded-lg mb-4 border-none">
          <Tabs.Tab
            id="address-based"
            className={({ isSelected }) =>
              isSelected
                ? "flex-1 text-center py-1.5 px-3 bg-white rounded-lg shadow-sm text-primary hover:text-primary font-medium transition-all"
                : "flex-1 text-center py-1.5 px-3 text-gray-600 rounded-lg hover:text-primary font-medium transition-all"
            }
          >
            <div className="flex translate-y-0.5 items-center justify-center space-x-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                  clipRule="evenodd"
                />
              </svg>
              <span>Address Lookup</span>
            </div>
          </Tabs.Tab>

          <Tabs.Tab
            id="token-based"
            className={({ isSelected }) =>
              isSelected
                ? "flex-1 text-center py-1.5 px-3 bg-white rounded-lg shadow-sm text-primary hover:text-primary font-medium transition-all"
                : "flex-1 text-center py-1.5 px-3 text-gray-600 rounded-lg hover:text-primary font-medium transition-all"
            }
          >
            <div className="flex translate-y-0.5 items-center justify-center space-x-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              </svg>
              <span>Token ID Lookup</span>
            </div>
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel id="address-based">
          <div className="space-y-6">
            {/* Address Form */}
            <AddressForm
              inputAddress={inputAddress}
              setInputAddress={setInputAddress}
              handleSubmit={handleAddressSubmit}
            />

            {/* Search Bar - Only shown when address is provided */}
            {addressData && (
              <SearchBar
                searchTokenId={searchTokenId}
                setSearchTokenId={setSearchTokenId}
                totalItems={totalItems}
                filteredItems={filteredItems}
              />
            )}

            {/* Loading State */}
            {isLoadingData && <Loading />}

            {/* Error State */}
            {errorMessage && <Error message={errorMessage} />}

            {/* Statistics Dashboard */}
            {chickenStats && !isLoadingData && (
              <StatsDashboard stats={chickenStats} />
            )}

            {/* Data Tabs */}
            {addressData && !isLoadingData && (
              <DataTabs
                chickens={addressData.chickens}
                eggs={addressData.eggs}
                nullAttributes={addressData.nullAttributes}
                searchTokenId={addressData.searchTokenId}
                chickensTotal={addressData.chickensTotal}
                eggsTotal={addressData.eggsTotal}
                nullAttributesTotal={addressData.nullAttributesTotal}
              />
            )}
          </div>
        </Tabs.Panel>

        <Tabs.Panel id="token-based">
          <div className="space-y-6">
            <TokenIdForm
              inputTokenId={tokenIdData.inputTokenId}
              setInputTokenId={tokenIdData.setInputTokenId}
              handleSubmit={tokenIdData.handleTokenIdSubmit}
              isLoading={isLoadingToken}
            />

            {tokenIdData.submittedTokenId && (
              <TokenInfoDisplay
                tokenId={tokenIdData.submittedTokenId}
                owner={owner as Address}
                contractType={contractType}
                isLoading={isLoadingToken || isLoadingEggInfo}
                isError={isError}
                error={tokenError as Error}
                onViewOwnerData={onViewOwnerData}
                isEgg={isEgg}
                eggInfo={eggStatus}
              />
            )}
          </div>
        </Tabs.Panel>
      </Tabs>
    </div>
  );
}
