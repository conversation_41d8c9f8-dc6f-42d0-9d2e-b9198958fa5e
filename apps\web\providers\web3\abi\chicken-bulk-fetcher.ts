import { chainId } from "../web3-provider";

export const chickenBulkFetcherAddress =
  chainId === 2020
    ? "0x14AFd32668766787E0498a72D5B3EB5F12580881"
    : "0xefB92F802BcfA00950455e390Df0f6aC82B419C7";
export const chickenBulkFetchAbi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "_legacyChickenAddress",
        type: "address",
      },
      {
        internalType: "address",
        name: "_genesisChickenAddress",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [],
    name: "genesisChickens",
    outputs: [
      {
        internalType: "contract IERC721Fetcher",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "player",
        type: "address",
      },
    ],
    name: "getGenesisChickenTokenIdsOfAddress",
    outputs: [
      {
        internalType: "uint256[]",
        name: "",
        type: "uint256[]",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "player",
        type: "address",
      },
    ],
    name: "getLegacyChickenTokenIdsOfAddress",
    outputs: [
      {
        internalType: "uint256[]",
        name: "",
        type: "uint256[]",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "legacyChickens",
    outputs: [
      {
        internalType: "contract IERC721Fetcher",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
] as const;
