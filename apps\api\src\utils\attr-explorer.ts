import { AttributeValue } from "../types";
import { MetadataAttribute } from "../types/nfts";

export function findAttribute<T extends string>(
  attributes: MetadataAttribute[],
  traitType: T
): AttributeValue<T> | undefined {
  return attributes.find((attr) => attr.trait_type === traitType) as
    | AttributeValue<T>
    | undefined;
}

export function getAttributeValue<T>(
  attributes: MetadataAttribute[],
  traitType: string,
  defaultValue: T
): T {
  const attribute = attributes.find((attr) => attr.trait_type === traitType);
  return attribute ? (attribute.value as T) : defaultValue;
}
