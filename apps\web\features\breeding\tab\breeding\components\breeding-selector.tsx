"use client";

import React from "react";
import { Select } from "@/components/ui";
import { IconChevronLgDown } from "justd-icons";
import { Key } from "react-aria-components";

interface BreedingSelectorProps {
  selectedKey: Key;
  onSelectionChange: (key: Key) => void;
}

export function BreedingSelector({
  selectedKey,
  onSelectionChange,
}: BreedingSelectorProps) {
  return (
    <div className="grid place-items-center">
      <Select
        selectedKey={selectedKey}
        onSelectionChange={onSelectionChange}
        className="w-full max-w-[230px]"
      >
        <Select.Trigger className="bg-[#191C21] rounded-lg p-4 w-full">
          <span className="text-white">
            {selectedKey === "manual" ? "SINGLE BREEDING" : "MASS BREEDING"}
          </span>
          <IconChevronLgDown className="text-white" />
        </Select.Trigger>
        <Select.List>
          <Select.Option id="manual" textValue="manual">
            SINGLE BREEDING
          </Select.Option>
          <Select.Option id="mass" textValue="mass">
            MASS BREEDING
          </Select.Option>
        </Select.List>
      </Select>
    </div>
  );
}
