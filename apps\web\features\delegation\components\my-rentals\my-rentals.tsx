"use client";

import { useState, useMemo } from "react";
import { <PERSON><PERSON>, Tabs } from "ui";
import { RefreshCw, TrendingUp, Clock, Coins, Users } from "lucide-react";
import { useMyRentals } from "../../hooks/useMyRentals";
import { useRentalHistoryEnhanced } from "../../hooks/useRentalHistory";
import { EnhancedRentalCard } from "./enhanced-rental-card";
import { EnhancedRentalHistoryTable } from "./enhanced-rental-history-table";
import RentalHistoryFilters from "./rental-history-filters";
import { useStateContext } from "@/providers/app/state";
import {
  IRentalWithMetadata,
  IRentalHistoryFilters,
} from "../../types/delegation.types";

export function MyRentals() {
  const [activeTab, setActiveTab] = useState("listed");
  const [historyFilters, setHistoryFilters] = useState<IRentalHistoryFilters>(
    {}
  );
  const { address, isConnected } = useStateContext();

  const {
    ownedRentalsByStatus,
    rentedChickensByStatus,
    expiredRentalsWithInsurance,
    stats,
    isLoading,
    isCancelling,
    error,
    refetch,
    cancelRental,
  } = useMyRentals(address);

  // Memoize the filters to prevent infinite re-renders
  const memoizedFilters = useMemo(
    () => ({
      pageSize: 10,
      ...historyFilters,
    }),
    [historyFilters]
  );

  // Enhanced rental history with filtering and comprehensive event data
  const {
    events,
    pagination,
    isLoading: isLoadingHistory,
    error: historyError,
    refetch: refetchHistory,
    goToNextPage,
    goToPreviousPage,
    goToFirstPage,
    goToLastPage,
  } = useRentalHistoryEnhanced(memoizedFilters);

  // Handle cancel rental
  const handleCancelRental = async (rental: IRentalWithMetadata) => {
    await cancelRental(rental.id);
  };

  // Handle history filters
  const handleFiltersChange = (newFilters: Partial<IRentalHistoryFilters>) => {
    setHistoryFilters((prev) => ({ ...prev, ...newFilters }));
  };

  const handleClearFilters = () => {
    setHistoryFilters({});
  };

  // New categorization for the updated tabs (excluding history which now uses dedicated hook)
  const categorizedRentals = useMemo(() => {
    // Listed: chickens owned by user that are available for rent
    const listed = ownedRentalsByStatus.available;

    // Delegated to me: chickens user is currently renting from others
    const delegatedToMe = rentedChickensByStatus.active;

    // Delegated to others: chickens owned by user that are currently rented by others
    const delegatedToOthers = ownedRentalsByStatus.active;

    return {
      listed,
      delegatedToMe,
      delegatedToOthers,
    };
  }, [ownedRentalsByStatus, rentedChickensByStatus]);

  // Show connection prompt if not connected
  if (!isConnected || !address) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          Connect your wallet to view your rentals
        </div>
        <p className="text-gray-500 text-sm">
          You need to connect your wallet to see your rental listings and rented
          chickens
        </p>
      </div>
    );
  }

  if (error || historyError) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-4">Failed to load your rentals</div>
        <Button
          onPress={() => Promise.all([refetch(), refetchHistory()])}
          appearance="outline"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        <div className="bg-stone-800 border border-stone-700 rounded-lg p-3">
          <div className="flex items-center gap-1.5 mb-1">
            <Users className="w-4 h-4 text-blue-400" />
            <span className="text-gray-400 text-xs">Owned</span>
          </div>
          <div className="text-lg font-bold text-white">
            {stats.totalOwnedRentals}
          </div>
          <div className="text-xs text-gray-400">
            {stats.activeOwnedRentals} active
          </div>
        </div>

        <div className="bg-stone-800 border border-stone-700 rounded-lg p-3">
          <div className="flex items-center gap-1.5 mb-1">
            <Clock className="w-4 h-4 text-purple-400" />
            <span className="text-gray-400 text-xs">Rented</span>
          </div>
          <div className="text-lg font-bold text-white">
            {stats.totalRentedChickens}
          </div>
          <div className="text-xs text-gray-400">
            {stats.activeRentedChickens} active
          </div>
        </div>

        <div className="bg-stone-800 border border-stone-700 rounded-lg p-3">
          <div className="flex items-center gap-1.5 mb-1">
            <TrendingUp className="w-4 h-4 text-green-400" />
            <span className="text-gray-400 text-xs">Earnings</span>
          </div>
          <div className="text-lg font-bold text-white">
            {stats.totalEarnings.toFixed(2)} RON
          </div>
        </div>

        <div className="bg-stone-800 border border-stone-700 rounded-lg p-3">
          <div className="flex items-center gap-1.5 mb-1">
            <Coins className="w-4 h-4 text-yellow-400" />
            <span className="text-gray-400 text-xs">Profit</span>
          </div>
          <div
            className={`text-lg font-bold ${stats.netProfit >= 0 ? "text-green-400" : "text-red-400"}`}
          >
            {stats.netProfit >= 0 ? "+" : ""}
            {stats.netProfit.toFixed(2)} RON
          </div>
        </div>
      </div>

      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-white">
            Delegation Dashboard
          </h2>
          <p className="text-gray-400 text-xs">
            Manage your delegations and rentals
          </p>
        </div>
        <Button
          onPress={() => Promise.all([refetch(), refetchHistory()])}
          appearance="outline"
          isDisabled={isLoading || isLoadingHistory}
          className="flex items-center gap-1.5 px-3 py-1.5 text-sm"
        >
          <RefreshCw
            className={`w-3.5 h-3.5 ${isLoading || isLoadingHistory ? "animate-spin" : ""}`}
          />
          Refresh
        </Button>
      </div>

      {/* Tabs */}
      <Tabs
        selectedKey={activeTab}
        onSelectionChange={(key) => setActiveTab(key as string)}
        className="w-full"
      >
        <Tabs.List className="">
          <Tabs.Tab id="listed" className="text-sm py-2">
            Listed ({categorizedRentals.listed.length})
          </Tabs.Tab>
          <Tabs.Tab id="delegated-to-me" className="text-sm py-2">
            Delegated to me ({categorizedRentals.delegatedToMe.length})
          </Tabs.Tab>
          <Tabs.Tab id="delegated-to-others" className="text-sm py-2">
            Delegated to others ({categorizedRentals.delegatedToOthers.length})
          </Tabs.Tab>
          <Tabs.Tab id="insurance-claims" className="text-sm py-2">
            Insurance Claims ({expiredRentalsWithInsurance.length})
          </Tabs.Tab>
          <Tabs.Tab id="history" className="text-sm py-2">
            History ({pagination?.totalItems || 0})
          </Tabs.Tab>
        </Tabs.List>

        {/* Listed Tab */}
        <Tabs.Panel id="listed">
          <div className="mt-4">
            {categorizedRentals.listed.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                {categorizedRentals.listed.map((rental) => (
                  <EnhancedRentalCard
                    key={rental.id}
                    rental={rental}
                    onCancel={handleCancelRental}
                    isCancelling={isCancelling}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-2">
                  No rental listings found
                </div>
                <p className="text-gray-500 text-sm">
                  Create your first rental listing to start earning
                </p>
              </div>
            )}
          </div>
        </Tabs.Panel>

        {/* Delegated to others Tab */}
        <Tabs.Panel id="delegated-to-others">
          <div className="mt-4">
            {categorizedRentals.delegatedToOthers.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                {categorizedRentals.delegatedToOthers.map((rental) => (
                  <EnhancedRentalCard
                    key={rental.id}
                    rental={rental}
                    onCancel={handleCancelRental}
                    isCancelling={isCancelling}
                    showInsuranceActions={true}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-2">
                  No chickens delegated to others
                </div>
                <p className="text-gray-500 text-sm">
                  Your chickens that are currently being used by others will
                  appear here
                </p>
              </div>
            )}
          </div>
        </Tabs.Panel>

        {/* Delegated to me Tab */}
        <Tabs.Panel id="delegated-to-me">
          <div className="mt-4">
            {categorizedRentals.delegatedToMe.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                {categorizedRentals.delegatedToMe.map((rental) => (
                  <EnhancedRentalCard
                    key={rental.id}
                    rental={rental}
                    showInsuranceActions={true}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-2">
                  No chickens delegated to you
                </div>
                <p className="text-gray-500 text-sm">
                  Browse the marketplace to rent your first chicken
                </p>
              </div>
            )}
          </div>
        </Tabs.Panel>

        {/* Insurance Claims Tab */}
        <Tabs.Panel id="insurance-claims">
          <div className="mt-4">
            {expiredRentalsWithInsurance.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                {expiredRentalsWithInsurance.map((rental) => (
                  <EnhancedRentalCard
                    key={rental.id}
                    rental={rental}
                    showInsuranceActions={true}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-2">
                  No expired rentals with insurance claims available
                </div>
                <p className="text-gray-500 text-sm">
                  Expired rentals with unclaimed insurance will appear here
                </p>
              </div>
            )}
          </div>
        </Tabs.Panel>

        {/* History Tab */}
        <Tabs.Panel id="history">
          <div className="mt-4 space-y-4">
            <RentalHistoryFilters
              filters={historyFilters}
              onFiltersChange={handleFiltersChange}
              onClearFilters={handleClearFilters}
            />
            <EnhancedRentalHistoryTable
              events={events}
              pagination={pagination}
              isLoading={isLoadingHistory}
              error={historyError}
              currentUserAddress={address}
              onRefresh={refetchHistory}
              onNextPage={goToNextPage}
              onPreviousPage={goToPreviousPage}
              onFirstPage={goToFirstPage}
              onLastPage={goToLastPage}
            />
          </div>
        </Tabs.Panel>
      </Tabs>
    </div>
  );
}
