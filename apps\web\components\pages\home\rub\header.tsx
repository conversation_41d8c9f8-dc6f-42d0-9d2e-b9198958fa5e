import { Tooltip } from "@/components/ui";
import { IconCircleQuestionmarkFill } from "justd-icons";
import { Text } from "react-aria-components";

export const RubHeader = () => {
  return (
    <div className="flex w-full gap-2 justify-center items-center mt-4">
      <Text className="font-bold font-Arcadia text-primary text-2xl">
        Daily Rub
      </Text>

      <Tooltip delay={0}>
        <Tooltip.Trigger aria-label="Claim">
          <IconCircleQuestionmarkFill className="text-primary" />
        </Tooltip.Trigger>
        <Tooltip.Content className={"w-1/3"}>
          <div className="flex flex-col gap-4">
            <div className="flex flex-col">
              <Text className="font-semibold font-Poppins text-lg">
                Daily Rub Mechanics
              </Text>
              <Text className="text-sm">
                Chickens transferred, listed, or currently in breeding state are
                not eligible for the daily rub and will not appear here. To rub
                your chicken, it must remain in your wallet and be unlisted for
                a full 24 hours before the next reset at 00:00 UTC.
              </Text>
            </div>
            <div className="flex flex-col">
              <Text className="font-semibold font-Poppins text-lg">
                Legendary Feather Mechanics
              </Text>
              <Text className="text-sm">
                Legendary chickens have a chance to generate Legendary Feathers.
                The more legendary parts a chicken has, the higher the chances
                of producing more Legendary Feathers.
              </Text>
            </div>
          </div>
        </Tooltip.Content>
      </Tooltip>
    </div>
  );
};
