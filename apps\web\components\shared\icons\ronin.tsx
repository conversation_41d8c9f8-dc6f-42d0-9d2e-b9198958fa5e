import { SVGProps } from "react";

interface RoninProps extends SVGProps<SVGSVGElement> {
  size?: number;
  primaryColor?: string;
  secondaryColor?: string;
}

export const Ronin = ({
  size = 196,
  primaryColor = "#1273EA",
  secondaryColor = "#FFFFFF",
  ...props
}: RoninProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 196 196"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.0938 15.0775V135.999C28.0952 138.257 28.604 140.487 29.5826 142.522C30.5612 144.558 31.9847 146.348 33.7478 147.759L90.7595 193.519C92.7651 195.125 95.2578 196 97.827 196C100.396 196 102.889 195.125 104.895 193.519L161.906 147.759C163.669 146.348 165.093 144.558 166.071 142.522C167.05 140.487 167.559 138.257 167.56 135.999V15.0775C167.56 11.0787 165.972 7.24366 163.144 4.41609C160.317 1.58851 156.482 0 152.483 0H43.1712C39.1724 0 35.3374 1.58851 32.5098 4.41609C29.6823 7.24366 28.0938 11.0787 28.0938 15.0775Z"
        fill={primaryColor}
      />
      <path
        d="M144.944 67.8486V37.6937C144.944 33.6949 143.356 29.8599 140.528 27.0323C137.7 24.2047 133.865 22.6162 129.867 22.6162H65.7874C61.7886 22.6162 57.9536 24.2047 55.1261 27.0323C52.2985 29.8599 50.71 33.6949 50.71 37.6937V125.19C50.7114 127.449 51.2202 129.678 52.1988 131.714C53.1775 133.749 54.6009 135.539 56.364 136.951L79.6964 155.684C79.9734 155.909 80.3084 156.05 80.6626 156.092C81.0168 156.134 81.3756 156.075 81.6973 155.921C82.019 155.767 82.2904 155.525 82.48 155.223C82.6696 154.921 82.7696 154.571 82.7684 154.214V92.3495C82.7684 91.8496 82.967 91.3703 83.3204 91.0168C83.6739 90.6634 84.1533 90.4648 84.6531 90.4648H101.615C104.614 90.4648 107.491 91.6562 109.611 93.7769C111.732 95.8976 112.923 98.7738 112.923 101.773V154.214C112.924 154.569 113.024 154.917 113.213 155.217C113.402 155.518 113.672 155.759 113.992 155.912C114.312 156.066 114.669 156.126 115.022 156.086C115.375 156.045 115.709 155.906 115.986 155.684L139.318 136.951C141.081 135.539 142.505 133.749 143.484 131.714C144.462 129.678 144.971 127.449 144.972 125.19V98.0035C144.972 94.0048 143.384 90.1697 140.556 87.3422C137.729 84.5146 133.894 82.9261 129.895 82.9261C133.889 82.9186 137.717 81.3268 140.538 78.5C143.36 75.6732 144.944 71.8425 144.944 67.8486ZM101.596 75.3873H84.6343C84.1344 75.3873 83.655 75.1888 83.3016 74.8353C82.9481 74.4819 82.7496 74.0025 82.7496 73.5027V39.5784C82.7496 39.0785 82.9481 38.5991 83.3016 38.2457C83.655 37.8922 84.1344 37.6937 84.6343 37.6937H111.02C111.52 37.6937 111.999 37.8922 112.353 38.2457C112.706 38.5991 112.905 39.0785 112.905 39.5784V64.0792C112.905 67.0783 111.713 69.9546 109.592 72.0753C107.472 74.196 104.596 75.3873 101.596 75.3873Z"
        fill={secondaryColor}
      />
    </svg>
  );
};

export default Ronin;
