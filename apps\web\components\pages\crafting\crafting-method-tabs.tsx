interface CraftingMethodTabsProps {
  hasRecipe: boolean;
  hasCornRecipe: boolean;
  activeMethod: "recipe" | "corn";
  onMethodChange: (method: "recipe" | "corn") => void;
}

export const CraftingMethodTabs: React.FC<CraftingMethodTabsProps> = ({
  hasRecipe,
  hasCornRecipe,
  activeMethod,
  onMethodChange,
}) => {
  if (!hasRecipe || !hasCornRecipe) return null;

  return (
    <div className="flex gap-1 mb-3 bg-stone-800/30 p-1 rounded-lg">
      <button
        onClick={() => onMethodChange("recipe")}
        className={`flex-1 px-3 py-2 text-xs font-medium rounded transition-colors ${
          activeMethod === "recipe"
            ? "bg-primary text-black"
            : "text-white/70 hover:text-white hover:bg-stone-700/50"
        }`}
      >
        Standard Crafting
      </button>
      <button
        onClick={() => onMethodChange("corn")}
        className={`flex-1 px-3 py-2 text-xs font-medium rounded transition-colors ${
          activeMethod === "corn"
            ? "bg-primary text-black"
            : "text-white/70 hover:text-white hover:bg-stone-700/50"
        }`}
      >
        Variable Crafting
      </button>
    </div>
  );
};