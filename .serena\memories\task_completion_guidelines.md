# Task Completion Guidelines

## When a Task is Completed

### 1. Code Quality Checks
- Run `pnpm lint:fix` instead of `pnpm build` for checking errors
- Fix any linting errors that appear
- Ensure TypeScript types are properly defined
- Check for unused imports and variables

### 2. Testing
- Write unit tests for new functionality
- Run existing tests to ensure nothing is broken
- Test the functionality manually in the browser
- Validate API responses and data structures

### 3. Code Review Checklist
- Follow established naming conventions
- Ensure components are reusable and small
- Check responsive design works properly
- Verify error handling is implemented
- Ensure loading states are handled

### 4. Documentation
- Update relevant documentation if needed
- Add JSDoc comments for complex functions
- Update type definitions if APIs changed

### 5. Performance Considerations
- Check for unnecessary re-renders
- Optimize API calls and data fetching
- Ensure proper pagination for large datasets
- Use React Query for efficient caching

### 6. User Experience
- Pay attention to UX details
- Ensure proper loading and error states
- Test on different screen sizes
- Validate form inputs and provide feedback

### 7. Final Validation
- Double-check that the implementation matches requirements
- Verify no breaking changes were introduced
- Test edge cases and error scenarios