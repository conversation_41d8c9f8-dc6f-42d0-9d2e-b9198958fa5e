# Client-Side Blockchain Interaction

This directory contains utilities for interacting with blockchain contracts directly from the client side.

## Why Client-Side Contract Reads?

We've moved contract reads to the client side for several reasons:

1. **Avoid Rate Limiting**: When contract reads are performed on the server, all requests come from the same IP address, which can lead to rate limiting issues with the RPC provider.

2. **Distribute Load**: Client-side reads distribute the load across user IPs, reducing the risk of hitting rate limits.

3. **Reduce Server Load**: Moving reads to the client reduces the load on our server.

4. **Improved Responsiveness**: Direct client-to-blockchain communication can be faster than going through our server.

## Available Utilities

### `contractReader.ts`

This file provides two main functions:

- `readContract<T>`: For single contract function calls
- `executeMulticall<T>`: For batching multiple contract calls into a single request

### `useContractRead.ts`

This file provides React hooks that wrap the contract reader functions:

- `useContractRead<T>`: Hook for single contract function calls with React Query integration
- `useMulticall<T>`: Hook for batching multiple contract calls with React Query integration

## Usage Examples

### Basic Contract Read

```tsx
import { readContract } from "@/lib/blockchain/contractReader";
import { chickenGenesisAbi } from "@/providers/web3/abi/chicken-genesis-abi";
import { Address } from "viem";

// Read a contract directly
const balance = await readContract<bigint>({
  address: "0x..." as Address,
  abi: chickenGenesisAbi,
  functionName: "balanceOf",
  args: ["0x..." as Address],
});
```

### Using the React Hook

```tsx
import { useContractRead } from "@/lib/hooks/useContractRead";
import { chickenGenesisAbi } from "@/providers/web3/abi/chicken-genesis-abi";
import { Address } from "viem";

function MyComponent() {
  const { data, isLoading, error } = useContractRead<bigint>({
    address: "0x..." as Address,
    abi: chickenGenesisAbi,
    functionName: "balanceOf",
    args: ["0x..." as Address],
  });
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return <div>Balance: {data?.toString()}</div>;
}
```

### Multicall Example

```tsx
import { useMulticall } from "@/lib/hooks/useContractRead";
import { cockAbi } from "@/providers/web3/abi/cock-abi";
import { Address } from "viem";

function MyComponent() {
  const { data, isLoading, error } = useMulticall({
    calls: [
      {
        address: "0x..." as Address,
        abi: cockAbi,
        functionName: "name",
        args: [],
      },
      {
        address: "0x..." as Address,
        abi: cockAbi,
        functionName: "symbol",
        args: [],
      },
    ],
  });
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <div>
      <p>Name: {data?.[0].result as string}</p>
      <p>Symbol: {data?.[1].result as string}</p>
    </div>
  );
}
```

## Best Practices

1. **Type Your Returns**: Always specify the expected return type using the generic parameter.

2. **Handle Loading and Error States**: Always handle loading and error states in your UI.

3. **Enable Conditionally**: Use the `enabled` parameter to control when the query should run.

4. **Cache Management**: Use `staleTime` and `cacheTime` parameters to control caching behavior.

5. **Custom Query Keys**: Use the `queryKey` parameter to add custom values to the query key for better cache control.
