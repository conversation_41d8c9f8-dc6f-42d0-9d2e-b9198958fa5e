import { NextRequest, NextResponse } from "next/server";
import { privateKeyToAccount } from "viem/accounts";
import { AbiCoder, keccak256, hashMessage } from "ethers";
import { type Address } from "viem";
import { cookies } from "next/headers";
import { CollectionType, StatType } from "@/types/daily-feed.type";

interface BatchFeedSignatureRequest {
  feederAddress: string;
  collectionType: CollectionType;
  nftTokenId: number;
  feedItemIds: number[];
  amounts: number[];
  debuffStatChoices: StatType[];
  nonce: number;
}

interface BatchFeedSignatureResponse {
  signature: string;
  deadline: number;
  totalAffectionPoints: number;
  messageHash: string;
  ethSignedMessageHash: string;
  // Debug info
  encodedData?: string;
  signerAddress?: string;
  itemDetails?: {
    feedItemId: number;
    amount: number;
    affectionPoints: number;
  }[];
}

// Configuration - replace with your actual values
const SIGNER_PRIVATE_KEY = process.env
  .DAILY_FEED_SIGNER_WALLET_PK as `0x${string}`;
const SIGNATURE_VALIDITY_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Calculate affection points based on feed parameters
 * This is a sample calculation - replace with your actual logic
 */
function calculateAffectionPoints(
  collectionType: CollectionType,
  nftTokenId: number,
  feedItemId: number,
  amount: number
): number {
  return 0;
}

/**
 * Calculate total affection points for batch feeding
 */
function calculateTotalAffectionPoints(
  collectionType: CollectionType,
  nftTokenId: number,
  feedItemIds: number[],
  amounts: number[]
): {
  total: number;
  details: { feedItemId: number; amount: number; affectionPoints: number }[];
} {
  let total = 0;
  const details: {
    feedItemId: number;
    amount: number;
    affectionPoints: number;
  }[] = [];

  for (let i = 0; i < feedItemIds.length; i++) {
    const feedItemId = feedItemIds[i];
    const amount = amounts[i];

    // Type safety check
    if (feedItemId === undefined || amount === undefined) {
      throw new Error(`Missing data at index ${i}`);
    }

    const affectionPoints = calculateAffectionPoints(
      collectionType,
      nftTokenId,
      feedItemId,
      amount
    );
    total += affectionPoints;
    details.push({
      feedItemId,
      amount,
      affectionPoints,
    });
  }

  return { total, details };
}

/**
 * Create message hash for batch feeding matching the contract's _getBatchFeedMessageHash function
 * From contract: uint8(params.collectionType), params.nftTokenId, params.totalAffectionPoints,
 * keccak256(abi.encode(params.feedItemIds)), keccak256(abi.encode(params.amounts)),
 * keccak256(abi.encode(_convertStatTypesToUint8(params.debuffStatChoices))), nonce, deadline
 */
function createBatchFeedMessageHash(
  collectionType: CollectionType,
  nftTokenId: number,
  totalAffectionPoints: number,
  feedItemIds: number[],
  amounts: number[],
  debuffStatChoices: StatType[],
  nonce: number,
  deadline: number
): { messageHash: Address; encodedData: string } {
  const abiCoder = new AbiCoder();

  // Hash the arrays first (as done in contract)
  const feedItemIdsHash = keccak256(
    abiCoder.encode(["uint256[]"], [feedItemIds])
  );
  const amountsHash = keccak256(abiCoder.encode(["uint256[]"], [amounts]));
  const debuffStatChoicesHash = keccak256(
    abiCoder.encode(["uint8[]"], [debuffStatChoices])
  );

  // Create the main encoding matching contract's _getBatchFeedMessageHash
  const encoded = abiCoder.encode(
    [
      "uint8", // uint8(params.collectionType)
      "uint256", // params.nftTokenId
      "uint256", // params.totalAffectionPoints
      "bytes32", // keccak256(abi.encode(params.feedItemIds))
      "bytes32", // keccak256(abi.encode(params.amounts))
      "bytes32", // keccak256(abi.encode(_convertStatTypesToUint8(params.debuffStatChoices)))
      "uint256", // nonce
      "uint256", // deadline
    ],
    [
      collectionType,
      nftTokenId,
      totalAffectionPoints,
      feedItemIdsHash,
      amountsHash,
      debuffStatChoicesHash,
      nonce,
      deadline,
    ]
  );

  const messageHash = keccak256(encoded) as Address;

  return {
    messageHash,
    encodedData: encoded,
  };
}

export async function POST(request: NextRequest) {
  try {
    const c = await cookies();
    const jwt = c.get("jwt")?.value;
    if (!jwt) {
      return NextResponse.json(
        {
          status: false,
          responseCode: 401,
          message: "Bad request",
        },
        { status: 401 }
      );
    }
    const signerAccount = privateKeyToAccount(SIGNER_PRIVATE_KEY);

    const body: BatchFeedSignatureRequest = await request.json();
    // Validate request
    if (
      !body.feederAddress ||
      typeof body.collectionType !== "number" ||
      typeof body.nftTokenId !== "number" ||
      !Array.isArray(body.feedItemIds) ||
      !Array.isArray(body.amounts) ||
      !Array.isArray(body.debuffStatChoices) ||
      typeof body.nonce !== "number"
    ) {
      return NextResponse.json(
        { error: "Invalid request parameters" },
        { status: 400 }
      );
    }

    // Validate array lengths match
    if (
      body.feedItemIds.length !== body.amounts.length ||
      body.feedItemIds.length !== body.debuffStatChoices.length ||
      body.feedItemIds.length === 0
    ) {
      return NextResponse.json(
        { error: "Array length mismatch or empty arrays" },
        { status: 400 }
      );
    }

    // Validate all amounts are positive
    if (body.amounts.some((amount) => amount <= 0)) {
      return NextResponse.json(
        { error: "All amounts must be positive" },
        { status: 400 }
      );
    }

    // Validate enum values
    if (
      !Object.values(CollectionType).includes(body.collectionType) ||
      !body.debuffStatChoices.every((choice) =>
        Object.values(StatType).includes(choice)
      )
    ) {
      return NextResponse.json(
        { error: "Invalid enum values" },
        { status: 400 }
      );
    }

    // Calculate deadline
    const deadline =
      Math.floor(Date.now() / 1000) +
      Math.floor(SIGNATURE_VALIDITY_DURATION / 1000);

    // Calculate total affection points
    const { total: totalAffectionPoints, details: itemDetails } =
      calculateTotalAffectionPoints(
        body.collectionType,
        body.nftTokenId,
        body.feedItemIds,
        body.amounts
      );

    // Create message hash
    const { messageHash, encodedData } = createBatchFeedMessageHash(
      body.collectionType,
      body.nftTokenId,
      totalAffectionPoints,
      body.feedItemIds,
      body.amounts,
      body.debuffStatChoices,
      body.nonce,
      deadline
    );

    // Create ETH signed message hash (EIP-191)
    const ethSignedMessageHash = hashMessage(messageHash);

    console.log("Batch message hash details:", {
      messageHash,
      ethSignedMessageHash,
      encodedData,
      deadline,
      totalAffectionPoints,
      itemDetails,
    });

    // Sign the ETH signed message hash
    const signature = await signerAccount.signMessage({
      message: { raw: messageHash as `0x${string}` },
    });

    console.log("Generated batch signature:", signature);

    const response: BatchFeedSignatureResponse = {
      signature,
      deadline,
      totalAffectionPoints,
      messageHash,
      ethSignedMessageHash,
      // Debug info (remove in production)
      encodedData,
      signerAddress: signerAccount.address,
      itemDetails,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error generating batch feed signature:", error);
    return NextResponse.json(
      { error: "Internal server error", details: (error as Error).message },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}
