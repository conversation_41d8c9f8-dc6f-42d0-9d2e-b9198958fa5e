export const truncateAddress = (address: string): string => {
  if (address.length <= 8) return address;
  return `${address.slice(0, 8)}...${address.slice(-6)}`;
};

export function truncateRonAddressCustom(str: string, frontChars = 6, backChars = 5) {
  // Check if string ends with .ron
  if (!str.endsWith(".ron")) return str;

  const base = str.slice(0, -4); // Remove .ron
  const suffix = ".ron";

  if (base.length <= frontChars + backChars + 3) return str;

  return base.slice(0, frontChars) + "..." + base.slice(-backChars) + suffix;
}
