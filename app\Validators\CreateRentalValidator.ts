import { schema, CustomMessages, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ValidatorReporter } from './Reporters/ValidatorReporter'

export default class CreateRentalValidator {
  constructor(protected ctx: HttpContextContract) {}

  public schema = schema.create({
    chickenTokenId: schema.number(),
    roninPrice: schema.string(),
    rentalPeriod: schema.number(),
    rewardDistribution: schema.number.optional(),
    gameRewardDistribution: schema.number.optional(),
    insurancePrice: schema.string.optional(),
    delegatedTask: schema.number.optional(),
    renterAddress: schema.string.optional([rules.requiredWhen('roninPrice', '=', 0)]),
    sharedRewardAmount: schema.number.optional([
      rules.requiredWhen('rewardDistribution', '=', 3),
      rules.range(1, 100), // Assuming the reward amount is a percentage between 1-100
    ]),
    rubStreakBenefactor: schema.number.optional(),
    legendaryFeatherBenefactor: schema.number.optional(),
  })

  public reporter = ValidatorReporter

  public messages: CustomMessages = {
    'required': '{{ field }} is required',
    'sharedRewardAmount.requiredWhen':
      'Shared reward amount is required when reward distribution is set to SHARED',
    'sharedRewardAmount.range': 'Shared reward amount must be between 1 and 100',
    'number': '{{ field }} is not a valid number',
    'string': '{{ field }} is not a valid string',
    'roninPrice.requiredWhen': 'Delegated address is required when ronin price is 0',
  }
}
