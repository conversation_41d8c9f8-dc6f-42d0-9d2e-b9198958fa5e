"use client";

import { useState, useRef } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";
import { chain } from "@/providers/web3/web3-provider";
import { rentalAbi } from "@/providers/web3/abi/rental-abi";
import { useChickenDeathVerification } from "./useChickenDeathVerification";
import { IRentalWithMetadata } from "../types/delegation.types";

/**
 * Insurance claim eligibility interface
 */
interface IInsuranceClaimEligibility {
  canClaim: boolean;
  reason: string;
  recipient: "owner" | "renter" | "none";
  verification?: any; // IChickenDeathVerification type
}

/**
 * Hook for handling insurance claiming process
 * Includes death verification and blockchain transaction execution
 */
export const useClaimInsurance = () => {
  const { publicClient, walletClient, address, Disconnect } = useStateContext();
  const { blockchainQuery } = useBlockchain();
  const { verifyChickenDeath } = useChickenDeathVerification();
  const queryClient = useQueryClient();

  const [isClaiming, setIsClaiming] = useState(false);
  const eligibilityCheckRef = useRef<
    Map<string, Promise<IInsuranceClaimEligibility>>
  >(new Map());

  // Get rental contract address from blockchain config
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  /**
   * Check if insurance can be claimed for a rental with debouncing
   */
  const checkInsuranceEligibility = async (
    rental: IRentalWithMetadata
  ): Promise<IInsuranceClaimEligibility> => {
    const cacheKey = `${rental.id}-${address}`;

    // Check if there's already a pending eligibility check for this rental
    if (eligibilityCheckRef.current.has(cacheKey)) {
      return eligibilityCheckRef.current.get(cacheKey)!;
    }

    const eligibilityPromise = performEligibilityCheck(rental);
    eligibilityCheckRef.current.set(cacheKey, eligibilityPromise);

    try {
      const result = await eligibilityPromise;
      return result;
    } finally {
      // Clean up the cache after completion
      setTimeout(() => {
        eligibilityCheckRef.current.delete(cacheKey);
      }, 1000);
    }
  };

  /**
   * Perform the actual eligibility check
   */
  const performEligibilityCheck = async (
    rental: IRentalWithMetadata
  ): Promise<IInsuranceClaimEligibility> => {
    try {
      // Check if rental has expired
      if (!rental.expiresAt) {
        return {
          canClaim: false,
          reason: "Rental has no expiration date",
          recipient: "none",
        };
      }

      const expirationDate = new Date(rental.expiresAt);
      const now = new Date();

      if (now < expirationDate) {
        return {
          canClaim: false,
          reason: "Rental period has not expired yet",
          recipient: "none",
        };
      }

      // Check if insurance has already been claimed
      // Note: This would need to be checked via contract or API
      // For now, we'll assume it hasn't been claimed

      // Verify chicken death status
      const verification = await verifyChickenDeath(rental.chickenTokenId);

      if (!verification.isVerified) {
        return {
          canClaim: false,
          reason: "Chicken death status requires manual review",
          recipient: "none",
          verification,
        };
      }

      // Determine who can claim based on chicken status
      if (verification.isDead) {
        // Chicken died - owner gets insurance
        if (address?.toLowerCase() === rental.ownerAddress.toLowerCase()) {
          return {
            canClaim: true,
            reason: "Chicken died during rental - owner eligible for insurance",
            recipient: "owner",
            verification,
          };
        } else {
          return {
            canClaim: false,
            reason: "Only the owner can claim insurance for a dead chicken",
            recipient: "owner",
            verification,
          };
        }
      } else {
        // Chicken alive - renter gets insurance back
        if (address?.toLowerCase() === rental.renterAddress?.toLowerCase()) {
          return {
            canClaim: true,
            reason:
              "Chicken survived rental - renter eligible for insurance refund",
            recipient: "renter",
            verification,
          };
        } else {
          return {
            canClaim: false,
            reason:
              "Only the renter can claim insurance refund for a surviving chicken",
            recipient: "renter",
            verification,
          };
        }
      }
    } catch (error) {
      console.error("Insurance eligibility check failed:", error);
      return {
        canClaim: false,
        reason: `Eligibility check failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        recipient: "none",
      };
    }
  };

  /**
   * Execute the insurance claiming process
   */
  const executeClaimInsurance = async (rental: IRentalWithMetadata) => {
    try {
      if (!address || !walletClient) {
        toast.error("Cannot claim insurance", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return;
      }

      if (!rentalAddress) {
        toast.error("Cannot claim insurance", {
          description: "Rental contract not configured",
          position: "top-right",
        });
        return;
      }

      setIsClaiming(true);

      // Step 1: Check eligibility
      toast.info("Checking insurance eligibility...", {
        description: "Verifying chicken status and rental conditions",
        position: "top-center",
      });

      const eligibility = await checkInsuranceEligibility(rental);

      if (!eligibility.canClaim) {
        throw new Error(eligibility.reason);
      }

      // Step 2: Simulate and execute transaction with retry logic
      toast.info("Preparing transaction...", {
        description: "Simulating and estimating gas",
        position: "top-center",
      });

      console.log("Claiming insurance for rental:", {
        rentId: BigInt(rental.id),
        recipient: eligibility.recipient,
        verification: eligibility.verification,
      });

      let simulateReq;
      let simulationSuccess = false;
      let simulationError;

      // Retry simulation up to 3 times
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          simulateReq = await publicClient.simulateContract({
            address: rentalAddress,
            abi: rentalAbi,
            functionName: "claimInsurance",
            args: [BigInt(rental.id)],
            chain,
            account: address,
          });
          simulationSuccess = true;
          break;
        } catch (error) {
          simulationError = error;
          if (attempt < 3) {
            console.warn(`Simulation attempt ${attempt} failed, retrying...`);
            await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
          }
        }
      }

      if (!simulationSuccess || !simulateReq) {
        throw (
          simulationError ||
          new Error("Transaction simulation failed after retries")
        );
      }

      // Step 3: Estimate gas with buffer (use 15% buffer like other hooks)
      const gasEstimate = await publicClient.estimateContractGas({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "claimInsurance",
        args: [BigInt(rental.id)],
        account: address,
      });

      // Add 15% buffer to gas estimate (consistent with other hooks)
      const gasWithBuffer = (gasEstimate * 115n) / 100n;

      // Step 4: Execute the transaction
      toast.info("Claiming insurance...", {
        description: "Please confirm the transaction in your wallet",
        position: "top-center",
      });

      const request = simulateReq.request;
      request.gas = gasWithBuffer;

      const hash = await walletClient.writeContract(request);

      // Step 5: Wait for transaction confirmation with timeout
      toast.info("Confirming transaction...", {
        description: "Waiting for blockchain confirmation",
        position: "top-center",
      });

      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        confirmations: 1,
        timeout: 60_000, // 60 second timeout
      });

      if (receipt.status === "success") {
        console.log("Insurance claim transaction successful:", {
          hash,
          receipt,
          eligibility,
        });

        // Note: Backend will be automatically updated via blockchain event listener
        // No need to manually call API - the InsuranceClaimed event will trigger
        // the ProcessRentalEventJob to update the database

        const successMessage =
          eligibility.recipient === "owner"
            ? "Insurance claimed successfully!"
            : "Insurance refund claimed successfully!";

        const successDescription =
          eligibility.recipient === "owner"
            ? "You have received compensation for the lost chicken"
            : "Your insurance deposit has been refunded";

        toast.success(successMessage, {
          description: successDescription,
          position: "top-center",
        });

        // Invalidate relevant queries to refresh data
        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: ["my-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["rental-history"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals", address] });
          queryClient.invalidateQueries({
            queryKey: ["chicken-death-verification", rental.chickenTokenId],
          });
        }, 500);

        return {
          success: true,
          hash,
          receipt,
          eligibility,
        };
      } else {
        throw new Error("Transaction failed");
      }
    } catch (error) {
      console.error("Claim insurance failed:", error);

      let errorMessage = "Failed to claim insurance";
      let errorDescription = "An unexpected error occurred";

      if (error instanceof Error) {
        // Handle specific error cases with better messaging
        if (
          error.message.includes("User rejected") ||
          error.message.includes("user rejected")
        ) {
          errorMessage = "Transaction cancelled";
          errorDescription = "You cancelled the transaction";
        } else if (error.message.includes("insufficient funds")) {
          errorMessage = "Insufficient funds";
          errorDescription = "Not enough funds to pay for gas";
        } else if (error.message.includes("execution reverted")) {
          errorMessage = "Transaction failed";
          errorDescription =
            "The contract rejected the transaction. Please check if the insurance is still claimable.";
        } else if (error.message.includes("timeout")) {
          errorMessage = "Transaction timeout";
          errorDescription =
            "The transaction took too long to confirm. Please check your wallet or try again.";
        } else if (error.message.includes("network")) {
          errorMessage = "Network error";
          errorDescription =
            "Please check your internet connection and try again.";
        } else if (error.message.includes("already claimed")) {
          errorMessage = "Already claimed";
          errorDescription = "Insurance has already been claimed";
        } else if (error.message.includes("not expired")) {
          errorMessage = "Not eligible";
          errorDescription = "Rental period has not expired yet";
        } else if (error.message.includes("not eligible")) {
          errorMessage = "Not eligible";
          errorDescription = "You are not eligible to claim this insurance";
        } else {
          errorMessage = "Transaction failed";
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        position: "top-center",
      });

      return { success: false, error };
    } finally {
      setIsClaiming(false);
    }
  };

  // Mutation for React Query integration
  const claimInsuranceMutation = useMutation({
    mutationFn: executeClaimInsurance,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Claim insurance mutation error:", error);
    },
  });

  return {
    executeClaimInsurance,
    checkInsuranceEligibility,
    claimInsuranceMutation,
    isClaiming,
    rentalAddress,
  };
};
