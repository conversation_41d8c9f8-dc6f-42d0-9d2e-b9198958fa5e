import axios from "axios";
import { env } from "../env";
import { Address } from "viem";

interface Order {
  startedAt: number;
}

type ResponseData = {
  erc721Tokens: {
    total: number;
    results: TokenData[];
  };
};

interface TokenData {
  tokenAddress: string;
  tokenId: string;
  owner: string;
  order: Order;
}

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

async function getNonListedNfts(
  owner: string,
  from: number = 0,
  contractAddress: Address
): Promise<ResponseData> {
  const graphqlEndpoint = env.MARKETPLACE_GRAPHQL_ENDPOINT;
  const data = {
    operationName: "GetERC721TokensList",
    variables: {
      auctionType: "NotForSale",
      from,
      owner: owner,
      rangeCriteria: [],
      size: 50,
      sort: "PriceAsc",
      tokenAddress: contractAddress,
    },
    query: `query GetERC721TokensList($tokenAddress: String, $owner: String, $from: Int!, $size: Int!,$sort: SortBy) {
  erc721Tokens(
    tokenAddress: $tokenAddress
    owner: $owner
    from: $from
    size: $size
    sort: $sort
  ) {
    total
    results {
      tokenAddress
      tokenId
      owner
      name
      image
      cdnImage
      order {
        id
        maker
        basePrice
        currentPrice
        timeLeft
        orderStatus
        paymentToken
        expiredAt
      }
      ownerProfile {
        name
        accountId
      }
    }
  }
}`,
  };

  const headers = {
    "Content-Type": "application/json",
    "x-api-key": env.SKYMAVIS_API,
  };

  const response = await axios.post<{ data: ResponseData }>(
    graphqlEndpoint,
    data,
    { headers }
  );
  return response.data.data;
}

export async function getNotListedNfts(address: string) {
  const tokens: { id: string; address: string }[] = [];
  const legacyAddress = env.LEGACY_CONTRACT as Address;
  const genesisAddress = env.CHICKEN_CONTRACT as Address;
  const contractAddresses = [genesisAddress, legacyAddress];
  const DELAY_BETWEEN_REQUESTS = 2000; // 1 second delay

  try {
    for (const contractAddress of contractAddresses) {
      let from = 0;
      const size = 50;
      while (true) {
        const fetchNft = await getNonListedNfts(address, from, contractAddress);

        // Process current batch
        fetchNft.erc721Tokens.results.forEach((item) => {
          tokens.push({
            id: item.tokenId,
            address: item.owner,
          });
        });

        // If we got less results than requested, we're done
        if (fetchNft.erc721Tokens.results.length < 50) {
          break;
        }

        // Update from for next batch
        from += 50;

        // Add delay before next request
        await delay(DELAY_BETWEEN_REQUESTS);
      }
    }
    return tokens;
  } catch (error) {
    console.error("Error fetching NFTs:", error);
    throw error;
  }
}
