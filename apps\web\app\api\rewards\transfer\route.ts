import { NextRequest, NextResponse } from "next/server";

const CHICKEN_API_BASE_URL = "https://chicken-api-ivory.vercel.app/api/game";
const CHICKEN_API_KEY = process.env.CHICKEN_API_KEY;

export async function POST(request: NextRequest) {
  try {
    // Check if API key is configured
    if (!CHICKEN_API_KEY) {
      return NextResponse.json(
        { error: "API key not configured" },
        { status: 500 }
      );
    }

    // Validate CSRF token
    const csrfToken = request.headers.get("X-CSRF-Token");
    if (!csrfToken) {
      return NextResponse.json(
        { error: "CSRF token required" },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { address, chickenIds } = body;

    // Validate request
    if (
      !address ||
      !chickenIds ||
      !Array.isArray(chickenIds) ||
      chickenIds.length === 0
    ) {
      return NextResponse.json(
        { error: "Invalid request. Address and chickenIds are required." },
        { status: 400 }
      );
    }

    console.log("🔄 Proxying transfer request to chicken-api-ivory:", {
      address,
      chickenIds,
    });

    // Make request to chicken-api-ivory
    const response = await fetch(`${CHICKEN_API_BASE_URL}/transfer`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": CHICKEN_API_KEY,
      },
      body: JSON.stringify({
        address,
        chickenIds,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error("❌ Transfer failed:", data);
      return NextResponse.json(
        {
          error: data.error || data.message || "Transfer failed",
          details: data,
        },
        { status: response.status }
      );
    }

    console.log("✅ Transfer successful:", data);
    return NextResponse.json(data);
  } catch (error: any) {
    console.error("❌ Transfer API error:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    );
  }
}
