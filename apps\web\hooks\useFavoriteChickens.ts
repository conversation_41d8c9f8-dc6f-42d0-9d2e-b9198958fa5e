import { useCallback, useEffect, useState } from "react";

export function useFavoriteChickens() {
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [isLoaded, setIsLoaded] = useState(false);

  // Load favorites from localStorage
  useEffect(() => {
    try {
      const storedFavorites = localStorage.getItem("favoriteChickens");
      if (storedFavorites) {
        const parsedFavorites = JSON.parse(storedFavorites);
        if (Array.isArray(parsedFavorites)) {
          setFavorites(new Set(parsedFavorites));
        }
      }
    } catch (e) {
      console.error("Error loading favorites from localStorage:", e);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save favorites to localStorage whenever they change
  useEffect(() => {
    // Only save after initial load to prevent overwriting with empty set
    if (isLoaded) {
      try {
        localStorage.setItem(
          "favoriteChickens",
          JSON.stringify([...favorites])
        );
      } catch (e) {
        console.error("Error saving favorites to localStorage:", e);
      }
    }
  }, [favorites, isLoaded]);

  // Toggle a chicken's favorite status
  const toggleFavorite = useCallback((tokenId: string) => {
    setFavorites((prev) => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(tokenId)) {
        newFavorites.delete(tokenId);
      } else {
        newFavorites.add(tokenId);
      }
      return newFavorites;
    });
  }, []);

  // Check if a chicken is favorited
  const isFavorite = useCallback(
    (tokenId: string) => favorites.has(tokenId),
    [favorites]
  );

  return {
    favorites,
    toggleFavorite,
    isFavorite,
    isLoaded,
  };
}