"use client";

import api from "@/lib/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// Types
export interface IBreedingCooldown {
  id: number;
  count: number;
  cooldown: number;
  created_at: string;
  updated_at: string;
}

export interface IBreedingFee {
  id: number;
  count: number;
  cock_usd: number;
  cock: number;
  feathers: number;
  created_at: string;
  updated_at: string;
}

export interface ICreateBreedingCooldownPayload {
  count: number;
  cooldown: number;
}

export interface IUpdateBreedingCooldownPayload {
  breedingCooldownId: number;
  count?: number;
  cooldown?: number;
}

export interface IUpdateBreedingFeePayload {
  breedingFeeId: number;
  cockUsd?: number;
  feathers?: number;
}

// API functions
export const fetchBreedingCooldowns = async (): Promise<
  IBreedingCooldown[]
> => {
  const response = await api.get("/admin/breeding-cooldowns");
  return response.data.data;
};

export const createBreedingCooldown = async (
  payload: ICreateBreedingCooldownPayload
) => {
  const response = await api.post("/admin/breeding-cooldowns/create", payload);
  return response.data;
};

export const updateBreedingCooldown = async (
  payload: IUpdateBreedingCooldownPayload
) => {
  const response = await api.post("/admin/breeding-cooldowns/update", payload);
  return response.data;
};

export const deleteBreedingCooldown = async (breedingCooldownId: number) => {
  const response = await api.post("/admin/breeding-cooldowns/delete", {
    breedingCooldownId,
  });
  return response.data;
};

export const fetchBreedingFees = async (): Promise<IBreedingFee[]> => {
  const response = await api.get("/admin/breeding-fees");
  return response.data.data;
};

export const updateBreedingFee = async (payload: IUpdateBreedingFeePayload) => {
  const response = await api.post("/admin/breeding-fees/update", payload);
  return response.data;
};

// React Query Hooks
export const useBreedingCooldowns = () => {
  return useQuery({
    queryKey: ["breedingCooldowns"],
    queryFn: fetchBreedingCooldowns,
  });
};

export const useCreateBreedingCooldown = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createBreedingCooldown,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["breedingCooldowns"] });
    },
  });
};

export const useUpdateBreedingCooldown = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateBreedingCooldown,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["breedingCooldowns"] });
    },
  });
};

export const useDeleteBreedingCooldown = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteBreedingCooldown,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["breedingCooldowns"] });
    },
  });
};

export const useBreedingFees = () => {
  return useQuery({
    queryKey: ["breedingFees"],
    queryFn: fetchBreedingFees,
  });
};

export const useUpdateBreedingFee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateBreedingFee,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["breedingFees"] });
    },
  });
};
