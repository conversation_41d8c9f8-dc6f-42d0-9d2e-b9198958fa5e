import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAccount, useWalletClient, usePublicClient } from "wagmi";
import { toast } from "sonner";
import { Address } from "viem";

import { DelegationAPI } from "../api/delegation.api";
import { rentalAbi } from "@/providers/web3/abi/rental-abi";
import { chain } from "@/providers/web3/web3-provider";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";

/**
 * Hook for canceling delegations and rentals with proper contract interactions
 * This handles both marketplace rentals and direct delegations
 *
 * The signature formats are now aligned:
 * - Backend generates: keccak256(rentalId, chickenTokenId, userAddress)
 * - Contract expects: keccak256(_rentId, _chickenId, msg.sender)
 *
 * Both API cancellation and contract interaction work correctly.
 */
export const useCancelDelegation = () => {
  const [isCancelling, setIsCancelling] = useState(false);
  const { address } = useAccount();
  const { data: walletClient } = useWalletClient();
  const publicClient = usePublicClient();
  const queryClient = useQueryClient();
  const { blockchainQuery } = useBlockchain();
  const { Disconnect } = useStateContext();

  // Get rental contract address from blockchain config
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  /**
   * Execute the complete cancellation process:
   * 1. Call API to get cancellation data
   * 2. Execute unlistChickenForRent contract function
   * 3. Handle success/error states
   *
   * NOTE: Due to signature mismatch, this currently only works for API-only cancellation
   * The contract interaction will fail until backend signature is fixed
   */
  const executeCancelDelegation = async (rentalId: number) => {
    try {
      if (!address || !walletClient || !publicClient) {
        toast.error("Cannot cancel delegation", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return { success: false, error: "Wallet not connected" };
      }

      if (!rentalAddress) {
        toast.error("Cannot cancel delegation", {
          description: "Rental contract not configured",
          position: "top-right",
        });
        return { success: false, error: "Contract not configured" };
      }

      setIsCancelling(true);

      // Step 1: Call API to get cancellation signature
      toast.info("Preparing to cancel delegation...", {
        description: "Getting cancellation data from server",
        position: "top-center",
      });

      const cancelResponse = await DelegationAPI.cancelRental(rentalId);

      if (cancelResponse.status !== 1 || !cancelResponse.data) {
        throw new Error("Failed to get cancellation data from server");
      }

      const {
        rentalId: contractRentalId,
        chickenTokenId,
        signature,
      } = cancelResponse.data;

      console.log("Cancel delegation parameters:", {
        originalRentalId: rentalId,
        contractRentalId,
        chickenTokenId,
        signature: signature ? "present" : "missing",
      });

      // Step 2: Execute contract interaction to transfer chicken back from escrow
      toast.info("Cancelling delegation on blockchain...", {
        description: "Please confirm the transaction in your wallet",
        position: "top-center",
      });
      // Simulate the contract call first
      await publicClient.simulateContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "unlistChickenForRent",
        args: [
          BigInt(contractRentalId),
          BigInt(chickenTokenId),
          signature as `0x${string}`,
        ],
        chain,
        account: address,
      });

      // Estimate gas
      const gasEstimate = await publicClient.estimateContractGas({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "unlistChickenForRent",
        args: [
          BigInt(contractRentalId),
          BigInt(chickenTokenId),
          signature as `0x${string}`,
        ],
        account: address,
      });

      // Execute transaction
      const hash = await walletClient.writeContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "unlistChickenForRent",
        args: [
          BigInt(contractRentalId),
          BigInt(chickenTokenId),
          signature as `0x${string}`,
        ],
        gas: gasEstimate + BigInt(50000),
        chain,
        account: address,
      });

      // Wait for confirmation
      toast.info("Confirming transaction...", {
        description: "Waiting for blockchain confirmation",
        position: "top-center",
      });

      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        confirmations: 1,
      });

      if (receipt.status !== "success") {
        throw new Error("Contract transaction failed");
      }

      toast.success("Delegation cancelled successfully!", {
        description: "Your delegation has been cancelled",
        position: "top-center",
      });

      // Invalidate queries to refresh data with proper sequencing
      // Use longer delay for cancel delegation to ensure blockchain state is updated
      setTimeout(async () => {
        // First, invalidate and refetch chickenTokenIds (blockchain data)
        await queryClient.invalidateQueries({
          queryKey: ["chickenTokenIds", "legacy", address],
        });
        await queryClient.invalidateQueries({
          queryKey: ["chickenTokenIds", "genesis", address],
        });

        // Then invalidate rental-related queries
        queryClient.invalidateQueries({ queryKey: ["my-rentals"] });
        queryClient.invalidateQueries({ queryKey: ["my-rentals", address] });
        queryClient.invalidateQueries({ queryKey: ["chickenRentalStatuses"] });
        queryClient.invalidateQueries({ queryKey: ["available-rentals"] });
      }, 3000); // Increased delay for cancel delegation

      return {
        success: true,
        data: cancelResponse.data,
        hash,
        receipt,
        rentalId: contractRentalId,
        message: "Delegation cancelled successfully",
      };
    } catch (error: unknown) {
      console.error("Cancel delegation error:", error);

      let errorMessage = "Failed to cancel delegation";
      let errorDescription = "Please try again";

      if (error instanceof Error) {
        if (error.message.includes("User rejected")) {
          errorMessage = "Transaction cancelled";
          errorDescription = "You cancelled the transaction";
        } else if (error.message.includes("insufficient funds")) {
          errorMessage = "Insufficient funds";
          errorDescription = "You don't have enough ETH for gas fees";
        } else if (error.message.includes("ErrInvalidSignature")) {
          errorMessage = "Signature verification failed";
          errorDescription = "Invalid signature or unauthorized cancellation";
        } else {
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        position: "top-center",
      });

      return { success: false, error };
    } finally {
      setIsCancelling(false);
    }
  };

  // Mutation for React Query integration
  const cancelDelegationMutation = useMutation({
    mutationFn: executeCancelDelegation,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Cancel delegation mutation error:", error);
    },
  });

  return {
    executeCancelDelegation,
    cancelDelegationMutation,
    isCancelling,
    rentalAddress,
  };
};
