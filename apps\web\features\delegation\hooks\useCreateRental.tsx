"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";
import { DelegationAPI } from "../api/delegation.api";
import { useListChickenForRent } from "./useListChickenForRent";
import { useListChickenForRentBulk } from "./useListChickenForRentBulk";
import { useDirectDelegation } from "./useDirectDelegation";
import {
  ICreateRentalFormData,
  ICreateRentalResponse,
  IBulkCreateRentalFormData,
  IBulkCreateRentalResponse,
  IChickenRentalConfig,
  IChickenSelection,
  ISelectedChickenInfo,
} from "../types/delegation.types";

/**
 * Hook for handling rental creation process
 * Integrates with blockchain listing functionality for marketplace rentals
 */
export const useCreateRental = () => {
  const { address } = useStateContext();
  const { blockchainQuery } = useBlockchain();
  const { executeListChickenForRent, isListing } = useListChickenForRent();
  const { executeBulkBlockchainListing, isListing: isBulkListing } =
    useListChickenForRentBulk();
  const { executeDirectDelegation, isProcessing } = useDirectDelegation();
  const queryClient = useQueryClient();

  const [isCreating, setIsCreating] = useState(false);

  // Get rental contract address (for future use)
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  /**
   * Execute the rental creation process
   * - Direct delegations: Create database entry + blockchain escrow + activation
   * - Rental listings: Create database entry + blockchain listing transaction
   */
  const executeCreateRental = async (formData: ICreateRentalFormData) => {
    try {
      if (!address) {
        toast.error("Cannot create rental", {
          description: "Wallet not connected",
          position: "top-right",
        });
        return { success: false, error: "Wallet not connected" };
      }

      setIsCreating(true);

      // Handle direct delegation (API + Blockchain with escrow)
      if (formData.isDirectDelegation) {
        toast.info("Creating delegation...", {
          description: "This will require blockchain confirmation for escrow",
          position: "top-center",
        });

        // Use the direct delegation hook for full blockchain integration
        const result = await executeDirectDelegation(formData);

        if (result?.success) {
          // Success message is already shown by the direct delegation hook
          return {
            success: true,
            data: result.data,
            hash: result.listHash,
            receipt: result.listReceipt,
            message: result.message,
          };
        } else {
          throw new Error("Failed to create delegation");
        }
      } else {
        // Handle marketplace listing (API + Blockchain)
        toast.info("Creating rental listing...", {
          description: "This will require blockchain confirmation",
          position: "top-center",
        });

        // Use the listing hook for blockchain integration
        const result = await executeListChickenForRent(formData);

        if (result?.success) {
          // Success message is already shown by the listing hook
          return {
            success: true,
            data: result.data,
            hash: result.hash,
            receipt: result.receipt,
          };
        } else {
          throw new Error("Failed to list chicken for rent");
        }
      }
    } catch (error) {
      console.error("Create rental failed:", error);

      let errorMessage = "Failed to create rental";
      let errorDescription = "An unexpected error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;

        // Handle specific error cases
        if (error.message.includes("Chicken not found")) {
          errorDescription = "The selected chicken was not found";
        } else if (error.message.includes("already rented")) {
          errorDescription = "This chicken is already rented or delegated";
        } else if (error.message.includes("not owned")) {
          errorDescription = "You don't own this chicken";
        } else if (error.message.includes("Validation")) {
          errorDescription = "Please check your input values";
        } else {
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        position: "top-center",
      });

      return { success: false, error: errorMessage };
    } finally {
      setIsCreating(false);
    }
  };

  /**
   * Execute bulk rental creation process
   * Creates multiple rental listings with the same configuration
   */
  const executeBulkCreateRental = async (
    selectedChickens: IChickenSelection,
    formData: ICreateRentalFormData,
    selectedChickensInfo: ISelectedChickenInfo[]
  ) => {
    // Helper function to check if a chicken needs insurance based on its type
    const chickenNeedsInsurance = (
      chickenInfo: ISelectedChickenInfo
    ): boolean => {
      if (!chickenInfo.metadata?.attributes) return false;
      const typeAttribute = chickenInfo.metadata.attributes.find(
        (attr: any) => attr.trait_type === "Type"
      );
      // Genesis chickens cannot die, so they don't need insurance
      // Legacy and Ordinary chickens can die, so they need insurance
      return (
        typeAttribute?.value === "Legacy" || typeAttribute?.value === "Ordinary"
      );
    };

    // Calculate insurance amount for a specific chicken
    const calculateInsuranceForChicken = (
      chickenInfo: ISelectedChickenInfo
    ): string => {
      // Only chickens that can die need insurance (Legacy and Ordinary)
      if (!chickenNeedsInsurance(chickenInfo)) return "0";

      // For Legacy/Ordinary chickens, use the insurance field value
      // If user specified custom insurance, use that (including empty = 0)
      if (
        formData.insurancePrice !== undefined &&
        formData.insurancePrice !== null
      ) {
        const customInsurance = parseFloat(formData.insurancePrice) || 0;
        return customInsurance.toString();
      }

      // If no insurance price specified, default to 0
      return "0";
    };
    try {
      if (!address) {
        toast.error("Cannot create bulk rentals", {
          description: "Wallet not connected",
          position: "top-right",
        });
        return { success: false, error: "Wallet not connected" };
      }

      // Convert selection to rental configs
      const selectedIds = Object.keys(selectedChickens)
        .filter((id) => selectedChickens[parseInt(id)])
        .map((id) => parseInt(id));

      if (selectedIds.length === 0) {
        toast.error("No chickens selected", {
          description: "Please select at least one chicken",
          position: "top-right",
        });
        return { success: false, error: "No chickens selected" };
      }

      setIsCreating(true);

      // Create rental configs for each selected chicken with proper insurance calculation
      const rentalConfigs: IChickenRentalConfig[] = selectedIds.map(
        (chickenTokenId) => {
          // Find the chicken info for this token ID
          const chickenInfo = selectedChickensInfo.find(
            (info) => info.tokenId === chickenTokenId
          );

          // Calculate insurance for this specific chicken
          const insurancePriceInRon = chickenInfo
            ? calculateInsuranceForChicken(chickenInfo)
            : "0";

          // Convert prices from RON to wei for backend
          let roninPriceInWei: string;
          let insurancePriceInWei: string;

          if (formData.isDirectDelegation) {
            roninPriceInWei = "0"; // Free delegation
          } else {
            // Calculate total price: daily rate × duration in days
            const dailyRateInRon = parseFloat(formData.roninPrice);
            const durationInDays = formData.rentalPeriod / 86400;
            const totalPriceInRon = dailyRateInRon * durationInDays;
            // Convert total price from RON to wei (multiply by 1e18)
            roninPriceInWei = (totalPriceInRon * 1e18).toString();
          }

          // Convert insurance price from RON to wei
          const insurancePriceInRonFloat = parseFloat(insurancePriceInRon);
          insurancePriceInWei =
            insurancePriceInRonFloat > 0
              ? (insurancePriceInRonFloat * 1e18).toString()
              : "0";

          return {
            chickenTokenId,
            roninPrice: roninPriceInWei,
            rentalPeriod: formData.rentalPeriod,
            rewardDistribution: formData.rewardDistribution,
            gameRewardDistribution: formData.gameRewardDistribution,
            delegatedTask: formData.delegatedTask,
            sharedRewardAmount: formData.sharedRewardAmount,
            renterAddress: formData.isDirectDelegation
              ? formData.renterAddress
              : undefined,
            insurancePrice: insurancePriceInWei,
          };
        }
      );

      toast.info("Creating bulk rental listings...", {
        description: `Processing ${selectedIds.length} chickens`,
        position: "top-center",
      });

      // Step 1: Call the bulk API endpoint to create rental records
      const apiResponse = await DelegationAPI.createBulkRental({
        rentals: rentalConfigs,
      });

      if (apiResponse.status !== 1) {
        throw new Error(apiResponse.message || "Failed to create bulk rentals");
      }

      const apiSuccessCount = apiResponse.data.filter(
        (result) => result.success
      ).length;
      const apiFailureCount = apiResponse.data.length - apiSuccessCount;

      if (apiSuccessCount === 0) {
        throw new Error("No rentals were created successfully");
      }

      // Step 2: Execute blockchain listing for successful API rentals
      toast.info("Processing blockchain transactions...", {
        description: `Listing ${apiSuccessCount} chickens on blockchain`,
        position: "top-center",
      });

      const blockchainResult = await executeBulkBlockchainListing(apiResponse);

      if (blockchainResult.success) {
        // Both API and blockchain succeeded
        toast.success("Bulk listing completed!", {
          description: `${blockchainResult.processedCount} chickens listed successfully${apiFailureCount > 0 ? `, ${apiFailureCount} failed` : ""}`,
          position: "top-center",
        });

        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ["rentals"] });
        queryClient.invalidateQueries({ queryKey: ["myRentals"] });
        queryClient.invalidateQueries({ queryKey: ["chickensForDelegation"] });

        return {
          success: true,
          data: apiResponse.data,
          successCount: blockchainResult.processedCount,
          failureCount: apiFailureCount,
          hash: blockchainResult.hash,
          receipt: blockchainResult.receipt,
        };
      } else {
        // API succeeded but blockchain failed
        toast.error("Blockchain listing failed", {
          description:
            "Rentals created but not listed on blockchain. Please try again.",
          position: "top-center",
        });

        return {
          success: false,
          error: blockchainResult.error,
          data: apiResponse.data,
          successCount: 0,
          failureCount: apiResponse.data.length,
        };
      }
    } catch (error: any) {
      console.error("Bulk rental creation error:", error);

      const errorMessage = error?.message || "Failed to create bulk rentals";
      toast.error("Bulk listing failed", {
        description: errorMessage,
        position: "top-center",
      });

      return { success: false, error: errorMessage };
    } finally {
      setIsCreating(false);
    }
  };

  /**
   * Validate form data before submission
   */
  const validateFormData = (formData: ICreateRentalFormData): string[] => {
    const errors: string[] = [];

    if (!formData.chickenTokenId) {
      errors.push("Please select a chicken");
    }

    if (formData.isDirectDelegation) {
      if (!formData.renterAddress) {
        errors.push("Renter address is required for direct delegation");
      } else if (
        !formData.renterAddress.startsWith("0x") ||
        formData.renterAddress.length !== 42
      ) {
        errors.push("Invalid renter address format");
      }
    } else {
      if (!formData.roninPrice || parseFloat(formData.roninPrice) <= 0) {
        errors.push("Valid daily rental rate is required");
      }
    }

    if (!formData.rentalPeriod || formData.rentalPeriod < 3600) {
      errors.push("Rental period must be at least 1 hour");
    }

    return errors;
  };

  // Mutation for React Query integration
  const createRentalMutation = useMutation({
    mutationFn: executeCreateRental,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Create rental mutation error:", error);
    },
  });

  return {
    executeCreateRental,
    executeBulkCreateRental,
    createRentalMutation,
    validateFormData,
    isCreating: isCreating || isListing || isBulkListing || isProcessing, // Include all processing states
    rentalAddress,
  };
};
