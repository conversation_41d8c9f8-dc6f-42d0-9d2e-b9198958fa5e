"use client";

import React, { useCallback, useState } from "react";
import { Button } from "ui";
import { cn } from "@/utils/classes";
import Image from "next/image";
import { useStateContext } from "@/providers/app/state";
import {
  IChickenWithRewards,
  ERewardType,
  EHistoryTab,
  getTokenDisplayName,
} from "../types/inventory.types";
import { useTransferHistory } from "../hooks/useTransferHistory";
import { useClaimHistory } from "../hooks/useClaimHistory";
import { rewardMetadataMap } from "../mock/mock-data";
import { toast } from "sonner";

interface IChickenRewardsHistoryProps {
  chickens: IChickenWithRewards[];
  className?: string;
}

interface IClaimHistoryItem {
  id: string;
  rewardType: ERewardType;
  rewardName: string;
  rewardImage: string;
  quantity: number;
  claimedAt: string;
  transactionHash?: string;
}

interface ITransferHistoryItem {
  id: string;
  chickenId: string;
  chickenName: string;
  chickenImage: string;
  rewardType: ERewardType;
  rewardName: string;
  rewardImage: string;
  quantity: number;
  transferredAt: string;
  fromAddress: string;
  toAddress: string;
  status: "pending" | "completed" | "failed";
}

// Type guard functions
const isClaimHistoryItem = (
  item: IClaimHistoryItem | ITransferHistoryItem
): item is IClaimHistoryItem => {
  return "claimedAt" in item;
};

/**
 * ChickenRewardsHistory Component
 *
 * Shows history of claimed rewards similar to ninuno rewards
 */
export const ChickenRewardsHistory: React.FC<IChickenRewardsHistoryProps> = ({
  chickens,
  className,
}) => {
  const { address } = useStateContext();
  const [activeTab, setActiveTab] = useState<EHistoryTab>(
    EHistoryTab.CLAIM_HISTORY
  );
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Transaction hash copied to clipboard.");
    } catch (err) {
      toast.error("Failed to copy transaction hash.");
    }
  }, []);
  // Fetch real data from APIs
  const {
    transferHistory,
    pagination: transferPagination,
    isLoading: isTransferLoading,
  } = useTransferHistory(address || null, itemsPerPage);

  const {
    claimHistory,
    pagination: claimPagination,
    isLoading: isClaimLoading,
  } = useClaimHistory(address || null, itemsPerPage);

  // Transform API data to UI format
  const transformedTransferHistory: ITransferHistoryItem[] =
    transferHistory.map((transfer) => {
      const metadata = rewardMetadataMap.get(transfer.tokenId);
      return {
        id: transfer._id,
        chickenId: transfer.chickenIds[0]?.toString() || "Unknown",
        chickenName: `Chicken #${transfer.chickenIds[0] || "Unknown"}`,
        chickenImage: `https://chicken-api-ivory.vercel.app/api/image/${transfer.chickenIds[0] || 1}.png`,
        rewardType: metadata?.type || ERewardType.CRYSTAL,
        rewardName: metadata?.name || `Token ${transfer.tokenId}`,
        rewardImage: metadata?.image || "/images/rewards/default.png",
        quantity: transfer.quantity,
        transferredAt: transfer.transferredAt,
        fromAddress: transfer.owner,
        toAddress: "Balance",
        status: transfer.status || "pending",
      };
    });

  const transformedClaimHistory: IClaimHistoryItem[] = claimHistory.flatMap(
    (claim) =>
      claim.claims.map((claimItem, index) => {
        const metadata = rewardMetadataMap.get(claimItem.tokenId);
        return {
          id: `${claim._id}-${index}`,
          rewardType: metadata?.type || ERewardType.CRYSTAL,
          rewardName: metadata?.name || `Token ${claimItem.tokenId}`,
          rewardImage: metadata?.image || "/images/rewards/default.png",
          quantity: claimItem.amount,
          claimedAt: claim.claimedAt,
          transactionHash: claim.transactionHash, // Mock transaction hash
        };
      })
  );

  // Get active history data based on tab
  const activeHistoryData =
    activeTab === EHistoryTab.CLAIM_HISTORY
      ? transformedClaimHistory
      : transformedTransferHistory;

  const activePagination =
    activeTab === EHistoryTab.CLAIM_HISTORY
      ? claimPagination
      : transferPagination;

  const isLoading =
    activeTab === EHistoryTab.CLAIM_HISTORY
      ? isClaimLoading
      : isTransferLoading;

  // Get reward type badge color
  const getTypeColor = (type: ERewardType) => {
    switch (type) {
      case ERewardType.CRYSTAL:
        return "bg-blue-600/30 text-blue-200 border border-blue-400/40";
      case ERewardType.SHARD:
        return "bg-purple-600/30 text-purple-200 border border-purple-400/40";
      case ERewardType.CORN:
        return "bg-amber-600/30 text-amber-200 border border-amber-400/40";
      default:
        return "bg-stone-600/30 text-stone-200 border border-stone-600/40";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatTransactionHash = (hash: string) => {
    return `${hash.slice(0, 6)}...${hash.slice(-4)}`;
  };

  // Use data directly from API (already paginated)
  const currentHistory = activeHistoryData;
  const totalPages = activePagination
    ? Math.ceil(activePagination.total / itemsPerPage)
    : 1;

  // Reset pagination when switching tabs
  React.useEffect(() => {
    setCurrentPage(1);
  }, [activeTab]);

  return (
    <div
      className={cn(
        "bg-stone-900/50 rounded-lg border border-stone-600/30 p-6",
        className
      )}
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-stone-200 mb-1">
            Rewards History
          </h2>
          <p className="text-sm text-stone-400">
            View your past reward claims and transfers
          </p>
        </div>
        <div className="text-sm text-stone-400">
          {isLoading
            ? "Loading..."
            : `Total: ${activePagination?.total || 0} ${activeTab === EHistoryTab.CLAIM_HISTORY ? "claims" : "transfers"}`}
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex items-center gap-1 bg-stone-800/60 rounded-lg p-1.5 border border-stone-600/40 mb-6">
        <Button
          size="small"
          appearance={
            activeTab === EHistoryTab.CLAIM_HISTORY ? "solid" : "plain"
          }
          onPress={() => setActiveTab(EHistoryTab.CLAIM_HISTORY)}
          className={cn(
            "text-sm font-medium px-4 py-2 rounded transition-all",
            activeTab === EHistoryTab.CLAIM_HISTORY
              ? "bg-amber-600 text-amber-50 shadow-md"
              : "text-stone-300 hover:text-amber-200 hover:bg-stone-700/50"
          )}
        >
          Claim History
        </Button>
        <Button
          size="small"
          appearance={
            activeTab === EHistoryTab.TRANSFER_HISTORY ? "solid" : "plain"
          }
          onPress={() => setActiveTab(EHistoryTab.TRANSFER_HISTORY)}
          className={cn(
            "text-sm font-medium px-4 py-2 rounded transition-all",
            activeTab === EHistoryTab.TRANSFER_HISTORY
              ? "bg-amber-600 text-amber-50 shadow-md"
              : "text-stone-300 hover:text-amber-200 hover:bg-stone-700/50"
          )}
        >
          Transfer History
        </Button>
      </div>

      {/* History Table */}
      <div className="overflow-hidden rounded-lg border border-stone-600/30">
        {/* Table Header - Hidden on mobile */}
        <div className="hidden md:block bg-stone-800/60 px-6 py-4 border-b border-stone-600/30">
          <div className="grid grid-cols-12 gap-4 items-center">
            <div className={cn(
              activeTab === EHistoryTab.CLAIM_HISTORY ? "col-span-4" : "col-span-6"
            )}>
              <span className="text-sm font-semibold text-stone-300">
                Reward
              </span>
            </div>
            <div className={cn(
              activeTab === EHistoryTab.CLAIM_HISTORY ? "col-span-2" : "col-span-3"
            )}>
              <span className="text-sm font-semibold text-stone-300">
                Quantity
              </span>
            </div>
            <div className="col-span-3">
              <span className="text-sm font-semibold text-stone-300">
                {activeTab === EHistoryTab.CLAIM_HISTORY
                  ? "Claimed"
                  : "Transferred"}
              </span>
            </div>
            {activeTab === EHistoryTab.CLAIM_HISTORY && (
              <div className="col-span-3">
                <span className="text-sm font-semibold text-stone-300">
                  Transaction
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Table Body */}
        <div className="bg-stone-900/40">
          {isLoading ? (
            <div className="px-6 py-12 text-center">
              <span className="text-4xl mb-3 block">⏳</span>
              <p className="text-stone-400">Loading history...</p>
            </div>
          ) : (
            currentHistory.map((item) => (
              <div
                key={item.id}
                className="px-4 md:px-6 py-4 border-b border-stone-600/20 hover:bg-stone-800/40 transition-colors"
              >
                {/* Desktop Layout */}
                <div className="hidden md:grid grid-cols-12 gap-4 items-center">
                  {/* Reward Info */}
                  <div className={cn(
                    "flex items-center gap-3",
                    activeTab === EHistoryTab.CLAIM_HISTORY ? "col-span-4" : "col-span-6"
                  )}>
                    <div className="w-8 h-8 rounded-lg overflow-hidden border border-stone-600/40 flex-shrink-0">
                      <Image
                        src={item.rewardImage}
                        alt={item.rewardName}
                        width={32}
                        height={32}
                        quality={100}
                        unoptimized
                        className="w-full h-full object-contain"
                      />
                    </div>
                    <div>
                      <h4 className="font-medium text-stone-200 text-sm">
                        {item.rewardName}
                      </h4>
                      <span
                        className={cn(
                          "px-1.5 py-0.5 rounded text-xs font-medium",
                          getTypeColor(item.rewardType)
                        )}
                      >
                        {item.rewardType}
                      </span>
                    </div>
                  </div>

                  {/* Quantity */}
                  <div className={cn(
                    activeTab === EHistoryTab.CLAIM_HISTORY ? "col-span-2" : "col-span-3"
                  )}>
                    <div className="bg-amber-500/90 text-amber-900 px-2 py-1 rounded-full text-sm font-bold inline-block">
                      {item.quantity}
                    </div>
                  </div>

                  {/* Date */}
                  <div className={cn(
                    activeTab === EHistoryTab.CLAIM_HISTORY ? "col-span-3" : "col-span-3"
                  )}>
                    <p className="text-sm text-stone-300">
                      {isClaimHistoryItem(item)
                        ? formatDate(item.claimedAt)
                        : formatDate(item.transferredAt)}
                    </p>
                  </div>

                  {/* Transaction (Only for Claim History) */}
                  {activeTab === EHistoryTab.CLAIM_HISTORY && (
                    <div className="col-span-3">
                      {isClaimHistoryItem(item) && item.transactionHash ? (
                        <Button
                          onPress={() => copyToClipboard(item.transactionHash!)}
                          size="small"
                          appearance="plain"
                          className="text-blue-400 hover:text-blue-300 text-sm"
                        >
                          {formatTransactionHash(item.transactionHash)}
                        </Button>
                      ) : (
                        <span className="text-xs text-stone-500">Pending</span>
                      )}
                    </div>
                  )}
                </div>

                {/* Mobile Layout */}
                <div className="md:hidden space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-lg overflow-hidden border border-stone-600/40 flex-shrink-0">
                        <Image
                          src={item.rewardImage}
                          alt={item.rewardName}
                          width={32}
                          height={32}
                          quality={100}
                          unoptimized
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-stone-200 text-sm">
                          {item.rewardName}
                        </h3>
                        <span
                          className={cn(
                            "px-1.5 py-0.5 rounded text-xs font-medium",
                            getTypeColor(item.rewardType)
                          )}
                        >
                          {item.rewardType}
                        </span>
                      </div>
                    </div>
                    <div className="bg-amber-500/90 text-amber-900 px-2 py-1 rounded-full text-sm font-bold">
                      {item.quantity}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <p className="text-xs text-stone-400">
                      {isClaimHistoryItem(item)
                        ? formatDate(item.claimedAt)
                        : formatDate(item.transferredAt)}
                    </p>
                    {activeTab === EHistoryTab.CLAIM_HISTORY &&
                      isClaimHistoryItem(item) &&
                      item.transactionHash && (
                        <Button
                          size="small"
                          appearance="plain"
                          className="text-blue-400 hover:text-blue-300 text-xs"
                        >
                          View Tx
                        </Button>
                      )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Empty State */}
        {!isLoading && activeHistoryData.length === 0 && (
          <div className="bg-stone-900/40 px-6 py-12 text-center">
            <span className="text-4xl mb-3 block">
              {!address
                ? "🔗"
                : activeTab === EHistoryTab.CLAIM_HISTORY
                  ? "📜"
                  : "🔄"}
            </span>
            <h3 className="text-lg font-medium text-stone-200 mb-2">
              {!address
                ? "Connect Your Wallet"
                : `No ${activeTab === EHistoryTab.CLAIM_HISTORY ? "claim" : "transfer"} history`}
            </h3>
            <p className="text-sm text-stone-400">
              {!address
                ? "Connect your wallet to view your reward history."
                : `Your reward ${activeTab === EHistoryTab.CLAIM_HISTORY ? "claims" : "transfers"} will appear here once you start ${activeTab === EHistoryTab.CLAIM_HISTORY ? "claiming rewards" : "transferring rewards to your balance"}.`}
            </p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-4 mt-6">
          <Button
            size="small"
            appearance="plain"
            onPress={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
            isDisabled={currentPage === 1}
            className="text-stone-300 hover:text-stone-200"
          >
            Previous
          </Button>

          <span className="text-sm text-stone-300">
            Page {currentPage} of {totalPages}
          </span>

          <Button
            size="small"
            appearance="plain"
            onPress={() =>
              setCurrentPage((prev) => Math.min(totalPages, prev + 1))
            }
            isDisabled={currentPage === totalPages}
            className="text-stone-300 hover:text-stone-200"
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
};
