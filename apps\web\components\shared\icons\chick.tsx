import { SVGProps } from "react";

interface ChickXProps extends SVGProps<SVGSVGElement> {
  size?: number;
}

export const ChickX = ({ size = 196, ...props }: ChickXProps) => {
  const aspectRatio = 345 / 300;
  const height = size * aspectRatio;
  return (
    <svg
      width={size}
      height={height}
      xmlns="http://www.w3.org/2000/svg"
      version="1.0"
      viewBox="0 0 300 345"
      fill="currentcolor"
      stroke="currentcolor"
      {...props}
    >
      <path d="M120.5 13.3c-10.2 4.9-15 12.3-15 23.2 0 5.7.4 7.6 2.3 10.2l2.3 3.2-8.8 4.5c-28.1 14.6-46 38-54.3 71.1-4 15.8-5 23.5-5.7 45.2-.3 10.6-.9 19.3-1.2 19.3-.3 0-3.1-.7-6.1-1.5-3-.8-7.9-1.5-10.8-1.5-4.6 0-5.8.4-8.3 2.9-1.6 1.6-2.9 3.9-2.9 5.1 0 3.2 2.5 8.2 5.3 10.9l2.6 2.4-2.9 3.4c-2.3 2.6-3 4.3-3 7.5 0 3.6.6 4.7 4.4 8.4 5.8 5.5 12.5 7.7 23.9 7.8l8.8.1 3.3 7.6c7.2 16.6 17.1 28.9 31.3 38.7 5 3.5 16.4 8.9 22.6 10.7l5.7 1.6-.6 4.2c-1 6.4-1.6 7-10.3 10.5-8.8 3.6-11.4 6.3-8.8 9 1.4 1.4 2.2 1.3 7.6-1.1l6.1-2.6 2 3.9c3.6 7.1 7.5 7.3 13.1.7l2.6-3.1 4.9 2.1c5.1 2.2 6.9 2.1 7.8-.4.8-2-1.4-4.4-8.1-8.9-5.2-3.5-5.3-3.6-4.7-7.2.4-2 .8-3.8.9-4 .1-.2 6.9.2 15.1.8 13.6.9 21.5.8 34.7-.6l4.7-.5v4.5c0 4.2-.2 4.6-4.9 7.5-2.6 1.7-5.6 4-6.6 5.1-1.8 2-1.8 2.2-.1 3.8 1.6 1.6 2 1.5 7.6-1.4l6-3.1 3.6 4.3c4.4 5.4 7.1 5.7 11.2 1 1.6-1.9 3.2-4 3.4-4.6.3-.9 1.7-.8 5.4.6 5.9 2 8.7 1.4 8.2-1.8-.4-2.7-6.6-6.1-14.6-8-4.6-1.1-5.2-1.6-5.3-4-.1-1.6-.2-3.3-.3-4-.1-.6 3.1-2.1 6.9-3.3 29.2-8.9 52.2-31.6 60-59.3.5-1.7 1.4-2.2 3.9-2.2 5.3 0 13.4-2.9 17-6.1 3.9-3.3 5.2-7.3 3.7-11.1-.8-2.2-.6-2.8 2-4.7 4.3-3.2 6.9-8 6.9-12.8 0-3.4-.5-4.6-3-6.7-4-3.3-8.1-4-17.4-2.7-12.7 1.8-11.6 2.7-11.6-9.7 0-32.4-5.7-62.8-15.4-81.9-8.9-17.7-29-37-46.3-44.4-2.6-1.1-4.9-2.2-5.1-2.4-.2-.2 1.4-2.1 3.7-4.3 3.3-3.2 4.1-4.6 4.1-7.5 0-4.6-2.9-7.7-7-7.7-2.9 0-3-.2-3-4.6 0-9.1-6-14.4-16.2-14.4-7.8.1-16.2 4.5-19.5 10.5-1 1.7-1.4 1.6-5.7-2.2-6.8-6-11-7.7-19.6-8.1-6.3-.2-8.3.1-12.5 2.1zm21.8 5.3c2 1 3.7 2 3.7 2.3-.1.3-1.5 1.5-3.2 2.6-5.9 3.8-7.2 6.7-7.3 15.8 0 7.2.2 8.2 1.9 8.5 2.6.5 3.5-1.3 2.9-5.9-.8-5.6 2.1-12.5 5.9-14.5 3.8-2 5.3-1.3 7.9 3.1 2.9 4.8 6.1 4.5 6.9-.5.9-5.4 3.7-8.7 9.6-11.6 4.2-2 6.3-2.5 9.2-2.1 4.5.8 9.2 5.5 9.2 9.5 0 2.5-.6 3.8-3.7 8.9-.7 1.2-.8 2.5-.3 3.3.8 1.2 1.6 1.1 5.5-.8 2.5-1.2 5.2-2.2 5.9-2.2 2 0 2.8 2.8 1.4 5-1.6 2.5-8 6-11 6-1.3 0-3.3.6-4.3 1.4-2.6 1.9-.7 4.1 4 4.9 5.3.9 21.2 8.8 28.1 14 8.9 6.8 19.4 18.3 25.1 27.6 9.8 15.8 15.3 38.8 17.3 71.8 1.5 25.4 1.6 24-2.4 24.7-3.6.7-5 2.4-3.6 4.5.6 1 4.2.9 17.3-.5 18.5-2 21.7-1.5 21.7 3 0 3.2-4.3 8.2-9.1 10.7-4.3 2.1-5 4.3-1.9 5.9 2.8 1.5 2.5 5.4-.6 7.9-8.2 6.5-24.4 5-33.1-3.1-2.5-2.4-5.3-1.8-5.3 1.1 0 2.4 4.8 6.2 11.3 9.1 3 1.3 4.7 2.7 4.7 3.8 0 2.9-6.4 16.4-10.7 22.7-4.6 6.8-14.4 16.5-21.6 21.4-6.1 4.1-17.4 9.4-25 11.6-5.5 1.6-5.9 1.6-7.8-.1-1.8-1.7-2-1.6-4.4.6-3.3 3.1-16.2 4.4-38.5 3.6-19.4-.6-21.7-1-22.5-3.6-.8-2.6-3.5-3.3-5.6-1.4-1.6 1.4-2.5 1.5-6.6.5-12.9-3.3-26.2-11-36.3-21.1-7.2-7.2-11.2-13.1-16-23.3-5.9-12.8-7.5-20.2-7.6-35.7l-.1-13.4-3-1.3c-2.8-1.1-3.2-1.8-3.8-7-.8-7.2 1-35 2.9-46 3.9-21.8 10.5-38.6 20.5-52 13.4-18.1 33.3-31.7 54.5-37.2 8.9-2.3 9.1-2.4 8.3-4.6-.8-1.9-3-1.9-9.2-.1-4.2 1.3-5.2 1.3-7.6 0-3.3-1.7-5.9-6.1-5.9-10.1.1-6.9 7-15.9 14.5-18.8 4.9-1.8 12.6-1.4 17.8 1.1zm-107.2 176c3.1.8 7.1 2 8.8 2.7l3.2 1.4.3 10.4c.2 5.7.9 12.4 1.5 14.8 1.5 5.4.8 6.1-7 6.1-7.9 0-15.7-2.6-19.6-6.5-1.8-1.8-3.3-3.7-3.3-4.3 0-2.4 3.9-5.5 7.6-6.1 3-.5 4-1.1 4.2-2.9.2-1.5-.2-2.2-1.2-2.2-2.5 0-11.6-9.3-11.6-11.8 0-4.2 4.9-4.6 17.1-1.6zm88.5 98.9c.6 1.5-1.2 4.5-2.7 4.5-.4 0-1.5-.8-2.4-1.7-1.6-1.6-1.6-1.7.1-3 2.4-1.7 4.3-1.6 5 .2zm65.2 7.8c-.2 1.2-.7 2.2-1.3 2.2-1.2 0-1.9-4.1-1-6.3.6-1.6.7-1.6 1.6.1.6 1 .9 2.8.7 4zm4.2 8.2c3.5 1.8 3.7 2.6 1.2 4.8-1.7 1.6-2 1.5-4.1-1.3-1.2-1.7-1.9-3.5-1.6-4 .8-1.3 1.1-1.3 4.5.5zm-72.4.9c.6 1.6-2.3 6.6-3.7 6.6-.5 0-1.4-1.2-2-2.7-.8-2.2-.7-3 .7-4 2.3-1.7 4.3-1.6 5 .1z" />
      <path d="m206.5 122.3-5.9 5.4-6.4-5c-5-3.9-6.8-4.8-8-4.1-2.9 1.9-2.3 3.2 3.8 7.9 3.3 2.7 6 5.1 6 5.5 0 .4-2.5 3.2-5.5 6.2-3.8 3.8-5.3 6-4.9 7.2 1 2.7 3.4 1.8 9.1-3.4 3-2.8 5.8-5 6.3-5s3.4 2.3 6.4 5.1c5.6 5.1 8 5.7 9.2 2.4.4-.9-1.4-3.2-5-6.6-3.1-2.8-5.6-5.6-5.6-6.2 0-.6 2.4-3.3 5.3-5.9 4.3-3.9 5-5 4.1-6.4-1.8-3-2.8-2.6-8.9 2.9zM90.9 120.6c-1.2 1.5-.6 2.3 4.8 7.1l6.2 5.5-6.1 5.8c-5.3 5-6 6.1-4.8 7.4 1.9 2.4 2.6 2.1 8.9-3.4 3.2-2.8 6.2-5 6.6-5 .5 0 3.1 2.1 5.7 4.7 3.9 3.9 5.2 4.6 6.5 3.7 2.9-1.8 2.5-3.1-2.2-7.5-2.5-2.3-4.5-4.7-4.5-5.3 0-.6 2.7-3.3 6-6.1 3.5-2.8 5.9-5.6 5.8-6.5-.6-3.1-4.3-2-10.3 2.9-3.3 2.8-6.4 5.1-6.8 5.1-.5 0-3.4-2.3-6.6-5-6.3-5.5-7.2-5.8-9.2-3.4zM147.9 129c-7.2 2.1-15.9 11.6-15.9 17.2 0 1.6 1.3 4.2 3.1 6.3 4.5 5.1 17.7 15.5 19.7 15.5 2.4 0 14.2-12.8 17-18.4 2.7-5.3 2.4-8-1.3-13.7-4.2-6.4-14.2-9.5-22.6-6.9zm12.1 5.6c4.5 1.8 8 6 8 9.6 0 2.2-9.4 15.2-12.2 16.9-1.4.9-5-1.5-12.9-8.8l-5.9-5.5 2.1-3.8c2.5-4.5 6.3-7.5 11-8.8 4.8-1.4 5.4-1.4 9.9.4zM195.7 236.7c-1.2 1.2-.7 4.8.9 7.2 1.3 1.8 2.3 2.2 4.4 1.7 3.3-.6 4 .6 4 6.5 0 4.4-2.1 6.1-4.9 4.1-3-2.2-4.3-2.5-5.1-1.2-1.2 2-.1 3.9 3.2 5.6 3.6 1.8 7 1.2 9.8-1.8 2.3-2.5 2.5-6.4.6-15.3-1.4-6.5-3.6-8.3-5.6-4.6-1.4 2.8-2.6 2.6-3.4-.4-.6-2.4-2.5-3.3-3.9-1.8z" />
    </svg>
  );
};

export default ChickX;
