"use client";

import { cn } from "@/components/ui";
import { Tooltip } from "@/components/ui/tooltip";

interface IGeneCell {
  value: string;
  type: string;
}

export const GeneCell = ({ value, type }: IGeneCell) => {
  // Get the base trait type (e.g., "Feet" from "Feet (P)")
  const getTraitType = () => {
    const baseType = type.split(" ")[0];
    return baseType;
  };

  // Get color based on trait type - colors match associated stats
  const getTraitColor = () => {
    const traitType = getTraitType();
    switch (traitType) {
      case "Feet": // Speed
        return "bg-blue-900/60 text-blue-300";
      case "Tail": // Evasion
        return "bg-purple-900/60 text-purple-300";
      case "Body": // HP
        return "bg-green-900/60 text-green-300";
      case "Wings": // Defense
        return "bg-red-900/60 text-red-300";
      case "Eyes": // Ferocity
        return "bg-cyan-900/60 text-cyan-300";
      case "Beak": // Attack
        return "bg-orange-900/60 text-orange-300";
      case "Comb": // Cockrage
        return "bg-pink-900/60 text-pink-300";
      case "Color": // No direct stat association
        return "bg-indigo-900/60 text-indigo-300";
      default:
        return "bg-[#2D2D2D] text-gray-300";
    }
  };

  // Check if a trait is legendary (for visual highlighting)
  const isLegendary = (value: string) => {
    // Legendary traits are typically rare traits
    // This is a simplified check - adjust based on your actual legendary criteria
    const legendaryKeywords = ["legendary", "rare", "mythic", "epic"];
    return legendaryKeywords.some((keyword) =>
      value.toLowerCase().includes(keyword)
    );
  };

  return (
    <Tooltip delay={0}>
      <Tooltip.Trigger
        className={cn(
          "p-2 rounded-md text-xs truncate w-full block transition-colors",
          getTraitColor(),
          isLegendary(value) &&
            "bg-yellow-800/70 text-yellow-300 font-medium border border-yellow-500/50 shadow-sm shadow-yellow-500/20"
        )}
      >
        {value || "-"}
      </Tooltip.Trigger>
      <Tooltip.Content>
        <div className="text-xs">
          <strong>{type}</strong>: {value || "None"}
        </div>
      </Tooltip.Content>
    </Tooltip>
  );
};
