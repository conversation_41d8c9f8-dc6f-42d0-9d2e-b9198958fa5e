import { create } from "zustand";
import { Client, Room } from "colyseus.js";
import useAuthStore from "./auth";
import { parseCookies } from "nookies";
import { BattleItemSlot } from "@/types/battle-items.types";
import { BATTLE_ITEMS, getBattleItemById } from "@/data/battle-items";

interface MatchmakingStateSchema {
  waitingCount: number;
  currentPhase: string;
  statusMessage: string;
  players: {
    [key: string]: {
      sessionId: string;
      fighterId: number;
      joinedAt: number;
    };
  };
}

interface MatchmakingState {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;

  // Queue state
  isInQueue: boolean;
  queuePosition: number;
  waitingCount: number;
  currentPhase: string;
  statusMessage: string;

  // Match state
  matchFound: boolean;
  matchCode: string | null;
  gameUrl: string | null;

  // Selected fighter
  selectedFighterId: number | null;

  // Battle verification data
  battleSignature: string | null;
  battleNonce: number | null;

  // Battle items selection
  selectedBattleItems: BattleItemSlot[];

  // Colyseus client and room
  client: Client | null;
  room: Room | null;

  address: string | null;

  // Actions
  setSelectedFighter: (fighterId: number) => void;
  setBattleVerification: (signature: string, nonce: number) => void;
  connectToServer: (serverUrl: string) => Promise<void>;
  joinMatchmaking: () => Promise<void>;
  leaveMatchmaking: () => void;
  resetMatchState: () => void;
  disconnect: () => void;
  setAddress: (address: string) => void;

  // Battle items actions
  setBattleItem: (slot: number, itemId: BattleItemSlot) => void;
  clearBattleItem: (slot: number) => void;
  resetBattleItems: () => void;
  isItemSelected: (itemId: number) => boolean;
  getNextAvailableSlot: () => number | null;
  getIncompleteSlots: () => number[];
}

const useMatchmakingStore = create<MatchmakingState>((set, get) => ({
  // Initial state
  isConnected: false,
  isConnecting: false,
  connectionError: null,
  isInQueue: false,
  queuePosition: 0,
  waitingCount: 0,
  currentPhase: "ready",
  statusMessage: "",
  matchFound: false,
  matchCode: null,
  gameUrl: null,
  selectedFighterId: null,
  battleSignature: null,
  battleNonce: null,
  selectedBattleItems: [-1, -1, -1], // Three slots, all initially empty (-1)
  client: null,
  room: null,
  address: null,

  // Set selected fighter
  setSelectedFighter: (fighterId: number) => {
    set({ selectedFighterId: fighterId });
  },

  // Set battle verification data
  setBattleVerification: (signature: string, nonce: number) => {
    set({ battleSignature: signature, battleNonce: nonce });
  },

  setAddress: (address: string) => {
    set({ address: address });
  },

  // Connect to Colyseus server
  connectToServer: async (serverUrl: string) => {
    set({ isConnecting: true, connectionError: null });

    try {
      const client = new Client(serverUrl);
      set({ client, isConnected: true, isConnecting: false });
    } catch (error) {
      console.error("Failed to connect to server:", error);
      set({
        connectionError:
          error instanceof Error
            ? error.message
            : "Unknown error connecting to server",
        isConnecting: false,
      });
    }
  },

  // Join matchmaking queue
  joinMatchmaking: async () => {
    const { client, selectedFighterId, address, selectedBattleItems } = get();

    if (!client) {
      set({ connectionError: "Not connected to server" });
      return;
    }

    if (!selectedFighterId) {
      set({ connectionError: "No fighter selected" });
      return;
    }

    if (!address) {
      set({
        connectionError: "No address connected. Please connect your wallet",
      });
      return;
    }

    // if (!battleSignature || !battleNonce) {
    //   set({
    //     connectionError:
    //       "Battle verification required. Please verify chicken ownership first.",
    //   });
    //   return;
    // }

    try {
      set({ isInQueue: true });
      const { rns } = useAuthStore.getState();

      // Get JWT token from cookies using nookies
      const cookies = parseCookies();
      const jwt = cookies.jwt || null;

      // JWT is required for authentication
      if (!jwt) {
        set({
          connectionError: "Authentication required. Please sign in again.",
          isInQueue: false,
        });
        return;
      }

      // Prepare options object with required JWT and battle items
      const options: any = {
        fighterId: selectedFighterId,
        jwt: jwt,
        rns,
        battleItems: selectedBattleItems,
      };

      // Join matchmaking room with JWT token
      const room = await client.joinOrCreate<MatchmakingStateSchema>(
        "matchmaking",
        options
      );

      set({ room });

      // Set up event listeners
      room.onStateChange((state: any) => {
        if (!state) {
          set({ waitingCount: 0, queuePosition: 0 });
          return;
        }

        // Get the waitingCount or default to 0
        const waitingCount =
          typeof state.waitingCount === "number" ? state.waitingCount : 0;

        // Get current phase and status message
        const currentPhase = state.currentPhase || "queuing";
        const statusMessage = state.statusMessage || "";

        // Calculate queue position
        let queuePosition = 0;

        // Define player interface to match your schema
        interface QueuePlayerData {
          sessionId: string;
          fighterId: number;
          joinedAt: number;
        }

        // Handle players data safely
        if (state.players) {
          let players: QueuePlayerData[] = [];

          // Check if players is an array, Map, or object
          if (Array.isArray(state.players)) {
            players = state.players as QueuePlayerData[];
          } else if (
            state.players instanceof Map ||
            typeof state.players.values === "function"
          ) {
            // For MapSchema or Map
            players = Array.from(state.players.values()) as QueuePlayerData[];
          } else if (typeof state.players === "object") {
            // For plain object representation
            players = Object.values(state.players) as QueuePlayerData[];
          }

          // Now safely sort and find position with proper type annotations
          if (players.length > 0) {
            queuePosition =
              players
                .sort(
                  (a: QueuePlayerData, b: QueuePlayerData) =>
                    (a.joinedAt || 0) - (b.joinedAt || 0)
                )
                .findIndex(
                  (p: QueuePlayerData) => p && p.sessionId === room.sessionId
                ) + 1;

            // If not found, default to last position
            if (queuePosition <= 0) {
              queuePosition = players.length;
            }
          }
        }

        set({ waitingCount, queuePosition, currentPhase, statusMessage });
      });

      // Listen for queue joined confirmation
      room.onMessage("queue_joined", (message) => {
        set({
          queuePosition: message.position,
          waitingCount: message.waitingCount,
          currentPhase: "queuing",
          statusMessage: `You are #${message.position} in queue with chicken #${message.chickenId}`,
        });
      });

      // Listen for match found
      room.onMessage("match_found", (message) => {
        set({
          matchFound: true,
          matchCode: message.code,
          gameUrl: message.gameUrl,
          currentPhase: "matched",
          statusMessage: "Match found! Preparing battle...",
        });
      });

      // Handle disconnection
      room.onLeave((code) => {
        if (code !== 1000) {
          // Abnormal close - provide specific error messages for new error codes
          let errorMessage = `Disconnected from matchmaking: Code ${code}`;

          switch (code) {
            case 4003:
              errorMessage =
                "Authentication failed. Please refresh and try again.";
              break;
            case 4004:
              errorMessage =
                "Address verification failed. Please reconnect your wallet.";
              break;
            case 4005:
              errorMessage =
                "You don't own this chicken. Please select a different chicken.";
              break;
            case 4002:
              errorMessage =
                "Chicken was recently transferred and is too stubborn to battle. Try again tomorrow.";
              break;
            default:
              errorMessage = `Connection error: ${code}`;
          }

          set({
            connectionError: errorMessage,
            isInQueue: false,
            room: null,
          });
        } else {
          set({
            isInQueue: false,
            room: null,
          });
        }
      });
    } catch (error) {
      console.error("Failed to join matchmaking:", error);
      set({
        connectionError:
          error instanceof Error ? error.message : "Failed to join matchmaking",
        isInQueue: false,
      });
    }
  },

  // Leave matchmaking queue
  leaveMatchmaking: () => {
    const { room } = get();

    if (room) {
      room.leave();
    }

    set({
      isInQueue: false,
      room: null,
      queuePosition: 0,
      waitingCount: 0,
    });
  },

  // Reset match state (after redirect)
  resetMatchState: () => {
    set({
      matchFound: false,
      matchCode: null,
      gameUrl: null,
      connectionError: null,
      currentPhase: "ready",
      statusMessage: "",
      battleSignature: null,
      battleNonce: null,
    });
  },

  // Disconnect from server
  disconnect: () => {
    const { room, client } = get();

    if (room) {
      room.leave();
    }

    if (client) {
      // Close client connection
      // Note: Colyseus.js doesn't have an explicit disconnect method
      // but we can set it to null to allow garbage collection
    }

    set({
      isConnected: false,
      isInQueue: false,
      room: null,
      client: null,
      queuePosition: 0,
      waitingCount: 0,
    });
  },

  // Battle items actions
  setBattleItem: (slot: number, itemId: BattleItemSlot) => {
    const { selectedBattleItems } = get();
    if (slot < 0 || slot > 2) return; // Invalid slot

    const newItems = [...selectedBattleItems];
    newItems[slot] = itemId;
    set({ selectedBattleItems: newItems });
  },

  clearBattleItem: (slot: number) => {
    const { selectedBattleItems } = get();
    if (slot < 0 || slot > 2) return; // Invalid slot

    const newItems = [...selectedBattleItems];
    newItems[slot] = -1;
    set({ selectedBattleItems: newItems });
  },

  resetBattleItems: () => {
    set({ selectedBattleItems: [-1, -1, -1] });
  },

  isItemSelected: (itemId: number) => {
    const { selectedBattleItems } = get();
    return selectedBattleItems.includes(itemId);
  },

  getNextAvailableSlot: () => {
    const { selectedBattleItems } = get();
    const emptySlot = selectedBattleItems.findIndex((item) => item === -1);
    return emptySlot !== -1 ? emptySlot : null;
  },

  getIncompleteSlots: () => {
    const { selectedBattleItems } = get();
    return selectedBattleItems
      .map((item, index) => (item === -1 ? index : -1))
      .filter((index) => index !== -1);
  },
}));

export default useMatchmakingStore;
