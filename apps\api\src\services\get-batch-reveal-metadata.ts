import axios from "axios";
import { env } from "../env";
import { BatchMetadataResponse } from "../types";

export const getBatchRevealMetadata = async ({
  tokenIds,
}: {
  tokenIds: number[] | string[];
}): Promise<BatchMetadataResponse> => {
  try {
    const { data } = await axios.post(
      `${env.REVEAL_METADATA_API_ENDPOINT}/batch`,
      { ids: tokenIds },
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return data as BatchMetadataResponse;
  } catch (error) {
    console.log(error);

    throw error;
  }
};
