import { SVGProps } from "react";

interface FeathersProps extends SVGProps<SVGSVGElement> {
  size?: number;
}

export const FriedLeg = ({ size = 196, ...props }: FeathersProps) => {
  return (
    <svg
      width={size}
      height={size}
      xmlns="http://www.w3.org/2000/svg"
      fill="currentcolor"
      viewBox="0 0 256 256"
    >
      <path d="M61.6 10.3c-12.2 1.1-15.9 2.4-23.7 8.2-12.1 9.1-19.4 19-25.2 34.1-2.5 6.4-3 9.9-2.7 18.6.4 10.3 1.4 17.4 3.4 22.8.9 2.5 2.8 7.9 4.2 12 3.2 9.2 5.4 13.8 8.8 18.3 3.8 5 10.8 12.9 13.7 15.3 6.5 5.5 17.8 11.1 31.9 16 5.4 1.8 12.7 4.5 16.4 6 7.8 3.1 15.2 5.3 25.6 7.6 10.4 2.3 13.4 3.4 34.7 12.7 5.9 2.5 11.2 4.7 11.9 4.7.9 0 4.8 3.1 15.4 12.3 7.8 6.7 14.7 12.8 15.3 13.5.6.7 1.5 2.1 1.9 3.1.8 1.7.8 2.4.4 6.7-.6 6.5-.3 9.4 1.4 12.7 3.9 7.7 10.9 11.7 19.2 11.1 4.6-.4 7.7-1.3 10-3s3.3-3 5.1-6.6c1.9-3.9 2.2-4.2 6.8-6.7 4.9-2.8 6.7-4.5 8.6-8.4 1.2-2.7 1.4-3.5 1.4-7 0-3.2-.2-4.4-1.2-6.5-3.7-7.9-10.4-12.2-20.9-13.4-6.4-.7-4.8.6-23.3-20.1-8-9-10.7-12.3-10.7-13.2 0-.6-1.7-4.1-3.7-7.6-2-3.6-4.4-8-5.3-9.9s-3.2-6.5-5.2-10.2c-2-3.7-6.7-13.3-10.4-21.3s-9.6-20.3-12.9-27.2c-3.4-7-7.9-16.6-10.1-21.3-4.7-10.3-6.6-13.7-11-19.4-1.9-2.4-5-6.5-6.9-9-2.8-3.6-4.9-5.8-9.6-9.7-10.1-8.4-12.4-9.6-26-13.4C81.7 10 81.2 10 74 9.8c-4.2.1-9.7.3-12.4.5zm137.5 173.3c6.4 7.2 12.2 13.5 13.1 14.2 2.3 1.9 4.6 3 8 3.5 1.7.3 4.4.7 6.1 1 5.3.8 9.3 3.6 11.4 7.9 1.3 2.6 1.5 4.4.6 6.8-.9 2.8-2.2 4.1-6.2 6.3-5 2.7-7.1 4.8-9.2 9.1-1.9 4.1-3 5.1-6.6 5.8-6.7 1.4-12.4-1.4-15-7.3-.8-1.8-.9-2.4-.4-7.8.6-6.4.3-8.5-1.2-11.5-1.6-3-3.6-5-16.3-16.1l-11.9-10.3 2.4-.7c3.3-1 4.4-1.8 5.5-4.2 1.1-2.4 2-2.9 5-3.4l2.1-.3.1-3.4c.1-1.9.3-3.2.5-3 .2.2 5.6 6.2 12 13.4z" />
    </svg>
  );
};

export default FriedLeg;
