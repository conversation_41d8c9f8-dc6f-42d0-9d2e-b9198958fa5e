import { IconProps } from "@/types/icon.type";

export const AdventureChickenIcon = ({ size = 196, ...props }: IconProps) => (
  <svg
    width={size}
    height={size}
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    viewBox="0 0 256 256"
    {...props}
  >
    {/* Ground/horizon line - Much thicker */}
    <path
      d="M16 208h224"
      stroke="currentColor"
      strokeWidth="16"
      opacity="0.7"
    />

    {/* Tree 1 - Large pine tree with thicker trunk */}
    <path
      d="M80 208v-48"
      stroke="currentColor"
      strokeWidth="32"
      strokeLinecap="round"
    />
    <path d="M80 160l-48-64h96l-48 64z" fill="currentColor" opacity="0.8" />
    <path d="M80 128l-40-48h80l-40 48z" fill="currentColor" opacity="0.9" />
    <path d="M80 96l-32-40h64l-32 40z" fill="currentColor" />

    {/* Tree 2 - Medium deciduous tree with thicker trunk */}
    <path
      d="M160 208v-64"
      stroke="currentColor"
      strokeWidth="32"
      strokeLinecap="round"
    />
    <circle cx="160" cy="96" r="56" fill="currentColor" opacity="0.7" />
    <circle cx="128" cy="80" r="32" fill="currentColor" opacity="0.8" />
    <circle cx="192" cy="88" r="36" fill="currentColor" opacity="0.8" />

    {/* Tree 3 - Small pine tree with thicker trunk */}
    <path
      d="M208 208v-32"
      stroke="currentColor"
      strokeWidth="24"
      strokeLinecap="round"
    />
    <path d="M208 176l-32-48h64l-32 48z" fill="currentColor" opacity="0.8" />
    <path d="M208 144l-24-32h48l-24 32z" fill="currentColor" opacity="0.9" />

    {/* Tree 4 - Small bush/shrub with thicker trunk */}
    <path
      d="M40 208v-16"
      stroke="currentColor"
      strokeWidth="16"
      strokeLinecap="round"
    />
    <circle cx="40" cy="176" r="24" fill="currentColor" opacity="0.6" />

    {/* Mountains in background - Bolder shapes */}
    <path
      d="M0 208l64-128 48 64 32-48 64 32 48-64 32 32 80-96v208"
      fill="currentColor"
      opacity="0.4"
    />

    {/* Sun/moon in sky - Larger */}
    <circle cx="200" cy="48" r="32" fill="currentColor" opacity="0.5" />

    {/* Clouds - Much larger and bolder */}
    <path
      d="M48 64c0-16 12-32 32-32s32 16 32 32c8-8 20-12 32-12s24 4 32 12c0 16-12 32-32 32H80c-20 0-32-16-32-32z"
      fill="currentColor"
      opacity="0.4"
    />

    {/* Small path/trail - Thicker */}
    <path
      d="M16 208c32-8 64-12 96-8 32 4 64 0 96-8 32-8 64-4 96 0"
      stroke="currentColor"
      strokeWidth="8"
      opacity="0.5"
      strokeDasharray="32,16"
    />

    {/* Adventure elements - larger details */}
    <circle cx="112" cy="192" r="4" fill="currentColor" opacity="0.6" />
    <circle cx="136" cy="188" r="3" fill="currentColor" opacity="0.6" />
    <circle cx="88" cy="196" r="3" fill="currentColor" opacity="0.6" />
  </svg>
);
