/**
 * Utility functions for blockchain explorer URLs
 */

// Get the current chain ID from environment
const getChainId = (): number => {
  return Number(process.env.NEXT_PUBLIC_CHAINDID) || 2021;
};

/**
 * Get the explorer base URL for the current network
 */
export const getExplorerBaseUrl = (): string => {
  const chainId = getChainId();

  // Chain ID 2020 = Ronin Mainnet, Chain ID 2021 = Saigon Testnet
  if (chainId === 2020) {
    return "https://app.roninchain.com";
  } else {
    return "https://saigon-app.roninchain.com";
  }
};

/**
 * Get explorer URL for a transaction hash
 */
export const getTransactionExplorerUrl = (txHash: string): string => {
  const baseUrl = getExplorerBaseUrl();
  return `${baseUrl}/tx/${txHash}`;
};

/**
 * Get explorer URL for an address
 */
export const getAddressExplorerUrl = (address: string): string => {
  const baseUrl = getExplorerBaseUrl();
  return `${baseUrl}/address/${address}`;
};

/**
 * Get marketplace URL for a token (always uses mainnet marketplace)
 */
export const getMarketplaceUrl = (tokenId: number): string => {
  if (tokenId <= 2222) {
    return `https://marketplace.roninchain.com/collections/sabong-saga-genesis/${tokenId}`;
  } else {
    return `https://marketplace.roninchain.com/collections/sabong-saga-chickens/${tokenId}`;
  }
};

/**
 * Get the current network name
 */
export const getNetworkName = (): string => {
  const chainId = getChainId();
  return chainId === 2020 ? "Ronin Mainnet" : "Saigon Testnet";
};
