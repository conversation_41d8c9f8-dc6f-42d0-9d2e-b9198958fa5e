// utils/insertMysticAllocations.mjs
import { parse } from "csv-parse";
import * as fs from "fs/promises";
import path from "path";
import { fileURLToPath } from "url";
import { MongoClient } from "mongodb";
import dotenv from "dotenv";
import { dirname } from "path";

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function connectToMongo() {
  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  return client;
}

async function insertMysticAllocations() {
  let client;
  try {
    // Read the CSV file
    const csvPath = path.join(process.cwd(), "data", "mystic_allocations.csv");
    const fileContent = await fs.readFile(csvPath, "utf-8");

    // Parse CSV
    const records = await new Promise((resolve, reject) => {
      parse(
        fileContent,
        {
          columns: true,
          skip_empty_lines: true,
          trim: true,
        },
        (err, records) => {
          if (err) reject(err);
          else resolve(records);
        }
      );
    });

    console.log(`Found ${records.length} records to process\n`);
    console.log("=".repeat(80));

    // Connect to MongoDB
    client = await connectToMongo();
    const db = client.db(process.env.MONGODB_DB);
    const collection = db.collection("allocations");

    // Summary counters
    let totalMystics = 0;
    let totalAllocations = 0;
    let updated = 0;
    let created = 0;

    // Process each record
    for (const record of records) {
      const address = record.address.toLowerCase();
      const count = parseInt(record.count);
      const mysticAllocation = count * 2;

      totalMystics += count;
      totalAllocations += mysticAllocation;

      console.log("\nProcessing:", address);
      console.log("Mystic Count:", count);
      console.log("Allocation:", mysticAllocation);

      // Check if address exists
      const existing = await collection.findOne({ address });

      if (existing) {
        console.log("Updating existing record");

        const updatedBreakdown = [...existing.breakdown];
        const mysticEntry = {
          type: "mystic",
          quantity: count,
          totalAllocation: mysticAllocation,
        };

        const mysticIndex = updatedBreakdown.findIndex(
          (b) => b.type === "mystic"
        );

        if (mysticIndex !== -1) {
          updatedBreakdown[mysticIndex] = mysticEntry;
        } else {
          updatedBreakdown.push(mysticEntry);
        }

        const newTotalAllocations = updatedBreakdown.reduce(
          (sum, item) => sum + (item.totalAllocation || 0),
          0
        );

        await collection.updateOne(
          { address },
          {
            $set: {
              totalAllocations: newTotalAllocations,
              breakdown: updatedBreakdown,
              lastUpdated: new Date(),
            },
          }
        );
        updated++;
      } else {
        console.log("Creating new record");

        await collection.insertOne({
          address,
          totalChickens: 0,
          totalAllocations: mysticAllocation,
          breakdown: [
            {
              type: "mystic",
              quantity: count,
              totalAllocation: mysticAllocation,
            },
          ],
          timestamp: new Date(),
          lastUpdated: new Date(),
        });
        created++;
      }
      console.log("✓ Processed successfully");
      console.log("-".repeat(80));
    }

    // Print summary
    console.log("\nSUMMARY:");
    console.log("=".repeat(80));
    console.log("Total Records Processed:", records.length);
    console.log("Records Updated:", updated);
    console.log("Records Created:", created);
    console.log("Total Mystics:", totalMystics);
    console.log("Total Allocations:", totalAllocations);
  } catch (error) {
    console.error("Error during insertion:", error);
    throw error;
  } finally {
    if (client) {
      await client.close();
      console.log("\nDatabase connection closed");
    }
  }
}

// Run the insertion
console.time("Total Insertion Time");
insertMysticAllocations()
  .then(() => {
    console.timeEnd("Total Insertion Time");
    console.log("Process completed successfully");
  })
  .catch((error) => {
    console.error("Error:", error);
    process.exit(1);
  });
