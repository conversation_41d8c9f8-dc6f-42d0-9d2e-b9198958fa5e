import { ChickenStats } from "@/types/chicken.type";

interface ChickenHpBarProps {
  tokenId: string;
  stats?: ChickenStats;
  isLoading: boolean;
}

// Helper function to get HP bar color based on percentage
const getHpColor = (hp: number, maxHp: number) => {
  const percentage = (hp / maxHp) * 100;
  if (percentage > 70) return "bg-green-500";
  if (percentage > 30) return "bg-yellow-500";
  return "bg-red-500";
};

export function ChickenHpBar({ tokenId, stats, isLoading }: ChickenHpBarProps) {
  if (!stats && isLoading) {
    // Loading state
    return (
      <div className="w-full bg-stone-600 rounded-full h-4 mt-1 animate-pulse relative overflow-hidden">
        <div className="absolute inset-0 bg-stone-500"></div>
        <div className="absolute inset-0 flex justify-center items-center text-xs font-bold text-white">
          Loading...
        </div>
      </div>
    );
  }

  const hp = stats?.hp || 0;
  const maxHp = stats?.maxHp || 100;

  return (
    <div className="w-full bg-stone-600 rounded-full h-4 mt-1 relative overflow-hidden">
      <div
        className={`${getHpColor(hp, maxHp)} h-4 rounded-full transition-all duration-300`}
        style={{ width: `${(hp / maxHp) * 100}%` }}
      ></div>
      <div className="absolute inset-0 flex justify-center items-center text-xs font-bold text-white">
        HP: {Math.round(hp)}/{Math.round(maxHp)}
      </div>
    </div>
  );
}