"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  IBalanceResponse,
  IClaimRequest,
  IClaimResponse,
  getTokenDisplayName,
} from "../types/inventory.types";
import { rewardMetadataMap } from "../mock/mock-data";

const CHICKEN_API_BASE_URL = "https://chicken-api-ivory.vercel.app/api/game";

/**
 * Fetch inventory balance from chicken-api-ivory API
 */
export const fetchInventoryBalance = async (
  address: string,
  includeHistory = false
): Promise<IBalanceResponse> => {
  const params = new URLSearchParams({ address });
  if (includeHistory) {
    params.append("includeHistory", "true");
    params.append("limit", "20");
  }

  const response = await fetch(
    `${CHICKEN_API_BASE_URL}/balance?${params.toString()}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch balance: ${response.statusText}`);
  }

  return response.json();
};

/**
 * Claim tokens from balance using secure server-side API
 */
export const claimFromBalance = async (
  request: IClaimRequest
): Promise<IClaimResponse> => {
  // Fetch CSRF token first
  const csrfResponse = await fetch("/csrf-token");
  if (!csrfResponse.ok) {
    throw new Error(`Failed to fetch CSRF token: ${csrfResponse.statusText}`);
  }
  const { csrfToken } = await csrfResponse.json();

  const response = await fetch("/api/rewards/claim", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-CSRF-Token": csrfToken,
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.error ||
        errorData.message ||
        `Claim failed: ${response.statusText}`
    );
  }

  return response.json();
};

/**
 * Hook to fetch inventory balance and manage claiming
 */
export function useInventoryBalance(
  address: string | null,
  includeHistory = false
) {
  const queryClient = useQueryClient();

  // Balance query
  const balanceQuery = useQuery({
    queryKey: ["inventoryBalance", address, includeHistory],
    queryFn: () => fetchInventoryBalance(address!, includeHistory),
    enabled: !!address,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
  });

  // Claim mutation
  const claimMutation = useMutation({
    mutationFn: claimFromBalance,
    onSuccess: (data, variables) => {
      // Invalidate balance to refresh claimable amounts
      queryClient.invalidateQueries({
        queryKey: ["inventoryBalance", variables.address],
      });

      console.log("✅ Claim successful:", data);
    },
    onError: (error: any) => {
      console.error("❌ Claim failed:", error);
    },
  });

  // Transform balance data to individual token format
  const tokenBalances = balanceQuery.data ? transformBalanceToTokens(balanceQuery.data) : [];
  const totalClaimableBalance = tokenBalances.reduce((sum, token) => sum + token.balance, 0);

  return {
    // Balance data
    balanceData: balanceQuery.data,
    tokenBalances,
    totalClaimableBalance,

    // Balance query state
    isLoading: balanceQuery.isLoading,
    isError: balanceQuery.isError,
    error: balanceQuery.error,
    refetch: balanceQuery.refetch,

    // Claim functionality
    claimMutation,
    isClaiming: claimMutation.isPending,
    claimError: claimMutation.error,
  };
}

/**
 * Transform API balance response to individual token format with metadata
 */
function transformBalanceToTokens(balanceData: IBalanceResponse) {
  if (balanceData.success && balanceData.data?.balances) {
    return balanceData.data.balances
      .filter(balance => balance.balance > 0)
      .map(balance => {
        const metadata = rewardMetadataMap.get(balance.tokenId);
        return {
          ...balance,
          displayName: getTokenDisplayName(balance.tokenId),
          name: metadata?.name || `Token ${balance.tokenId}`,
          image: metadata?.image || "/images/rewards/default.png",
          description: metadata?.description || "",
        };
      });
  }

  return [];
}
