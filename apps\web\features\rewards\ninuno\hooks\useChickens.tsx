"use client";

import useChickenMetadataBatch from "@/features/breeding/tab/breeding/hooks/useChickenMetadataBatch";
import { fetchChickens } from "@/features/breeding/tab/breeding/hooks/useChickens";
import { IEggInfo } from "@/features/breeding/tab/hatching/types/egg-info.types";
import axios from "@/lib/api";
import { isStaging } from "@/lib/constants";
import { IAttribute, IChickenMetadata } from "@/lib/types/chicken.types";
import { useQuery } from "@tanstack/react-query";
import { Address } from "viem";
import { mockChickenQueryData } from "../mock/chicken-query-data";

const fetchInfo = async (tokenIds: number[]) => {
  const { data } = await axios.post("/ninuno-rewards/view-chicken-info", {
    chickenTokenIds: tokenIds,
  });
  return data.data as IEggInfo[];
};

export function useChickens(address: Address) {
  let chickenQuery = useQuery({
    queryKey: ["chickens", address],
    queryFn: () => fetchChickens(address),
    enabled: !!address,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  if (
    isStaging &&
    address === "0xED3f049B1a396495D3b0Bea9A13622CE6E8C2FD7".toLowerCase()
  ) {
    chickenQuery = mockChickenQueryData;
  }

  const allChickenTokenIds = chickenQuery.isSuccess
    ? chickenQuery.data.map((chicken) => Number(chicken.tokenId))
    : [];

  // Fetch metadata for chickens
  const metadataQuery = useChickenMetadataBatch(allChickenTokenIds);
  // Create a map for efficient metadata lookup
  const metadataMap: Map<number, IChickenMetadata> = metadataQuery.metadataQuery
    .isSuccess
    ? new Map(
        metadataQuery.metadataQuery.data?.map(
          (meta: IChickenMetadata, index: number) => [
            allChickenTokenIds[index],
            meta,
          ]
        )
      )
    : new Map();
  // filter the legacy and genesis chickens
  const chickensLegacyGenesis = metadataQuery.metadataQuery.isSuccess
    ? allChickenTokenIds.filter((chicken) => {
        const chickenMetadata = metadataMap.get(chicken);

        const chickenType = chickenMetadata?.attributes.find(
          (attr: IAttribute) => attr.trait_type === "Type"
        )?.value;
        return chickenType === "Legacy" || chickenType === "Genesis";
      })
    : [];

  const ninunoRewardsQuery = useQuery({
    queryKey: ["ninunoRewards", chickensLegacyGenesis],
    queryFn: () => fetchInfo(chickensLegacyGenesis),
    enabled: metadataQuery.metadataQuery.isSuccess,
  });

  const ninunoRewardsMap: Map<number, IEggInfo> = ninunoRewardsQuery.isSuccess
    ? new Map(
        ninunoRewardsQuery.data?.map((chicken) => [chicken.token_id, chicken])
      )
    : new Map();

  return {
    chickens: chickensLegacyGenesis,
    ninunoRewards: ninunoRewardsQuery.data,
    metadataMap,
    ninunoRewardsMap,
    isLoading:
      chickenQuery.isFetching ||
      chickenQuery.isLoading ||
      metadataQuery.isLoading ||
      ninunoRewardsQuery.isLoading ||
      ninunoRewardsQuery.isFetching,
    error:
      chickenQuery.error || metadataQuery.error || ninunoRewardsQuery.error,
  };
}
