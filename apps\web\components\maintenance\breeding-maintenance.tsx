"use client";

import React from "react";

export function BreedingMaintenance() {
  return (
    <div className="min-h-[calc(100vh-74px)] p-8 grid place-items-center">
      <div className="relative max-w-[1680px] w-full mx-auto my-6 px-4 md:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
          <div className="bg-stone-800/50 backdrop-blur-sm border border-primary/20 rounded-lg p-8 max-w-md mx-auto">
            <div className="mb-6">
              <div className="w-16 h-16 mx-auto mb-4 bg-yellow-500/20 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-yellow-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-white mb-2">
                Breeding Under Maintenance
              </h2>
              <p className="text-gray-400 text-sm">
                The breeding system is currently undergoing maintenance to
                improve your experience.
              </p>
            </div>

            <div className="space-y-3 text-sm text-gray-300">
              <p>🔧 We&apos;re working hard to enhance the breeding features</p>
              <p>⏰ This maintenance is temporary and will be completed soon</p>
              <p>
                🐓 Your chickens are safe and will be ready for breeding once
                maintenance is complete
              </p>
            </div>

            <div className="mt-6 pt-4 border-t border-stone-700">
              <p className="text-xs text-gray-500">
                Thank you for your patience. Please check back later.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
