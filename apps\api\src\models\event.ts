import mongoose from "mongoose";

const { Schema, model } = mongoose;

// Define the TokenTransfer schema
const EventDataSchema = new Schema(
  {
    blockNumber: { type: Number, required: true },
    tokenId: { type: String, required: true },
    to: { type: String, required: true },
    from: { type: String, required: true },
    transactionHash: { type: String, required: true },
    approved: { type: Boolean, default: true },
    eventType: {
      type: String,
      enum: ["Transfer", "Approval", "ApprovalForAll"],
      default: "Transfer",
    },
  },
  { _id: false }
);

// Define the main schema
const EventSchema = new Schema(
  {
    epoch: { type: Number, required: true },
    data: { type: EventDataSchema, required: true },
  },
  { timestamps: true }
);

export default model("event", EventSchema);
