"use client";

import { useContractRead, useMulticall } from "@/lib/hooks/useContractRead";
import { chickenGenesisAbi } from "@/providers/web3/abi/chicken-genesis-abi";
import { cockAbi } from "@/providers/web3/abi/cock-abi";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";

interface IContractReaderExampleProps {
  className?: string;
}

export default function ContractReaderExample({
  className,
}: IContractReaderExampleProps) {
  const { address } = useStateContext();
  const { blockchainQuery } = useBlockchain();

  // Example of a single contract read
  const balanceQuery = useContractRead<bigint>({
    address: blockchainQuery.data?.chicken_genesis_address as Address,
    abi: chickenGenesisAbi,
    functionName: "balanceOf",
    args: address ? [address] : [],
    enabled: !!address && blockchainQuery.isSuccess,
  });

  // Define the MulticallResult type
  interface MulticallResult {
    status: "success" | "failure";
    result: unknown;
    error?: string;
  }

  // Example of a multicall
  const multicallQuery = useMulticall<MulticallResult[]>({
    calls: [
      {
        address: blockchainQuery.data?.cock_address as Address,
        abi: cockAbi,
        functionName: "name",
        args: [],
      },
      {
        address: blockchainQuery.data?.cock_address as Address,
        abi: cockAbi,
        functionName: "symbol",
        args: [],
      },
    ],
    enabled: blockchainQuery.isSuccess,
  });

  return (
    <div className={className}>
      <h2 className="text-xl font-bold mb-4">Contract Reader Example</h2>

      <div className="space-y-4">
        <div>
          <h3 className="font-semibold">Single Contract Read</h3>
          {balanceQuery.isPending ? (
            <p>Loading balance...</p>
          ) : balanceQuery.isError ? (
            <p className="text-red-500">Error: {balanceQuery.error?.message}</p>
          ) : (
            <p>Your chicken balance: {balanceQuery.data?.toString() || "0"}</p>
          )}
        </div>

        <div>
          <h3 className="font-semibold">Multicall Example</h3>
          {multicallQuery.isPending ? (
            <p>Loading token info...</p>
          ) : multicallQuery.isError ? (
            <p className="text-red-500">
              Error: {multicallQuery.error?.message}
            </p>
          ) : (
            <div>
              <p>
                Token Name:{" "}
                {multicallQuery.data && multicallQuery.data[0]
                  ? String(multicallQuery.data[0].result)
                  : ""}
              </p>
              <p>
                Token Symbol:{" "}
                {multicallQuery.data && multicallQuery.data[1]
                  ? String(multicallQuery.data[1].result)
                  : ""}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
