"use client";

import React from "react";
import { cn } from "@/utils/classes";
import { User, UserCheck, Gamepad2 } from "lucide-react";
import {
  EGameRewardDistributionType,
  GAME_REWARD_DISTRIBUTION_LABELS,
} from "../../types/delegation.types";

interface IGameRewardDistributionSelectorProps {
  value: EGameRewardDistributionType;
  onChange: (value: EGameRewardDistributionType) => void;
  disabled?: boolean;
  className?: string;
}

export function GameRewardDistributionSelector({
  value,
  onChange,
  disabled = false,
  className,
}: IGameRewardDistributionSelectorProps) {
  const allOptions = [
    {
      value: EGameRewardDistributionType.DELEGATOR_ONLY,
      label: "Owner",
      description: "You keep all game rewards (crystals, shards, corn) earned by the chicken",
      icon: User,
      isPopular: true,
      advantages: ["Maximum game reward earnings for you", "Simple arrangement"],
      considerations: ["May be less attractive to renters"],
      disabled: false,
    },
    {
      value: EGameRewardDistributionType.DELEGATEE_ONLY,
      label: "Renter",
      description: "Renter keeps all game rewards (crystals, shards, corn) earned by the chicken",
      icon: UserCheck,
      isPopular: false,
      advantages: ["Very attractive to renters", "Higher rental demand"],
      considerations: ["No game reward earnings for you"],
      disabled: false,
    },
  ];

  // Filter out disabled options
  const availableOptions = allOptions.filter((option) => !option.disabled);

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center gap-2 mb-4">
        <Gamepad2 className="w-5 h-5 text-blue-400" />
        <h3 className="text-lg font-semibold text-white">Game Reward Distribution</h3>
      </div>
      <p className="text-sm text-gray-400 mb-6">
        Choose who receives game rewards (crystals, shards, corn) earned from gameplay activities.
      </p>

      <div className="grid gap-4">
        {availableOptions.map((option) => {
          const isSelected = value === option.value;
          const IconComponent = option.icon;

          return (
            <div
              key={option.value}
              className={cn(
                "relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-200",
                "hover:border-blue-500/70 hover:shadow-lg hover:shadow-blue-500/10",
                isSelected
                  ? "border-blue-500 bg-blue-500/10 shadow-lg shadow-blue-500/20"
                  : "border-stone-600 bg-stone-800/50",
                disabled && "opacity-50 cursor-not-allowed"
              )}
              onClick={() => !disabled && onChange(option.value)}
            >
              {/* Popular Badge */}
              {option.isPopular && (
                <div className="absolute -top-2 -right-2">
                  <div className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                    Recommended
                  </div>
                </div>
              )}

              <div className="flex items-start gap-4">
                {/* Icon */}
                <div
                  className={cn(
                    "flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center",
                    isSelected
                      ? "bg-blue-500/20 text-blue-400"
                      : "bg-stone-700 text-gray-400"
                  )}
                >
                  <IconComponent className="w-6 h-6" />
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-semibold text-white">{option.label}</h4>
                    <span className="text-sm text-gray-400">
                      ({GAME_REWARD_DISTRIBUTION_LABELS[option.value]})
                    </span>
                  </div>

                  <p className="text-sm text-gray-300 mb-3">
                    {option.description}
                  </p>

                  {/* Advantages */}
                  <div className="space-y-2">
                    <div>
                      <h5 className="text-xs font-medium text-green-400 mb-1">
                        Advantages:
                      </h5>
                      <ul className="text-xs text-gray-400 space-y-1">
                        {option.advantages.map((advantage, index) => (
                          <li key={index} className="flex items-center gap-1">
                            <span className="w-1 h-1 bg-green-400 rounded-full flex-shrink-0" />
                            {advantage}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Considerations */}
                    <div>
                      <h5 className="text-xs font-medium text-yellow-400 mb-1">
                        Considerations:
                      </h5>
                      <ul className="text-xs text-gray-400 space-y-1">
                        {option.considerations.map((consideration, index) => (
                          <li key={index} className="flex items-center gap-1">
                            <span className="w-1 h-1 bg-yellow-400 rounded-full flex-shrink-0" />
                            {consideration}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Selection Indicator */}
                <div className="flex-shrink-0">
                  <div
                    className={cn(
                      "w-6 h-6 rounded-full border-2 flex items-center justify-center",
                      isSelected
                        ? "border-blue-500 bg-blue-500"
                        : "border-stone-500"
                    )}
                  >
                    {isSelected && (
                      <div className="w-2 h-2 bg-white rounded-full" />
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Info Note */}
      <div className="mt-4 p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg">
        <p className="text-xs text-blue-300">
          <strong>Note:</strong> Game rewards include crystals, shards, and corn earned from gameplay activities. 
          This setting is separate from daily feather reward distribution.
        </p>
      </div>
    </div>
  );
}
