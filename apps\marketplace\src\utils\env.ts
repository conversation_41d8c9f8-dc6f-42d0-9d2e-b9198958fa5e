import "dotenv/config";
import { z } from "zod";

const envSchema = z.object({
  MONGODB_URI: z.string(),
  MARKETPLACE_GRAPHQL_ENDPOINT: z.string(),
  SKYMAVIS_API_KEY: z.string(),
  CHICKEN_CONTRACT:z.string(),
  LEGACY_CONTRACT: z.string(),
  RPC:z.string(),
  OPENSEA_API_KEY:z.string()
});

type Env = z.infer<typeof envSchema>;

export const env: Env = envSchema.parse(process.env);

for (const key in env) {
  if (!(key in env)) {
    throw new Error(
      `Missing env variable: ${key}. Please check the .env file and try again.`
    );
  }
}
