"use client";

import { useState, useEffect } from "react";
import { Button } from "ui";
import { Grid, List, Search } from "lucide-react";
import {
  IRentalWithMetadata,
  IRentalFilters,
  RENTAL_FILTER_DEFAULTS,
} from "../../types/delegation.types";
import { RentalCard } from "./rental-card";
import { RentalFilters } from "./rental-filters";
import { useRentals } from "../../hooks/useRentals";

interface IRentalGridProps {
  rentals: IRentalWithMetadata[];
  loading?: boolean;
  onRent?: (rental: IRentalWithMetadata) => void;
  currentUserAddress?: string;
  className?: string;
}

interface IRentalGridPropsWithFilters {
  onFiltersChange?: (filters: IRentalFilters, searchQuery: string) => void;
  currentUserAddress?: string;
  className?: string;
}

export function RentalGrid({
  onFiltersChange,
  currentUserAddress,
  className,
}: IRentalGridPropsWithFilters) {
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [filters, setFilters] = useState<IRentalFilters>({
    priceRange: {
      min: RENTAL_FILTER_DEFAULTS.PRICE_RANGE.MIN,
      max: RENTAL_FILTER_DEFAULTS.PRICE_RANGE.MAX,
    },
    durationRange: {
      min: RENTAL_FILTER_DEFAULTS.DURATION_RANGE.MIN,
      max: RENTAL_FILTER_DEFAULTS.DURATION_RANGE.MAX,
    },

    featherRewardDistribution: [],
    gameRewardDistribution: [],
    delegatedTask: [],
    sortBy: "created",
    sortOrder: "desc",
  });

  // Use the rentals hook with filters
  const {
    rentals,
    isLoading: loading,
    rentChicken,
    currentPage,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    goToNextPage,
    goToPreviousPage,
    goToPage,
    resetToFirstPage,
    refetch,
    meta,
  } = useRentals(filters, searchQuery);

  // Reset to first page when filters change
  useEffect(() => {
    resetToFirstPage();
  }, [filters, searchQuery]); // Remove resetToFirstPage from dependencies to prevent infinite loop

  // All filtering is now handled server-side via API
  // No client-side filtering to ensure pagination works correctly
  const sortedRentals = rentals;

  const resetFilters = () => {
    setFilters({
      priceRange: {
        min: RENTAL_FILTER_DEFAULTS.PRICE_RANGE.MIN,
        max: RENTAL_FILTER_DEFAULTS.PRICE_RANGE.MAX,
      },
      durationRange: {
        min: RENTAL_FILTER_DEFAULTS.DURATION_RANGE.MIN,
        max: RENTAL_FILTER_DEFAULTS.DURATION_RANGE.MAX,
      },

      featherRewardDistribution: [],
      gameRewardDistribution: [],
      delegatedTask: [],
      sortBy: "created",
      sortOrder: "desc",
    });
    setSearchQuery("");
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-20 bg-stone-800 rounded-lg mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="h-80 bg-stone-800 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search and View Controls */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        {/* Search */}
        <div className="relative w-full md:w-96">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
            <Search className="h-5 w-5" />
          </div>
          <input
            type="text"
            className="bg-stone-700 text-white pl-10 pr-4 py-2 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-yellow-500 border border-stone-600"
            placeholder="Search by token ID or owner address..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center gap-2">
          <Button
            size="small"
            appearance={viewMode === "grid" ? "solid" : "outline"}
            onPress={() => setViewMode("grid")}
          >
            <Grid className="w-4 h-4" />
          </Button>
          <Button
            size="small"
            appearance={viewMode === "list" ? "solid" : "outline"}
            onPress={() => setViewMode("list")}
          >
            <List className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Filters */}
      <RentalFilters
        filters={filters}
        onFiltersChange={setFilters}
        onReset={resetFilters}
      />

      {/* Results Count and Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-gray-400">
          Showing {sortedRentals.length} of {meta?.total || 0} rentals
        </p>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="flex items-center gap-2">
            <Button
              size="small"
              appearance="outline"
              onPress={goToPreviousPage}
              isDisabled={!hasPreviousPage}
            >
              Previous
            </Button>
            <span className="text-gray-400 text-sm">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              size="small"
              appearance="outline"
              onPress={goToNextPage}
              isDisabled={!hasNextPage}
            >
              Next
            </Button>
          </div>
        )}
      </div>

      {/* Rental Grid/List */}
      {sortedRentals.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            {searchQuery ||
            filters.priceRange.min > 0 ||
            filters.priceRange.max !== Number.MAX_SAFE_INTEGER ||
            filters.durationRange.min > 1 ||
            filters.durationRange.max !== Number.MAX_SAFE_INTEGER ||
            filters.featherRewardDistribution.length > 0 ||
            filters.gameRewardDistribution.length > 0 ||
            filters.delegatedTask.length > 0
              ? "No rentals match your search criteria"
              : "No rentals available"}
          </div>
          {(searchQuery ||
            filters.priceRange.min > 0 ||
            filters.priceRange.max !== Number.MAX_SAFE_INTEGER ||
            filters.durationRange.min > 1 ||
            filters.durationRange.max !== Number.MAX_SAFE_INTEGER ||
            filters.featherRewardDistribution.length > 0 ||
            filters.gameRewardDistribution.length > 0 ||
            filters.delegatedTask.length > 0) && (
            <Button onPress={resetFilters} appearance="outline">
              Clear Filters
            </Button>
          )}
        </div>
      ) : (
        <div
          className={
            viewMode === "grid"
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              : "space-y-4"
          }
        >
          {sortedRentals.map((rental) => (
            <RentalCard
              key={rental.id}
              rental={rental}
              onRent={rentChicken}
              isOwner={
                currentUserAddress && rental.ownerAddress
                  ? rental.ownerAddress.toLowerCase() ===
                    currentUserAddress.toLowerCase()
                  : false
              }
              className={viewMode === "list" ? "max-w-none" : ""}
            />
          ))}
        </div>
      )}
    </div>
  );
}
