import { ChickX } from "@/components/shared/icons";
import Feathers from "@/components/shared/icons/feathers";
import { Badge } from "@/components/ui";
import { ChickensData } from "@/types/chicken.type";
import React from "react";
import { Text } from "react-aria-components";

export const NftGrid = ({ chickens }: { chickens: ChickensData[] | null }) => {
  if (!chickens || chickens.length === 0) {
    return (
      <div className="flex flex-col items-center text-stone-700 font-Poppins pb-10">
        <ChickX />
        <Text> No chickens...</Text>
      </div>
    );
  }
  return (
    <div className="flex flex-col items-center justify-center px-4">
      <div className="grid max-sm:grid-cols-2 grid-cols-3 gap-2 overflow-y-auto">
        {chickens.map((chicken, index) => (
          <div
            key={index}
            className="bg-bg border rounded-lg shadow-xl p-4 cursor-grab"
          >
            <div className="flex flex-col items-center justify-center relative">
              <img
                src={chicken?.image}
                className="w-auto rounded-md mb-2"
                alt="Chicken"
              />
              <div className="flex w-full justify-between items-center">
                <Badge intent="secondary">#{chicken.tokenId}</Badge>
                <Badge className="gap-1 items-center ">
                  <Feathers size={12} />
                  {chicken.dailyFeathers}
                </Badge>
              </div>
              {/* Display chicken type if available */}
              {chicken.type && (
                <div className="mt-1 w-full text-center">
                  <Badge intent="primary" className="text-xs">
                    {chicken.type}
                  </Badge>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
