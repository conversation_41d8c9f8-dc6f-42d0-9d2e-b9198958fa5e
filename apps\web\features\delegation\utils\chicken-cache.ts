"use client";

import { IChickenForDelegationProgressive } from "../../chickens/hooks/delegation/useProgressiveChickensForDelegation";

// Cache configuration
const CACHE_CONFIG = {
  METADATA_KEY: "chicken_metadata_cache",
  RENTAL_KEY: "chicken_rental_cache",
  CACHE_VERSION: "v1.0.0",
  MAX_CACHE_SIZE: 1000, // Maximum number of chickens to cache
  CACHE_EXPIRY_HOURS: 24, // Cache expires after 24 hours
  RENTAL_CACHE_EXPIRY_MINUTES: 5, // Rental data expires after 5 minutes (more dynamic)
};

// Cache entry interface
interface ICacheEntry<T> {
  data: T;
  timestamp: number;
  version: string;
}

// Cache metadata interface
interface ICachedChickenMetadata {
  tokenId: number;
  metadata: any;
  image: string;
  type: string;
  level: number;
  timestamp: number;
}

// Cache rental data interface
interface ICachedChickenRental {
  tokenId: number;
  rentalData: any;
  isAvailable: boolean;
  rentalStatus: any;
  timestamp: number;
}

// Cache statistics interface
export interface ICacheStats {
  metadataCount: number;
  rentalCount: number;
  oldestEntry: number;
  newestEntry: number;
  cacheSize: string;
}

/**
 * Chicken Cache Utility
 * Manages localStorage caching for chicken metadata and rental data
 */
export class ChickenCache {
  /**
   * Check if localStorage is available
   */
  private static isLocalStorageAvailable(): boolean {
    try {
      const test = "__localStorage_test__";
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get cache key with version
   */
  private static getCacheKey(baseKey: string): string {
    return `${baseKey}_${CACHE_CONFIG.CACHE_VERSION}`;
  }

  /**
   * Check if cache entry is expired
   */
  private static isExpired(timestamp: number, expiryMinutes: number): boolean {
    const now = Date.now();
    const expiryTime = timestamp + expiryMinutes * 60 * 1000;
    return now > expiryTime;
  }

  /**
   * Get cached metadata for multiple chickens
   */
  static getCachedMetadata(
    tokenIds: number[]
  ): Record<number, ICachedChickenMetadata> {
    if (!this.isLocalStorageAvailable()) return {};

    try {
      const cacheKey = this.getCacheKey(CACHE_CONFIG.METADATA_KEY);
      const cached = localStorage.getItem(cacheKey);
      if (!cached) return {};

      const cacheEntry: ICacheEntry<Record<number, ICachedChickenMetadata>> =
        JSON.parse(cached);

      // Check cache version
      if (cacheEntry.version !== CACHE_CONFIG.CACHE_VERSION) {
        this.clearMetadataCache();
        return {};
      }

      const result: Record<number, ICachedChickenMetadata> = {};
      const expiryMinutes = CACHE_CONFIG.CACHE_EXPIRY_HOURS * 60;

      tokenIds.forEach((tokenId) => {
        const cachedItem = cacheEntry.data[tokenId];
        if (
          cachedItem &&
          !this.isExpired(cachedItem.timestamp, expiryMinutes)
        ) {
          result[tokenId] = cachedItem;
        }
      });

      return result;
    } catch (error) {
      console.warn("Failed to get cached metadata:", error);
      return {};
    }
  }

  /**
   * Cache metadata for multiple chickens
   */
  static setCachedMetadata(
    metadataMap: Record<number, ICachedChickenMetadata>
  ): void {
    if (!this.isLocalStorageAvailable()) return;

    try {
      const cacheKey = this.getCacheKey(CACHE_CONFIG.METADATA_KEY);
      const existing = localStorage.getItem(cacheKey);

      let cacheData: Record<number, ICachedChickenMetadata> = {};

      // Merge with existing cache
      if (existing) {
        const existingEntry: ICacheEntry<
          Record<number, ICachedChickenMetadata>
        > = JSON.parse(existing);
        if (existingEntry.version === CACHE_CONFIG.CACHE_VERSION) {
          cacheData = existingEntry.data;
        }
      }

      // Add new metadata
      Object.assign(cacheData, metadataMap);

      // Limit cache size (keep most recent entries)
      const entries = Object.entries(cacheData);
      if (entries.length > CACHE_CONFIG.MAX_CACHE_SIZE) {
        // Sort by timestamp (newest first) and keep only the most recent entries
        entries.sort(([, a], [, b]) => b.timestamp - a.timestamp);
        cacheData = Object.fromEntries(
          entries.slice(0, CACHE_CONFIG.MAX_CACHE_SIZE)
        );
      }

      const cacheEntry: ICacheEntry<Record<number, ICachedChickenMetadata>> = {
        data: cacheData,
        timestamp: Date.now(),
        version: CACHE_CONFIG.CACHE_VERSION,
      };

      localStorage.setItem(cacheKey, JSON.stringify(cacheEntry));
    } catch (error) {
      console.warn("Failed to cache metadata:", error);
      // If storage is full, try to clear old entries
      this.cleanupOldEntries();
    }
  }

  /**
   * Get cached rental data for multiple chickens
   */
  static getCachedRentalData(
    tokenIds: number[]
  ): Record<number, ICachedChickenRental> {
    if (!this.isLocalStorageAvailable()) return {};

    try {
      const cacheKey = this.getCacheKey(CACHE_CONFIG.RENTAL_KEY);
      const cached = localStorage.getItem(cacheKey);
      if (!cached) return {};

      const cacheEntry: ICacheEntry<Record<number, ICachedChickenRental>> =
        JSON.parse(cached);

      // Check cache version
      if (cacheEntry.version !== CACHE_CONFIG.CACHE_VERSION) {
        this.clearRentalCache();
        return {};
      }

      const result: Record<number, ICachedChickenRental> = {};

      tokenIds.forEach((tokenId) => {
        const cachedItem = cacheEntry.data[tokenId];
        if (
          cachedItem &&
          !this.isExpired(
            cachedItem.timestamp,
            CACHE_CONFIG.RENTAL_CACHE_EXPIRY_MINUTES
          )
        ) {
          result[tokenId] = cachedItem;
        }
      });

      return result;
    } catch (error) {
      console.warn("Failed to get cached rental data:", error);
      return {};
    }
  }

  /**
   * Cache rental data for multiple chickens
   */
  static setCachedRentalData(
    rentalMap: Record<number, ICachedChickenRental>
  ): void {
    if (!this.isLocalStorageAvailable()) return;

    try {
      const cacheKey = this.getCacheKey(CACHE_CONFIG.RENTAL_KEY);
      const existing = localStorage.getItem(cacheKey);

      let cacheData: Record<number, ICachedChickenRental> = {};

      // Merge with existing cache
      if (existing) {
        const existingEntry: ICacheEntry<Record<number, ICachedChickenRental>> =
          JSON.parse(existing);
        if (existingEntry.version === CACHE_CONFIG.CACHE_VERSION) {
          cacheData = existingEntry.data;
        }
      }

      // Add new rental data
      Object.assign(cacheData, rentalMap);

      // Limit cache size
      const entries = Object.entries(cacheData);
      if (entries.length > CACHE_CONFIG.MAX_CACHE_SIZE) {
        entries.sort(([, a], [, b]) => b.timestamp - a.timestamp);
        cacheData = Object.fromEntries(
          entries.slice(0, CACHE_CONFIG.MAX_CACHE_SIZE)
        );
      }

      const cacheEntry: ICacheEntry<Record<number, ICachedChickenRental>> = {
        data: cacheData,
        timestamp: Date.now(),
        version: CACHE_CONFIG.CACHE_VERSION,
      };

      localStorage.setItem(cacheKey, JSON.stringify(cacheEntry));
    } catch (error) {
      console.warn("Failed to cache rental data:", error);
      this.cleanupOldEntries();
    }
  }

  /**
   * Get token IDs that are not cached or expired
   */
  static getUncachedTokenIds(
    tokenIds: number[],
    cacheType: "metadata" | "rental"
  ): number[] {
    const cached =
      cacheType === "metadata"
        ? this.getCachedMetadata(tokenIds)
        : this.getCachedRentalData(tokenIds);

    return tokenIds.filter((tokenId) => !cached[tokenId]);
  }

  /**
   * Clear metadata cache
   */
  static clearMetadataCache(): void {
    if (!this.isLocalStorageAvailable()) return;

    try {
      const cacheKey = this.getCacheKey(CACHE_CONFIG.METADATA_KEY);
      localStorage.removeItem(cacheKey);
    } catch (error) {
      console.warn("Failed to clear metadata cache:", error);
    }
  }

  /**
   * Clear rental cache
   */
  static clearRentalCache(): void {
    if (!this.isLocalStorageAvailable()) return;

    try {
      const cacheKey = this.getCacheKey(CACHE_CONFIG.RENTAL_KEY);
      localStorage.removeItem(cacheKey);
    } catch (error) {
      console.warn("Failed to clear rental cache:", error);
    }
  }

  /**
   * Clear all caches
   */
  static clearAllCaches(): void {
    this.clearMetadataCache();
    this.clearRentalCache();
  }

  /**
   * Cleanup old entries to free up space
   */
  private static cleanupOldEntries(): void {
    try {
      // Remove expired entries from both caches
      const metadataKey = this.getCacheKey(CACHE_CONFIG.METADATA_KEY);
      const rentalKey = this.getCacheKey(CACHE_CONFIG.RENTAL_KEY);

      // Clean metadata cache
      const metadataCache = localStorage.getItem(metadataKey);
      if (metadataCache) {
        const entry: ICacheEntry<Record<number, ICachedChickenMetadata>> =
          JSON.parse(metadataCache);
        const cleanedData: Record<number, ICachedChickenMetadata> = {};
        const expiryMinutes = CACHE_CONFIG.CACHE_EXPIRY_HOURS * 60;

        Object.entries(entry.data).forEach(([tokenId, data]) => {
          if (!this.isExpired(data.timestamp, expiryMinutes)) {
            cleanedData[Number(tokenId)] = data;
          }
        });

        entry.data = cleanedData;
        localStorage.setItem(metadataKey, JSON.stringify(entry));
      }

      // Clean rental cache
      const rentalCache = localStorage.getItem(rentalKey);
      if (rentalCache) {
        const entry: ICacheEntry<Record<number, ICachedChickenRental>> =
          JSON.parse(rentalCache);
        const cleanedData: Record<number, ICachedChickenRental> = {};

        Object.entries(entry.data).forEach(([tokenId, data]) => {
          if (
            !this.isExpired(
              data.timestamp,
              CACHE_CONFIG.RENTAL_CACHE_EXPIRY_MINUTES
            )
          ) {
            cleanedData[Number(tokenId)] = data;
          }
        });

        entry.data = cleanedData;
        localStorage.setItem(rentalKey, JSON.stringify(entry));
      }
    } catch (error) {
      console.warn("Failed to cleanup old entries:", error);
    }
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): ICacheStats {
    const metadataCache = this.getCachedMetadata([]);
    const rentalCache = this.getCachedRentalData([]);

    const allTimestamps = [
      ...Object.values(metadataCache).map((item) => item.timestamp),
      ...Object.values(rentalCache).map((item) => item.timestamp),
    ];

    const cacheSize = this.isLocalStorageAvailable()
      ? `${Math.round(JSON.stringify(localStorage).length / 1024)} KB`
      : "0 KB";

    return {
      metadataCount: Object.keys(metadataCache).length,
      rentalCount: Object.keys(rentalCache).length,
      oldestEntry: allTimestamps.length > 0 ? Math.min(...allTimestamps) : 0,
      newestEntry: allTimestamps.length > 0 ? Math.max(...allTimestamps) : 0,
      cacheSize,
    };
  }
}
