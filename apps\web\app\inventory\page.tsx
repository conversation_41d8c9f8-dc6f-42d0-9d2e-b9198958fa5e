import { Suspense } from "react";
import AppNavbar from "@/components/shared/navbar";
import AppInitializer from "@/providers/app/initializer";
import InventoryLayout from "@/components/pages/inventory/layout";
import { Loading } from "@/components/shared/loading";

export default async function InventoryPage() {
  return (
    <AppInitializer>
      <div className="min-h-screen relative bg-gradient-to-b from-stone-900 via-black to-stone-900 bg-opacity-85 overflow-hidden">
        <div className="relative z-10">
          <AppNavbar />
        </div>
        
        <Suspense fallback={<Loading />}>
          <InventoryLayout />
        </Suspense>
      </div>
    </AppInitializer>
  );
}