import { ChickensData } from "@/types/chicken.type";
import { create } from "zustand";

type ChickenState = {
  isPending: boolean;
  chickens?: ChickensData[];
};

type Actions = {
  getChickens: (isConnected: boolean) => void;
};

type StoreState = ChickenState & Actions;

const useChickenStore = create<StoreState>()((set) => {
  return {
    isPending: false,

    getChickens: async (isConnected: boolean) => {
      try {
        set({ isPending: true });

        if (!isConnected) {
          // If not authenticated, don't make the API call
          set({ chickens: [] });
          return;
        }

        const response = await fetch(`/api/chickens/able-to-rub`, {
          cache: "no-store",
          method: "GET",
        });
        if (response.ok) {
          const data: { data: ChickensData[] } = await response.json();

          set({ chickens: data.data });
        }
      } catch (_error: unknown) {
        set({ chickens: undefined });
      } finally {
        set({ isPending: false });
      }
    },
  };
});

export default useChickenStore;
