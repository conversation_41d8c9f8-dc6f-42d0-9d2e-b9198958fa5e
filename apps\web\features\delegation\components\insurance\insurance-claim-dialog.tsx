"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Modal } from "ui";
import { AlertTriangle, CheckCircle, Clock, Coins } from "lucide-react";
import {
  IRentalWithMetadata,
  formatRoninPrice,
} from "../../types/delegation.types";
import { useClaimInsurance } from "../../hooks/useClaimInsurance";
import { useChickenDeathVerification } from "../../hooks/useChickenDeathVerification";
import { InsuranceStatusBadge } from "./insurance-status-badge";
import { CooldownTimer } from "@/components/common/cooldown-timer";

interface IInsuranceClaimDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  rental: IRentalWithMetadata;
}

export function InsuranceClaimDialog({
  isOpen,
  onOpenChange,
  rental,
}: IInsuranceClaimDialogProps) {
  const { executeClaimInsurance, checkInsuranceEligibility, isClaiming } =
    useClaimInsurance();
  const { verifyChickenDeath } = useChickenDeathVerification();

  const [eligibility, setEligibility] = useState<any>(null);
  const [verification, setVerification] = useState<any>(null);
  const [isChecking, setIsChecking] = useState(false);

  // Check eligibility when dialog opens
  useEffect(() => {
    if (isOpen && rental) {
      checkEligibility();
    }
  }, [isOpen, rental]);

  const checkEligibility = async () => {
    setIsChecking(true);
    try {
      // First verify chicken death status
      const deathVerification = await verifyChickenDeath(rental.chickenTokenId);
      setVerification(deathVerification);

      // Then check insurance eligibility
      const eligibilityResult = await checkInsuranceEligibility(rental);
      setEligibility(eligibilityResult);
    } catch (error) {
      console.error("Failed to check eligibility:", error);
    } finally {
      setIsChecking(false);
    }
  };

  const handleClaimInsurance = async () => {
    try {
      const result = await executeClaimInsurance(rental);
      if (result?.success) {
        onOpenChange(false);
      }
    } catch (error) {
      console.error("Failed to claim insurance:", error);
    }
  };

  // Calculate insurance amount from actual insurance price or fallback to 50% of rental price
  const insuranceAmount =
    rental.insurancePrice && rental.insurancePrice !== "0"
      ? parseFloat(formatRoninPrice(rental.insurancePrice))
      : rental.roninPrice
        ? parseFloat(formatRoninPrice(rental.roninPrice)) * 0.5 // Fallback to 50% insurance
        : 0;

  // Check if rental has expired
  const isExpired = rental.expiresAt && new Date(rental.expiresAt) < new Date();
  const timeUntilExpiry = rental.expiresAt
    ? Math.max(0, new Date(rental.expiresAt).getTime() - new Date().getTime()) /
      1000
    : 0;

  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
      <Modal.Content size="md">
        <Modal.Header>
          <Modal.Title>Insurance Claim</Modal.Title>
          <Modal.Description>
            Review insurance eligibility and claim status for Chicken #
            {rental.chickenTokenId}
          </Modal.Description>
        </Modal.Header>

        <Modal.Body>
          <div className="space-y-6">
            {/* Rental Information */}
            <div className="bg-stone-700/30 rounded-lg p-4">
              <h3 className="text-sm font-medium text-white mb-3">
                Rental Details
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Chicken ID:</span>
                  <span className="text-white ml-2">
                    #{rental.chickenTokenId}
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">Daily Rate:</span>
                  <span className="text-white ml-2">
                    {(() => {
                      if (rental.roninPrice === "0") return "Free";
                      const totalPriceEth =
                        parseFloat(rental.roninPrice) / 10 ** 18;
                      const days = Math.floor(
                        rental.rentalPeriod / (24 * 60 * 60)
                      );
                      const dailyRate = totalPriceEth / days;
                      return `${dailyRate.toFixed(4)} RON/day`;
                    })()}
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">Insurance:</span>
                  <span className="text-white ml-2">
                    {insuranceAmount.toFixed(4)} RON
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">Status:</span>
                  <span className="text-white ml-2">
                    {isExpired ? "Expired" : "Active"}
                  </span>
                </div>
              </div>
            </div>

            {/* Expiry Status */}
            {!isExpired && (
              <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="w-4 h-4 text-blue-400" />
                  <span className="text-sm font-medium text-blue-300">
                    Rental Active
                  </span>
                </div>
                <div className="text-sm text-gray-300">
                  Time remaining:{" "}
                  <CooldownTimer remainingSeconds={timeUntilExpiry} />
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  Insurance can only be claimed after the rental period expires
                </div>
              </div>
            )}

            {/* Death Verification Status */}
            {verification && (
              <div className="bg-stone-700/30 rounded-lg p-4">
                <h3 className="text-sm font-medium text-white mb-3">
                  Death Verification
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-400">Status:</span>
                    <InsuranceStatusBadge
                      isDead={verification.isDead}
                      isVerified={verification.isVerified}
                      confidence={verification.confidence}
                      requiresReview={verification.requiresReview}
                      verificationMethod={verification.verificationMethod}
                    />
                  </div>

                  {verification.error && (
                    <div className="text-xs text-red-400 bg-red-500/10 border border-red-500/30 rounded p-2">
                      {verification.error}
                    </div>
                  )}

                  <div className="text-xs text-gray-400">
                    Verification method: {verification.verificationMethod}
                    {verification.confidence &&
                      ` (${verification.confidence} confidence)`}
                  </div>
                </div>
              </div>
            )}

            {/* Eligibility Status */}
            {eligibility && (
              <div className="bg-stone-700/30 rounded-lg p-4">
                <h3 className="text-sm font-medium text-white mb-3">
                  Claim Eligibility
                </h3>
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    {eligibility.canClaim ? (
                      <CheckCircle className="w-4 h-4 text-green-400 mt-0.5" />
                    ) : (
                      <AlertTriangle className="w-4 h-4 text-red-400 mt-0.5" />
                    )}
                    <div className="flex-1">
                      <div
                        className={`text-sm font-medium ${
                          eligibility.canClaim
                            ? "text-green-400"
                            : "text-red-400"
                        }`}
                      >
                        {eligibility.canClaim
                          ? "Eligible to Claim"
                          : "Not Eligible"}
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        {eligibility.reason}
                      </div>
                    </div>
                  </div>

                  {eligibility.canClaim && (
                    <div className="bg-green-500/10 border border-green-500/30 rounded p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <Coins className="w-4 h-4 text-green-400" />
                        <span className="text-sm font-medium text-green-300">
                          Insurance Payout
                        </span>
                      </div>
                      <div className="text-sm text-white">
                        {insuranceAmount.toFixed(4)} RON
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        {eligibility.recipient === "owner"
                          ? "Compensation for lost chicken"
                          : "Insurance deposit refund"}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Loading State */}
            {isChecking && (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4"></div>
                <div className="text-sm text-gray-400">
                  Checking eligibility...
                </div>
              </div>
            )}
          </div>
        </Modal.Body>

        <Modal.Footer>
          <Button
            appearance="outline"
            onPress={() => onOpenChange(false)}
            isDisabled={isClaiming}
          >
            Close
          </Button>

          {eligibility?.canClaim && (
            <Button
              onPress={handleClaimInsurance}
              isDisabled={isClaiming || !eligibility.canClaim}
              className="bg-green-600 hover:bg-green-700"
            >
              {isClaiming ? "Claiming..." : "Claim Insurance"}
            </Button>
          )}

          {!isChecking && !eligibility?.canClaim && isExpired && (
            <Button
              appearance="outline"
              onPress={checkEligibility}
              className="border-blue-500 text-blue-400 hover:bg-blue-500/10"
            >
              Refresh Status
            </Button>
          )}
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
}
