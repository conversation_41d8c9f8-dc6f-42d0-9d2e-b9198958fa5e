"use client";

import React from "react";
import Image from "next/image";
import { BattleItem } from "@/types/battle-items.types";

interface BattleItemCardProps {
  item: BattleItem;
  isSelected: boolean;
  isDisabled: boolean;
  onClick: () => void;
  className?: string;
}

export const BattleItemCard: React.FC<BattleItemCardProps> = ({
  item,
  isSelected,
  isDisabled,
  onClick,
  className = ""
}) => {
  return (
    <div
      className={`
        relative bg-gray-800 rounded-lg border-2 transition-all duration-200 cursor-pointer
        ${isSelected ? "border-yellow-400 shadow-[0_0_15px_rgba(251,191,36,0.5)]" : "border-gray-600"}
        ${isDisabled ? "opacity-50 cursor-not-allowed" : "hover:border-gray-400 hover:shadow-lg"}
        ${className}
      `}
      onClick={isDisabled ? undefined : onClick}
    >
      {/* Item Image */}
      <div className="aspect-square bg-gray-700 rounded-t-lg overflow-hidden relative">
        <Image
          src={item.image}
          alt={item.name}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 50vw, 25vw"
        />
        {isSelected && (
          <div className="absolute top-2 right-2 bg-yellow-400 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">
            ✓
          </div>
        )}
        {isDisabled && !isSelected && (
          <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
            <span className="text-white text-xs font-medium bg-red-600 px-2 py-1 rounded">
              SELECTED
            </span>
          </div>
        )}
      </div>

      {/* Item Info */}
      <div className="p-3">
        <h4 className="text-white font-bold text-sm mb-1 md:truncate" title={item.name}>
          {item.name}
        </h4>
        <p 
          className="text-gray-300 text-xs leading-relaxed overflow-hidden md:line-clamp-3"
        >
          {item.description}
        </p>
      </div>
    </div>
  );
};