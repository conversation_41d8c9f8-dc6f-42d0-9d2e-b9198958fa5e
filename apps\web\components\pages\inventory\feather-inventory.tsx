"use client";

import { useFeatherInventory } from "@/hooks/useInventoryData";
import { Button } from "@/components/ui";
import { ExternalLink, ShoppingCart } from "lucide-react";
import Image from "next/image";

export default function FeatherInventory() {
  const { regularFeathers, legendaryFeathers, totalFeathers } =
    useFeatherInventory();

  const featherTypes = [
    {
      id: "regular",
      name: "Regular Feathers",
      image: "/images/feathers.png",
      amount: regularFeathers,
      description: "Common feathers used for crafting basic items and foods.",
      tokenId: 1,
      rarity: "Common",
      rarityColor: "text-gray-400",
      bgColor: "bg-stone-800",
      borderColor: "border-stone-600",
    },
    {
      id: "legendary",
      name: "Legendary Feathers",
      image: "/images/legendary-feathers.png",
      amount: legendaryFeathers,
      description:
        "Rare feathers used for crafting powerful items and premium foods.",
      tokenId: 2,
      rarity: "Legendary",
      rarityColor: "text-amber-400",
      bgColor: "bg-gradient-to-br from-amber-900/20 to-stone-800",
      borderColor: "border-amber-600/50",
    },
  ];

  const openMarketplace = (type: string) => {
    const marketplaceUrl =
      type === "regular"
        ? "https://marketplace.roninchain.com/collections/sabong-saga-game-items/1"
        : "https://marketplace.roninchain.com/collections/sabong-saga-game-items/2";
    window.open(marketplaceUrl, "_blank");
  };

  if (totalFeathers === 0) {
    return (
      <div className="text-center p-12">
        <div className="mb-6">
          <div className="mx-auto w-24 h-24 bg-stone-800 rounded-full flex items-center justify-center mb-4">
            <Image
              src="/images/feathers.png"
              alt="Feathers"
              width={48}
              height={48}
              className="opacity-50"
            />
          </div>
          <h3 className="text-xl font-medium text-white mb-2">
            No Feathers Found
          </h3>
          <p className="text-stone-400 mb-6">
            You don't have any feathers yet. Purchase some from the marketplace
            to start crafting!
          </p>
          <Button
            appearance="outline"
            size="small"
            onPress={() => openMarketplace("regular")}
            className="flex items-center gap-2"
          >
            <ShoppingCart className="w-4 h-4" />
            <span>Buy Feathers</span>
            <ExternalLink className="w-4 h-4" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="bg-stone-900/50 rounded-lg p-4 border border-stone-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-white">Total Feathers</h3>
            <p className="text-stone-400">Combined feather balance</p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-primary">
              {totalFeathers.toLocaleString()}
            </p>
            <p className="text-sm text-stone-400">
              {regularFeathers > 0 && legendaryFeathers > 0
                ? `${regularFeathers.toLocaleString()} Regular + ${legendaryFeathers.toLocaleString()} Legendary`
                : regularFeathers > 0
                  ? `${regularFeathers.toLocaleString()} Regular`
                  : `${legendaryFeathers.toLocaleString()} Legendary`}
            </p>
          </div>
        </div>
      </div>

      {/* Feather Types Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {featherTypes.map((featherType) => (
          <div
            key={featherType.id}
            className={`${featherType.bgColor} ${featherType.borderColor} border rounded-lg p-6 transition-all hover:border-primary/30`}
          >
            <div className="flex items-start space-x-4">
              <div className="relative">
                <Image
                  src={featherType.image}
                  alt={featherType.name}
                  width={64}
                  height={64}
                  className="object-contain"
                />
                {featherType.id === "legendary" && (
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-black">★</span>
                  </div>
                )}
              </div>

              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-lg font-medium text-white">
                    {featherType.name}
                  </h4>
                  <span
                    className={`text-sm font-medium ${featherType.rarityColor}`}
                  >
                    {featherType.rarity}
                  </span>
                </div>

                <p className="text-stone-400 text-sm mb-3">
                  {featherType.description}
                </p>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold text-white">
                      {featherType.amount.toLocaleString()}
                    </p>
                    <p className="text-xs text-stone-500">
                      Token ID: {featherType.tokenId}
                    </p>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      appearance="outline"
                      size="small"
                      onPress={() => openMarketplace(featherType.id)}
                      className="flex items-center gap-1"
                    >
                      <ShoppingCart className="w-3 h-3" />
                      <span>Buy</span>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Usage Information */}
      <div className="bg-stone-900/30 rounded-lg p-4 border border-stone-700">
        <h4 className="text-lg font-medium text-white mb-3">
          How to Use Feathers
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h5 className="text-primary font-medium">Regular Feathers</h5>
            <ul className="text-sm text-stone-400 space-y-1">
              <li>• Craft basic food items</li>
              <li>• Create common battle items</li>
              <li>• Used for common breeding items</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h5 className="text-amber-400 font-medium">Legendary Feathers</h5>
            <ul className="text-sm text-stone-400 space-y-1">
              <li>• Craft premium food items</li>
              <li>• Create rare battle items</li>
              <li>• Used for special breeding items</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="flex flex-col sm:flex-row gap-3">
        <Button
          appearance="outline"
          onPress={() => (window.location.href = "/crafting")}
          className="flex items-center gap-2"
        >
          <span>Go to Crafting</span>
          <ExternalLink className="w-4 h-4" />
        </Button>
        <Button
          appearance="outline"
          onPress={() => openMarketplace("regular")}
          className="flex items-center gap-2"
        >
          <ShoppingCart className="w-4 h-4" />
          <span>Buy More Feathers</span>
          <ExternalLink className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
