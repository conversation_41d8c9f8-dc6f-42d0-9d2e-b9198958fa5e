"use client";

import { Button, Form, Input, Label, Loader, Modal } from "@/components/ui";
import { useStateContext } from "@/providers/app/state";
import useAllocationStore from "@/store/allocation";
import { IconPencilBoxFill, IconPlus } from "justd-icons";
import React, { Dispatch, SetStateAction, useState } from "react";
import { isAddress } from "viem";

interface FormState {
  walletAddress: string;
  amount: number;
}

interface FormErrors {
  walletAddress?: string;
  amount?: string;
}

interface DelegateModalProps {
  isOpen: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  isUpdate: boolean;
}

export function DelegateModal({
  isOpen,
  setOpen,
  isUpdate = false,
}: DelegateModalProps) {
  const { address } = useStateContext();
  const { addOrUpdate, setSelectedAcct, selectedAccount, isPending } =
    useAllocationStore();

  const [errors, setErrors] = useState<FormErrors>({});

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!selectedAccount?.walletAddress!.trim()) {
      newErrors.walletAddress = "Wallet address is required";
    }

    if (
      selectedAccount?.walletAddress &&
      !isAddress(selectedAccount.walletAddress)
    ) {
      newErrors.walletAddress = "Invalid wallet address";
    }

    if (
      selectedAccount?.walletAddress &&
      isAddress(selectedAccount.walletAddress) &&
      address === selectedAccount.walletAddress
    ) {
      newErrors.walletAddress =
        "Invalid wallet address. You cannot delegate to your own wallet.";
    }

    if (selectedAccount?.amount! < 0) {
      newErrors.amount = "Amount must be a positive number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Convert to number and validate
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue) && numValue >= 0) {
      setSelectedAcct({ amount: numValue });
    }
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      await addOrUpdate(
        selectedAccount?.walletAddress!,
        selectedAccount?.amount!,
        false
      );
      // Reset form after successful submission

      setSelectedAcct({ walletAddress: "", amount: 0 });
      setErrors({});
      setOpen(false);
    }
  };

  return (
    <Modal>
      <Modal.Content isOpen={isOpen} onOpenChange={setOpen}>
        <Modal.Header>
          <Modal.Title>Add Delegatee</Modal.Title>
          <Modal.Description>
            Enter the wallet address and amount of WL spot for your delegatee.
          </Modal.Description>
        </Modal.Header>
        <Form onSubmit={onSubmit}>
          <Modal.Body>
            <div className="flex flex-col gap-1">
              <Label>Wallet address</Label>
              <Input
                className="outline-border rounded-md"
                required
                value={selectedAccount?.walletAddress || ""}
                onChange={(e) => {
                  setSelectedAcct({ walletAddress: e.target.value });
                }}
                placeholder="Enter wallet address"
              />
              {errors.walletAddress && (
                <span className="text-red-500 text-sm">
                  {errors.walletAddress}
                </span>
              )}
            </div>
            <div className="flex flex-col gap-1 mt-2">
              <Label>Amount</Label>
              <Input
                className="focus-border outline-border rounded-md"
                type="number"
                required
                min={0}
                value={selectedAccount?.amount || 0}
                onChange={handleAmountChange}
                placeholder="Enter amount of WL Spot"
              />
              {errors.amount && (
                <span className="text-red-500 text-sm">{errors.amount}</span>
              )}
            </div>
          </Modal.Body>
          <Modal.Footer>
            <div className="flex w-full justify-end items-center gap-2">
              <Modal.Close>Cancel</Modal.Close>
              <Button
                isDisabled={
                  !selectedAccount?.walletAddress ||
                  !selectedAccount.amount ||
                  isPending
                }
                isPending={isPending}
                type="submit"
                size="small"
              >
                {({ isPending }) => (
                  <>
                    {isPending ? (
                      <Loader isIndeterminate aria-label="Creating..." />
                    ) : isUpdate ? (
                      <IconPencilBoxFill />
                    ) : (
                      <IconPlus />
                    )}
                    {!isPending
                      ? isUpdate
                        ? "Update Delegatee"
                        : "Add Delegatee"
                      : "Saving..."}
                  </>
                )}
              </Button>
            </div>
          </Modal.Footer>
        </Form>
      </Modal.Content>
    </Modal>
  );
}
