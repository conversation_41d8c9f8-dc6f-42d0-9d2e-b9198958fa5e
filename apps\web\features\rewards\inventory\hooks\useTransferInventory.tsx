"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  ITransferBalanceRequest,
  ITransferBalanceResponse,
} from "../types/inventory.types";

/**
 * Transfer inventory rewards to balance using secure server-side API
 */
export const transferInventoryBalance = async (
  request: ITransferBalanceRequest
): Promise<ITransferBalanceResponse> => {
  // Fetch CSRF token first
  const csrfResponse = await fetch("/csrf-token");
  if (!csrfResponse.ok) {
    throw new Error(`Failed to fetch CSRF token: ${csrfResponse.statusText}`);
  }
  const { csrfToken } = await csrfResponse.json();

  const response = await fetch("/api/rewards/transfer", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-CSRF-Token": csrfToken,
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.error ||
        errorData.message ||
        `Transfer failed: ${response.statusText}`
    );
  }

  return response.json();
};

/**
 * Hook to handle inventory rewards transfer to balance
 */
export function useTransferInventory() {
  const queryClient = useQueryClient();

  const transferInventoryMutation = useMutation({
    mutationFn: transferInventoryBalance,
    onSuccess: (data, variables) => {
      // Invalidate and refetch all relevant queries to update UI
      queryClient.invalidateQueries({
        queryKey: ["gameRewards", variables.address],
      });

      // Invalidate per-chicken rewards query (used by chicken rewards table)
      queryClient.invalidateQueries({
        queryKey: ["perChickenRewards", variables.address],
      });

      // Invalidate balance query to update claimable amounts
      queryClient.invalidateQueries({
        queryKey: ["inventoryBalance", variables.address],
      });

      console.log("✅ Transfer successful:", data);
    },
    onError: (error: any) => {
      console.error("❌ Transfer failed:", error);
    },
  });

  return {
    transferInventoryMutation,
    isTransferring: transferInventoryMutation.isPending,
    transferError: transferInventoryMutation.error,
  };
}
