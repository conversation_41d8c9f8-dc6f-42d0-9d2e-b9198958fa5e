"use client";

import { CooldownTimer } from "@/components/common/cooldown-timer";
import { IChickenMetadata } from "@/lib/types/chicken.types";
import { IconChevronLgDown, IconSearch, IconFilter } from "justd-icons";
import { <PERSON><PERSON>, cn, Modal } from "ui";
import { useOptimizedChickensForBreeding } from "../hooks/useOptimizedChickensForBreeding";
import { useSelectedChickenGenes } from "../hooks/useSelectedChickenGenes";
import { IParentData } from "../types/breeding.types";
import { IGenesResponse } from "../types/genes.types";
import { ChickenIcon } from "@/features/dapp/components/icon/chicken.icon";
import { useState } from "react";
import { useHookstate } from "@hookstate/core";

interface OptimizedChickenSelectionDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSelect: (chicken: IParentData) => void;
  title?: string;
  selectedChickens?: number[]; // Prop to track selected chicken IDs
}

export function OptimizedChickenSelectionDialog({
  isOpen,
  onOpenChange,
  onSelect,
  title = "Select a chicken to breed",
  selectedChickens,
}: OptimizedChickenSelectionDialogProps) {
  const searchQuery = useHookstate("");
  const [filterType, setFilterType] = useState<
    "all" | "legacy" | "genesis" | "ordinary"
  >("all");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  const {
    chickens,
    currentPage,
    totalPages,
    hasMore,
    goToNextPage,
    goToPreviousPage,
    isLoading,
    stats,
    getRemainingCooldown,
    resetPage,
  } = useOptimizedChickensForBreeding(searchQuery.value, {
    pageSize: 20,
    filterType,
    sortOrder,
  });

  // Load genes only for selected chickens
  const { getGenesForChicken } = useSelectedChickenGenes(
    selectedChickens || []
  );

  const toggleSortOrder = () => {
    setSortOrder((order) => (order === "asc" ? "desc" : "asc"));
    resetPage();
  };

  const handleSearchChange = (value: string) => {
    searchQuery.set(value);
    resetPage();
  };

  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
      <Modal.Content size="3xl" closeButton>
        <div className="py-8 max-h-[94vh]">
          {/* Header with Title and Search */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
            <h2 className="text-lg sm:text-2xl font-semibold text-white">
              {title}
            </h2>
            <div className="relative w-full sm:max-w-xs">
              <input
                type="text"
                placeholder="SEARCH BY TOKEN ID"
                value={searchQuery.value}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="bg-[#2D2D2D] h-8 text-white w-full pl-10 pr-4 py-1.5 sm:py-2 rounded-lg text-sm sm:text-base"
              />
              <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 sm:w-5 sm:h-5" />
            </div>
          </div>

          <div className="flex flex-col gap-6">
            {/* Filter and Sort Section */}
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
              {/* Filter Badges */}
              <div className="flex justify-center gap-2 flex-wrap">
                <button
                  onClick={() => setFilterType("all")}
                  className={cn(
                    "px-3 py-1 text-xs rounded-full font-medium transition-all duration-200",
                    filterType === "all"
                      ? "bg-white text-black"
                      : "bg-white/20 text-gray-400 border border-white/30 hover:bg-white/30"
                  )}
                >
                  All ({stats?.total || 0})
                </button>

                <button
                  onClick={() => setFilterType("genesis")}
                  className={cn(
                    "px-3 py-1 text-xs rounded-full font-medium transition-all duration-200",
                    filterType === "genesis"
                      ? "bg-orange-500 text-white"
                      : "bg-orange-500/20 text-orange-400 border border-orange-500/30 hover:bg-orange-500/30"
                  )}
                >
                  Genesis ({stats?.genesis || 0})
                </button>

                <button
                  onClick={() => setFilterType("legacy")}
                  className={cn(
                    "px-3 py-1 text-xs rounded-full font-medium transition-all duration-200",
                    filterType === "legacy"
                      ? "bg-blue-500 text-white"
                      : "bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:bg-blue-500/30"
                  )}
                >
                  Legacy ({stats?.legacy || 0})
                </button>

                <button
                  onClick={() => setFilterType("ordinary")}
                  className={cn(
                    "px-3 py-1 text-xs rounded-full font-medium transition-all duration-200",
                    filterType === "ordinary"
                      ? "bg-green-500 text-white"
                      : "bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30"
                  )}
                >
                  Ordinary ({stats?.ordinary || 0})
                </button>
              </div>

              <Button
                intent="secondary"
                onPress={toggleSortOrder}
                className="h-8 bg-[#2D2D2D] hover:bg-[#3D3D3D] text-white px-3 sm:px-4 py-2 rounded-lg flex items-center gap-2 text-sm sm:text-base w-full sm:w-auto justify-between sm:justify-start"
              >
                <span className="text-white">
                  Sort: {sortOrder === "asc" ? "Lowest ID" : "Highest ID"}
                </span>
                <IconChevronLgDown className="text-white w-4 h-4 flex-shrink-0" />
              </Button>
            </div>

            {/* Chickens Grid */}
            {isLoading && chickens.length === 0 ? (
              <div className="flex justify-center items-center h-40 sm:h-64">
                <span className="text-white text-sm sm:text-base">
                  Loading chickens...
                </span>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-4 p-1 sm:p-2 max-h-[40vh] sm:max-h-[50vh] md:max-h-[60vh] overflow-y-auto pr-1 sm:pr-2">
                  {chickens.map((chicken) => {
                    const isAlreadySelected = selectedChickens?.includes(
                      chicken.tokenId
                    );
                    const isChickenOnCooldown = chicken.isOnCooldown;
                    const remainingCooldown = getRemainingCooldown
                      ? getRemainingCooldown(chicken.tokenId)
                      : 0;
                    const isDisabled = isAlreadySelected || isChickenOnCooldown;

                    const data: IParentData = {
                      tokenId: chicken.tokenId,
                      image: chicken.image,
                      breedCount: chicken.breedCount,
                      metadata: chicken.metadata as IChickenMetadata,
                      genes: getGenesForChicken(
                        chicken.tokenId
                      ) as IGenesResponse,
                    };

                    return (
                      <div
                        key={chicken.tokenId}
                        onClick={() => !isDisabled && onSelect(data)}
                        className={cn(
                          "cursor-pointer transition-transform",
                          isDisabled
                            ? "opacity-50 cursor-not-allowed"
                            : "hover:scale-105"
                        )}
                      >
                        <div
                          className={cn(
                            "border-2 rounded-lg p-0.5 sm:p-1 transition-colors",
                            isDisabled
                              ? "border-gray-600"
                              : "border-[#2D2D2D] hover:border-yellow-500"
                          )}
                        >
                          <div className="relative bg-gradient-to-b from-[#1E1E1E] to-[#171717] rounded-lg overflow-hidden aspect-square">
                            {chicken.image ? (
                              <img
                                src={chicken.image}
                                alt={`Chicken #${chicken.tokenId}`}
                                className={cn(
                                  "w-full h-full object-contain",
                                  isDisabled && "grayscale"
                                )}
                              />
                            ) : (
                              <div className="size-full grid place-items-center">
                                <ChickenIcon className="-translate-y-1 size-[4rem] text-[#2D2D2D] group-hover:text-[#373737] transition duration-300 ease-in-out" />
                              </div>
                            )}

                            <div className="absolute bottom-1 sm:bottom-2 left-1 sm:left-2 text-[0.6rem] sm:text-[0.65rem] bg-primary text-black font-bold px-1 sm:px-2 py-px rounded-md">
                              #{chicken.tokenId}
                            </div>

                            {/* Breed Count Display */}
                            <div className="absolute top-1 sm:top-2 right-1 sm:right-2 text-[0.6rem] sm:text-[0.65rem] bg-gray-600 text-white font-bold px-1 sm:px-2 py-px rounded-md">
                              {chicken.breedCount}
                            </div>

                            {/* Type Badge */}
                            <div
                              className={cn(
                                "absolute top-1 sm:top-2 left-1 sm:left-2 text-[0.6rem] sm:text-[0.65rem] font-bold px-1 sm:px-2 py-px rounded-md",
                                chicken.type === "genesis"
                                  ? "bg-orange-600 text-white"
                                  : chicken.type === "legacy"
                                    ? "bg-blue-600 text-white"
                                    : "bg-green-600 text-white"
                              )}
                            >
                              {chicken.type.charAt(0).toUpperCase() +
                                chicken.type.slice(1)}
                            </div>

                            {isAlreadySelected && (
                              <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                                <span className="text-white text-center -translate-y-3 text-xs sm:text-sm font-bold px-1">
                                  Already Selected
                                </span>
                              </div>
                            )}
                            {isChickenOnCooldown && !isAlreadySelected && (
                              <div className="absolute inset-0 bg-black bg-opacity-40 flex flex-col items-center justify-center p-1">
                                <span className="text-white text-center text-xs sm:text-sm font-bold mb-1">
                                  Breeding State
                                </span>
                                <div className="bg-red-600 text-white px-1 sm:px-2 py-1 rounded-md text-[0.6rem] sm:text-xs">
                                  <CooldownTimer
                                    remainingSeconds={remainingCooldown}
                                  />
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Pagination Controls */}
                {totalPages > 1 && (
                  <div className="flex justify-center items-center gap-2 mt-3 sm:mt-4">
                    <Button
                      intent="secondary"
                      onPress={goToPreviousPage}
                      isDisabled={currentPage === 1 || isLoading}
                      className="text-sm h-8 sm:text-base py-1 sm:py-2"
                    >
                      Previous
                    </Button>
                    <span className="text-white text-sm">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      intent="secondary"
                      onPress={goToNextPage}
                      isDisabled={!hasMore || isLoading}
                      className="text-sm h-8 sm:text-base py-1 sm:py-2"
                    >
                      Next
                    </Button>
                  </div>
                )}
              </>
            )}

            {chickens.length === 0 && !isLoading && (
              <div className="flex justify-center items-center h-40 sm:h-64">
                <span className="text-white text-sm sm:text-base">
                  No chickens found
                </span>
              </div>
            )}
          </div>
        </div>
      </Modal.Content>
    </Modal>
  );
}
