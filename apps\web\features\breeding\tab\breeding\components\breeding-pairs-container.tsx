"use client";

import { Button, cn } from "@/components/ui";
import { useOptimizedBreeding } from "../hooks/useBreeding";
import BreedingPair from "./breeding-pair";

export default function BreedingPairsContainer() {
  const {
    state,
    getAvailableChickenCount,
    areAllChickensSelected,
    isPreviousPairIncomplete,
  } = useOptimizedBreeding();
  const totalMassBreedingPairs = state.massBreedingPairs.length;

  return (
    <>
      {state.breedOption.value === "manual" ? (
        <BreedingPair pair={state.manualBreedingPair} />
      ) : (
        <div className="grid gap-8">
          {state.massBreedingPairs.map((pair, index) => (
            <BreedingPair key={pair.id.value} pair={pair} index={index} />
          ))}

          <div className="grid place-items-center">
            <Button
              onPress={() => {
                state.massBreedingPairs.merge([
                  {
                    id: totalMassBreedingPairs + 1,
                    parent1: 0,
                    parent2: 0,
                    breedingItem: 0,
                  },
                ]);
              }}
              isDisabled={
                // Check if there are enough available chickens
                getAvailableChickenCount() < 2 ||
                // Check if all chickens are already selected
                areAllChickensSelected() ||
                // Check if previous pair is incomplete
                (totalMassBreedingPairs > 0 && isPreviousPairIncomplete()) ||
                // Check if maximum pairs limit reached
                totalMassBreedingPairs === 10
              }
              className={cn(
                "bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-3 rounded-lg",
                (getAvailableChickenCount() < 2 ||
                  areAllChickensSelected() ||
                  (totalMassBreedingPairs > 0 && isPreviousPairIncomplete())) &&
                  "opacity-50 cursor-not-allowed"
              )}
            >
              {totalMassBreedingPairs === 10 ? "Max Reached" : "ADD PAIR"}
            </Button>
          </div>
        </div>
      )}
    </>
  );
}
