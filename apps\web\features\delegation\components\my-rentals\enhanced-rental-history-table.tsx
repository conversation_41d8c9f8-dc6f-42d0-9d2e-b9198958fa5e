"use client";

import { useMemo } from "react";
import { Table, Button } from "ui";
import {
  RefreshCw,
  Calendar,
  Gamepad2,
  Info,
  Shield,
  ExternalLink,
  CheckCircle,
  XCircle,
  AlertCircle,
  List,
} from "lucide-react";
import {
  ERentalHistoryEventType,
  ERewardDistributionType,
  EDelegatedTaskType,
  REWARD_DISTRIBUTION_LABELS,
  DELEGATED_TASK_LABELS,
} from "../../types/delegation.types";
import { IChickenMetadata } from "@/lib/types/chicken.types";
import { formatDistanceToNow } from "date-fns";
import { getTransactionExplorerUrl } from "@/lib/utils/explorer";

interface IEnhancedRentalHistoryTableProps {
  events: (any & { chickenMetadata?: IChickenMetadata | null })[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  } | null;
  isLoading: boolean;
  error: any;
  currentUserAddress?: string;
  onRefresh: () => void;
  onNextPage: () => void;
  onPreviousPage: () => void;
  onFirstPage: () => void;
  onLastPage: () => void;
}

// Event type styling and icons
const getEventTypeInfo = (eventType: ERentalHistoryEventType) => {
  switch (eventType) {
    case ERentalHistoryEventType.LISTED:
      return {
        icon: List,
        color: "blue",
        label: "Listed",
        bgColor: "bg-green-500/20",
        textColor: "text-green-400",
      };
    case ERentalHistoryEventType.RENTED:
      return {
        icon: CheckCircle,
        color: "green",
        label: "Delegated",
        bgColor: "bg-blue-500/20",
        textColor: "text-blue-400",
      };
    case ERentalHistoryEventType.CANCELLED:
      return {
        icon: XCircle,
        color: "red",
        label: "Cancelled",
        bgColor: "bg-red-500/20",
        textColor: "text-red-400",
      };
    case ERentalHistoryEventType.INSURANCE_CLAIMED:
      return {
        icon: Shield,
        color: "purple",
        label: "Insurance Claimed",
        bgColor: "bg-purple-500/20",
        textColor: "text-purple-400",
      };
    case ERentalHistoryEventType.UNLISTED:
      return {
        icon: AlertCircle,
        color: "gray",
        label: "Unlisted",
        bgColor: "bg-gray-500/20",
        textColor: "text-gray-400",
      };
    default:
      return {
        icon: Info,
        color: "gray",
        label: "Unknown",
        bgColor: "bg-gray-500/20",
        textColor: "text-gray-400",
      };
  }
};

// Format price display
const formatPrice = (
  price: string,
  period: number
): { daily: string; total: string | null } => {
  const priceInEth = parseFloat(price) / 1e18;
  const dailyPrice = priceInEth / (period / 86400); // Convert seconds to days

  return {
    daily: `${dailyPrice.toFixed(4)} RON/day`,
    total: period > 86400 ? `${priceInEth.toFixed(4)} RON total` : null,
  };
};

// Format duration
const formatDuration = (seconds: number): string => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);

  if (days > 0) {
    return hours > 0 ? `${days}d ${hours}h` : `${days}d`;
  }
  return `${hours}h`;
};

// Format date
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return formatDistanceToNow(date, { addSuffix: true });
};

// Get delegation terms label
const getDelegationTermsLabel = (
  rewardDistribution: ERewardDistributionType,
  delegatedTask: EDelegatedTaskType
): string => {
  const rewardLabel =
    REWARD_DISTRIBUTION_LABELS[rewardDistribution] || "Unknown";
  const taskLabel = DELEGATED_TASK_LABELS[delegatedTask] || "Unknown";
  return `${rewardLabel} • ${taskLabel}`;
};

// Truncate address for display
const truncateAddress = (address: string): string => {
  if (!address) return "";
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
};

export function EnhancedRentalHistoryTable({
  events,
  pagination,
  isLoading,
  error,
  currentUserAddress,
  onRefresh,
  onNextPage,
  onPreviousPage,
  onFirstPage,
  onLastPage,
}: IEnhancedRentalHistoryTableProps) {
  // Transform events for display
  const displayEvents = useMemo(() => {
    if (!currentUserAddress) return [];

    // Debug: Log the first event to see what data we're getting
    if (events.length > 0) {
      console.log("Sample rental history event:", events[0]);
    }

    return events.map((event) => {
      const eventTypeInfo = getEventTypeInfo(
        event.event_type || event.eventType
      );

      // Handle rental data from nested rental object and event_data as fallback (API uses snake_case)
      const rental = event.rental as any; // Type as any to avoid strict typing issues
      const eventData = event.event_data || {};

      const ownerAddress =
        rental?.owner_address ||
        eventData.ownerAddress ||
        event.actor_address ||
        event.actorAddress;
      const renterAddress =
        rental?.renter_address || eventData.renterAddress || null;
      const roninPrice = rental?.ronin_price || eventData.originalPrice || "0";
      const rentalPeriod =
        rental?.rental_period || eventData.rentalDuration || 86400; // Default to 1 day
      const rewardDistribution =
        rental?.reward_distribution ||
        eventData.originalTerms?.rewardDistribution ||
        1;
      const delegatedTask =
        rental?.delegated_task || eventData.originalTerms?.delegatedTask || 3;
      const chickenTokenId = rental?.chicken_token_id || 0;

      // Add null checks before calling toLowerCase()
      const isOwner =
        ownerAddress?.toLowerCase() === currentUserAddress?.toLowerCase();
      const price = formatPrice(roninPrice, rentalPeriod);

      return {
        ...event,
        eventTypeInfo,
        isOwner,
        price,
        duration: formatDuration(rentalPeriod),
        date: formatDate(event.created_at || event.createdAt),
        delegationTerms: getDelegationTermsLabel(
          rewardDistribution,
          delegatedTask
        ),
        chickenType: String(
          event.chickenMetadata?.attributes?.find(
            (attr: any) => attr.trait_type === "Type"
          )?.value || "Unknown"
        ),
        otherParty: isOwner ? renterAddress : ownerAddress,
        // Add the extracted fields for easier access
        ownerAddress,
        renterAddress,
        roninPrice,
        rentalPeriod,
        rewardDistribution,
        delegatedTask,
        chickenTokenId,
      };
    });
  }, [events, currentUserAddress]);

  return (
    <div className="space-y-4">
      {/* Loading State */}
      {isLoading ? (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">
            Loading delegation history...
          </div>
        </div>
      ) : displayEvents.length > 0 ? (
        <>
          {/* Table */}
          <div className="bg-stone-800 rounded-lg overflow-hidden">
            <Table aria-label="Enhanced delegation history table">
              <Table.Header>
                <Table.Column isRowHeader={true}>Chicken</Table.Column>
                <Table.Column>Event</Table.Column>
                <Table.Column>Price</Table.Column>
                <Table.Column>Duration</Table.Column>
                <Table.Column>Date</Table.Column>
                <Table.Column>Terms</Table.Column>
                <Table.Column>Details</Table.Column>
              </Table.Header>
              <Table.Body>
                {displayEvents.map((event) => (
                  <Table.Row key={`${event.id}-${event.eventType}`}>
                    {/* Chicken */}
                    <Table.Cell>
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 rounded-lg overflow-hidden bg-stone-700 flex-shrink-0">
                          {event.chickenMetadata?.image ? (
                            <img
                              src={event.chickenMetadata.image}
                              alt={`Chicken #${event.chickenTokenId}`}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-stone-400">
                              <Gamepad2 size={20} />
                            </div>
                          )}
                        </div>
                        <div>
                          <div className="font-medium text-white">
                            #{event.chicken_token_id}
                          </div>
                          <div className="text-sm text-gray-400">
                            {event.chickenType}
                          </div>
                        </div>
                      </div>
                    </Table.Cell>

                    {/* Event */}
                    <Table.Cell>
                      <div className="flex items-center gap-2">
                        <div
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${event.eventTypeInfo.bgColor} ${event.eventTypeInfo.textColor} border-0`}
                        >
                          <event.eventTypeInfo.icon
                            size={12}
                            className="mr-1"
                          />
                          {event.eventTypeInfo.label}
                        </div>
                      </div>
                      {event.description && (
                        <div className="text-xs text-gray-400 mt-1">
                          {event.description}
                        </div>
                      )}
                    </Table.Cell>

                    {/* Price */}
                    <Table.Cell>
                      <div className="text-white font-medium">
                        {event.price.daily}
                      </div>
                      {event.price.total && (
                        <div className="text-sm text-gray-400">
                          {event.price.total}
                        </div>
                      )}
                    </Table.Cell>

                    {/* Duration */}
                    <Table.Cell>
                      <div className="flex items-center gap-1 text-gray-300">
                        <Calendar size={14} />
                        <span>{event.duration}</span>
                      </div>
                    </Table.Cell>

                    {/* Date */}
                    <Table.Cell>
                      <div className="flex items-center gap-1 text-gray-300">
                        <Calendar size={14} />
                        <span>{event.date}</span>
                      </div>
                    </Table.Cell>

                    {/* Terms */}
                    <Table.Cell>
                      <div className="text-sm text-gray-300">
                        {event.delegationTerms}
                      </div>
                    </Table.Cell>

                    {/* Details */}
                    <Table.Cell>
                      <div className="flex items-center gap-2">
                        {event.otherParty && (
                          <div className="text-xs text-gray-400">
                            {event.isOwner ? "To: " : "From: "}
                            {truncateAddress(event.otherParty)}
                          </div>
                        )}
                        {event.transaction_hash && (
                          <a
                            href={getTransactionExplorerUrl(
                              event.transaction_hash
                            )}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-400 hover:text-blue-300"
                          >
                            <ExternalLink size={14} />
                          </a>
                        )}
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex items-center justify-between pt-4 border-t border-stone-700">
              <div className="text-sm text-gray-400">
                Showing{" "}
                {(pagination.currentPage - 1) * pagination.itemsPerPage + 1} to{" "}
                {Math.min(
                  pagination.currentPage * pagination.itemsPerPage,
                  pagination.totalItems
                )}{" "}
                of {pagination.totalItems} events
              </div>

              <div className="flex items-center gap-2">
                <Button
                  intent="secondary"
                  size="small"
                  isDisabled={!pagination.hasPreviousPage}
                  onPress={onFirstPage}
                >
                  First
                </Button>
                <Button
                  intent="secondary"
                  size="small"
                  isDisabled={!pagination.hasPreviousPage}
                  onPress={onPreviousPage}
                >
                  Previous
                </Button>
                <span className="text-sm text-gray-400 px-3">
                  Page {pagination.currentPage} of {pagination.totalPages}
                </span>
                <Button
                  intent="secondary"
                  size="small"
                  isDisabled={!pagination.hasNextPage}
                  onPress={onNextPage}
                >
                  Next
                </Button>
                <Button
                  intent="secondary"
                  size="small"
                  isDisabled={!pagination.hasNextPage}
                  onPress={onLastPage}
                >
                  Last
                </Button>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Calendar size={48} className="mx-auto mb-2" />
            No delegation history found
          </div>
          <p className="text-gray-500 text-sm">
            Your delegation activities will appear here once you start
            delegating chickens.
          </p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-900/20 border border-red-800 rounded-lg p-4">
          <div className="flex items-center gap-2 text-red-400 mb-2">
            <AlertCircle size={16} />
            <span className="font-medium">Error loading history</span>
          </div>
          <p className="text-red-300 text-sm">{error.message}</p>
          <Button
            intent="secondary"
            size="small"
            className="mt-3"
            onPress={onRefresh}
          >
            <RefreshCw size={14} className="mr-1" />
            Try Again
          </Button>
        </div>
      )}
    </div>
  );
}
