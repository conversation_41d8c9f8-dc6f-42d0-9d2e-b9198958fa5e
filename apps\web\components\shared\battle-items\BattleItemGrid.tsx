"use client";

import React from "react";
import { BattleItemCard } from "./BattleItemCard";
import { BATTLE_ITEMS } from "@/data/battle-items";
import useMatchmakingStore from "@/store/match-making";

interface BattleItemGridProps {
  className?: string;
}

export const BattleItemGrid: React.FC<BattleItemGridProps> = ({ className = "" }) => {
  const {
    selectedBattleItems,
    setBattleItem,
    isItemSelected,
    getNextAvailableSlot
  } = useMatchmakingStore();

  const handleItemClick = (itemId: number) => {
    // Check if item is already selected
    if (isItemSelected(itemId)) {
      return; // Don't allow selection of already selected items
    }

    // Find next available slot
    const nextSlot = getNextAvailableSlot();
    if (nextSlot !== null) {
      setBattleItem(nextSlot, itemId);
    }
  };

  return (
    <div className={`${className}`}>
      {/* Grid Header */}
      <div className="mb-4">
        <h3 className="text-white font-bold text-lg mb-2">Select Battle Items</h3>
        <p className="text-gray-400 text-sm">
          Choose up to 3 items to use during battle. Items will be used in order of selection.
        </p>
      </div>

      {/* Items Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
        {BATTLE_ITEMS.map((item) => {
          const isSelected = isItemSelected(item.id);
          const isDisabled = isSelected || getNextAvailableSlot() === null;

          return (
            <BattleItemCard
              key={item.id}
              item={item}
              isSelected={isSelected}
              isDisabled={isDisabled}
              onClick={() => handleItemClick(item.id)}
              className="min-h-[200px] sm:min-h-[180px]"
            />
          );
        })}
      </div>

      {/* Selection Status */}
      <div className="mt-4 text-center">
        <p className="text-gray-400 text-sm">
          Selected: {selectedBattleItems.filter(item => item !== -1).length} / 3 items
        </p>
      </div>
    </div>
  );
};