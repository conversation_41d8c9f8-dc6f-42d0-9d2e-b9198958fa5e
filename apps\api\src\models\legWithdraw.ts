import mongoose from "mongoose";

const { Schema, model } = mongoose;

const LegendaryFeatherClaim = new Schema(
  {
    address: { type: String, required: true },
    legendaryFeathers: { type: Number, default: 0 },
    distributed: { type: Boolean, default: false },
    transactionHash:{ type: String, default :null },
  },
  { timestamps: true }
);

export default model("LegendaryFeatherClaim", LegendaryFeatherClaim);
