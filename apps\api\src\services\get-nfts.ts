import axios, { AxiosError } from "axios";
import { env } from "../env";
import { NFT } from "../types/nfts";
import { Address } from "viem";

// Define constants
const PAGE_LIMIT = 200;
const BASE_URL = `${env.SKYNET_API}/ronin/web3/v2/accounts`;

// Improved type definitions
interface PaginationInfo {
  nextCursor: string | null;
}

interface ApiResponse {
  result: {
    items: NFT[];
    paging: PaginationInfo;
  };
}

// Create axios instance with common config
const apiClient = axios.create({
  headers: {
    "X-API-KEY": env.SKYMAVIS_API,
  },
  timeout: 10000, // 10 second timeout
});

/**
 * Fetches NFTs for a given address with pagination support
 * @param address The wallet address to fetch NFTs for
 * @returns Promise containing array of NFTs or null if error occurs
 */
export const getNfts = async ({
  address,
}: {
  address: Address;
}): Promise<NFT[] | null> => {
  const allNfts: NFT[] = [];
  let nextCursor: string | null = null;

  try {
    do {
      const url = new URL(
        `${BASE_URL}/${address}/contracts/${env.CHICKEN_CONTRACT}/tokens`
      );

      // Set query parameters
      url.searchParams.set("limit", PAGE_LIMIT.toString());
      if (nextCursor) {
        url.searchParams.set("cursor", nextCursor);
      }

      const { data } = await apiClient.get<ApiResponse>(url.toString());

      // Use push for better performance than spread
      allNfts.push(...data.result.items);
      nextCursor = data.result.paging.nextCursor;
    } while (nextCursor);

    return allNfts;
  } catch (error) {
    const axiosError = error as AxiosError;
    console.error("Error fetching NFTs:", {
      status: axiosError.response?.status,
      message: axiosError.message,
      address,
    });
    return null;
  }
};
