import {
  Address,
  createPublicClient,
  http,
  Log,
  decodeEventLog,
  getAddress,
} from "viem";
import { env } from "../env";
import { ronin, saigon } from "viem/chains";
import <PERSON><PERSON><PERSON> from "../abi/Chicken.abi.json";
import { UTCTimestamp } from "../utils/timestamp-utc";
import Transfer from "../models/event";

// Define event argument types
interface BaseEventArgs {
  tokenId: bigint;
}

interface ApprovalEventArgs extends BaseEventArgs {
  approved: Address;
  owner: Address;
}

interface ApprovalAllEventArgs extends BaseEventArgs {
  operator: Address;
  owner: Address;
  approved: boolean;
}

interface TransferEventArgs extends BaseEventArgs {
  from: Address;
  to: Address;
}

// Initialize client
const client = createPublicClient({
  chain: ronin,
  transport: http(`${env.RPC}?apikey=${env.SKYMAVIS_API}`),
});

// Generic log processor
const processLogs = async <T extends BaseEventArgs>(
  logs: Log[],
  eventName: string,
  transformArgs: (args: T) => Record<string, any> | null
) => {
  if (!logs?.length) return;

  const epoch = UTCTimestamp();

  const insertLogs = logs
    .map((log) => {
      try {
        const decodedLog = decodeEventLog({
          abi: ChickenAbi,
          data: log.data,
          topics: log.topics,
        });

        const args = decodedLog.args as unknown as T;
        if (!args) {
          console.log(`Decoded log has no args: ${JSON.stringify(log)}`);
          return null;
        }
        const cleanArgs = transformArgs(args);
        if (cleanArgs) {
          return {
            epoch,
            data: {
              blockNumber: Number(log.blockNumber),
              transactionHash: log.transactionHash,
              tokenId: Number(args.tokenId),
              ...cleanArgs,
            },
          };
        }
        return null;
      } catch (error) {
        console.log(`Failed to decode log: ${error}`);
        return null;
      }
    })
    .filter(Boolean);

  if (insertLogs.length) {
    await Transfer.insertMany(insertLogs);
    console.log(
      `Done processed new ${eventName} event: ${JSON.stringify(insertLogs)}`
    );
  }
};

// Define event configurations
interface EventConfig<T extends BaseEventArgs> {
  transformArgs: (args: T) => Record<string, any> | null;
}

const eventConfigs: {
  Approval: EventConfig<ApprovalEventArgs>;
  ApprovalForAll: EventConfig<ApprovalAllEventArgs>;
  Transfer: EventConfig<TransferEventArgs>;
} = {
  Approval: {
    transformArgs: (args: ApprovalEventArgs) => {
      const CA = getAddress(env.MARKETPLACE_CONTRACT);

      if (args.approved === CA) {
        return {
          from: args.owner,
          to: args.approved,
          eventType: "Approval",
        };
      } else {
        return null;
      }
    },
  },
  ApprovalForAll: {
    transformArgs: (args: ApprovalAllEventArgs) => {
      const CA = getAddress(env.MARKETPLACE_CONTRACT);
      if (args.operator === CA) {
        return {
          from: args.owner,
          to: args.operator,
          approved: args.approved,
          eventType: "ApprovalForAll",
        };
      } else {
        return null;
      }
    },
  },
  Transfer: {
    transformArgs: (args: TransferEventArgs) => ({
      from: args.from,
      to: args.to,
    }),
  },
};

export const startEventListener = async () => {
  (
    Object.entries(eventConfigs) as [
      keyof typeof eventConfigs,
      EventConfig<BaseEventArgs>,
    ][]
  ).forEach(([eventName, config]) => {
    client.watchContractEvent({
      address: env.CHICKEN_CONTRACT as Address,
      abi: ChickenAbi,
      eventName,
      onLogs: async (logs) => {
        try {
          await processLogs(logs, eventName, config.transformArgs);
        } catch (error) {
          console.log(`Can't process new ${eventName} event: ${error}`);
        }
      },
    });
  });
};
