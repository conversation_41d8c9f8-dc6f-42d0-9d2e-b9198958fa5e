"use client";

import { useState, useMemo, useCallback } from "react";
import { ChickensData } from "@/types/chicken.type";
import { IRental } from "../types/delegation.types";

/**
 * Interface for bulk action selection state
 */
export interface IBulkActionSelection {
  [chickenTokenId: number]: boolean;
}

/**
 * Interface for chicken with action eligibility
 */
export interface IChickenWithActions extends ChickensData {
  rentalInfo?: IRental;
  canCancelDelegation: boolean;
  canUnlistFromMarket: boolean;
  actionReason?: string; // Reason why action is/isn't available
}

/**
 * Interface for bulk action summary
 */
export interface IBulkActionSummary {
  totalSelected: number;
  canCancelDelegation: number;
  canUnlistFromMarket: number;
  ineligible: number;
}

/**
 * Hook for managing bulk action selection state and eligibility
 */
export const useBulkActionSelection = (
  chickens: ChickensData[],
  ownedRentals: IRental[] = []
) => {
  const [isBulkMode, setIsBulkMode] = useState(false);
  const [selectedChickens, setSelectedChickens] = useState<IBulkActionSelection>({});

  // Create a map of chicken token IDs to rental info for quick lookup
  const rentalMap = useMemo(() => {
    const map = new Map<number, IRental>();
    ownedRentals.forEach((rental) => {
      map.set(rental.chickenTokenId, rental);
    });
    return map;
  }, [ownedRentals]);

  // Enhance chickens with action eligibility
  const chickensWithActions = useMemo((): IChickenWithActions[] => {
    return chickens.map((chicken) => {
      const rentalInfo = rentalMap.get(chicken.tokenId);
      
      let canCancelDelegation = false;
      let canUnlistFromMarket = false;
      let actionReason = "Available for actions";

      if (rentalInfo) {
        // Check if it's a delegation (roninPrice = "0") that's currently rented
        const isDelegation = rentalInfo.roninPrice === "0";
        const isRented = rentalInfo.status === 1; // RENTED status
        const isListed = rentalInfo.status === 0; // AVAILABLE status

        if (isDelegation && isRented) {
          canCancelDelegation = true;
          actionReason = "Can cancel delegation";
        } else if (!isDelegation && isListed) {
          canUnlistFromMarket = true;
          actionReason = "Can unlist from market";
        } else if (!isDelegation && isRented) {
          actionReason = "Currently rented - cannot unlist";
        } else {
          actionReason = "No active rental or delegation";
        }
      } else {
        actionReason = "Not listed or delegated";
      }

      return {
        ...chicken,
        rentalInfo,
        canCancelDelegation,
        canUnlistFromMarket,
        actionReason,
      };
    });
  }, [chickens, rentalMap]);

  // Get chickens that can be acted upon
  const actionableChickens = useMemo(() => {
    return chickensWithActions.filter(
      (chicken) => chicken.canCancelDelegation || chicken.canUnlistFromMarket
    );
  }, [chickensWithActions]);

  // Get currently selected chickens with their data
  const selectedChickensData = useMemo(() => {
    return chickensWithActions.filter(
      (chicken) => selectedChickens[chicken.tokenId]
    );
  }, [chickensWithActions, selectedChickens]);

  // Calculate bulk action summary
  const bulkActionSummary = useMemo((): IBulkActionSummary => {
    const selected = selectedChickensData;
    return {
      totalSelected: selected.length,
      canCancelDelegation: selected.filter((c) => c.canCancelDelegation).length,
      canUnlistFromMarket: selected.filter((c) => c.canUnlistFromMarket).length,
      ineligible: selected.filter(
        (c) => !c.canCancelDelegation && !c.canUnlistFromMarket
      ).length,
    };
  }, [selectedChickensData]);

  // Get rental IDs for selected chickens that can be cancelled
  const selectedCancellationRentalIds = useMemo(() => {
    return selectedChickensData
      .filter((chicken) => chicken.canCancelDelegation && chicken.rentalInfo)
      .map((chicken) => chicken.rentalInfo!.id);
  }, [selectedChickensData]);

  // Get rental IDs for selected chickens that can be unlisted
  const selectedUnlistingRentalIds = useMemo(() => {
    return selectedChickensData
      .filter((chicken) => chicken.canUnlistFromMarket && chicken.rentalInfo)
      .map((chicken) => chicken.rentalInfo!.id);
  }, [selectedChickensData]);

  // Toggle bulk mode
  const toggleBulkMode = useCallback(() => {
    setIsBulkMode((prev) => {
      if (prev) {
        // Exiting bulk mode - clear selections
        setSelectedChickens({});
      }
      return !prev;
    });
  }, []);

  // Toggle individual chicken selection
  const toggleChickenSelection = useCallback((chickenTokenId: number) => {
    setSelectedChickens((prev) => ({
      ...prev,
      [chickenTokenId]: !prev[chickenTokenId],
    }));
  }, []);

  // Select all actionable chickens
  const selectAllActionable = useCallback(() => {
    const newSelection: IBulkActionSelection = {};
    actionableChickens.forEach((chicken) => {
      newSelection[chicken.tokenId] = true;
    });
    setSelectedChickens(newSelection);
  }, [actionableChickens]);

  // Select all chickens that can be cancelled
  const selectAllCancellable = useCallback(() => {
    const newSelection: IBulkActionSelection = {};
    chickensWithActions
      .filter((chicken) => chicken.canCancelDelegation)
      .forEach((chicken) => {
        newSelection[chicken.tokenId] = true;
      });
    setSelectedChickens(newSelection);
  }, [chickensWithActions]);

  // Select all chickens that can be unlisted
  const selectAllUnlistable = useCallback(() => {
    const newSelection: IBulkActionSelection = {};
    chickensWithActions
      .filter((chicken) => chicken.canUnlistFromMarket)
      .forEach((chicken) => {
        newSelection[chicken.tokenId] = true;
      });
    setSelectedChickens(newSelection);
  }, [chickensWithActions]);

  // Clear all selections
  const clearSelection = useCallback(() => {
    setSelectedChickens({});
  }, []);

  // Check if a chicken is selected
  const isChickenSelected = useCallback(
    (chickenTokenId: number) => {
      return selectedChickens[chickenTokenId] || false;
    },
    [selectedChickens]
  );

  // Check if a chicken can be selected (is actionable)
  const canSelectChicken = useCallback(
    (chickenTokenId: number) => {
      const chicken = chickensWithActions.find((c) => c.tokenId === chickenTokenId);
      return chicken ? (chicken.canCancelDelegation || chicken.canUnlistFromMarket) : false;
    },
    [chickensWithActions]
  );

  // Get action reason for a chicken
  const getChickenActionReason = useCallback(
    (chickenTokenId: number) => {
      const chicken = chickensWithActions.find((c) => c.tokenId === chickenTokenId);
      return chicken?.actionReason || "Unknown status";
    },
    [chickensWithActions]
  );

  return {
    // State
    isBulkMode,
    selectedChickens,
    
    // Enhanced data
    chickensWithActions,
    actionableChickens,
    selectedChickensData,
    
    // Summary
    bulkActionSummary,
    selectedCancellationRentalIds,
    selectedUnlistingRentalIds,
    
    // Actions
    toggleBulkMode,
    toggleChickenSelection,
    selectAllActionable,
    selectAllCancellable,
    selectAllUnlistable,
    clearSelection,
    
    // Utilities
    isChickenSelected,
    canSelectChicken,
    getChickenActionReason,
  };
};
