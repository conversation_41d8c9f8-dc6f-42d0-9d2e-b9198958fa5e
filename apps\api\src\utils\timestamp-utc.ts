export function UTCTimestamp() {
  const now = new Date();

  // Create a new Date object for the start of the current day in UTC
  const startOfDayUTC = new Date(
    Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate())
  );

  // Get the timestamp in seconds
  const timestampInSeconds = Math.floor(startOfDayUTC.getTime() / 1000);
  return timestampInSeconds;
}

export function getYesterdayUTCTimestamp() {
  const now = new Date();

  // Create a new Date object for the start of yesterday in UTC
  const startOfYesterdayUTC = new Date(
    Date.UTC(
      now.getUTCFullYear(),
      now.getUTCMonth(),
      now.getUTCDate() - 1 // Subtract 1 day to get yesterday
    )
  );

  // Get the timestamp in seconds
  const timestampInSeconds = Math.floor(startOfYesterdayUTC.getTime() / 1000);
  return timestampInSeconds;
}
