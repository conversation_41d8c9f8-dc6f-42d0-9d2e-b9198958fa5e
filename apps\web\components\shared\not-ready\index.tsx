// components/shared/NotReady.tsx
import { useEffect, useState } from "react";

type NotReadyProps = {
  isOpen: boolean;
  onClose: () => void;
};

export default function NotReady({ isOpen, onClose }: NotReadyProps) {
  const [visible, setVisible] = useState(isOpen);

  useEffect(() => {
    if (isOpen) {
      setVisible(true);
      const timer = setTimeout(() => {
        setVisible(false);
        onClose();
      }, 3000); // Fade out after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [isOpen, onClose]);

  if (!visible) return null;

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg p-6 flex flex-col items-center">
        <img
          src="/images/not-ready.gif"
          alt="Not Ready"
          className="w-96 h-96"
        />
        <p className="text-white mt-4">This feature is not ready yet!</p>
      </div>
    </div>
  );
}
