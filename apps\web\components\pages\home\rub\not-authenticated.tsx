"use client";
import { Text } from "react-aria-components";
import { NftCarousel } from "./nft-carousel";
import { Grid } from "@/components/ui";
import Box from "@/components/shared/box";

import { cn } from "@/utils/classes";
import { RubHeader } from "./header";

export function NotAuthenticatedRubContent() {
  const chickens = [];
  const totalFeathers = 0;
  return (
    <Grid.Item className="relative h-full">
      <Box>
       <RubHeader />
        <div className="flex justify-center">
          <NftCarousel chickens={[]} />
        </div>
        <div className="flex flex-col items-center w-full">
          <p className="font-bold text-lg">
            Rubbing your chickens give you Feathers.
          </p>
          <p className="text-sm w-full md:w-[90%] text-center text-muted-fg">
            You have{" "}
            <span className="font-bold text-white">
              {chickens.length} {chickens.length > 1 ? "chickens" : "chicken"}
            </span>{" "}
            ready to rub and can earn{" "}
            <span className="font-bold text-white">
              {totalFeathers} {totalFeathers > 1 ? "Feathers" : "Feather"}
            </span>
            {totalFeathers > 1 ? ". Click the button below to start!" : "."}
          </p>
        </div>
        <div className="flex items-center justify-center mt-4 mb-8">
          <button
            className={cn(
              "w-full md:w-fit px-8 py-3 text-lg font-bold text-black uppercase rounded-lg bg-yellow-400 hover:bg-yellow-500 transition-all duration-300 transform hover:scale-105",
              chickens.length === 0 &&
                " opacity-60 forced-colors:disabled:text-[GrayText] cursor-not-allowed pointer-events-none"
            )}
            style={{
              boxShadow:
                "0 0 15px rgba(247, 203, 66, 0.7), 0 0 30px rgba(247, 203, 66, 0.4)",
              textShadow: "0 0 2px rgba(0,0,0,0.2)",
            }}
          >
            RUB YOUR CHICKENS
          </button>
        </div>
      </Box>

      {/* Blur overlay */}
      <div className="absolute inset-0 backdrop-blur-md bg-black/50 z-20 h-full rounded-2xl ">
        {/* Optional: Add a message for non-authenticated users */}
        <div className="flex items-center justify-center h-full">
          <p className="text-white text-xl font-Poppins">
            Please login to get started!
          </p>
        </div>
      </div>
    </Grid.Item>
  );
}
