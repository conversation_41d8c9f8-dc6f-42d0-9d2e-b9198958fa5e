"use client";

import { publicClient } from "@/lib/publicClient";
import { Abi, Address } from "viem";

/**
 * Client-side utility for reading contract data
 */
export const readContract = async <T = unknown>({
  address,
  abi,
  functionName,
  args = [],
}: {
  address: Address;
  abi: Abi;
  functionName: string;
  args?: unknown[];
}): Promise<T> => {
  try {
    const result = await publicClient.readContract({
      address,
      abi,
      functionName,
      args: args || [],
    });

    return result as T;
  } catch (error) {
    console.error(`Error reading contract ${functionName}:`, error);
    throw error;
  }
};

/**
 * Client-side utility for executing multicall
 */
export const executeMulticall = async <T = unknown[]>({
  calls,
}: {
  calls: {
    address: Address;
    abi: Abi;
    functionName: string;
    args?: unknown[];
  }[];
}): Promise<T> => {
  try {
    const results = await publicClient.multicall({
      contracts: calls.map((call) => ({
        address: call.address,
        abi: call.abi,
        functionName: call.functionName,
        args: call.args || [],
      })),
    });

    return results as unknown as T;
  } catch (error) {
    console.error("Error executing multicall:", error);
    throw error;
  }
};
