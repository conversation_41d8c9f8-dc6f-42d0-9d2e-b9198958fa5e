import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAccount, useWalletClient, usePublicClient } from "wagmi";
import { toast } from "sonner";
import { Address } from "viem";

import { rentalAbi } from "@/providers/web3/abi/rental-abi";
import { chain } from "@/providers/web3/web3-provider";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";

interface IActivationData {
  rentId: bigint;
  chickenId: bigint;
  ethPrice: bigint;
  insurancePrice: bigint;
  renterAddress: Address;
  ownerAddress: Address;
  signature: `0x${string}`;
}

/**
 * Hook for delegatees to activate their direct delegations
 * This executes the rentChicken call with 0 payment to activate the delegation
 */
export const useActivateDelegation = () => {
  const [isActivating, setIsActivating] = useState(false);
  const { address } = useAccount();
  const { data: walletClient } = useWalletClient();
  const publicClient = usePublicClient();
  const queryClient = useQueryClient();
  const { blockchainQuery } = useBlockchain();
  const { Disconnect } = useStateContext();

  // Get rental contract address from blockchain config
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  /**
   * Execute the delegation activation process:
   * 1. Validate that the user is the intended renter
   * 2. Execute rentChicken with 0 payment
   * 3. Handle success/error states
   */
  const executeActivateDelegation = async (activationData: IActivationData) => {
    try {
      if (!address || !walletClient || !publicClient) {
        toast.error("Cannot activate delegation", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return { success: false, error: "Wallet not connected" };
      }

      if (!rentalAddress) {
        toast.error("Cannot activate delegation", {
          description: "Rental contract not configured",
          position: "top-right",
        });
        return { success: false, error: "Contract not configured" };
      }

      // Validate that the connected wallet is the intended renter
      if (
        address.toLowerCase() !== activationData.renterAddress.toLowerCase()
      ) {
        toast.error("Cannot activate delegation", {
          description: "You are not the intended delegatee for this chicken",
          position: "top-right",
        });
        return { success: false, error: "Unauthorized renter" };
      }

      setIsActivating(true);

      // Step 1: Simulate the rent transaction
      toast.info("Validating delegation activation...", {
        description: "Checking transaction validity",
        position: "top-center",
      });

      // Prepare the RentChickenParams struct
      const rentParams = {
        rentId: activationData.rentId,
        chickenId: activationData.chickenId,
        ethPrice: activationData.ethPrice, // Should be 0 for direct delegation
        insurancePrice: activationData.insurancePrice, // Should be 0 for direct delegation
        renterAddress: activationData.renterAddress,
        ownerAddress: activationData.ownerAddress,
        signature: activationData.signature,
      };

      const simulateReq = await publicClient.simulateContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "rentChicken",
        args: [rentParams],
        value: 0n, // No payment for direct delegation
        chain,
        account: address,
      });

      // Step 2: Estimate gas for the transaction
      const gasEstimate = await publicClient.estimateContractGas({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "rentChicken",
        args: [rentParams],
        value: 0n,
        account: address,
      });

      // Add 15% buffer to gas estimate
      const gasWithBuffer = (gasEstimate * 115n) / 100n;

      const request = simulateReq.request;
      request.gas = gasWithBuffer;

      // Step 3: Execute the transaction
      toast.info("Activating delegation...", {
        description: "Please confirm the transaction in your wallet",
        position: "top-center",
      });

      const hash = await walletClient.writeContract(request);

      toast.info("Delegation activation sent", {
        description: `Transaction hash: ${hash}`,
        position: "top-center",
      });

      // Step 4: Wait for transaction confirmation
      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        confirmations: 1,
      });

      if (receipt.status === "success") {
        toast.success("Delegation activated successfully!", {
          description: "You now have access to the delegated chicken",
          position: "top-center",
        });

        // Invalidate queries with proper sequencing
        setTimeout(async () => {
          // First, invalidate and refetch chickenTokenIds (blockchain data) if needed
          await queryClient.invalidateQueries({
            queryKey: ["chickenTokenIds", "legacy", address],
          });
          await queryClient.invalidateQueries({
            queryKey: ["chickenTokenIds", "genesis", address],
          });

          // Then invalidate rental-related queries
          queryClient.invalidateQueries({ queryKey: ["my-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals", address] });
          queryClient.invalidateQueries({
            queryKey: ["chickenRentalStatuses"],
          });
        }, 500);

        return {
          success: true,
          hash,
          receipt,
          message: "Delegation activated successfully",
        };
      } else {
        throw new Error("Transaction failed");
      }
    } catch (error: unknown) {
      console.error("Delegation activation error:", error);

      let errorMessage = "Failed to activate delegation";
      let errorDescription = "Please try again";

      if (error instanceof Error) {
        if (error.message.includes("User rejected")) {
          errorMessage = "Transaction cancelled";
          errorDescription = "You cancelled the transaction";
        } else if (error.message.includes("insufficient funds")) {
          errorMessage = "Insufficient funds";
          errorDescription = "You don't have enough ETH for gas fees";
        } else if (error.message.includes("ErrRentIdAlreadyUsed")) {
          errorMessage = "Delegation already activated";
          errorDescription = "This delegation has already been activated";
        } else {
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        position: "top-center",
      });

      return { success: false, error };
    } finally {
      setIsActivating(false);
    }
  };

  // Mutation for React Query integration
  const activateDelegationMutation = useMutation({
    mutationFn: executeActivateDelegation,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Activate delegation mutation error:", error);
    },
  });

  return {
    executeActivateDelegation,
    activateDelegationMutation,
    isActivating,
    rentalAddress,
  };
};
