import { NextRequest, NextResponse } from "next/server";
import { createPublicClient, http } from "viem";
import { ronin } from "viem/chains";
import RNSPublicResolverAbi from "@/abi/RnsPublicResolver.abi.json";
// @ts-ignore
import { namehash } from "@roninnetwork/rnsjs";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ address: string }> }
) {
  const { address } = await params;
  const publicClient = createPublicClient({
    chain: ronin,
    transport: http(),
  });

  if (address) {
    const reverseNode = `${address.toLowerCase().substring(2)}.addr.reverse`;
    const normAddress = namehash(reverseNode);

    try {
      const rns = await publicClient.readContract({
        address: "0xadb077d236d9e81fb24b96ae9cb8089ab9942d48",
        abi: RNSPublicResolverAbi,
        functionName: "name",
        args: [normAddress],
      });

      if (rns) {
        const data = {
          rns,
          address: address.toLowerCase(),
        };

        return NextResponse.json(data, { status: 200 });
      }
    } catch (error) {
      return NextResponse.json(
        {
          status: false,
          responseCode: 400,
          message: "Bad request",
        },
        { status: 400 }
      );
    }
  }
  return NextResponse.json(
    {
      status: false,
      responseCode: 400,
      message: "Bad request",
    },
    { status: 400 }
  );
}
