"use client";

import { useState } from "react";
import {
  AdminLayout,
  IBreedingFee,
  IUpdateBreedingFeePayload,
  useBreedingFees,
  useUpdateBreedingFee,
} from "@/features/admin";
import { Button } from "@/components/ui/button";
import { Popover } from "@/components/ui/popover";
import { toast } from "sonner";

export default function BreedingFeesPage() {
  const { data: fees, isLoading } = useBreedingFees();
  const updateMutation = useUpdateBreedingFee();

  // We don't need to track the editing fee anymore since we're using popovers
  // but we keep the setter for the startEditing function
  const [updateForm, setUpdateForm] = useState<IUpdateBreedingFeePayload>({
    breedingFeeId: 0,
    cockUsd: 0,
    feathers: 0,
  });

  // Handle update form changes
  const handleUpdateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUpdateForm((prev) => ({
      ...prev,
      [name]: parseFloat(value),
    }));
  };

  // Start editing a fee
  const startEditing = (fee: IBreedingFee) => {
    // Set the form values based on the fee
    setUpdateForm({
      breedingFeeId: fee.id,
      cockUsd: fee.cock_usd !== undefined ? fee.cock_usd : 0,
      feathers: fee.feathers !== undefined ? fee.feathers : 0,
    });
  };

  // Cancel editing
  const cancelEditing = () => {
    // Reset form values
    setUpdateForm({
      breedingFeeId: 0,
      cockUsd: 0,
      feathers: 0,
    });
  };

  // Submit update form
  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await updateMutation.mutateAsync(updateForm);
      toast.success("Breeding fee updated successfully");
      // Reset form values
      setUpdateForm({
        breedingFeeId: 0,
        cockUsd: 0,
        feathers: 0,
      });
    } catch (error) {
      toast.error("Failed to update breeding fee");
      console.error(error);
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">Breeding Fees</h1>
          <p className="text-gray-400">
            Manage breeding fees based on breed count
          </p>
        </div>

        {/* Fees Table */}
        <div className="bg-[#1E1E1E] rounded-lg p-6 border border-[#333]">
          <h2 className="text-xl font-semibold mb-4">Breeding Fees</h2>

          {isLoading ? (
            <p>Loading fees...</p>
          ) : fees && fees.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-[#2D2D2D] text-left">
                    <th className="px-4 py-2 border-b border-[#444]">
                      Breed Count
                    </th>
                    <th className="px-4 py-2 border-b border-[#444]">
                      COCK (USD)
                    </th>
                    <th className="px-4 py-2 border-b border-[#444]">COCK</th>
                    <th className="px-4 py-2 border-b border-[#444]">
                      Feathers
                    </th>
                    <th className="px-4 py-2 border-b border-[#444]">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {fees?.map((fee) => (
                    <tr
                      key={fee.id}
                      className="border-b border-[#333] hover:bg-[#2A2A2A]"
                    >
                      <td className="px-4 py-3">{fee.count}</td>
                      <td className="px-4 py-3">
                        $
                        {fee.cock_usd !== undefined
                          ? fee.cock_usd.toFixed(2)
                          : "0.00"}
                      </td>
                      <td className="px-4 py-3">
                        {fee.cock !== undefined
                          ? fee.cock.toLocaleString()
                          : "0.00"}
                      </td>
                      <td className="px-4 py-3">{fee.feathers}</td>
                      <td className="px-4 py-3">
                        <div className="flex space-x-2">
                          <Popover>
                            {/* @ts-expect-error - The type error is a false positive, this pattern works correctly */}
                            <Popover.Trigger asChild>
                              <Button
                                intent="secondary"
                                size="extra-small"
                                onPress={() => startEditing(fee)}
                              >
                                Edit
                              </Button>
                            </Popover.Trigger>
                            <Popover.Content className="p-6 w-[400px] bg-[#1E1E1E] border-[#333]">
                              <div className="space-y-4">
                                <h2 className="text-xl font-semibold mb-4">
                                  Edit Fee for Breed Count {fee.count}
                                </h2>
                                <form
                                  onSubmit={handleUpdate}
                                  className="space-y-4"
                                >
                                  <div className="grid grid-cols-1 gap-4">
                                    <div>
                                      <label
                                        htmlFor="cockUsd"
                                        className="block mb-2 text-sm font-medium"
                                      >
                                        COCK (USD)
                                      </label>
                                      <input
                                        type="number"
                                        id="cockUsd"
                                        name="cockUsd"
                                        value={updateForm.cockUsd}
                                        onChange={handleUpdateChange}
                                        className="w-full px-4 py-2 bg-[#2D2D2D] border border-[#444] rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                                        min="0"
                                        step="0.01"
                                        required
                                      />
                                      <p className="mt-1 text-sm text-gray-400">
                                        The USD value will be converted to COCK
                                        tokens automatically
                                      </p>
                                    </div>
                                    <div>
                                      <label
                                        htmlFor="feathers"
                                        className="block mb-2 text-sm font-medium"
                                      >
                                        Feathers
                                      </label>
                                      <input
                                        type="number"
                                        id="feathers"
                                        name="feathers"
                                        value={updateForm.feathers}
                                        onChange={handleUpdateChange}
                                        className="w-full px-4 py-2 bg-[#2D2D2D] border border-[#444] rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                                        min="0"
                                        required
                                      />
                                    </div>
                                  </div>
                                  <div className="flex space-x-4">
                                    <Button
                                      intent="primary"
                                      type="submit"
                                      isDisabled={updateMutation.isPending}
                                    >
                                      {updateMutation.isPending
                                        ? "Updating..."
                                        : "Update Fee"}
                                    </Button>
                                    <Button
                                      intent="secondary"
                                      type="button"
                                      onPress={() => {
                                        cancelEditing();
                                        // Close the popover
                                        document.body.click();
                                      }}
                                    >
                                      Cancel
                                    </Button>
                                  </div>
                                </form>
                              </div>
                            </Popover.Content>
                          </Popover>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p>No fees found.</p>
          )}
        </div>

        {/* Edit Form is now in a popover */}
      </div>
    </AdminLayout>
  );
}
