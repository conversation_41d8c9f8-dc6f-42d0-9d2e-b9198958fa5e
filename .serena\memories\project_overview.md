# Sabong Saga Project Overview

## Purpose
Sabong Saga is a blockchain-based gaming application built on the Ronin network, featuring chicken breeding, battling, and NFT management. It's a play-to-earn game where users can breed chickens, participate in battles, and manage their digital assets.

## Tech Stack
- **Frontend**: Next.js 14+ with TypeScript
- **UI Framework**: Custom UI components with Tailwind CSS
- **State Management**: Hookstate for global state
- **Blockchain**: Viem for Web3 interactions on Ronin network
- **Data Fetching**: React Query (TanStack Query)
- **Package Manager**: pnpm
- **Build System**: Turborepo monorepo
- **Styling**: Tailwind CSS with custom design system

## Project Structure
- **Monorepo**: Uses Turborepo with multiple apps and packages
- **apps/web**: Main frontend application
- **apps/api**: Backend API service
- **apps/marketplace**: Marketplace service
- **apps/maintenance**: Maintenance page
- **packages**: Shared packages and utilities

## Key Features
- Chicken breeding and genetics system
- NFT inventory management
- Delegation/rental system for chickens
- Battle system with items and stats
- Rewards and claiming system
- Admin panel for game management
- Crafting system for items and potions