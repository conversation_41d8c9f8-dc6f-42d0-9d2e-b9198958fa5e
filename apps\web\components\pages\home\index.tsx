'use client'

import { Grid } from "@/components/ui";
import { memo, PropsWithChildren } from "react";

function Home(props: PropsWithChildren) {
  return (
    <div className="relative  flex max-w-[1680px] w-full mx-auto mt-10 px-4 md:px-2 lg:px-0">
      <Grid
        columns={{
          initial: 1,
          sm: 1,
          md: 1,
          lg: 3,
        }}
        gap={{
          initial: 6,
          md: 4,
        }}
        className="w-full"
      >
       {props.children}
      </Grid>
    </div>
  );
}

export default memo(Home)