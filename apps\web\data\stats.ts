export const beakStats: Record<
  string,
  { attack: number; defensePenalty: number; hpPenalty: number }
> = {
  "Chim Lạc": { attack: 100, defensePenalty: -12, hpPenalty: -11 },
  Thunderbird: { attack: 100, defensePenalty: -12, hpPenalty: -11 },
  Adarna: { attack: 100, defensePenalty: -12, hpPenalty: -11 },
  Sarimanok: { attack: 100, defensePenalty: -12, hpPenalty: -11 },
  Wormtongue: { attack: 80, defensePenalty: -8, hpPenalty: -7 },
  Raven: { attack: 80, defensePenalty: -8, hpPenalty: -7 },
  Ironclad: { attack: 80, defensePenalty: -8, hpPenalty: -7 },
  "Piercing Fang": { attack: 80, defensePenalty: -8, hpPenalty: -7 },
  Haki: { attack: 79, defensePenalty: -6, hpPenalty: -5 },
  Nightwave: { attack: 79, defensePenalty: -6, hpPenalty: -5 },
  Touca: { attack: 79, defensePenalty: -6, hpPenalty: -5 },
  "Blade Spire": { attack: 79, defensePenalty: -6, hpPenalty: -5 },
  Aurora: { attack: 78, defensePenalty: -4, hpPenalty: -3 },
  Verdant: { attack: 78, defensePenalty: -4, hpPenalty: -3 },
  Greenbill: { attack: 78, defensePenalty: -4, hpPenalty: -3 },
  Bluelip: { attack: 78, defensePenalty: -4, hpPenalty: -3 },
  Radiant: { attack: 77, defensePenalty: 0, hpPenalty: 0 },
  Flare: { attack: 77, defensePenalty: 0, hpPenalty: 0 },
  Ashfire: { attack: 77, defensePenalty: 0, hpPenalty: 0 },
  Boneblade: { attack: 77, defensePenalty: 0, hpPenalty: 0 },
};

export const combStats: Record<
  string,
  { cockrage: number; defensePenalty: number; ferocityPenalty: number }
> = {
  Minokawa: { cockrage: 100, defensePenalty: -12, ferocityPenalty: -11 },
  Adarna: { cockrage: 100, defensePenalty: -12, ferocityPenalty: -11 },
  Garuda: { cockrage: 100, defensePenalty: -12, ferocityPenalty: -11 },
  Simurgh: { cockrage: 100, defensePenalty: -12, ferocityPenalty: -11 },
  Suave: { cockrage: 80, defensePenalty: -8, ferocityPenalty: -7 },
  Single: { cockrage: 80, defensePenalty: -8, ferocityPenalty: -7 },
  Corona: { cockrage: 80, defensePenalty: -8, ferocityPenalty: -7 },
  Goodboy: { cockrage: 80, defensePenalty: -8, ferocityPenalty: -7 },
  Sasuke: { cockrage: 79, defensePenalty: -6, ferocityPenalty: -5 },
  Cubao: { cockrage: 79, defensePenalty: -6, ferocityPenalty: -5 },
  Igop: { cockrage: 79, defensePenalty: -6, ferocityPenalty: -5 },
  Hellboy: { cockrage: 79, defensePenalty: -6, ferocityPenalty: -5 },
  Spike: { cockrage: 78, defensePenalty: -4, ferocityPenalty: -3 },
  Raditz: { cockrage: 78, defensePenalty: -4, ferocityPenalty: -3 },
  "Super Sayang 4": {
    cockrage: 78,
    defensePenalty: -4,
    ferocityPenalty: -3,
  },
  "Power Geyser": { cockrage: 78, defensePenalty: -4, ferocityPenalty: -3 },
  Yugi: { cockrage: 77, defensePenalty: 0, ferocityPenalty: 0 },
  "Super Sayang 1": { cockrage: 77, defensePenalty: 0, ferocityPenalty: 0 },
  Killua: { cockrage: 77, defensePenalty: 0, ferocityPenalty: 0 },
  Sakuragi: { cockrage: 77, defensePenalty: 0, ferocityPenalty: 0 },
};

export const eyesStats: Record<
  string,
  { ferocity: number; evasionPenalty: number; speedPenalty: number }
> = {
  Garuda: { ferocity: 100, evasionPenalty: -12, speedPenalty: -11 },
  Minokawa: { ferocity: 100, evasionPenalty: -12, speedPenalty: -11 },
  Adarna: { ferocity: 100, evasionPenalty: -12, speedPenalty: -11 },
  Sarimanok: { ferocity: 100, evasionPenalty: -12, speedPenalty: -11 },
  Batak: { ferocity: 80, evasionPenalty: -8, speedPenalty: -7 },
  Bagyo: { ferocity: 80, evasionPenalty: -8, speedPenalty: -7 },
  Shookt: { ferocity: 80, evasionPenalty: -8, speedPenalty: -7 },
  Dyosa: { ferocity: 80, evasionPenalty: -8, speedPenalty: -7 },
  Silog: { ferocity: 79, evasionPenalty: -6, speedPenalty: -5 },
  Santelmo: { ferocity: 79, evasionPenalty: -6, speedPenalty: -5 },
  Tuko: { ferocity: 79, evasionPenalty: -6, speedPenalty: -5 },
  Peyups: { ferocity: 79, evasionPenalty: -6, speedPenalty: -5 },
  Agila: { ferocity: 78, evasionPenalty: -4, speedPenalty: -3 },
  Atenista: { ferocity: 78, evasionPenalty: -4, speedPenalty: -3 },
  Lasallista: { ferocity: 78, evasionPenalty: -4, speedPenalty: -3 },
  Wildfire: { ferocity: 78, evasionPenalty: -4, speedPenalty: -3 },
  Diwata: { ferocity: 77, evasionPenalty: 0, speedPenalty: 0 },
  Maxx: { ferocity: 77, evasionPenalty: 0, speedPenalty: 0 },
  Retokada: { ferocity: 77, evasionPenalty: 0, speedPenalty: 0 },
  Yinyang: { ferocity: 77, evasionPenalty: 0, speedPenalty: 0 },
};

export const feetStats: Record<
  string,
  { speed: number; hpPenalty: number; cockragePenalty: number }
> = {
  Buakaw: { speed: 100, hpPenalty: -12, cockragePenalty: -11 },
  "Alicanto Oro": { speed: 100, hpPenalty: -12, cockragePenalty: -11 },
  "Alicanto Plata": { speed: 100, hpPenalty: -12, cockragePenalty: -11 },
  Thunderbird: { speed: 100, hpPenalty: -12, cockragePenalty: -11 },
  Mahiwaga: { speed: 80, hpPenalty: -8, cockragePenalty: -7 },
  Luntian: { speed: 80, hpPenalty: -8, cockragePenalty: -7 },
  Makopa: { speed: 80, hpPenalty: -8, cockragePenalty: -7 },
  Sibat: { speed: 80, hpPenalty: -8, cockragePenalty: -7 },
  Cemani: { speed: 79, hpPenalty: -6, cockragePenalty: -5 },
  Pula: { speed: 79, hpPenalty: -6, cockragePenalty: -5 },
  Zenki: { speed: 79, hpPenalty: -6, cockragePenalty: -5 },
  Ember: { speed: 79, hpPenalty: -6, cockragePenalty: -5 },
  "Hepa Lane": { speed: 78, hpPenalty: -4, cockragePenalty: -3 },
  Kaliskis: { speed: 78, hpPenalty: -4, cockragePenalty: -3 },
  "Mewling Tiger": { speed: 78, hpPenalty: -4, cockragePenalty: -3 },
  Dionela: { speed: 78, hpPenalty: -4, cockragePenalty: -3 },
  Onyx: { speed: 77, hpPenalty: 0, cockragePenalty: 0 },
  Chernobyl: { speed: 77, hpPenalty: 0, cockragePenalty: 0 },
  Paleclaws: { speed: 77, hpPenalty: 0, cockragePenalty: 0 },
  Catriona: { speed: 77, hpPenalty: 0, cockragePenalty: 0 },
};

export const wingsStats: Record<
  string,
  { defense: number; attackPenalty: number; cockragePenalty: number }
> = {
  Adarna: { defense: 100, attackPenalty: -12, cockragePenalty: -11 },
  Minokawa: { defense: 100, attackPenalty: -12, cockragePenalty: -11 },
  Garuda: { defense: 100, attackPenalty: -12, cockragePenalty: -11 },
  Simurgh: { defense: 100, attackPenalty: -12, cockragePenalty: -11 },
  Mandingo: { defense: 80, attackPenalty: -8, cockragePenalty: -6 },
  Helena: { defense: 80, attackPenalty: -8, cockragePenalty: -6 },
  Potsu: { defense: 78, attackPenalty: -6, cockragePenalty: -4 },
  Johny: { defense: 78, attackPenalty: -6, cockragePenalty: -4 },
  Slenduh: { defense: 76, attackPenalty: -4, cockragePenalty: -2 },
  Awra: { defense: 76, attackPenalty: -4, cockragePenalty: -2 },
};

export const tailStats: Record<
  string,
  { evasion: number; attackPenalty: number; ferocityPenalty: number }
> = {
  Simurgh: { evasion: 100, attackPenalty: -12, ferocityPenalty: -11 },
  "Chim Lạc": { evasion: 100, attackPenalty: -12, ferocityPenalty: -11 },
  Minokawa: { evasion: 100, attackPenalty: -12, ferocityPenalty: -11 },
  Adarna: { evasion: 100, attackPenalty: -12, ferocityPenalty: -11 },
  Agave: { evasion: 80, attackPenalty: -8, ferocityPenalty: -6 },
  Rengoku: { evasion: 80, attackPenalty: -8, ferocityPenalty: -6 },
  Starjeatl: { evasion: 78, attackPenalty: -6, ferocityPenalty: -4 },
  Carota: { evasion: 78, attackPenalty: -6, ferocityPenalty: -4 },
  Abaniko: { evasion: 76, attackPenalty: -4, ferocityPenalty: -2 },
  Onagadori: { evasion: 76, attackPenalty: -4, ferocityPenalty: -2 },
};

export const bodyStats: Record<
  string,
  { hp: number; speedPenalty: number; evasionPenalty: number }
> = {
  Hoeltaf: { hp: 320, speedPenalty: -14, evasionPenalty: -12 },
  Chibidei: { hp: 315, speedPenalty: -12, evasionPenalty: -10 },
  Draken: { hp: 310, speedPenalty: -10, evasionPenalty: -8 },
  Emilia: { hp: 305, speedPenalty: -8, evasionPenalty: -6 },
  Badwitch: { hp: 300, speedPenalty: -6, evasionPenalty: -4 },
  Chummiest: { hp: 295, speedPenalty: -4, evasionPenalty: -2 },
  Wickid: { hp: 290, speedPenalty: -2, evasionPenalty: 0 },
  Jordi: { hp: 285, speedPenalty: 0, evasionPenalty: 0 },
};
