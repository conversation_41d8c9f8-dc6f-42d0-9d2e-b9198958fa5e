import { ethers, getAddress, getBytes, solidityPackedKeccak256 } from "ethers";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

const API_BASE_URL = process.env.HONO_API_ENDPOINT;

async function fetchApi(endpoint: string, options: RequestInit = {}) {
  return fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      "content-type": "application/json",
      ...options.headers,
    },
    cache: "no-store",
  });
}

function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

async function getMe(jwt: string) {
  return fetchApi("/api/me", {
    method: "GET",
    headers: { authorization: `Bearer ${jwt}` },
  });
}

export async function GET(request: NextRequest) {
  const c = await cookies();
  const jwt = c.get("jwt")?.value;
  if (!jwt) {
    return NextResponse.json(
      {
        status: false,
        responseCode: 401,
        message: "Bad request",
      },
      { status: 401 }
    );
  }

  const me = await getMe(jwt);

  if (me.ok) {
    const data = await me.json();

    const address = data.data.address;

    const owner = new ethers.Wallet(process.env.VERIFIER_KEY as string);
    const seed = getRandomNumber(1, 9999999999);
    const deadline = Math.floor(Date.now() / 1000) + 180;

    // Create the same hash as the contract
    const messageHash = ethers.keccak256(
      ethers.solidityPacked(
        ["uint256", "uint256", "address"],
        [seed, deadline, getAddress(address)]
      )
    );

    // Sign with the Ethereum message prefix (this is what toEthSignedMessageHash does)
    const signature = await owner.signMessage(ethers.getBytes(messageHash));

    return NextResponse.json({ deadline, seed, signature });
  }
  return NextResponse.json(
    {
      status: false,
      responseCode: 401,
      message: "Bad request",
    },
    { status: 401 }
  );
}
