"use client";

import React from "react";
import { Loader } from "@/components/ui/loader";

interface ILoadingSpinnerProps {
  size?: "small" | "medium" | "large" | "extra-large";
  className?: string;
}

export function LoadingSpinner({ 
  size = "medium", 
  className 
}: ILoadingSpinnerProps) {
  return (
    <Loader 
      variant="ring" 
      size={size} 
      className={className} 
      aria-label="Loading..." 
    />
  );
}
