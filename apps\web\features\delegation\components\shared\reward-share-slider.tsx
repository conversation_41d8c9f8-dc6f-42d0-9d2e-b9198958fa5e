"use client";

import { useState } from "react";
import { cn } from "ui";
import { User, Users } from "lucide-react";
// Using HTML5 range input for better compatibility

interface IRewardShareSliderProps {
  value: number; // Now represents delegatee feathers amount, not percentage
  onChange: (value: number) => void;
  maxFeathers: number; // Total daily feathers of the selected chicken
  disabled?: boolean;
  className?: string;
  error?: string;
}

export function RewardShareSlider({
  value,
  onChange,
  maxFeathers,
  disabled = false,
  className,
  error,
}: IRewardShareSliderProps) {
  const [isDragging, setIsDragging] = useState(false);

  // Calculate actual feather amounts
  const delegateeFeathers = Math.min(value, maxFeathers - 1); // Ensure at least 1 feather for owner
  const ownerFeathers = maxFeathers - delegateeFeathers;

  // Calculate percentages for visual representation
  const ownerPercentage =
    maxFeathers > 0 ? Math.round((ownerFeathers / maxFeathers) * 100) : 50;
  const delegateePercentage =
    maxFeathers > 0 ? Math.round((delegateeFeathers / maxFeathers) * 100) : 50;

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-white">
          Delegatee Reward Share
        </label>
        <div className="text-sm text-gray-400">
          {delegateeFeathers} feathers to renter
        </div>
      </div>

      {/* Visual representation */}
      <div className="space-y-3">
        {/* Daily Rub amounts with percentages */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <div className="p-1 bg-green-500/20 rounded">
              <User className="w-3 h-3 text-green-400" />
            </div>
            <span className="text-gray-300">You get:</span>
            <span className="font-semibold text-green-400">
              {ownerFeathers} 🪶 ({ownerPercentage}%)
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-gray-300">They get:</span>
            <span className="font-semibold text-blue-400">
              {delegateeFeathers} 🪶 ({delegateePercentage}%)
            </span>
            <div className="p-1 bg-blue-500/20 rounded">
              <Users className="w-3 h-3 text-blue-400" />
            </div>
          </div>
        </div>

        {/* Slider */}
        <div className="relative">
          {/* Background track with dual colors */}
          <div className="relative w-full h-2 bg-stone-600 rounded-full overflow-hidden mb-2">
            {/* Owner portion (left side) */}
            <div
              className="absolute left-0 top-0 h-full bg-gradient-to-r from-green-500 to-green-400 transition-all duration-200"
              style={{ width: `${ownerPercentage}%` }}
            />
            {/* Renter portion (right side) */}
            <div
              className="absolute right-0 top-0 h-full bg-gradient-to-l from-blue-500 to-blue-400 transition-all duration-200"
              style={{ width: `${delegateePercentage}%` }}
            />
          </div>

          {/* HTML5 Range Input */}
          <input
            type="range"
            min={1}
            max={Math.max(1, maxFeathers - 1)} // Ensure at least 1 feather for owner
            step={1}
            value={delegateeFeathers}
            onChange={(e) => onChange(parseInt(e.target.value))}
            onMouseDown={() => setIsDragging(true)}
            onMouseUp={() => setIsDragging(false)}
            onTouchStart={() => setIsDragging(true)}
            onTouchEnd={() => setIsDragging(false)}
            disabled={disabled || maxFeathers <= 1}
            className={cn(
              "absolute top-0 w-full h-2 appearance-none bg-transparent cursor-pointer",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-stone-800",
              (disabled || maxFeathers <= 1) && "opacity-50 cursor-not-allowed",
              // Custom slider thumb styles
              "[&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-5 [&::-webkit-slider-thumb]:h-5",
              "[&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:border-2 [&::-webkit-slider-thumb]:border-stone-400",
              "[&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:shadow-lg",
              "[&::-webkit-slider-thumb]:cursor-grab [&::-webkit-slider-thumb]:transition-all [&::-webkit-slider-thumb]:duration-200",
              "[&::-webkit-slider-thumb]:hover:border-stone-300",
              isDragging &&
                "[&::-webkit-slider-thumb]:scale-110 [&::-webkit-slider-thumb]:border-blue-400 [&::-webkit-slider-thumb]:shadow-xl",
              // Firefox styles
              "[&::-moz-range-thumb]:appearance-none [&::-moz-range-thumb]:w-5 [&::-moz-range-thumb]:h-5",
              "[&::-moz-range-thumb]:bg-white [&::-moz-range-thumb]:border-2 [&::-moz-range-thumb]:border-stone-400",
              "[&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:cursor-grab",
              "[&::-moz-range-track]:bg-transparent"
            )}
          />
        </div>

        {/* Quick preset buttons */}
        <div className="flex items-center justify-center gap-2">
          <span className="text-xs text-gray-500">Quick presets:</span>
          {maxFeathers > 1 &&
            [25, 50, 75].map((percentage) => {
              const presetFeathers = Math.round(
                (maxFeathers * percentage) / 100
              );
              const clampedPreset = Math.min(presetFeathers, maxFeathers - 1);
              return (
                <button
                  key={percentage}
                  onClick={() => !disabled && onChange(clampedPreset)}
                  disabled={disabled || maxFeathers <= 1}
                  className={cn(
                    "px-2 py-1 text-xs rounded border transition-all duration-200",
                    delegateeFeathers === clampedPreset
                      ? "bg-blue-500/20 border-blue-500 text-blue-400"
                      : "bg-stone-700 border-stone-600 text-gray-400 hover:border-stone-500 hover:text-gray-300",
                    (disabled || maxFeathers <= 1) &&
                      "opacity-50 cursor-not-allowed"
                  )}
                >
                  {clampedPreset} 🪶
                </button>
              );
            })}
        </div>
      </div>

      {/* Error message */}
      {error && (
        <p className="text-sm text-red-400 flex items-center gap-1">
          <span className="w-3 h-3 rounded-full bg-red-500/20 flex items-center justify-center">
            !
          </span>
          {error}
        </p>
      )}

      {/* Help text */}
      {!error && (
        <p className="text-xs text-gray-500">
          {maxFeathers > 1
            ? "Drag the slider or click preset buttons to set how many Daily Rub rewards the renter will receive. Higher amounts make your listing more attractive to renters."
            : "This chicken generates too few Daily Rub rewards to share. Consider using 'Delegatee Only' reward distribution instead."}
        </p>
      )}
    </div>
  );
}
