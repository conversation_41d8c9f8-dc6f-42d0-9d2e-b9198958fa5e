"use client";

import React, { useState, useMemo } from "react";
import { <PERSON><PERSON>, Badge, Checkbox } from "ui";
import { cn } from "@/utils/classes";
import Image from "next/image";
import {
  ERewardType,
  IInventorySelection,
  EViewMode,
  IChickenSelection,
} from "../types/inventory.types";
import { mockInventorySelection } from "../mock/mock-data";
import { useGameRewards } from "../hooks/useGameRewards";
import { usePerChickenRewards } from "../hooks/usePerChickenRewards";
import { ViewToggle } from "./view-toggle";
import { ChickenRewardsTable } from "./chicken-rewards-table";
import { ChickenSelectionActions } from "./chicken-selection-actions";
import { ChickenRewardsHistory } from "./chicken-rewards-history";

interface IRewardsInventoryTabProps {
  className?: string;
}

/**
 * RewardsInventoryTab Component
 *
 * Displays the inventory of collected rewards with filtering, selection, and claim functionality.
 * Follows existing design patterns and styling from the codebase.
 */
export const RewardsInventoryTab: React.FC<IRewardsInventoryTabProps> = ({
  className,
}) => {
  const {
    rewardItems,
    isLoading: isApiLoading,
    error,
    address,
  } = useGameRewards();

  const {
    chickensWithRewards,
    isLoading: isChickenLoading,
    error: chickenError,
  } = usePerChickenRewards();

  // Debug logging
  console.log("🏠 RewardsInventoryTab - Current address:", address);
  console.log("🎁 RewardsInventoryTab - Reward items:", rewardItems);
  console.log(
    "🐔 RewardsInventoryTab - Chickens with rewards:",
    chickensWithRewards
  );

  // View mode state (default to per-chicken view)
  const [viewMode, setViewMode] = useState<EViewMode>(EViewMode.PER_CHICKEN);

  // Aggregated view state
  const [selectedItems, setSelectedItems] = useState<IInventorySelection>(
    mockInventorySelection
  );
  const [selectedItem, setSelectedItem] = useState<number | null>(0);
  const [filterType, setFilterType] = useState<ERewardType | "ALL">("ALL");
  const [sortBy, setSortBy] = useState<"quantity" | "name" | "type">(
    "quantity"
  );

  // Per-chicken view state
  const [selectedChickens, setSelectedChickens] = useState<IChickenSelection>(
    {}
  );

  // Filter and sort items (aggregated view)
  const filteredAndSortedItems = useMemo(() => {
    let filtered = rewardItems;

    if (filterType !== "ALL") {
      filtered = filtered.filter((item) => item.type === filterType);
    }

    return filtered.sort((a, b) => {
      switch (sortBy) {
        case "quantity":
          return b.quantity - a.quantity;
        case "name":
          return a.name.localeCompare(b.name);
        case "type":
          return a.type.localeCompare(b.type);
        default:
          return 0;
      }
    });
  }, [rewardItems, filterType, sortBy]);

  // Get selected items (aggregated view)
  const selectedClaimableItems = filteredAndSortedItems.filter(
    (item) => selectedItems[item.id]
  );

  // Aggregated view functions
  const toggleItemSelection = (itemId: string) => {
    setSelectedItems((prev) => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  const selectAllItems = () => {
    const newSelection: IInventorySelection = {};
    filteredAndSortedItems.forEach((item) => {
      newSelection[item.id] = true;
    });
    setSelectedItems(newSelection);
  };

  const deselectAll = () => {
    setSelectedItems({});
  };

  // Per-chicken view functions
  const toggleChickenSelection = (chickenId: string) => {
    setSelectedChickens((prev) => ({
      ...prev,
      [chickenId]: !prev[chickenId],
    }));
  };

  const selectAllChickens = () => {
    const newSelection: IChickenSelection = {};
    chickensWithRewards.forEach((chicken) => {
      newSelection[chicken.chickenId] = true;
    });
    setSelectedChickens(newSelection);
  };

  const deselectAllChickens = () => {
    setSelectedChickens({});
  };

  const handleChickenClick = (chickenId: string) => {
    // This function can be used for future click handling if needed
    console.log("Chicken clicked:", chickenId);
  };

  // Claiming functions (placeholder for now)
  const handleClaimSelectedChickens = () => {
    console.log("🐔 Claiming selected chickens:", selectedChickens);
    // TODO: Implement claiming logic
  };

  const handleClaimAllChickens = () => {
    console.log("🐔 Claiming all chickens");
    // TODO: Implement claiming logic
  };

  const handleClaimChicken = (chickenId: string) => {
    console.log("🐔 Claiming chicken:", chickenId);
    // TODO: Implement claiming logic
  };

  // Clear chicken selection
  const handleClearChickenSelection = () => {
    setSelectedChickens({});
  };

  // Get reward type badge color
  const getTypeColor = (type: ERewardType) => {
    switch (type) {
      case ERewardType.CRYSTAL:
        return "bg-blue-500/20 text-blue-400";
      case ERewardType.SHARD:
        return "bg-purple-500/20 text-purple-400";
      case ERewardType.CORN:
        return "bg-yellow-500/20 text-yellow-400";
      case ERewardType.ESSENCE:
        return "bg-pink-500/20 text-pink-400";
      default:
        return "bg-muted/20 text-muted-fg";
    }
  };

  // Show loading state
  const isLoading = isApiLoading || isChickenLoading;
  const currentError = error || chickenError;

  if (isLoading) {
    return (
      <div className={className}>
        <div className="mt-6 flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-fg">Loading your rewards...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (currentError) {
    return (
      <div className={className}>
        <div className="mt-6 flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-red-400 mb-4">Failed to load rewards</p>
            <p className="text-muted-fg text-sm">
              {currentError instanceof Error
                ? currentError.message
                : "Unknown error"}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="mt-6">
        {/* View Toggle and Filters - Hidden for now since per-chicken is default */}
        {/* 
        <div className="flex flex-col sm:flex-row gap-4 w-full items-center mb-6">
          <ViewToggle
            viewMode={viewMode}
            onViewModeChange={setViewMode}
            className="flex-shrink-0"
          />
          
          {viewMode === EViewMode.AGGREGATED && (
            <div className="flex gap-2 w-full items-center">
              <select
                value={filterType}
                onChange={(e) =>
                  setFilterType(e.target.value as ERewardType | "ALL")
                }
                className="px-3 py-2 border border-primary/20 rounded-lg bg-stone-800 text-primary"
              >
                <option value="ALL">All Types</option>
                <option value={ERewardType.CRYSTAL}>Crystals</option>
                <option value={ERewardType.SHARD}>Shards</option>
                <option value={ERewardType.CORN}>Corn</option>
              </select>

              <select
                value={sortBy}
                onChange={(e) =>
                  setSortBy(e.target.value as "quantity" | "name" | "type")
                }
                className="px-3 py-2 border border-primary/20 rounded-lg bg-stone-800 text-primary"
              >
                <option value="quantity">Sort by Quantity</option>
                <option value="name">Sort by Name</option>
                <option value="type">Sort by Type</option>
              </select>
            </div>
          )}
        </div>
        */}

        {/* Default view is now Per-Chicken. Aggregated view code preserved below for future use */}

        {/* Per-Chicken View - Default Implementation */}
        <div className="space-y-6">
          {/* Main Section: Table and Right Sidebar */}
          <div className="flex flex-col xl:flex-row gap-6">
            <div className="w-full xl:w-2/3">
              <ChickenRewardsTable
                chickens={chickensWithRewards}
                selectedChickens={selectedChickens}
                onToggleChickenSelection={toggleChickenSelection}
                onSelectAllChickens={selectAllChickens}
                onDeselectAllChickens={deselectAllChickens}
                onChickenClick={handleChickenClick}
                selectedChickenId={null}
                onClaimChicken={handleClaimChicken}
              />
            </div>

            <div className="w-full xl:w-1/3">
              {/* Claim Actions Only */}
              <ChickenSelectionActions
                selectedChickens={selectedChickens}
                chickensWithRewards={chickensWithRewards}
                onClaimSelectedChickens={handleClaimSelectedChickens}
                onClaimAllChickens={handleClaimAllChickens}
                onClearSelection={handleClearChickenSelection}
              />
            </div>
          </div>

          {/* History Section - Bottom */}
          <ChickenRewardsHistory chickens={chickensWithRewards} />
        </div>

        {/* 
        ===============================================================================
        AGGREGATED VIEW CODE (PRESERVED FOR FUTURE USE)
        ===============================================================================
        
        {viewMode === EViewMode.AGGREGATED ? (
          <div className="flex flex-col lg:flex-row gap-6">
            <div className="w-full lg:w-3/5 bg-stone-900/50 rounded-lg border border-primary/5 p-4">
              <div className="flex justify-between items-center mb-3">
                <h2 className="text-lg font-medium text-primary/80">
                  Inventory ({filteredAndSortedItems.length} items)
                </h2>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-fg">
                    {selectedClaimableItems.length} selected
                  </span>
                  <Button
                    size="small"
                    appearance="plain"
                    onPress={selectAllItems}
                    isDisabled={filteredAndSortedItems.length === 0}
                  >
                    Select All
                  </Button>
                  <Button
                    size="small"
                    appearance="plain"
                    onPress={deselectAll}
                    isDisabled={Object.keys(selectedItems).length === 0}
                  >
                    Clear
                  </Button>
                </div>
              </div>

              <div className="h-[450px] overflow-y-auto pr-2 custom-scrollbar">
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 p-2">
                  {filteredAndSortedItems.map((item, index) => (
                    <div
                      key={item.id}
                      className={cn(
                        "relative group rounded-lg overflow-hidden transition-all duration-200 cursor-pointer",
                        selectedItem === index
                          ? "ring-2 ring-primary scale-105 shadow-lg shadow-primary/20"
                          : "hover:ring-1 hover:ring-primary/50 hover:scale-102"
                      )}
                      onClick={() => setSelectedItem(index)}
                    >
                      <div className="aspect-square bg-stone-800/80 p-2 flex items-center justify-center relative">
                        <Image
                          src={item.image}
                          alt={item.name}
                          width={60}
                          height={60}
                          quality={100}
                          unoptimized
                          className="w-full h-full object-contain"
                        />

                        <div className="absolute top-1 left-1">
                          <Checkbox
                            isSelected={selectedItems[item.id] || false}
                            onChange={() => {
                              toggleItemSelection(item.id);
                            }}
                            className="scale-75"
                          />
                        </div>
                      </div>

                      <div className="p-2 bg-stone-800/60 border-t border-primary/10 relative">
                        <h3 className="text-xs font-medium text-primary/90 truncate">
                          {item.name}
                        </h3>
                        <div className="flex justify-between items-end mt-1">
                          <span
                            className={`px-1 py-0.5 rounded text-xs ${getTypeColor(item.type)}`}
                          >
                            {item.type}
                          </span>
                          <Badge intent="secondary" className="text-xs">
                            {item.quantity}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {filteredAndSortedItems.length === 0 && (
                  <div className="text-center py-12">
                    {rewardItems.length === 0 ? (
                      <div>
                        <span className="text-4xl mb-3 block">🎁</span>
                        <p className="text-muted-fg">
                          No claimable rewards available.
                        </p>
                        <p className="text-muted-fg text-sm mt-2">
                          Play battles to earn rewards!
                        </p>
                      </div>
                    ) : (
                      <p className="text-muted-fg">
                        No rewards found matching your filters.
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="w-full lg:w-2/5">
              {selectedItem !== null &&
              selectedItem >= 0 &&
              selectedItem < filteredAndSortedItems.length ? (
                <div className="bg-stone-900 rounded-lg border border-primary/10 p-6 h-full">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-20 h-20 bg-stone-800/50 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Image
                        src={filteredAndSortedItems[selectedItem]!.image}
                        alt={filteredAndSortedItems[selectedItem]!.name}
                        width={80}
                        height={80}
                        quality={100}
                        unoptimized
                        className="w-20 h-20 object-fill rounded-md"
                      />
                    </div>
                    <div>
                      <div className="flex flex-wrap gap-2 mb-1">
                        <Badge intent="secondary">
                          Qty: {filteredAndSortedItems[selectedItem]?.quantity}
                        </Badge>
                      </div>

                      <h2 className="text-xl font-bold text-primary/90">
                        {filteredAndSortedItems[selectedItem]?.name}
                      </h2>

                      <p className="text-sm text-white/70 mt-1">
                        {filteredAndSortedItems[selectedItem]?.description}
                      </p>
                    </div>
                  </div>

                  <div className="h-px w-full bg-gradient-to-r from-transparent via-primary/30 to-transparent my-4"></div>

                  <div className="grid grid-cols-1 gap-4 mb-6">
                    <div className="bg-stone-800/30 p-3 rounded-lg">
                      <p className="font-medium text-primary/80 mb-2">
                        Reward Details:
                      </p>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-white/70">Type:</span>
                          <span className="text-sm text-primary">
                            {filteredAndSortedItems[selectedItem]?.type}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-white/70">Quantity:</span>
                          <span className="text-sm text-white/90">
                            {filteredAndSortedItems[selectedItem]?.quantity}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {filteredAndSortedItems[selectedItem] && (
                    <div className="bg-stone-800/50 rounded-lg p-4 border border-primary/10">
                      <div className="flex flex-col gap-3">
                        <div className="flex items-center justify-between">
                          <p className="text-amber-200">Individual Claim:</p>
                          <Button
                            intent="primary"
                            size="medium"
                            isDisabled={true}
                            className="bg-amber-600 hover:bg-amber-500 text-amber-50 font-semibold disabled:bg-stone-600 disabled:text-stone-400"
                          >
                            Coming Soon
                          </Button>
                        </div>

                        {selectedClaimableItems.length > 0 && (
                          <>
                            <div className="h-px w-full bg-gradient-to-r from-transparent via-amber-400/30 to-transparent"></div>
                            <div className="flex items-center justify-between">
                              <p className="text-amber-200">
                                Bulk Claim ({selectedClaimableItems.length}{" "}
                                items):
                              </p>
                              <Button
                                intent="warning"
                                size="medium"
                                isDisabled={true}
                                className="bg-orange-600 hover:bg-orange-500 text-orange-50 font-semibold disabled:bg-stone-600 disabled:text-stone-400"
                              >
                                Coming Soon
                              </Button>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-stone-900/30 rounded-lg border border-dashed border-primary/20 p-6 h-full flex flex-col items-center justify-center text-center">
                  <span className="text-4xl mb-3">🎁</span>
                  <h3 className="text-lg font-medium text-primary/70">
                    Select a reward
                  </h3>
                  <p className="text-sm text-white/50 mt-1">
                    Choose a reward from the inventory to view details and claim
                  </p>
                </div>
              )}
            </div>
          </div>
        ) : (
          // Per-Chicken View would go here
        )}
        
        ===============================================================================
        END OF AGGREGATED VIEW CODE
        ===============================================================================
        */}
      </div>
    </div>
  );
};
