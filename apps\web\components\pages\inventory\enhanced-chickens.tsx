"use client";

import { Loading } from "@/components/shared/loading";
import { useStateContext } from "@/providers/app/state";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import useFoodCraftingStore from "@/store/food-crafting";
import useDailyFeedStore from "@/store/daily-feeding";
// Import our new hooks and components
import { useFavoriteChickens } from "@/hooks/useFavoriteChickens";
import { useChickenTimers } from "@/hooks/useChickenTimers";
import { useChickenStats } from "@/hooks/useChickenStats";
import { useHealInfo } from "@/hooks/useHealInfo";
import { EnhancedInventoryControls } from "./components/EnhancedInventoryControls";
import { ChickenGrid } from "./components/ChickenGrid";
import { InventoryPagination } from "./components/InventoryPagination";
// Import modals
import BattleModal from "./battle-modal";
import { FeedModal } from "./feed-modal";
import HealModal from "./heal-modal";
import { DelegationDetailsDialog } from "./delegation-details-dialog";
import { CreateRentalModal } from "@/features/delegation/components/create-rental/create-rental-modal";

// Define the tab type locally since we removed the nested tabs
export type InventoryTabType = "owned" | "delegated-out";
import { useChickenTokenIds } from "@/features/chickens/hooks/useChickenTokenIds";
import { useMyRentals } from "@/features/delegation/hooks/useMyRentals";
import { useCancelDelegation } from "@/features/delegation/hooks/useCancelDelegation";
import { useUnlistChickenForRent } from "@/features/delegation/hooks/useUnlistChickenForRent";
import { useBulkCancelDelegation } from "@/features/delegation/hooks/useBulkCancelDelegation";
import useChickenMetadata from "@/features/breeding/tab/breeding/hooks/useChickenMetadata";
import { IRentalWithMetadata } from "@/features/delegation/types/delegation.types";
import { toast } from "sonner";
import useDebounce from "@/lib/hooks/useDebounce";
// Bulk action imports
import { useBulkActionSelection } from "@/features/delegation/hooks/useBulkActionSelection";
import { useBulkUnlistChickenForRent } from "@/features/delegation/hooks/useBulkUnlistChickenForRent";

import { BulkActionBar } from "./components/BulkActionBar";
import { BulkActionConfirmationModal } from "./components/BulkActionConfirmationModal";
import { BulkActionProgressModal } from "./components/BulkActionProgressModal";
import { BulkActionResultModal } from "./components/BulkActionResultModal";

interface Chicken {
  tokenId: string;
  image: string;
  attributes?: {
    Type?: string[];
  };
  chickenType?:
    | "owned"
    | "delegated-to-me"
    | "rented-to-me"
    | "delegated-out"
    | "rented-out"
    | "listed-in-market";
  isDelegated?: boolean;
  isRented?: boolean;
  isDisabled?: boolean;
  delegationInfo?: IRentalWithMetadata | null;
  rentalInfo?: IRentalWithMetadata | null;
  rentalStatus?: IRentalWithMetadata;
}

type SortableAttribute =
  | "id"
  | "hp"
  | "level"
  | "attack"
  | "defense"
  | "speed"
  | "ferocity"
  | "cockrage"
  | "evasion"
  | "mmr";

type FilterType =
  | "all"
  | "genesis"
  | "legacy"
  | "ordinary"
  | "rented"
  | "delegated"
  | "listed";

// Helper function to check if chicken is an egg
const isEgg = (chicken: Chicken) => {
  return chicken?.attributes?.Type?.[0] === "egg";
};

interface EnhancedChickensProps {
  initialTab?: InventoryTabType;
}

export default function EnhancedChickens({
  initialTab = "owned",
}: EnhancedChickensProps) {
  const router = useRouter();
  const { address } = useStateContext();
  const { fetchFoodBalance } = useFoodCraftingStore();
  const { checkApproval } = useDailyFeedStore();
  // Tab state - use initialTab prop (no need for setActiveTab since tabs are controlled by parent)
  const activeTab = initialTab;

  // Step 1: Load token IDs from contracts first
  const {
    tokenIdsByType,
    isLoading: tokenIdsLoading,
    error: tokenIdsError,
  } = useChickenTokenIds(address);

  // Step 2: Load rental data
  const {
    ownedRentals,
    rentedChickens,
    isLoading: rentalsLoading,
  } = useMyRentals(address);

  // Step 3: Delegation action hooks
  const { executeCancelDelegation } = useCancelDelegation();
  const { executeUnlistChickenForRent } = useUnlistChickenForRent();

  // Step 4: Bulk action hooks
  const { executeBulkUnlisting, isUnlisting, progress } =
    useBulkUnlistChickenForRent();
  const {
    executeBulkCancellation,
    isCancelling,
    progress: cancelProgress,
  } = useBulkCancelDelegation();

  // Local state
  const [searchQuery, setSearchQuery] = useState("");
  const { debouncedValue: debouncedSearchQuery } = useDebounce(
    searchQuery,
    500
  );

  const [currentPage, setCurrentPage] = useState(1);
  const [filterType, setFilterType] = useState<FilterType>("all");
  const itemsPerPage = 20;
  const [showSortDropdown, setShowSortDropdown] = useState(false);

  // Modal states
  const [battleModalChicken, setBattleModalChicken] = useState<string | null>(
    null
  );
  const [showFeedModal, setShowFeedModal] = useState<boolean>(false);
  const [selectedChicken, setSelectedChicken] = useState<number>();
  const [healModalChicken, setHealModalChicken] = useState<string | null>(null);
  const [delegationDetailsChicken, setDelegationDetailsChicken] = useState<
    string | null
  >(null);
  const [isDelegateModalOpen, setIsDelegateModalOpen] =
    useState<boolean>(false);
  const [selectedChickenData, setSelectedChickenData] = useState<{
    tokenId: number;
    image: string;
    metadata?: any;
    type?: string;
    level?: number;
    chickenStats?: any;
  } | null>(null);

  // Bulk action modal states
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [showResultModal, setShowResultModal] = useState(false);
  const [bulkActionType, setBulkActionType] = useState<
    "cancel-delegation" | "unlist-market"
  >("cancel-delegation");
  const [bulkActionResult, setBulkActionResult] = useState<any>(null);

  // Sorting state
  const [sortAttribute, setSortAttribute] = useState<SortableAttribute>(() => {
    const savedSort = localStorage.getItem("chickenSortAttribute");
    return (savedSort as SortableAttribute) || "id";
  });

  const [sortOrder, setSortOrder] = useState<"asc" | "desc">(() => {
    const savedOrder = localStorage.getItem("chickenSortOrder");
    return (savedOrder as "asc" | "desc") || "asc";
  });

  // Custom hooks
  const { toggleFavorite, isFavorite } = useFavoriteChickens();
  const {
    chickenStats,
    statsLoading,
    setChickenStats,
    fetchChickenStatsBatch,
    isDead,
    isFaint,
    isBreeding,
    isListed,
    wasTransferredToday,
    wasListedToday,
    isImmortal,
  } = useChickenStats();

  const {
    cooldownTimers,
    recoveryTimers,
    breedingTimers,
    listedTimers,
    transferTimers,
    immortalTimers,
    formatTime: formatTimeFromHook,
  } = useChickenTimers(chickenStats);

  const { healInfo, healInfoLoading, fetchHealInfo, handleHeal } = useHealInfo(
    address || null
  );

  // Wrapper to ensure formatTime always returns a string
  const formatTime = (
    seconds: number,
    type:
      | "cooldown"
      | "recovery"
      | "breeding"
      | "listed"
      | "transfer"
      | "immortal"
  ): string => {
    return formatTimeFromHook(seconds, type) || "";
  };

  const hasRun = useRef(false);

  useEffect(() => {
    if (hasRun.current) return;
    hasRun.current = true;

    checkApproval();
    fetchFoodBalance();
    fetchHealInfo();
  }, [checkApproval, fetchFoodBalance, fetchHealInfo]);

  // Action handlers
  const handleBattle = useCallback(
    (e: React.MouseEvent, tokenId: string) => {
      e.stopPropagation();
      e.preventDefault();

      // Check if chicken is faint or dead
      if (isFaint(tokenId) || isDead(tokenId)) {
        return; // Do nothing for faint/dead chickens
      }

      // Get chicken stats
      const stats = chickenStats[tokenId];

      // Check if HP is at least 50
      if (stats && stats.hp >= 50) {
        setBattleModalChicken(tokenId); // Show battle confirmation modal
      } else {
        // Show a notification that HP is too low
        toast.error(
          "Your chicken's HP is too low for battle. Please heal your chicken to at least 50 HP."
        );
      }
    },
    [chickenStats, isFaint, isDead]
  );

  const handleBreed = useCallback(
    (e: React.MouseEvent, tokenId: string) => {
      e.stopPropagation();
      e.preventDefault();

      // Check if chicken is faint or dead
      if (isFaint(tokenId) || isDead(tokenId)) {
        return; // Do nothing for faint/dead chickens
      }

      router.push(`/breeding?parent1=${tokenId}`);
    },
    [router, isFaint, isDead]
  );

  const openFeedModal = useCallback(
    (e: React.MouseEvent, tokenId: string) => {
      e.stopPropagation();
      e.preventDefault();

      // Check if chicken is faint or dead
      if (isFaint(tokenId) || isDead(tokenId)) {
        return; // Do nothing for faint/dead chickens
      }

      setSelectedChicken(Number(tokenId));
      setShowFeedModal(true);
    },
    [isFaint, isDead]
  );

  const openHealModal = useCallback(
    (e: React.MouseEvent, tokenId: string) => {
      e.stopPropagation();
      e.preventDefault();

      // Check if chicken is faint or dead
      if (isFaint(tokenId) || isDead(tokenId)) {
        return; // Do nothing for faint/dead chickens
      }

      setHealModalChicken(tokenId);
    },
    [isFaint, isDead]
  );

  const handleMenuAction = useCallback(
    (e: React.MouseEvent, action: string, tokenId: string) => {
      e.stopPropagation();
      e.preventDefault();

      // Handle different actions
      switch (action) {
        case "feed":
          openFeedModal(e, tokenId);
          break;
        case "delegate": {
          // Open delegation modal with pre-selected chicken data
          const chickenData = currentPageChickens.find(
            (c: Chicken) => c.tokenId === tokenId
          );
          const metadata = metadataMap[Number(tokenId)];
          const stats = chickenStats[tokenId];

          if (chickenData && metadata) {
            // Get chicken type from metadata
            const typeAttribute = metadata.attributes?.find(
              (attr) => attr.trait_type === "Type"
            );
            const levelAttribute = metadata.attributes?.find(
              (attr) => attr.trait_type === "Level"
            );

            setSelectedChickenData({
              tokenId: Number(tokenId),
              image: chickenData.image,
              metadata,
              type: typeAttribute?.value as string,
              level: Number(levelAttribute?.value) || stats?.level || 1,
              chickenStats: stats,
            });
          }

          setSelectedChicken(Number(tokenId));
          setIsDelegateModalOpen(true);
          break;
        }
        case "cancel-delegation": {
          // Find the rental for this chicken
          const delegationToCancel = ownedRentals.find(
            (rental) => rental.chickenTokenId.toString() === tokenId
          );
          if (delegationToCancel) {
            executeCancelDelegation(delegationToCancel.id);
          } else {
            toast.error("Delegation not found for this chicken");
          }
          break;
        }
        case "unlist-from-market": {
          // Find the rental listing for this chicken
          const listingToUnlist = ownedRentals.find(
            (rental) => rental.chickenTokenId.toString() === tokenId
          );
          if (listingToUnlist) {
            executeUnlistChickenForRent(listingToUnlist.id);
          } else {
            toast.error("Marketplace listing not found for this chicken");
          }
          break;
        }
        case "delegation-details":
          setDelegationDetailsChicken(tokenId);
          break;
        case "release":
          // TODO: Implement release
          toast.info("Release feature coming soon!");
          break;
        default:
          break;
      }
    },
    [
      openFeedModal,
      executeCancelDelegation,
      executeUnlistChickenForRent,
      ownedRentals,
    ]
  );

  const handleToggleFavorite = useCallback(
    (e: React.MouseEvent, tokenId: string) => {
      e.stopPropagation();
      e.preventDefault();
      toggleFavorite(tokenId);
    },
    [toggleFavorite]
  );

  // Add this function to perform the heal
  const performHeal = useCallback(async () => {
    if (!healModalChicken) return false;
    return await handleHeal(healModalChicken, setChickenStats);
  }, [healModalChicken, handleHeal, setChickenStats]);

  useEffect(() => {
    localStorage.setItem("chickenSortAttribute", sortAttribute);
    localStorage.setItem("chickenSortOrder", sortOrder);
  }, [sortAttribute, sortOrder]);

  // Step 3: Merge token IDs with rental data and apply filtering
  const filteredTokenIds = useMemo(() => {
    if (activeTab === "owned") {
      // For "My Chickens" tab: owned + delegated to me + rented to me
      const ownedTokenIds = tokenIdsByType.all || [];
      const rentedToMeTokenIds = rentedChickens
        .filter((rental) => rental.roninPrice !== "0") // Only paid rentals
        .map((rental) => rental.chickenTokenId);
      const delegatedToMeTokenIds = rentedChickens
        .filter((rental) => rental.roninPrice === "0") // Free delegations
        .map((rental) => rental.chickenTokenId);

      const allTokenIds = [
        ...ownedTokenIds,
        ...rentedToMeTokenIds,
        ...delegatedToMeTokenIds,
      ];

      // Apply type filtering
      let filtered = allTokenIds;
      if (filterType === "genesis") {
        filtered = allTokenIds.filter((id) =>
          tokenIdsByType.genesis.includes(id)
        );
      } else if (filterType === "legacy") {
        filtered = allTokenIds.filter((id) =>
          tokenIdsByType.legacy.includes(id)
        );
      } else if (filterType === "ordinary") {
        filtered = allTokenIds.filter((id) =>
          tokenIdsByType.ordinary.includes(id)
        );
      } else if (filterType === "rented") {
        filtered = rentedToMeTokenIds;
      } else if (filterType === "delegated") {
        filtered = delegatedToMeTokenIds;
      }

      // Apply search filter
      if (debouncedSearchQuery.trim()) {
        filtered = filtered.filter((id) =>
          id.toString().includes(debouncedSearchQuery.trim())
        );
      }

      return filtered;
    } else {
      // For "Delegated to others" tab: only owned rentals
      const delegatedOutTokenIds = ownedRentals.map(
        (rental) => rental.chickenTokenId
      );

      let filtered = delegatedOutTokenIds;

      // Apply filtering for delegated out chickens
      if (filterType === "listed") {
        // Show only chickens listed for rent (status = 0)
        filtered = ownedRentals
          .filter((rental) => rental.status === 0)
          .map((rental) => rental.chickenTokenId);
      } else if (filterType === "rented") {
        // Show only rented out chickens (status = 1 and roninPrice > 0)
        filtered = ownedRentals
          .filter((rental) => rental.status === 1 && rental.roninPrice !== "0")
          .map((rental) => rental.chickenTokenId);
      } else if (filterType === "delegated") {
        // Show only delegated chickens (status = 1 and roninPrice = 0)
        filtered = ownedRentals
          .filter((rental) => rental.status === 1 && rental.roninPrice === "0")
          .map((rental) => rental.chickenTokenId);
      } else if (filterType === "genesis") {
        // Filter by genesis type
        filtered = delegatedOutTokenIds.filter((id) =>
          tokenIdsByType.genesis.includes(id)
        );
      } else if (filterType === "legacy") {
        // Filter by legacy type
        filtered = delegatedOutTokenIds.filter((id) =>
          tokenIdsByType.legacy.includes(id)
        );
      } else if (filterType === "ordinary") {
        // Filter by ordinary type
        filtered = delegatedOutTokenIds.filter((id) =>
          tokenIdsByType.ordinary.includes(id)
        );
      }
      // "all" shows all delegated out chickens

      // Apply search filter
      if (debouncedSearchQuery.trim()) {
        filtered = filtered.filter((id) =>
          id.toString().includes(debouncedSearchQuery.trim())
        );
      }

      return filtered;
    }
  }, [
    activeTab,
    tokenIdsByType,
    rentedChickens,
    ownedRentals,
    filterType,
    debouncedSearchQuery,
  ]);

  // Step 3.5: Apply sorting to filtered token IDs (before pagination)
  const sortedTokenIds = useMemo(() => {
    if (activeTab === "delegated-out") {
      // For delegated-out tab, just sort by ID ascending
      return [...filteredTokenIds].sort((a, b) => a - b);
    }

    // For owned tab, apply the selected sorting
    if (sortAttribute === "id") {
      return [...filteredTokenIds].sort((a, b) => {
        return sortOrder === "asc" ? a - b : b - a;
      });
    }

    // For other sort attributes, we need to sort by ID as fallback since we don't have stats at this level
    // The actual stat-based sorting will happen after we load the metadata and stats
    return [...filteredTokenIds].sort((a, b) => a - b);
  }, [filteredTokenIds, activeTab, sortAttribute, sortOrder]);

  // Step 4: Apply pagination at token ID level
  const paginatedTokenIds = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return sortedTokenIds.slice(startIndex, endIndex);
  }, [sortedTokenIds, currentPage, itemsPerPage]);

  // Step 5: Load metadata only for current page token IDs
  const {
    metadataMap,
    isLoading: metadataLoading,
    error: metadataError,
  } = useChickenMetadata(paginatedTokenIds);

  // Create chicken objects for current page
  const currentPageChickens = useMemo(() => {
    return paginatedTokenIds.map((tokenId) => {
      const metadata = metadataMap[tokenId];
      const typeAttribute = metadata?.attributes?.find(
        (attr) => attr.trait_type === "Type"
      );

      // Determine chicken type and status
      let chickenType: Chicken["chickenType"] = "owned";
      let isDelegated = false;
      let isRented = false;
      let delegationInfo = null;
      let rentalInfo = null;

      // Check if it's rented to me
      const rentedToMe = rentedChickens.find(
        (rental) =>
          rental.chickenTokenId === tokenId && rental.roninPrice !== "0"
      );
      if (rentedToMe) {
        chickenType = "rented-to-me";
        isRented = true;
        rentalInfo = rentedToMe;
      }

      // Check if it's delegated to me
      const delegatedToMe = rentedChickens.find(
        (rental) =>
          rental.chickenTokenId === tokenId && rental.roninPrice === "0"
      );
      if (delegatedToMe) {
        chickenType = "delegated-to-me";
        isDelegated = true;
        delegationInfo = delegatedToMe;
      }

      // Check if it's delegated/rented out (regardless of active tab)
      const ownedRental = ownedRentals.find(
        (rental) => rental.chickenTokenId === tokenId
      );
      if (ownedRental) {
        if (ownedRental.status === 0) {
          // Status 0 = Listed for rent
          chickenType = "listed-in-market";
        } else if (ownedRental.status === 1) {
          // Status 1 = Rented/Delegated - differentiate based on price
          const roninPrice = ownedRental.roninPrice || "0";
          const isDelegation = roninPrice === "0";

          if (isDelegation) {
            chickenType = "delegated-out";
          } else {
            chickenType = "rented-out";
          }
        }
      }

      return {
        tokenId: tokenId.toString(),
        image:
          metadata?.image ||
          `https://chicken-api-ivory.vercel.app/api/image/${tokenId}.png`,
        attributes: metadata?.attributes
          ? {
              Type: [(typeAttribute?.value as string) || "Unknown"],
            }
          : undefined,
        chickenType,
        isDelegated,
        isRented,
        isDisabled: false, // Let ChickenActions handle restrictions based on chickenType
        delegationInfo,
        rentalInfo,
      };
    });
  }, [paginatedTokenIds, metadataMap, rentedChickens, ownedRentals]);

  // Since sorting is now done at the token ID level before pagination,
  // we just need to handle eggs positioning within the current page
  const sortedChickens = useMemo(() => {
    if (!currentPageChickens.length) return currentPageChickens;

    // For ID sorting, the order is already correct from token ID sorting
    // We just need to handle eggs positioning
    const sorted = [...currentPageChickens].sort((a, b) => {
      const aIsEgg = isEgg(a);
      const bIsEgg = isEgg(b);

      // Always put eggs at the end
      if (aIsEgg && !bIsEgg) return 1;
      if (!aIsEgg && bIsEgg) return -1;

      // If both are eggs or both are not eggs, maintain the order from token ID sorting
      return parseInt(a.tokenId) - parseInt(b.tokenId);
    });

    return sorted;
  }, [currentPageChickens]);

  // Bulk action selection hook
  const bulkActionSelection = useBulkActionSelection(
    sortedChickens.map((chicken) => ({
      tokenId: parseInt(chicken.tokenId),
      image: chicken.image,
      dailyFeathers: 0, // Not available in this context
      breedCount: 0, // Not available in this context
      type: chicken.chickenType || "unknown",
    })),
    ownedRentals
  );

  // Bulk action handlers
  const handleBulkCancelDelegation = useCallback(async () => {
    const rentalIds = bulkActionSelection.selectedCancellationRentalIds;
    if (rentalIds.length === 0) {
      toast.error("No delegations selected", {
        description: "Please select chickens with active delegations to cancel",
        position: "top-right",
      });
      return;
    }

    setBulkActionType("cancel-delegation");
    setShowConfirmationModal(true);
  }, [bulkActionSelection.selectedCancellationRentalIds]);

  const handleBulkUnlistFromMarket = useCallback(async () => {
    const rentalIds = bulkActionSelection.selectedUnlistingRentalIds;
    if (rentalIds.length === 0) {
      toast.error("No listed chickens selected", {
        description:
          "Please select chickens that are listed on the market to unlist",
        position: "top-right",
      });
      return;
    }

    setBulkActionType("unlist-market");
    setShowConfirmationModal(true);
  }, [bulkActionSelection.selectedUnlistingRentalIds]);

  const executeBulkAction = useCallback(async () => {
    setShowConfirmationModal(false);
    setShowProgressModal(true);

    try {
      let result;
      if (bulkActionType === "cancel-delegation") {
        // For delegation cancellation, use the blockchain bulk cancellation
        const rentalIds = bulkActionSelection.selectedCancellationRentalIds;
        result = await executeBulkCancellation(rentalIds);
      } else {
        // For market unlisting, use the blockchain bulk unlisting
        const rentalIds = bulkActionSelection.selectedUnlistingRentalIds;
        result = await executeBulkUnlisting(rentalIds);
      }

      setBulkActionResult(result);
      setShowProgressModal(false);
      setShowResultModal(true);

      if (result.success) {
        // Clear selection after successful operation
        bulkActionSelection.clearSelection();
      }
    } catch (error) {
      console.error("Bulk action failed:", error);
      setBulkActionResult({
        success: false,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
        successfulCount: 0,
        totalRequested:
          bulkActionType === "cancel-delegation"
            ? bulkActionSelection.selectedCancellationRentalIds.length
            : bulkActionSelection.selectedUnlistingRentalIds.length,
        failedCount:
          bulkActionType === "cancel-delegation"
            ? bulkActionSelection.selectedCancellationRentalIds.length
            : bulkActionSelection.selectedUnlistingRentalIds.length,
      });
      setShowProgressModal(false);
      setShowResultModal(true);
    }
  }, [
    bulkActionType,
    bulkActionSelection.selectedCancellationRentalIds,
    bulkActionSelection.selectedUnlistingRentalIds,
    bulkActionSelection.clearSelection,
    executeBulkUnlisting,
    executeBulkCancellation,
  ]);

  // Load stats for current page chickens
  useEffect(() => {
    if (paginatedTokenIds.length > 0) {
      fetchChickenStatsBatch(paginatedTokenIds.map((id) => id.toString()));
    }
  }, [paginatedTokenIds, fetchChickenStatsBatch]);

  // Calculate pagination info
  const totalPages = Math.ceil(sortedTokenIds.length / itemsPerPage);

  // Loading state
  const isLoading = tokenIdsLoading || rentalsLoading || metadataLoading;

  // Reset page when filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [filterType, debouncedSearchQuery, activeTab]);

  // Clear bulk selection when switching to "owned" tab (bulk actions only available in "delegated-out")
  useEffect(() => {
    if (activeTab === "owned" && bulkActionSelection.isBulkMode) {
      bulkActionSelection.clearSelection();
      bulkActionSelection.toggleBulkMode(); // Turn off bulk mode
    }
  }, [activeTab, bulkActionSelection]);

  return (
    <div className="space-y-6">
      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <Loading />
        </div>
      )}

      {/* Error State */}
      {(tokenIdsError || metadataError) && (
        <div className="text-center text-red-500 py-8">
          Error loading chickens. Please try again.
        </div>
      )}

      {/* Content */}
      {!isLoading && !tokenIdsError && !metadataError && (
        <>
          {/* Controls */}
          <EnhancedInventoryControls
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            sortAttribute={sortAttribute}
            sortOrder={sortOrder}
            onSortChange={(attr: SortableAttribute, order: "asc" | "desc") => {
              setSortAttribute(attr);
              setSortOrder(order);
            }}
            showSortDropdown={showSortDropdown}
            onToggleSortDropdown={() => setShowSortDropdown(!showSortDropdown)}
            filterType={filterType}
            onFilterChange={setFilterType}
            totalCount={sortedTokenIds.length}
            activeTab={activeTab}
            // Heal info props
            healInfo={healInfo}
            healInfoLoading={healInfoLoading}
            onRefreshHealInfo={fetchHealInfo}
            // Bulk action props (only for delegated-out tab)
            isBulkMode={
              activeTab === "delegated-out"
                ? bulkActionSelection.isBulkMode
                : false
            }
            bulkActionSummary={
              activeTab === "delegated-out"
                ? bulkActionSelection.bulkActionSummary
                : undefined
            }
            onToggleBulkMode={
              activeTab === "delegated-out"
                ? bulkActionSelection.toggleBulkMode
                : undefined
            }
          />

          {/* Chicken Grid */}
          <ChickenGrid
            chickens={sortedChickens}
            chickenStats={chickenStats}
            statsLoading={statsLoading}
            cooldownTimers={cooldownTimers}
            recoveryTimers={recoveryTimers}
            breedingTimers={breedingTimers}
            listedTimers={listedTimers}
            transferTimers={transferTimers}
            immortalTimers={immortalTimers}
            formatTime={formatTime}
            isFavorite={isFavorite}
            isEgg={isEgg}
            isFaint={isFaint}
            isDead={isDead}
            isBreeding={isBreeding}
            isListed={isListed}
            wasTransferredToday={wasTransferredToday}
            wasListedToday={wasListedToday}
            isImmortal={isImmortal}
            onChickenClick={(tokenId: string) =>
              router.push(`/inventory/chickens/${tokenId}`)
            }
            onToggleFavorite={handleToggleFavorite}
            onBattle={handleBattle}
            onHeal={openHealModal}
            onFeed={openFeedModal}
            onBreed={handleBreed}
            onMenuAction={handleMenuAction}
            // Bulk action props (only for delegated-out tab)
            isBulkMode={
              activeTab === "delegated-out"
                ? bulkActionSelection.isBulkMode
                : false
            }
            isChickenSelected={
              activeTab === "delegated-out"
                ? (tokenId: string) =>
                    bulkActionSelection.isChickenSelected(parseInt(tokenId))
                : undefined
            }
            canSelectChicken={
              activeTab === "delegated-out"
                ? (tokenId: string) =>
                    bulkActionSelection.canSelectChicken(parseInt(tokenId))
                : undefined
            }
            getChickenActionReason={
              activeTab === "delegated-out"
                ? (tokenId: string) =>
                    bulkActionSelection.getChickenActionReason(
                      parseInt(tokenId)
                    )
                : undefined
            }
            onToggleChickenSelection={
              activeTab === "delegated-out"
                ? (tokenId: string) =>
                    bulkActionSelection.toggleChickenSelection(
                      parseInt(tokenId)
                    )
                : undefined
            }
          />

          {/* Pagination */}
          <InventoryPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </>
      )}

      {/* Modals */}
      {battleModalChicken && (
        <BattleModal
          isOpen={true}
          onClose={() => setBattleModalChicken(null)}
          chickenId={battleModalChicken}
          chickenImage={
            metadataMap[Number(battleModalChicken)]?.image ||
            `https://chicken-api-ivory.vercel.app/api/image/${battleModalChicken}.png`
          }
          chickenStats={
            chickenStats[battleModalChicken] || {
              level: 1,
              attack: 0,
              defense: 0,
              speed: 0,
              ferocity: 0,
              cockrage: 0,
              evasion: 0,
              hp: 100,
              maxHp: 100,
            }
          }
        />
      )}

      {healModalChicken && (
        <HealModal
          isOpen={true}
          onClose={() => setHealModalChicken(null)}
          chickenId={healModalChicken}
          chickenImage={
            metadataMap[Number(healModalChicken)]?.image ||
            `https://chicken-api-ivory.vercel.app/api/image/${healModalChicken}.png`
          }
          currentHp={chickenStats[healModalChicken]?.hp || 0}
          maxHp={chickenStats[healModalChicken]?.maxHp || 100}
          onHeal={performHeal}
          healInfo={healInfo}
          isLoading={healInfoLoading}
          refetch={async () => {
            if (paginatedTokenIds.length > 0) {
              fetchChickenStatsBatch(
                paginatedTokenIds.map((id) => id.toString())
              );
            }
          }}
          onBattle={() => {
            setHealModalChicken(null);
            if (healModalChicken) {
              setBattleModalChicken(healModalChicken);
            }
          }}
        />
      )}

      {showFeedModal && selectedChicken && (
        <FeedModal
          show={showFeedModal}
          setShow={setShowFeedModal}
          tokenId={selectedChicken}
        />
      )}

      <CreateRentalModal
        isOpen={isDelegateModalOpen}
        onOpenChange={setIsDelegateModalOpen}
        preSelectedChickenId={selectedChicken}
        preSelectedChickenData={selectedChickenData}
      />

      {delegationDetailsChicken && (
        <DelegationDetailsDialog
          isOpen={true}
          onOpenChange={(open) => {
            if (!open) {
              setDelegationDetailsChicken(null);
            }
          }}
          delegatedChicken={(() => {
            // Find the rental data for this token ID
            // For delegated-out tab, look in ownedRentals; for owned tab, look in rentedChickens
            const rental =
              activeTab === "delegated-out"
                ? ownedRentals.find(
                    (r) => r.chickenTokenId === Number(delegationDetailsChicken)
                  )
                : rentedChickens.find(
                    (r) => r.chickenTokenId === Number(delegationDetailsChicken)
                  );

            if (!rental) return null;

            // Create a compatible IDelegatedChicken object
            const metadata = metadataMap[Number(delegationDetailsChicken)];
            const typeAttribute = metadata?.attributes?.find(
              (attr) => attr.trait_type === "Type"
            );
            const levelAttribute = metadata?.attributes?.find(
              (attr) => attr.trait_type === "Level"
            );
            const breedCountAttribute = metadata?.attributes?.find(
              (attr) => attr.trait_type === "Breed Count"
            );

            return {
              tokenId: rental.chickenTokenId,
              image:
                metadata?.image ||
                `https://chicken-api-ivory.vercel.app/api/image/${rental.chickenTokenId}.png`,
              metadata,
              type: typeAttribute?.value as string,
              dailyFeathers: rental.dailyFeathers || 0,
              level: Number(levelAttribute?.value) || 1,
              breedCount: Number(breedCountAttribute?.value) || 0,
              winRate: 0, // Default value
              // Delegation specific fields
              delegatedTask: rental.delegatedTask,
              rewardDistribution: rental.rewardDistribution,
              sharedRewardAmount: rental.sharedRewardAmount,
              renterAddress: rental.renterAddress || "",
              ownerAddress: rental.ownerAddress,
              legendaryCount: rental.legendaryCount || 0,
              isDelegated: true as const,
            };
          })()}
        />
      )}

      {/* Bulk Action Bar (only for delegated-out tab) */}
      {activeTab === "delegated-out" && (
        <BulkActionBar
          isVisible={bulkActionSelection.isBulkMode}
          summary={bulkActionSelection.bulkActionSummary}
          isProcessing={isUnlisting || isCancelling}
          onCancelDelegation={handleBulkCancelDelegation}
          onUnlistFromMarket={handleBulkUnlistFromMarket}
          onSelectAllCancellable={bulkActionSelection.selectAllCancellable}
          onSelectAllUnlistable={bulkActionSelection.selectAllUnlistable}
          onClearSelection={bulkActionSelection.clearSelection}
          onClose={bulkActionSelection.toggleBulkMode}
        />
      )}

      {/* Bulk Action Modals */}
      <BulkActionConfirmationModal
        isOpen={showConfirmationModal}
        onOpenChange={setShowConfirmationModal}
        actionType={bulkActionType}
        selectedChickens={bulkActionSelection.selectedChickensData}
        onConfirm={executeBulkAction}
        onCancel={() => setShowConfirmationModal(false)}
      />

      <BulkActionProgressModal
        isOpen={showProgressModal}
        onOpenChange={setShowProgressModal}
        actionType={bulkActionType}
        progress={
          bulkActionType === "cancel-delegation" ? cancelProgress : progress
        }
        onClose={() => setShowProgressModal(false)}
      />

      <BulkActionResultModal
        isOpen={showResultModal}
        onOpenChange={setShowResultModal}
        actionType={bulkActionType}
        result={bulkActionResult}
        onClose={() => {
          setShowResultModal(false);
          setBulkActionResult(null);
        }}
      />
    </div>
  );
}
