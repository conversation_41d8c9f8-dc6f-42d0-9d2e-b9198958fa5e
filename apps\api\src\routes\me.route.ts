import { Hono } from "hono";
import { getMe } from "../controllers/me/get-me";
import { env } from "process";
import { createAuthMiddleware } from "../middlewares/auth";
import { getMeChickens } from "../controllers/me/get-me-chickens";
const auth = createAuthMiddleware({
  jwtSecret: env.JWT_SECRET,
});

const me = new Hono();

me.get("/", auth.protect, getMe); // GET /book
me.get("/chickens", auth.protect, getMeChickens);

export default me;
