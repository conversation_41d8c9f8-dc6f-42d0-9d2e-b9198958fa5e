import { ArrowRightLeft, Clock, Egg, ShoppingCart } from "lucide-react";

interface ChickenTimersProps {
  tokenId: string;
  isFaint: boolean;
  isDead: boolean;
  isBreeding: boolean;
  isListed: boolean;
  wasTransferred: boolean;
  isImmortal: boolean;
  isEgg: boolean;
  cooldownTimers: Record<string, number>;
  recoveryTimers: Record<string, number>;
  breedingTimers: Record<string, number>;
  listedTimers: Record<string, number>;
  transferTimers: Record<string, number>;
  immortalTimers: Record<string, number>;
  formatTime: (
    seconds: number,
    type: "cooldown" | "recovery" | "breeding" | "listed" | "transfer" | "immortal"
  ) => string;
  hp?: number;
  maxHp?: number;
}

export function ChickenTimers({
  tokenId,
  isFaint,
  isDead,
  isBreeding,
  isListed,
  wasTransferred,
  isImmortal,
  isEgg,
  cooldownTimers,
  recoveryTimers,
  breedingTimers,
  listedTimers,
  transferTimers,
  immortalTimers,
  formatTime,
  hp = 0,
  maxHp = 100,
}: ChickenTimersProps) {
  // HP Cooldown Timer - only show for non-eggs and non-faint/dead chickens
  const renderCooldownTimer = () => {
    const cooldownSeconds = cooldownTimers[tokenId];

    if (!cooldownSeconds || cooldownSeconds <= 0) return null;
    if (hp >= maxHp) return null;
    if (isEgg || isFaint || isDead) return null;

    return (
      <div className="absolute top-2 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30">
        {formatTime(cooldownSeconds, "cooldown")}
      </div>
    );
  };

  // Recovery Timer - only show for faint chickens
  const renderRecoveryTimer = () => {
    const recoverySeconds = recoveryTimers[tokenId];

    if (!isFaint) return null;

    if (!recoverySeconds || recoverySeconds <= 0) {
      return (
        <div className="absolute top-2 left-2 bg-green-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30">
          Ready!
        </div>
      );
    }

    return (
      <div className="absolute top-2 left-2 bg-orange-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30 flex items-center gap-1">
        <Clock className="h-3 w-3" />
        {formatTime(recoverySeconds, "recovery")}
      </div>
    );
  };

  // Breeding Timer - only show for breeding chickens
  const renderBreedingTimer = () => {
    const breedingSeconds = breedingTimers[tokenId];

    if (!isBreeding) return null;

    if (!breedingSeconds || breedingSeconds <= 0) {
      return (
        <div className="absolute top-2 left-2 bg-green-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30">
          Complete!
        </div>
      );
    }

    return (
      <div className="absolute top-2 left-2 bg-pink-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30 flex items-center gap-1">
        <Egg className="h-3 w-3" />
        {formatTime(breedingSeconds, "breeding")}
      </div>
    );
  };

  // Listed Timer - only show for listed chickens
  const renderListedTimer = () => {
    if (!isListed) return null;

    const listedSeconds = listedTimers[tokenId];

    if (!listedSeconds || listedSeconds <= 0) {
      return (
        <div className="absolute top-2 left-2 bg-green-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30">
          Available!
        </div>
      );
    }

    return (
      <div className="absolute top-2 left-2 bg-orange-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30 flex items-center gap-1">
        <div className="relative">
          <ShoppingCart className="h-3 w-3" />
          <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-orange-300 rounded-full animate-pulse"></div>
        </div>
        {formatTime(listedSeconds, "listed")}
      </div>
    );
  };

  // Transfer Timer - only show for transferred chickens
  const renderTransferTimer = () => {
    if (!wasTransferred) return null;

    const transferSeconds = transferTimers[tokenId];

    if (!transferSeconds || transferSeconds <= 0) {
      return (
        <div className="absolute top-2 left-2 bg-green-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30">
          Available!
        </div>
      );
    }

    return (
      <div className="absolute top-2 left-2 bg-purple-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30 flex items-center gap-1">
        <ArrowRightLeft className="h-3 w-3" />
        {formatTime(transferSeconds, "transfer")}
      </div>
    );
  };

  // Immortal Timer - show for immortal chickens (positioned differently to avoid conflicts)
  const renderImmortalTimer = () => {
    if (!isImmortal) return null;

    const immortalSeconds = immortalTimers[tokenId];

    if (!immortalSeconds || immortalSeconds <= 0) {
      return (
        <div className="absolute top-2 right-14 bg-amber-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30">
          Expired!
        </div>
      );
    }

    return (
      <div className="absolute top-2/3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-yellow-400 to-amber-500 text-black text-xs font-bold px-2 py-1 rounded-full z-30 flex items-center gap-1">
        ⚡ {formatTime(immortalSeconds, "immortal")}
      </div>
    );
  };

  return (
    <>
      {renderCooldownTimer()}
      {renderRecoveryTimer()}
      {renderListedTimer()}
      {renderTransferTimer()}
      {renderBreedingTimer()}
      {/* TODO: Fix immortal timer logic - should be 12hrs after ambrosia feeding, not UTC midnight */}
      {/* {renderImmortalTimer()} */}
    </>
  );
}
