/**
 * Rewards Inventory Types
 *
 * This file contains TypeScript interfaces for the Rewards Inventory feature.
 */

/**
 * Enum for reward item types
 */
export enum ERewardType {
  CRYSTAL = "CRYSTAL",
  SHARD = "SHARD", 
  CORN = "CORN",
  ESSENCE = "ESSENCE",
}

/**
 * Enum for view modes
 */
export enum EViewMode {
  AGGREGATED = "AGGREGATED",
  PER_CHICKEN = "PER_CHICKEN",
}

/**
 * Interface for reward item information
 */
export interface IRewardItem {
  id: string;
  name: string;
  type: ERewardType;
  quantity: number;
  image: string;
  description: string;
}

/**
 * Interface for reward inventory selection state
 */
export interface IInventorySelection {
  [itemId: string]: boolean;
}

/**
 * Interface for chicken selection state (similar to ninuno pattern)
 */
export interface IChickenSelection {
  [chickenId: string]: boolean;
}

/**
 * Interface for chicken with rewards data
 */
export interface IChickenWithRewards {
  chickenId: string;
  tokenId: number;
  name: string;
  image: string;
  totalRewards: number;
  rewardsByType: {
    [type: string]: number;
  };
  rewards: IGameRewardItem[];
  hasUnclaimedRewards: boolean;
  lastUpdated: string;
}

/**
 * Interface for claim inventory request
 */
export interface IClaimInventoryRequest {
  itemIds: string[];
  address: string;
}

/**
 * Interface for per-chicken claim request
 */
export interface IPerChickenClaimRequest {
  chickenIds: string[];
  address: string;
}

/**
 * API Response Types for Game Rewards
 */
export interface IGameRewardItem {
  _id: string;
  chickenId: number;
  owner: string;
  matchId: string;
  tokenId: number;
  contractType: string;
  rewardId: number;
  rewardType: string;
  claimed: boolean;
  createdAt: string;
  __v: number;
  deadChickenId?: string;
  quantity?: number;
}

export interface IGameRewardsResponse {
  success: boolean;
  data: {
    rewardsByChicken: {
      [chickenId: string]: IGameRewardItem[];
    };
    tokenIds: number[];
    totalRewards: number;
    totalChickens: number;
  };
}

/**
 * Transfer History Types
 */
export interface ITransferHistoryItem {
  id: string;
  chickenId: string;
  chickenName: string;
  chickenImage: string;
  rewardType: ERewardType;
  rewardName: string;
  rewardImage: string;
  quantity: number;
  transferredAt: string;
  transactionHash?: string;
  fromAddress: string;
  toAddress: string;
  status: 'pending' | 'completed' | 'failed';
}

/**
 * Transfer Balance API Types
 */
export interface ITransferBalanceRequest {
  address: string;
  chickenIds: number[];
}

export interface ITransferBalanceResponse {
  success: boolean;
  message: string;
  data: {
    transferredRewards: number;
    processedChickens: number;
    balances: {
      tokenId: number;
      balance: number;
      contractType: string;
      totalReceived: number;
      totalWithdrawn: number;
    }[];
    totalTokenTypes: number;
  };
}

/**
 * Balance API Types
 */
export interface IBalanceResponse {
  success: boolean;
  data: {
    balances: {
      tokenId: number;
      balance: number;
      contractType: string;
      totalReceived: number;
      totalWithdrawn: number;
      lastUpdated: string;
    }[];
    summary: {
      totalTokenTypes: number;
      totalBalance: number;
      totalReceived: number;
      totalWithdrawn: number;
      hasBalance: boolean;
    };
    history?: {
      transfers: any[];
      claims: any[];
      totalTransfers: number;
      totalClaims: number;
    };
  };
}

/**
 * Claim API Types
 */
export interface IClaimRequest {
  address: string;
  tokenId?: number;
  amount?: number;
  claims?: {
    tokenId: number;
    amount: number;
  }[];
}

export interface IClaimResponse {
  success: boolean;
  message: string;
  data: {
    claimHistoryId: string;
    claimMethod: string;
    totalClaimed: number;
    tokenTypesClaimed: number;
    balances: {
      tokenId: number;
      balance: number;
      contractType: string;
      totalReceived: number;
      totalWithdrawn: number;
    }[];
    claimDetails: {
      tokenId: number;
      contractType: string;
      balanceBefore: number;
      balanceAfter: number;
      amountClaimed: number;
    }[];
  };
}

/**
 * Token Information Helper
 */
export const getTokenDisplayName = (tokenId: number): string => {
  switch (tokenId) {
    case 9:
    case 10:
      return `Crystals (Token ${tokenId})`;
    case 11:
      return `Shiny Corn (Token ${tokenId})`;
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 18:
      return `Colored Corn (Token ${tokenId})`;
    case 19:
    case 20:
    case 21:
    case 22:
    case 23:
    case 24:
    case 25:
    case 26:
      return `Shards (Token ${tokenId})`;
    case 27:
    case 28:
    case 29:
    case 30:
    case 31:
    case 32:
    case 33:
      return `Essence (Token ${tokenId})`;
    default:
      return `Token ${tokenId}`;
  }
};

/**
 * History Tab Types
 */
export enum EHistoryTab {
  CLAIM_HISTORY = 'CLAIM_HISTORY',
  TRANSFER_HISTORY = 'TRANSFER_HISTORY'
}