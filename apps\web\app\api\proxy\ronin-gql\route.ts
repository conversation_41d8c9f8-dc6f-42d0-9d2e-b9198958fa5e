import { NextRequest, NextResponse } from "next/server";
import { Address } from "viem";
import { delay } from "@/utils/delay";

interface Order {
  startedAt: number;
}

type ResponseData = {
  erc721Tokens: {
    total: number;
    results: TokenData[];
  };
};

interface TokenData {
  tokenAddress: string;
  tokenId: string;
  owner: string;
  order: Order;
  image: string;
  name?: string;
  cdnImage?: string;
  attributes: any;
}

async function _getNfts(
  owner: string,
  from: number = 0,
  contractAddress: Address
): Promise<ResponseData> {
  const graphqlEndpoint = process.env.RONIN_MARKETPLACE_GQL as string;
  const data = {
    operationName: "GetERC721TokensList",
    variables: {
      from,
      owner: owner,
      size: 50,
      tokenAddress: contractAddress,
    },
    query: `query GetERC721TokensList($tokenAddress: String, $owner: String, $from: Int!, $size: Int!) {
      erc721Tokens(
        tokenAddress: $tokenAddress
        owner: $owner
        from: $from
        size: $size
      ) {
        total
        results {
          tokenAddress
          tokenId
          owner
          name
          image
          cdnImage
          attributes
        }
      }
    }`,
  };

  const headers = {
    "Content-Type": "application/json",
  };

  const response = await fetch(graphqlEndpoint, {
    method: "POST",
    headers,
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch NFTs: ${response.status}`);
  }

  const responseData = await response.json();
  return responseData.data;
}

export async function GET(req: NextRequest) {
  const searchParams = req.nextUrl.searchParams;
  const address = searchParams.get("address");

  if (!address) {
    return NextResponse.json(
      { error: "Address parameter is required" },
      { status: 400 }
    );
  }

  try {
    const tokens: {
      tokenId: string;
      address: string;
      image: string;
      attributes: any;
    }[] = [];
    const legacyAddress = process.env.LEGACY_CONTRACT as Address;
    const genesisAddress = process.env.GENESIS_CONTRACT as Address;
    const contractAddresses = [genesisAddress, legacyAddress];
    const DELAY_BETWEEN_REQUESTS = 1000; // 1 second delay

    for (const contractAddress of contractAddresses) {
      let from = 0;
      while (true) {
        const fetchNft = await _getNfts(address, from, contractAddress);

        // Process current batch
        fetchNft.erc721Tokens.results.forEach((item) => {
          tokens.push({
            tokenId: item.tokenId,
            address: item.owner,
            image: item.image,
            attributes: item.attributes,
          });
        });

        // If we got less results than requested, we're done
        if (fetchNft.erc721Tokens.results.length < 50) {
          break;
        }

        // Update from for next batch
        from += 50;

        // Add delay before next request
        await delay(DELAY_BETWEEN_REQUESTS);
      }
    }

    return NextResponse.json({ tokens });
  } catch (error) {
    console.error("Error fetching NFTs:", error);
    return NextResponse.json(
      { error: "Failed to fetch NFTs" },
      { status: 500 }
    );
  }
}
