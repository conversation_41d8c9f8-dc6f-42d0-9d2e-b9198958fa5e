"use client";

import { Modal } from "@/components/ui/modal";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, X } from "lucide-react";
import { IChickenWithActions } from "@/features/delegation/hooks/useBulkActionSelection";
import Image from "next/image";

interface BulkActionConfirmationModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  actionType: "cancel-delegation" | "unlist-market";
  selectedChickens: IChickenWithActions[];
  onConfirm: () => void;
  onCancel: () => void;
  isProcessing?: boolean;
}

export function BulkActionConfirmationModal({
  isOpen,
  onOpenChange,
  actionType,
  selectedChickens,
  onConfirm,
  onCancel,
  isProcessing = false,
}: BulkActionConfirmationModalProps) {
  const actionConfig = {
    "cancel-delegation": {
      title: "Cancel Delegations",
      description: "Cancel delegation for the selected chickens",
      buttonText: "Cancel Delegations",
      buttonClass: "bg-orange-600 hover:bg-orange-700 text-white",
      icon: "🔄",
      warningText:
        "This will return the chickens to your wallet and end their delegation.",
    },
    "unlist-market": {
      title: "Unlist from Market",
      description: "Remove the selected chickens from the marketplace",
      buttonText: "Unlist Chickens",
      buttonClass: "bg-red-600 hover:bg-red-700 text-white",
      icon: "🏪",
      warningText:
        "This will remove the chickens from the marketplace and return them to your wallet.",
    },
  };

  const config = actionConfig[actionType];
  const eligibleChickens = selectedChickens.filter((chicken) =>
    actionType === "cancel-delegation"
      ? chicken.canCancelDelegation
      : chicken.canUnlistFromMarket
  );

  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
      <Modal.Content>
        <Modal.Header>
          <Modal.Title className="flex items-center gap-2">
            <span className="text-2xl">{config.icon}</span>
            {config.title}
          </Modal.Title>
          <Modal.Description>{config.description}</Modal.Description>
        </Modal.Header>

        <Modal.Body className="space-y-6">
          {/* Warning */}
          <div className="flex items-start gap-3 p-4 bg-amber-500/10 border border-amber-500/20 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-amber-400 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-amber-200 font-medium">Important</p>
              <p className="text-amber-300/80 text-sm mt-1">
                {config.warningText}
              </p>
            </div>
          </div>

          {/* Summary */}
          <div className="bg-stone-800/50 rounded-lg p-4">
            <h3 className="text-white font-medium mb-2">Action Summary</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-stone-300">Total selected:</span>
                <span className="text-white">
                  {selectedChickens.length} chickens
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-stone-300">Eligible for action:</span>
                <span className="text-green-400">
                  {eligibleChickens.length} chickens
                </span>
              </div>
              {selectedChickens.length > eligibleChickens.length && (
                <div className="flex justify-between">
                  <span className="text-stone-300">Will be skipped:</span>
                  <span className="text-stone-400">
                    {selectedChickens.length - eligibleChickens.length} chickens
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Chicken List */}
          <div className="space-y-3">
            <h3 className="text-white font-medium">
              Chickens to{" "}
              {actionType === "cancel-delegation"
                ? "cancel delegation"
                : "unlist"}
              :
            </h3>
            <div className="max-h-64 overflow-y-auto space-y-2">
              {eligibleChickens.map((chicken) => (
                <div
                  key={chicken.tokenId}
                  className="flex items-center gap-3 p-3 bg-stone-800/30 rounded-lg"
                >
                  <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-stone-700">
                    <Image
                      src={chicken.image}
                      alt={`Chicken #${chicken.tokenId}`}
                      fill
                      className="object-cover"
                      sizes="48px"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="text-white font-medium">
                      {chicken.metadata?.name || `Chicken #${chicken.tokenId}`}
                    </div>
                    <div className="text-stone-400 text-sm">
                      Token ID: {chicken.tokenId}
                    </div>
                  </div>
                  <div className="text-right">
                    {actionType === "cancel-delegation" &&
                      chicken.canCancelDelegation && (
                        <div className="text-orange-400 text-sm">Delegated</div>
                      )}
                    {actionType === "unlist-market" &&
                      chicken.canUnlistFromMarket && (
                        <div className="text-red-400 text-sm">Listed</div>
                      )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Ineligible chickens warning */}
          {selectedChickens.length > eligibleChickens.length && (
            <div className="bg-stone-700/30 rounded-lg p-3">
              <p className="text-stone-300 text-sm">
                <strong>
                  {selectedChickens.length - eligibleChickens.length}
                </strong>{" "}
                selected chicken
                {selectedChickens.length - eligibleChickens.length !== 1
                  ? "s"
                  : ""}
                {actionType === "cancel-delegation"
                  ? " are not currently delegated and will be skipped."
                  : " are not currently listed and will be skipped."}
              </p>
            </div>
          )}
        </Modal.Body>

        <Modal.Footer>
          <Button
            appearance="outline"
            onPress={onCancel}
            isDisabled={isProcessing}
          >
            Cancel
          </Button>
          <Button
            className={config.buttonClass}
            onPress={onConfirm}
            isDisabled={isProcessing || eligibleChickens.length === 0}
          >
            {isProcessing ? "Processing..." : config.buttonText}
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
}
