import { Badge } from "@/components/ui";
import { CornAllocationInputs } from "./CornAllocationInputs";
import {
  CornAllocation,
  CraftingPool,
  ItemData,
  RecipeMaterial,
  CornMaterial,
  getCurrentBalance,
  getMaterialDisplayText,
  isType2RandomPool,
  getType2RequirementsForRandomPool,
  getAcceptedResourceIdsForRandomPool,
  getTotalAllocated,
} from "@/utils/crafting-helpers";

interface MaterialRequirementsProps {
  item: ItemData;
  craftingMethod?: "recipe" | "corn";
  recipe?: { materials: RecipeMaterial[] } | null;
  cornRecipe?: {
    recipes: CornMaterial[];
    tokenId: bigint;
    acceptedResourceIds: bigint[];
  } | null;
  craftingPools: Record<number, CraftingPool>;
  quantity: number;
  cornAllocation: CornAllocation;
  foodBalances: Record<number, bigint>;
  cock: bigint;
  feathers: number;
  legendaryFeathers: number;
  onAllocationChange: (resourceId: number, amount: number) => void;
}

const MaterialBadge: React.FC<{
  material: any;
  quantity: number;
  cock: bigint;
  feathers: number;
  legendaryFeathers: number;
  foodBalances: Record<number, bigint>;
}> = ({ material, quantity, cock, feathers, legendaryFeathers, foodBalances }) => {
  const currentBalance = getCurrentBalance(
    material,
    cock,
    feathers,
    legendaryFeathers,
    foodBalances
  );
  const requiredAmount = material.amount * BigInt(quantity);
  const isSufficient = currentBalance >= requiredAmount;
  const displayText = getMaterialDisplayText(
    material,
    quantity,
    cock,
    feathers,
    legendaryFeathers,
    foodBalances
  );

  const tokenAddress = "tokenAddress" in material ? material.tokenAddress : material.address;

  return (
    <Badge
      intent={isSufficient ? "success" : "danger"}
      className="flex items-center gap-1 p-1 lg:p-2 text-xs"
    >
      <img
        src={
          tokenAddress?.toLowerCase() ===
          process.env.NEXT_PUBLIC_COCK_CONTRACT?.toLowerCase()
            ? "/images/COCK_TOKEN_BLUE.webp"
            : tokenAddress?.toLowerCase() ===
                process.env.NEXT_PUBLIC_FEATHERS_CONTRACT?.toLowerCase()
              ? Number(material.tokenId) !== 1
                ? "/images/legendary-feathers.png"
                : "/images/feathers.png"
              : `https://sabong-saga-resources.s3.ap-southeast-1.amazonaws.com/${material.tokenId}.png`
        }
        alt="Material"
        className="w-3 h-3 lg:w-4 lg:h-4 object-contain"
      />
      <span className="font-Poppins font-medium text-xs">
        {displayText}
      </span>
    </Badge>
  );
};

const RandomPoolMaterials: React.FC<{
  craftingPools: Record<number, CraftingPool>;
  quantity: number;
  cornAllocation: CornAllocation;
  foodBalances: Record<number, bigint>;
  cock: bigint;
  feathers: number;
  legendaryFeathers: number;
  onAllocationChange: (resourceId: number, amount: number) => void;
}> = ({
  craftingPools,
  quantity,
  cornAllocation,
  foodBalances,
  cock,
  feathers,
  legendaryFeathers,
  onAllocationChange,
}) => {
  const poolId = 0;

  if (isType2RandomPool(poolId, craftingPools)) {
    const acceptedIds = getAcceptedResourceIdsForRandomPool(poolId, craftingPools);
    const required = getType2RequirementsForRandomPool(poolId, craftingPools);

    return (
      <div className="mb-2 lg:mb-4 space-y-3">
        {acceptedIds.length === 0 ? (
          <div className="p-2 bg-red-500/10 border border-red-500/20 rounded text-red-400 text-xs">
            ⚠️ No accepted resources configured for this pool
          </div>
        ) : (
          <CornAllocationInputs
            foodBalances={foodBalances}
            getTotalAllocated={getTotalAllocated}
            allocation={cornAllocation}
            requiredTotal={required! * quantity}
            acceptedResourceIds={acceptedIds}
            onAllocationChange={onAllocationChange}
          />
        )}

        {/* Show other (non-type 2) materials if they exist */}
        {(() => {
          const pool = craftingPools[poolId];
          if (!pool) return null;
          const otherMaterials = pool.materials.filter(
            (material: any) => material.tokenType !== 2
          );

          if (otherMaterials.length === 0) return null;

          return (
            <div>
              <p className="text-xs text-white/60 mb-2">Additional Requirements:</p>
              <div className="flex flex-wrap gap-1 lg:gap-2 items-center">
                {otherMaterials.map((material: any, index: number) => (
                  <MaterialBadge
                    key={index}
                    material={material}
                    quantity={quantity}
                    cock={cock}
                    feathers={feathers}
                    legendaryFeathers={legendaryFeathers}
                    foodBalances={foodBalances}
                  />
                ))}
              </div>
            </div>
          );
        })()}
      </div>
    );
  }

  // Non-type 2 random pools
  const pool = craftingPools[poolId];
  if (!pool || !pool.materials) return null;

  return (
    <div className="flex flex-wrap gap-1 lg:gap-2 items-center mb-2 lg:mb-3">
      {pool.materials.map((material: any, index: number) => (
        <MaterialBadge
          key={index}
          material={material}
          quantity={quantity}
          cock={cock}
          feathers={feathers}
          legendaryFeathers={legendaryFeathers}
          foodBalances={foodBalances}
        />
      ))}
    </div>
  );
};

const RegularMaterials: React.FC<{
  materials: (RecipeMaterial | CornMaterial)[];
  quantity: number;
  cock: bigint;
  feathers: number;
  legendaryFeathers: number;
  foodBalances: Record<number, bigint>;
}> = ({ materials, quantity, cock, feathers, legendaryFeathers, foodBalances }) => {
  return (
    <div className="flex flex-wrap gap-1 lg:gap-2 items-center mb-2 lg:mb-4">
      {materials.map((material, index) => (
        <MaterialBadge
          key={index}
          material={material}
          quantity={quantity}
          cock={cock}
          feathers={feathers}
          legendaryFeathers={legendaryFeathers}
          foodBalances={foodBalances}
        />
      ))}
    </div>
  );
};

const VariableCraftingMaterials: React.FC<{
  cornRecipe: {
    recipes: CornMaterial[];
    tokenId: bigint;
    acceptedResourceIds: bigint[];
  };
  quantity: number;
  cornAllocation: CornAllocation;
  foodBalances: Record<number, bigint>;
  cock: bigint;
  feathers: number;
  legendaryFeathers: number;
  onAllocationChange: (resourceId: number, amount: number) => void;
  isType2: boolean;
  getType2Requirements: (cornRecipe: any) => number | null;
  getAcceptedResourceIds: (cornRecipe: any) => number[];
}> = ({
  cornRecipe,
  quantity,
  cornAllocation,
  foodBalances,
  cock,
  feathers,
  legendaryFeathers,
  onAllocationChange,
  isType2,
  getType2Requirements,
  getAcceptedResourceIds,
}) => {
  if (isType2) {
    const acceptedIds = getAcceptedResourceIds(cornRecipe);
    const required = getType2Requirements(cornRecipe);

    return (
      <div className="mb-2 lg:mb-4 space-y-3">
        {acceptedIds.length === 0 ? (
          <div className="p-2 bg-red-500/10 border border-red-500/20 rounded text-red-400 text-xs">
            ⚠️ No accepted resources configured for this recipe
          </div>
        ) : (
          <CornAllocationInputs
            foodBalances={foodBalances}
            getTotalAllocated={getTotalAllocated}
            allocation={cornAllocation}
            requiredTotal={required! * quantity}
            acceptedResourceIds={acceptedIds}
            onAllocationChange={onAllocationChange}
          />
        )}

        {/* Show other (non-type 2) materials if they exist */}
        {(() => {
          const otherMaterials = cornRecipe.recipes.filter(
            (r: CornMaterial) => r.tokenType !== 2
          );

          if (otherMaterials.length === 0) return null;

          return (
            <div>
              <p className="text-xs text-white/60 mb-2">Additional Requirements:</p>
              <RegularMaterials
                materials={otherMaterials}
                quantity={quantity}
                cock={cock}
                feathers={feathers}
                legendaryFeathers={legendaryFeathers}
                foodBalances={foodBalances}
              />
            </div>
          );
        })()}
      </div>
    );
  }

  // Non-type 2 variable recipes
  return (
    <RegularMaterials
      materials={cornRecipe.recipes}
      quantity={quantity}
      cock={cock}
      feathers={feathers}
      legendaryFeathers={legendaryFeathers}
      foodBalances={foodBalances}
    />
  );
};

export const MaterialRequirements: React.FC<MaterialRequirementsProps> = ({
  item,
  craftingMethod,
  recipe,
  cornRecipe,
  craftingPools,
  quantity,
  cornAllocation,
  foodBalances,
  cock,
  feathers,
  legendaryFeathers,
  onAllocationChange,
}) => {
  // Handle random items
  if (item.isRandom) {
    return (
      <RandomPoolMaterials
        craftingPools={craftingPools}
        quantity={quantity}
        cornAllocation={cornAllocation}
        foodBalances={foodBalances}
        cock={cock}
        feathers={feathers}
        legendaryFeathers={legendaryFeathers}
        onAllocationChange={onAllocationChange}
      />
    );
  }

  // Handle regular items based on crafting method
  if (craftingMethod === "recipe" && recipe) {
    return (
      <RegularMaterials
        materials={recipe.materials}
        quantity={quantity}
        cock={cock}
        feathers={feathers}
        legendaryFeathers={legendaryFeathers}
        foodBalances={foodBalances}
      />
    );
  }

  if (craftingMethod === "corn" && cornRecipe) {
    // Import these from crafting-helpers
    const isType2CornRecipe = (cornRecipe: any): boolean => {
      if (!cornRecipe || !cornRecipe.recipes) return false;
      return cornRecipe.recipes.some((recipe: any) => recipe.tokenType === 2);
    };

    const getType2Requirements = (cornRecipe: any): number | null => {
      if (!cornRecipe || !cornRecipe.recipes) return null;
      const type2Recipe = cornRecipe.recipes.find(
        (recipe: any) => recipe.tokenType === 2
      );
      return type2Recipe ? Number(type2Recipe.amount) : null;
    };

    const getAcceptedResourceIds = (cornRecipe: any): number[] => {
      if (!cornRecipe || !cornRecipe.acceptedResourceIds) return [];
      return cornRecipe.acceptedResourceIds.map((id: bigint) => Number(id));
    };

    return (
      <VariableCraftingMaterials
        cornRecipe={cornRecipe}
        quantity={quantity}
        cornAllocation={cornAllocation}
        foodBalances={foodBalances}
        cock={cock}
        feathers={feathers}
        legendaryFeathers={legendaryFeathers}
        onAllocationChange={onAllocationChange}
        isType2={isType2CornRecipe(cornRecipe)}
        getType2Requirements={getType2Requirements}
        getAcceptedResourceIds={getAcceptedResourceIds}
      />
    );
  }

  return null;
};