import React, { useEffect, useRef } from "react";

interface GradientAvatarProps {
  address: string;
  className?: string;
  size?: number;
}

const getGradientColors = (address: string): string[] => {
  const seedArr = address.match(/.{1,7}/g)?.slice(0, 5) || [];
  return seedArr.map((seed) => {
    let hash = 0;
    for (let i = 0; i < seed.length; i++) {
      hash = seed.charCodeAt(i) + ((hash << 5) - hash);
      hash = hash & hash;
    }

    const rgb = Array(3)
      .fill(0)
      .map((_, i) => (hash >> (i * 8)) & 255);

    return `rgb(${rgb.join(", ")})`;
  });
};

export const GradientAvatar: React.FC<GradientAvatarProps> = ({
  address,
  className = "",
  size = 40,
}) => {
  const avatarRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (avatarRef.current) {
      const colors = getGradientColors(address);
      const defaultColor = "rgb(200, 200, 200)";

      const backgroundImage = `
        radial-gradient(at 66% 77%, ${colors[1] || defaultColor} 0px, transparent 50%),
        radial-gradient(at 29% 97%, ${colors[2] || defaultColor} 0px, transparent 50%),
        radial-gradient(at 99% 86%, ${colors[3] || defaultColor} 0px, transparent 50%),
        radial-gradient(at 29% 88%, ${colors[4] || defaultColor} 0px, transparent 50%)
      `;

      avatarRef.current.style.backgroundColor = colors[0] || defaultColor;
      avatarRef.current.style.backgroundImage = backgroundImage;
    }
  }, [address]);

  return (
    <div
      ref={avatarRef}
      className={`rounded-full shadow-[inset_0_0_0_1px_rgba(0,0,0,0.1)] ${className}`}
      style={{
        width: size,
        height: size,
      }}
    />
  );
};
