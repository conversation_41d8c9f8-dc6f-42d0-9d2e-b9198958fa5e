import { proxyApi } from "@/lib/proxy-api";
import { SIWEResponse } from "@/types/api.types";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  // Get the full URL
  const { searchParams } = new URL(request.url);

  // Get specific param
  const page = searchParams.get("page");

  const req = await fetch(
    `${process.env.HONO_API_ENDPOINT}/api/leaderboard?page=${page}&limit=7`, {
      cache: "no-store",
    }
  );

  if (req.ok) {
    const data = await req.json();

    return NextResponse.json(data, { status: 200 });
  } else {
    return NextResponse.json(
      {
        status: false,
        responseCode: 400,
        message: "Bad request",
      },
      { status: 400 }
    );
  }
}
