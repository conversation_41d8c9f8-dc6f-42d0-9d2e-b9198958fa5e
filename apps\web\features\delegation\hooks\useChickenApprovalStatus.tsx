"use client";

import { useChickenApproval } from "./useChickenApproval";

/**
 * Simplified hook for checking chicken approval status in UI components
 * This is a convenience wrapper around useChickenApproval for UI usage
 */
export const useChickenApprovalStatus = (tokenId: number) => {
  const { useApprovalStatus, executeApproval, isApproving } = useChickenApproval();
  
  // Get approval status for the specific chicken
  const approvalQuery = useApprovalStatus(tokenId);
  
  return {
    // Status
    isApproved: approvalQuery.data ?? false,
    isLoading: approvalQuery.isLoading,
    isApproving,
    error: approvalQuery.error,
    
    // Actions
    executeApproval: () => executeApproval(tokenId),
    refetch: approvalQuery.refetch,
    
    // Query object for advanced usage
    approvalQuery,
  };
};
