import { ChickenCard } from "./ChickenCard";
import { ChickenStats } from "@/types/chicken.type";

interface Chicken {
  tokenId: string;
  image: string;
  attributes?: {
    Type?: string[];
  };
  chickenType?:
    | "owned"
    | "delegated-to-me"
    | "rented-to-me"
    | "delegated-out"
    | "rented-out"
    | "listed-in-market";
  isDelegated?: boolean;
  isRented?: boolean;
  isDisabled?: boolean;
  delegationInfo?: any;
  rentalInfo?: any;
}

interface ChickenGridProps {
  chickens: Chicken[];
  chickenStats: Record<string, ChickenStats>;
  statsLoading: Set<string>;
  cooldownTimers: Record<string, number>;
  recoveryTimers: Record<string, number>;
  breedingTimers: Record<string, number>;
  listedTimers: Record<string, number>;
  transferTimers: Record<string, number>;
  immortalTimers: Record<string, number>;
  formatTime: (
    seconds: number,
    type:
      | "cooldown"
      | "recovery"
      | "breeding"
      | "listed"
      | "transfer"
      | "immortal"
  ) => string;
  isFavorite: (tokenId: string) => boolean;
  isEgg: (chicken: Chicken) => boolean;
  isFaint: (tokenId: string) => boolean;
  isDead: (tokenId: string) => boolean;
  isBreeding: (tokenId: string) => boolean;
  isListed: (tokenId: string) => boolean;
  wasTransferredToday: (tokenId: string) => boolean;
  wasListedToday: (tokenId: string) => boolean;
  isImmortal: (tokenId: string) => boolean;
  onChickenClick: (tokenId: string) => void;
  onToggleFavorite: (e: React.MouseEvent, tokenId: string) => void;
  onBattle: (e: React.MouseEvent, tokenId: string) => void;
  onHeal: (e: React.MouseEvent, tokenId: string) => void;
  onFeed: (e: React.MouseEvent, tokenId: string) => void;
  onBreed: (e: React.MouseEvent, tokenId: string) => void;
  onMenuAction: (e: React.MouseEvent, action: string, tokenId: string) => void;
  // Bulk action props
  isBulkMode?: boolean;
  isChickenSelected?: (tokenId: string) => boolean;
  canSelectChicken?: (tokenId: string) => boolean;
  getChickenActionReason?: (tokenId: string) => string;
  onToggleChickenSelection?: (tokenId: string) => void;
}

export function ChickenGrid({
  chickens,
  chickenStats,
  statsLoading,
  cooldownTimers,
  recoveryTimers,
  breedingTimers,
  listedTimers,
  transferTimers,
  immortalTimers,
  formatTime,
  isFavorite,
  isEgg,
  isFaint,
  isDead,
  isBreeding,
  isListed,
  wasTransferredToday,
  wasListedToday,
  isImmortal,
  onChickenClick,
  onToggleFavorite,
  onBattle,
  onHeal,
  onFeed,
  onBreed,
  onMenuAction,
  isBulkMode = false,
  isChickenSelected,
  canSelectChicken,
  getChickenActionReason,
  onToggleChickenSelection,
}: ChickenGridProps) {
  if (chickens.length === 0) {
    return (
      <div className="text-center text-white p-8">
        You don't have any chickens yet.
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {chickens.map((chicken) => {
        const chickenIsFavorite = isFavorite(chicken.tokenId);
        const chickenIsEgg = isEgg(chicken);
        const chickenIsFaint = isFaint(chicken.tokenId);
        const chickenIsDead = isDead(chicken.tokenId);
        const chickenIsBreeding = isBreeding(chicken.tokenId);
        const chickenIsListed = isListed(chicken.tokenId);
        const chickenWasTransferred = wasTransferredToday(chicken.tokenId);
        const chickenWasListed = wasListedToday(chicken.tokenId);
        const chickenIsImmortal = isImmortal(chicken.tokenId);

        return (
          <ChickenCard
            key={chicken.tokenId}
            chicken={chicken}
            stats={chickenStats[chicken.tokenId]}
            isStatsLoading={statsLoading.has(chicken.tokenId)}
            isFavorite={chickenIsFavorite}
            isEgg={chickenIsEgg}
            isFaint={chickenIsFaint}
            isDead={chickenIsDead}
            isBreeding={chickenIsBreeding}
            isListed={chickenIsListed}
            wasTransferred={chickenWasTransferred}
            isImmortal={chickenIsImmortal}
            cooldownTimers={cooldownTimers}
            recoveryTimers={recoveryTimers}
            breedingTimers={breedingTimers}
            listedTimers={listedTimers}
            transferTimers={transferTimers}
            immortalTimers={immortalTimers}
            formatTime={formatTime}
            onChickenClick={onChickenClick}
            onToggleFavorite={onToggleFavorite}
            onBattle={onBattle}
            onHeal={onHeal}
            onFeed={onFeed}
            onBreed={onBreed}
            onMenuAction={onMenuAction}
            // Bulk action props
            isBulkMode={isBulkMode}
            isSelected={
              isChickenSelected ? isChickenSelected(chicken.tokenId) : false
            }
            canSelect={
              canSelectChicken ? canSelectChicken(chicken.tokenId) : true
            }
            onToggleSelection={onToggleChickenSelection}
          />
        );
      })}
    </div>
  );
}
