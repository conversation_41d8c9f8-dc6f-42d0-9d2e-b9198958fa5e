"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Modal } from "ui";
import { cn } from "@/utils/classes";
import { toast } from "sonner";
import Image from "next/image";
import { useStateContext } from "@/providers/app/state";
import {
  IChickenWithRewards,
  IChickenSelection,
  ERewardType,
} from "../types/inventory.types";
import { useTransferInventory } from "../hooks/useTransferInventory";
import { useInventoryBalance } from "../hooks/useInventoryBalance";
import useClaimRewardStore from "@/store/claim-rewards";
import { useClaimHistory } from "../hooks/useClaimHistory";
import { delay } from "@/utils/delay";

interface IChickenSelectionActionsProps {
  selectedChickens: IChickenSelection;
  chickensWithRewards: IChickenWithRewards[];
  onClaimSelectedChickens: () => void;
  onClaimAllChickens: () => void;
  onClearSelection?: () => void;
  className?: string;
}

/**
 * ChickenSelectionActions Component
 *
 * Provides bulk action controls for selected chickens
 * Similar to ninuno rewards transfer section
 */
export const ChickenSelectionActions: React.FC<
  IChickenSelectionActionsProps
> = ({
  selectedChickens,
  chickensWithRewards,
  onClaimSelectedChickens,
  onClaimAllChickens,
  onClearSelection,
  className,
}) => {
  const {
    claimItems,
    getGameClaimSignature,
    isPending,
    canClaim,
    ableToClaimIn,
  } = useClaimRewardStore();
  const { address } = useStateContext();
  const { refetch: refetchClaimHistory } = useClaimHistory(
    (address as string) || null
  );
  const [showTransferModal, setShowTransferModal] = useState(false);

  const [secondsLeft, setSecondsLeft] = useState<number>(Number(ableToClaimIn));

  useEffect(() => {
    setSecondsLeft(Number(ableToClaimIn)); // reset timer when store updates
  }, [ableToClaimIn]);

  useEffect(() => {
    if (secondsLeft <= 0) return;

    const interval = setInterval(() => {
      setSecondsLeft((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [secondsLeft]);

  const formatTime = (s: number) => {
    const hours = Math.floor(s / 3600);
    const minutes = Math.floor((s % 3600) / 60);
    const seconds = s % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  // Use real hooks for transfer and balance
  const { transferInventoryMutation, isTransferring } = useTransferInventory();
  const {
    tokenBalances,
    totalClaimableBalance,
    isLoading: isBalanceLoading,
    claimMutation,
    isClaiming,
    refetch,
  } = useInventoryBalance(address || null);

  const selectedChickenIds = Object.keys(selectedChickens).filter(
    (id) => selectedChickens[id]
  );

  const selectedChickensData = chickensWithRewards.filter((chicken) =>
    selectedChickenIds.includes(chicken.chickenId)
  );

  // Calculate total rewards from selected chickens
  const totalSelectedRewards = selectedChickensData.reduce(
    (sum, chicken) => sum + chicken.totalRewards,
    0
  );

  // Calculate rewards by type from selected chickens
  const selectedRewardsByType = selectedChickensData.reduce(
    (acc, chicken) => {
      Object.entries(chicken.rewardsByType).forEach(([type, quantity]) => {
        acc[type] = (acc[type] || 0) + quantity;
      });
      return acc;
    },
    {} as Record<string, number>
  );

  // Transfer to balance functions
  const handleTransferToBalance = async () => {
    if (!address) {
      toast.error("Wallet address not available");
      return;
    }

    try {
      // Collect chicken IDs from selected chickens
      const chickenIds = selectedChickenIds.map((id) => parseInt(id, 10));

      if (chickenIds.length === 0) {
        toast.error("No chickens selected for transfer");
        return;
      }

      console.log("🔄 Transferring rewards to balance:", {
        address,
        chickenIds,
        selectedChickens: selectedChickenIds,
        rewards: selectedRewardsByType,
      });

      await transferInventoryMutation.mutateAsync({
        address,
        chickenIds,
      });

      setShowTransferModal(false);
      // Clear selection after successful transfer
      onClearSelection?.();
      toast.success("Rewards transferred to balance successfully!");
    } catch (error: any) {
      console.error("Transfer failed:", error);
      toast.error(
        error.response?.data?.error ||
          error.response?.data?.message ||
          "Failed to transfer rewards. Please try again later."
      );
    }
  };

  // Individual token claiming states
  const [claimingToken, setClaimingToken] = useState<number | null>(null);
  const [showTokenClaimModal, setShowTokenClaimModal] = useState(false);
  const [selectedToken, setSelectedToken] = useState<{
    tokenId: number;
    displayName: string;
    name: string;
    image: string;
    balance: number;
  } | null>(null);

  // Handle per-token claiming
  const handleClaimToken = async (token: {
    tokenId: number;
    displayName: string;
    name: string;
    image: string;
    balance: number;
  }) => {
    setSelectedToken(token);
    setShowTokenClaimModal(true);
  };

  // Handle claiming all tokens
  const handleClaimAllTokens = async () => {
    if (!address) {
      toast.error("Wallet address not available");
      return;
    }

    try {
      // Create claims array from tokenBalances
      const contracts: string[] = [];
      const tokenIds: number[] = [];
      const amounts: number[] = [];

      tokenBalances
        .filter((token) => token.balance > 0)
        .forEach((token) => {
          tokenIds.push(token.tokenId);
          amounts.push(token.balance);
          contracts.push(process.env.NEXT_PUBLIC_GAMEITEMS_CONTRACT as string);
        });

      // Now you have the data in the desired format
      const claims = {
        contracts,
        tokenIds,
        amounts,
      };

      const signatureWithData = await getGameClaimSignature(
        claims.contracts,
        claims.tokenIds,
        claims.amounts
      );

      await claimItems(signatureWithData);
      await delay(5000);
      await refetch();
      await refetchClaimHistory();

      toast.success(`Successfully claimed all tokens!`);
    } catch (error: any) {
      console.error("Claim all failed:", error);
      toast.error(
        error.response?.data?.error ||
          error.response?.data?.message ||
          "Failed to claim tokens. Please try again later."
      );
    }
  };

  // Execute token claim
  const executeClaimToken = async () => {
    if (!selectedToken || !address) return;

    setClaimingToken(selectedToken.tokenId);
    try {
      console.log(`💎 Claiming ${selectedToken.name} from balance:`, {
        address,
        tokenId: selectedToken.tokenId,
        amount: selectedToken.balance,
      });
      const CA = process.env.NEXT_PUBLIC_GAMEITEMS_CONTRACT as string;

      const claims = {
        contracts: [CA],
        tokenIds: [selectedToken.tokenId],
        amounts: [selectedToken.balance],
      };

      const signatureWithData = await getGameClaimSignature(
        claims.contracts,
        claims.tokenIds,
        claims.amounts
      );

      await claimItems(signatureWithData);
      await delay(5000);
      await refetch();
      await refetchClaimHistory();

      setShowTokenClaimModal(false);
      toast.success(
        `Successfully claimed ${selectedToken.balance} ${selectedToken.name}!`
      );
    } catch (error: any) {
      console.error(`Claim failed for ${selectedToken.name}:`, error);
      toast.error(
        error.response?.data?.error ||
          error.response?.data?.message ||
          `Failed to claim ${selectedToken.name}. Please try again later.`
      );
    } finally {
      setClaimingToken(null);
      setSelectedToken(null);
    }
  };

  // Get reward type badge color - improved contrast
  const getTypeColor = (type: ERewardType) => {
    switch (type) {
      case ERewardType.CRYSTAL:
        return "bg-blue-600/30 text-blue-200 border border-blue-400/40";
      case ERewardType.SHARD:
        return "bg-purple-600/30 text-purple-200 border border-purple-400/40";
      case ERewardType.CORN:
        return "bg-amber-600/30 text-amber-200 border border-amber-400/40";
      default:
        return "bg-stone-600/30 text-stone-200 border border-stone-400/40";
    }
  };

  return (
    <>
      <div
        className={cn(
          "bg-stone-900/80 rounded-lg border border-amber-400/20 p-6 backdrop-blur-sm",
          className
        )}
      >
        {/* <h3 className="text-xl font-semibold text-amber-200 mb-6">
          Rewards Management
        </h3> */}

        {/* STEP 1: Transfer to Balance Section */}
        <div className="bg-stone-800/60 rounded-lg p-5 mb-6 border border-stone-600/30">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h4 className="text-lg font-semibold text-white">
                Step 1: Transfer to Balance
              </h4>
              <p className="text-sm text-stone-400 mt-1">
                Transfer rewards from selected chickens to your claimable
                balance
              </p>
            </div>
            {/* <div className="bg-amber-500/20 text-amber-200 px-3 py-1 rounded-full text-sm font-semibold">
              {selectedChickenIds.length} Selected
            </div> */}
          </div>

          {selectedChickenIds.length > 0 && (
            <div className="space-y-3 mb-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-stone-300">
                  Total Rewards to Transfer:
                </span>
                <div className="bg-amber-500/90 text-amber-900 px-3 py-1 rounded-full text-sm font-bold">
                  {totalSelectedRewards}
                </div>
              </div>

              {/* Rewards by Type */}
              {Object.keys(selectedRewardsByType).length > 0 && (
                <div className="space-y-2">
                  <span className="text-sm font-medium text-stone-300">
                    Rewards by Type:
                  </span>
                  <div className="flex flex-wrap gap-1.5">
                    {Object.entries(selectedRewardsByType).map(
                      ([type, quantity]) => (
                        <span
                          key={type}
                          className={cn(
                            "px-2 py-1 rounded text-xs font-medium",
                            getTypeColor(type as ERewardType)
                          )}
                        >
                          {quantity} {type}
                        </span>
                      )
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          <Button
            intent="primary"
            size="medium"
            onPress={() => setShowTransferModal(true)}
            isDisabled={selectedChickenIds.length === 0 || isTransferring}
            className="w-full bg-blue-600 hover:bg-blue-500 text-blue-50 font-semibold disabled:bg-stone-600 disabled:text-stone-400 disabled:cursor-not-allowed"
          >
            {isTransferring
              ? "Transferring..."
              : selectedChickenIds.length === 0
                ? "Select Chickens to Transfer"
                : `Transfer ${selectedChickenIds.length} Chicken Rewards`}
          </Button>
        </div>

        {/* STEP 2: Claim Available Materials Section */}
        <div className="bg-stone-800/60 rounded-lg p-5 mb-6 border border-stone-600/30">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h4 className="text-lg font-semibold text-white">
                Step 2: Claim Available Materials
              </h4>
              <p className="text-sm text-stone-400 mt-1">
                Claim individual materials from your balance to receive tokens
                in your wallet
              </p>
            </div>
          </div>
          <div className="bg-green-500/20 text-green-200 px-3 py-1 rounded-full text-sm font-semibold border border-green-400/40 mb-5 text-center">
            {totalClaimableBalance} Total Items
          </div>

          {/* Claim All Button */}
          {(totalClaimableBalance as number) > 0 && (
            <>
              <div className="mt-4 p-4 bg-stone-800/40 rounded-lg border border-stone-600/30">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <div>
                    <p className="text-sm font-semibold text-green-200 mb-1">
                      Claim All Tokens
                    </p>
                    <p className="text-xs text-stone-400">
                      Claim all {totalClaimableBalance} tokens at once
                    </p>
                  </div>
                  <Button
                    intent="primary"
                    size="medium"
                    onPress={() => handleClaimAllTokens()}
                    isDisabled={
                      !canClaim ||
                      claimingToken !== null ||
                      isClaiming ||
                      totalClaimableBalance === 0 ||
                      isPending
                    }
                    className="bg-green-600 hover:bg-green-500 text-green-50 font-semibold disabled:bg-stone-600 disabled:text-stone-400 disabled:cursor-not-allowed w-full sm:w-auto"
                  >
                    {claimingToken !== null || isClaiming || isPending
                      ? "Processing..."
                      : "Claim All"}
                  </Button>
                </div>
              </div>

              {!canClaim && (
                <div className="flex justify-center font-Poppins mt-4 text-primary">
                  {secondsLeft > 0 && (
                    <span className="text-yellow-500">
                      ⏳ Claim available in {formatTime(secondsLeft)}
                    </span>
                  )}
                </div>
              )}
            </>
          )}

          {/* Token List */}
          <div className="space-y-3 mt-5">
            {tokenBalances.map((token) => (
              <div
                key={token.tokenId}
                className="relative group rounded-lg transition-all duration-200 bg-stone-700/40 border border-stone-600/30 hover:border-stone-500/50 hover:bg-stone-700/60"
              >
                <div className="p-4">
                  <div className="flex items-center gap-4">
                    {/* Token Icon */}
                    <div className="w-12 h-12 rounded-lg overflow-hidden border border-stone-600/40 flex-shrink-0 bg-stone-800">
                      <Image
                        src={token.image}
                        alt={token.name}
                        width={48}
                        height={48}
                        quality={100}
                        unoptimized
                        className="w-full h-full object-contain"
                      />
                    </div>

                    {/* Token Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-sm font-semibold text-stone-200">
                          {token.name}
                        </h3>
                        {/* <span className="bg-stone-600/40 text-stone-300 px-2 py-0.5 rounded text-xs font-medium">
                          ID: {token.tokenId}
                        </span> */}
                      </div>
                      <p className="text-xs text-stone-400">
                        {token.balance} available to claim
                      </p>
                    </div>

                    {/* Claim Button */}
                    <div className="flex items-center">
                      <Button
                        intent="primary"
                        size="small"
                        onPress={() => handleClaimToken(token)}
                        isDisabled={
                          !canClaim ||
                          token.balance === 0 ||
                          claimingToken === token.tokenId ||
                          isClaiming
                        }
                        className="bg-green-600 hover:bg-green-500 text-green-50 font-semibold disabled:bg-stone-600 disabled:text-stone-400 disabled:cursor-not-allowed min-w-[80px]"
                      >
                        {claimingToken === token.tokenId || isClaiming
                          ? "Claiming..."
                          : token.balance === 0
                            ? "No Balance"
                            : "Claim"}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Loading State */}
          {isBalanceLoading && (
            <div className="text-center py-8">
              <span className="text-4xl mb-3 block">⏳</span>
              <p className="text-stone-400">Loading balance...</p>
            </div>
          )}

          {/* Empty State */}
          {!isBalanceLoading && totalClaimableBalance === 0 && (
            <div className="text-center py-8">
              <span className="text-4xl mb-3 block">💰</span>
              <p className="text-stone-400">No tokens available to claim.</p>
              <p className="text-stone-500 text-sm mt-2">
                Transfer rewards from chickens first to build your claimable
                balance.
              </p>
            </div>
          )}
        </div>

        {/* Info */}
        <div className="mt-6 p-4 bg-amber-600/10 rounded-lg border border-amber-400/20">
          <p className="text-xs text-amber-200/90 leading-relaxed">
            💡 <span className="font-semibold">Two-Step Process:</span> First
            transfer rewards from selected chickens to your balance, then claim
            individual tokens to receive them in your wallet.
          </p>
        </div>
      </div>

      {/* Transfer to Balance Modal */}
      <Modal isOpen={showTransferModal} onOpenChange={setShowTransferModal}>
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>Transfer Rewards to Balance</Modal.Title>
            <Modal.Description>
              Transfer selected chicken rewards to your claimable balance
            </Modal.Description>
          </Modal.Header>

          <Modal.Body>
            <div className="py-4 space-y-4">
              <div className="rounded-lg bg-stone-800/60 p-4 border border-stone-600/30">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-stone-300">
                      Selected Chickens:
                    </span>
                    <span className="bg-amber-500/20 text-amber-200 px-2 py-1 rounded text-sm font-semibold">
                      {selectedChickenIds.length}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-stone-300">
                      Total Rewards:
                    </span>
                    <span className="bg-amber-500/90 text-amber-900 px-3 py-1 rounded-full text-sm font-bold">
                      {totalSelectedRewards}
                    </span>
                  </div>

                  {Object.keys(selectedRewardsByType).length > 0 && (
                    <div className="space-y-2">
                      <span className="text-sm font-medium text-stone-300">
                        Rewards by Type:
                      </span>
                      <div className="flex flex-wrap gap-2">
                        {Object.entries(selectedRewardsByType).map(
                          ([type, quantity]) => (
                            <span
                              key={type}
                              className={cn(
                                "px-2 py-1 rounded text-xs font-medium",
                                getTypeColor(type as ERewardType)
                              )}
                            >
                              {quantity} {type}
                            </span>
                          )
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="text-sm text-stone-400">
                <p>
                  This will transfer the accumulated rewards from your selected
                  chickens to your claimable balance. Once transferred, you can
                  claim them to your wallet using the "Claim Balance" button.
                </p>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button
              intent="secondary"
              onPress={() => setShowTransferModal(false)}
              isDisabled={isTransferring}
            >
              Cancel
            </Button>
            <Button
              intent="primary"
              onPress={handleTransferToBalance}
              isDisabled={isTransferring || selectedChickenIds.length === 0}
              className="bg-blue-600 hover:bg-blue-500"
            >
              {isTransferring ? "Transferring..." : "Transfer to Balance"}
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>

      {/* Per-Token Claim Modal */}
      <Modal isOpen={showTokenClaimModal} onOpenChange={setShowTokenClaimModal}>
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>Claim {selectedToken?.name} to Wallet</Modal.Title>
            <Modal.Description>
              Claim your {selectedToken?.name} balance to receive tokens in your
              wallet
            </Modal.Description>
          </Modal.Header>

          <Modal.Body>
            <div className="py-4 space-y-4">
              <div className="rounded-lg bg-stone-800/60 p-4 border border-stone-600/30">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-stone-300">
                      Token:
                    </span>
                    {selectedToken && (
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 rounded overflow-hidden border border-stone-600/40 bg-stone-800">
                          <Image
                            src={selectedToken.image}
                            alt={selectedToken.name}
                            width={32}
                            height={32}
                            quality={100}
                            unoptimized
                            className="w-full h-full object-contain"
                          />
                        </div>
                        <span className="text-sm font-semibold text-stone-200">
                          {selectedToken.name}
                        </span>
                        <span className="bg-stone-600/40 text-stone-300 px-2 py-0.5 rounded text-xs font-medium">
                          ID: {selectedToken.tokenId}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-stone-300">
                      Amount to Claim:
                    </span>
                    <span className="bg-green-500/20 text-green-200 px-3 py-1 rounded-full text-sm font-bold">
                      {selectedToken?.balance} tokens
                    </span>
                  </div>
                </div>
              </div>

              <div className="text-sm text-stone-400">
                <p>
                  This will initiate a blockchain transaction to claim your{" "}
                  {selectedToken?.name} balance. You will need to confirm the
                  transaction in your wallet to receive the tokens.
                </p>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button
              intent="secondary"
              onPress={() => {
                setShowTokenClaimModal(false);
                setSelectedToken(null);
              }}
              isDisabled={claimingToken !== null || isClaiming}
            >
              Cancel
            </Button>
            <Button
              intent="primary"
              onPress={executeClaimToken}
              isDisabled={
                !canClaim ||
                claimingToken !== null ||
                isClaiming ||
                !selectedToken ||
                selectedToken.balance === 0 ||
                isPending
              }
              className="bg-green-600 hover:bg-green-500"
            >
              {claimingToken !== null || isClaiming || isPending
                ? "Processing..."
                : `Claim ${selectedToken?.name}`}
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>
    </>
  );
};
