export type NFT = {
  contractAddress: string;
  name: string;
  symbol: string;

  balance: string;
  tokenUri: string;
  tokenId: string;

  metadata: {
    image: string;
    description: string;
    name: string;
    externalUrl: string;
    attributes: {
      trait_type: string;
      value: number | string | boolean;
      display_type: "number" | "string" | "date" | "bool";
    }[];
  };
};

export interface MetadataAttribute {
  trait_type: string;
  value: string | number | boolean;
  display_type: "string" | "number" | "date" | "bool";
}

export type NFTMetadata = {
  name: string;
  description: string;
  image: string;
  external_url: string;
  attributes: MetadataAttribute[];
};

export type MetadataAttributes = {
  trait_type: string;
  value: number | string | boolean;
  display_type: "number" | "string" | "date" | "bool";
}[];
