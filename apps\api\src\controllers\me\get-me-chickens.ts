import { Context } from "hono";
import { ResponseHelper, sendResponse } from "../../utils/response-helper";
import { getAddress } from "viem";
import { getNfts } from "../../services/getNfts";
import { getRevealMetadata } from "../../services/get-reveal-metadata";

interface ChickenData {
  tokenId: string;
  image?: string;
}

async function fetchNFTMetadata(nft: {
  id: string;
  address: string;
}): Promise<ChickenData> {
  try {
    const metadata = await getRevealMetadata({ tokenId: nft.id });

    if (!metadata) {
      throw new Error(`No metadata found for token ${nft.id}`);
    }
    return {
      tokenId: nft.id,
      image: metadata.image,
    };
  } catch (error) {
    return {
      tokenId: nft.id,
    };
  }
}

async function batchGetMetadata(
  nfts: { id: string; address: string }[]
): Promise<ChickenData[]> {
  const metadataPromises = nfts.map(fetchNFTMetadata);
  return Promise.all(metadataPromises);
}

export const getMeChickens = async (c: Context) => {
  const user = c.get("user");
  const address = user.address;

  try {
    const checksumAddress = getAddress(address).toLowerCase();
    const nfts = await getNfts(checksumAddress);

    if (!nfts?.length) {
      return sendResponse(c, ResponseHelper.success([]));
    }

    const nftWithMetadata = await batchGetMetadata(nfts);

    return sendResponse(c, ResponseHelper.success(nftWithMetadata));
  } catch (error) {
    const knownError = error as Error;
    return sendResponse(c, ResponseHelper.serverError(knownError.message));
  }
};
