import AppInitializer from "@/providers/app/initializer";
import dynamic from "next/dynamic";
import CardsLoaders from "@/components/shared/cards-loaders";
import AppNavbar from "@/components/shared/navbar";

// Dynamically import the Rewards feature component
const DynamicRewards = dynamic(() => import("../../features/rewards"), {
  ssr: true,
  loading: () => <CardsLoaders />,
});

/**
 * Rewards Page
 *
 * This page serves as the entry point for the Rewards feature.
 * It uses the AppInitializer to ensure the app is properly initialized
 * and dynamically loads the Rewards feature component.
 */
export default async function RewardsPage() {
  return (
    <AppInitializer>
      <div className="min-h-screen relative bg-gradient-to-b from-stone-900 via-black to-stone-900 bg-opacity-85 overflow-hidden">
        {/* Header */}
        <div className="relative z-10">
          <AppNavbar />
        </div>

        {/* Main Content */}
        <DynamicRewards />
      </div>
    </AppInitializer>
  );
}
