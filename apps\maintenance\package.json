{"name": "maintenance", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.1.6"}, "devDependencies": {"@sabongsaga/eslint-config": "workspace:*", "@sabongsaga/typescript-config": "workspace:*", "typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.1", "eslint": "^9", "eslint-config-next": "15.1.6", "@eslint/eslintrc": "^3"}}