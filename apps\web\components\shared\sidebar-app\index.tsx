"use client";

import { <PERSON>readcrumbs, Separator, Sidebar } from "@/components/ui";
import { usePathname } from "next/navigation";
import React from "react";

import { SidebarItems } from "@/data/sidebar";
import ConnectWallet from "@/features/connect-wallet";
import { Link } from "react-aria-components";

export default function AppSidebar({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  return (
    <Sidebar.Provider>
      <Sidebar collapsible="dock">
        <Sidebar.Header>
          <Link
            className="flex items-center group-data-[collapsible=dock]:size-10 group-data-[collapsible=dock]:justify-center"
            href="/"
          >
            <img src="/sabong-saga-logo.png" />
          </Link>
        </Sidebar.Header>
        <Sidebar.Content>
          <Sidebar.Section>
            {SidebarItems.map((data, idx) => (
              <Sidebar.Item
                isCurrent={data.path === pathname}
                key={idx}
                href={data.path}
                tooltip={data.label}
              >
                {data.icon}
                <Sidebar.Label aria-label={data.label}>
                  {data.label}
                </Sidebar.Label>
              </Sidebar.Item>
            ))}
          </Sidebar.Section>
        </Sidebar.Content>
        <Sidebar.Rail />
      </Sidebar>
      <Sidebar.Inset>
        <Sidebar.Nav>
          <span className="flex items-center gap-x-4">
            <Sidebar.Trigger className="-mx-2" />
            <Separator
              className="@md:block hidden h-6"
              orientation="vertical"
            />
            <Breadcrumbs>
              <Breadcrumbs.Item>
                {SidebarItems.find((item) => item.path === pathname)?.label}
              </Breadcrumbs.Item>
            </Breadcrumbs>
          </span>
          <div className="ml-auto flex items-center gap-x-2">
            <ConnectWallet />
          </div>
        </Sidebar.Nav>
        {children}
      </Sidebar.Inset>
    </Sidebar.Provider>
  );
}
