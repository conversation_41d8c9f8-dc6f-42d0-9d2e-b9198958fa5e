import { useEffect, useRef, useState } from "react";
import { ChickenStats } from "@/types/chicken.type";

export function useChickenTimers(chickenStats: Record<string, ChickenStats>) {
  // HP Cooldown tracking
  const [cooldownTimers, setCooldownTimers] = useState<Record<string, number>>({});
  const cooldownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Recovery countdown tracking for faint chickens
  const [recoveryTimers, setRecoveryTimers] = useState<Record<string, number>>({});
  const recoveryIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Breeding timer tracking
  const [breedingTimers, setBreedingTimers] = useState<Record<string, number>>({});
  const breedingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Transfer timer tracking (until next UTC midnight)
  const [transferTimers, setTransferTimers] = useState<Record<string, number>>({});
  const transferIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Listed timer tracking (until next UTC midnight)
  const [listedTimers, setListedTimers] = useState<Record<string, number>>({});
  const listedIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Immortal timer tracking (until next UTC midnight)
  const [immortalTimers, setImmortalTimers] = useState<Record<string, number>>({});
  const immortalIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Helper function to format time
  const formatTime = (seconds: number, type: 'cooldown' | 'recovery' | 'breeding' | 'listed' | 'transfer' | 'immortal' = 'cooldown') => {
    if (seconds <= 0) {
      switch (type) {
        case 'recovery':
          return "Ready to recover";
        case 'breeding':
          return "Breeding complete";
        case 'listed':
          return "Available now";
        case 'transfer':
          return "Available now";
        case 'immortal':
          return "Expired";
        default:
          return null;
      }
    }

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (type === 'cooldown') {
      if (hours > 0) {
        return `${hours}h:${minutes.toString().padStart(2, "0")}m:${remainingSeconds.toString().padStart(2, "0")}s`;
      } else {
        return `${minutes}m:${remainingSeconds.toString().padStart(2, "0")}s`;
      }
    } else {
      if (hours > 0) {
        return `${hours}h ${minutes}m ${remainingSeconds}s`;
      } else if (minutes > 0) {
        return `${minutes}m ${remainingSeconds}s`;
      } else {
        return `${remainingSeconds}s`;
      }
    }
  };

  // Helper functions to check chicken states
  const isBreeding = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    return stats?.state === "breeding";
  };

  const isFaint = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    return stats?.state === "faint";
  };

  const getBreedingTimeRemaining = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    if (!stats?.breedingTime) return null;

    const now = Date.now();
    const breedingEndTime = stats.breedingTime * 1000;
    const timeRemaining = breedingEndTime - now;

    return timeRemaining > 0 ? Math.ceil(timeRemaining / 1000) : 0;
  };

  const getRecoveryTimeRemaining = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    if (!stats?.recoverDate) return null;

    const now = new Date();
    const recoverDate = new Date(stats.recoverDate);
    const timeRemaining = recoverDate.getTime() - now.getTime();

    return timeRemaining > 0 ? Math.ceil(timeRemaining / 1000) : 0;
  };

  const getTransferTimeRemaining = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    if (!stats?.wasTransferredToday) return null;

    // Calculate time until next UTC midnight (00:00:00 UTC)
    const now = new Date();
    const nextMidnight = new Date();
    nextMidnight.setUTCDate(nextMidnight.getUTCDate() + 1);
    nextMidnight.setUTCHours(0, 0, 0, 0);
    
    const timeRemaining = nextMidnight.getTime() - now.getTime();
    return timeRemaining > 0 ? Math.ceil(timeRemaining / 1000) : 0;
  };

  const getListedTimeRemaining = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    if (!stats?.wasListedToday) return null;

    // Calculate time until next UTC midnight (00:00:00 UTC)
    const now = new Date();
    const nextMidnight = new Date();
    nextMidnight.setUTCDate(nextMidnight.getUTCDate() + 1);
    nextMidnight.setUTCHours(0, 0, 0, 0);
    
    const timeRemaining = nextMidnight.getTime() - now.getTime();
    return timeRemaining > 0 ? Math.ceil(timeRemaining / 1000) : 0;
  };

  const getImmortalTimeRemaining = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    if (!stats?.isImmortal) return null;

    // Calculate time until next UTC midnight (00:00:00 UTC)
    const now = new Date();
    const nextMidnight = new Date();
    nextMidnight.setUTCDate(nextMidnight.getUTCDate() + 1);
    nextMidnight.setUTCHours(0, 0, 0, 0);
    
    const timeRemaining = nextMidnight.getTime() - now.getTime();
    return timeRemaining > 0 ? Math.ceil(timeRemaining / 1000) : 0;
  };

  // Set initial timers when chicken stats are loaded/updated
  const setInitialTimers = (
    newCooldowns: Record<string, number>,
    newRecoveryTimers: Record<string, number>,
    newBreedingTimers: Record<string, number>,
    newTransferTimers: Record<string, number>,
    newListedTimers: Record<string, number>,
    newImmortalTimers: Record<string, number>
  ) => {
    setCooldownTimers(prev => ({ ...prev, ...newCooldowns }));
    setRecoveryTimers(prev => ({ ...prev, ...newRecoveryTimers }));
    setBreedingTimers(prev => ({ ...prev, ...newBreedingTimers }));
    setTransferTimers(prev => ({ ...prev, ...newTransferTimers }));
    setListedTimers(prev => ({ ...prev, ...newListedTimers }));
    setImmortalTimers(prev => ({ ...prev, ...newImmortalTimers }));
  };

  // Breeding timer effect
  useEffect(() => {
    if (Object.keys(breedingTimers).length === 0) return;

    breedingIntervalRef.current = setInterval(() => {
      setBreedingTimers((prev) => {
        const updated: Record<string, number> = {};
        let hasChanges = false;

        for (const tokenId in prev) {
          const timerValue = prev[tokenId];
          if (timerValue === undefined || timerValue === null) continue;

          if (timerValue > 0) {
            updated[tokenId] = timerValue - 1;
            hasChanges = true;
          } else if (timerValue === 0) {
            hasChanges = true;
          }
        }

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => {
      if (breedingIntervalRef.current) {
        clearInterval(breedingIntervalRef.current);
      }
    };
  }, [breedingTimers]);

  // Recovery timer effect
  useEffect(() => {
    if (Object.keys(recoveryTimers).length === 0) return;

    recoveryIntervalRef.current = setInterval(() => {
      setRecoveryTimers((prev) => {
        const updated: Record<string, number> = {};
        let hasChanges = false;

        for (const tokenId in prev) {
          const timerValue = prev[tokenId];
          if (timerValue === undefined || timerValue === null) continue;

          if (timerValue > 0) {
            updated[tokenId] = timerValue - 1;
            hasChanges = true;
          } else if (timerValue === 0) {
            hasChanges = true;
          }
        }

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => {
      if (recoveryIntervalRef.current) {
        clearInterval(recoveryIntervalRef.current);
      }
    };
  }, [recoveryTimers]);

  // Transfer timer effect
  useEffect(() => {
    if (Object.keys(transferTimers).length === 0) return;

    transferIntervalRef.current = setInterval(() => {
      setTransferTimers((prev) => {
        const updated: Record<string, number> = {};
        let hasChanges = false;

        for (const tokenId in prev) {
          const timerValue = prev[tokenId];
          if (timerValue === undefined || timerValue === null) continue;

          if (timerValue > 0) {
            updated[tokenId] = timerValue - 1;
            hasChanges = true;
          } else if (timerValue === 0) {
            hasChanges = true;
          }
        }

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => {
      if (transferIntervalRef.current) {
        clearInterval(transferIntervalRef.current);
      }
    };
  }, [transferTimers]);

  // Listed timer effect
  useEffect(() => {
    if (Object.keys(listedTimers).length === 0) return;

    listedIntervalRef.current = setInterval(() => {
      setListedTimers((prev) => {
        const updated: Record<string, number> = {};
        let hasChanges = false;

        for (const tokenId in prev) {
          const timerValue = prev[tokenId];
          if (timerValue === undefined || timerValue === null) continue;

          if (timerValue > 0) {
            updated[tokenId] = timerValue - 1;
            hasChanges = true;
          } else if (timerValue === 0) {
            hasChanges = true;
          }
        }

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => {
      if (listedIntervalRef.current) {
        clearInterval(listedIntervalRef.current);
      }
    };
  }, [listedTimers]);

  // Immortal timer effect
  useEffect(() => {
    if (Object.keys(immortalTimers).length === 0) return;

    immortalIntervalRef.current = setInterval(() => {
      setImmortalTimers((prev) => {
        const updated: Record<string, number> = {};
        let hasChanges = false;

        for (const tokenId in prev) {
          const timerValue = prev[tokenId];
          if (timerValue === undefined || timerValue === null) continue;

          if (timerValue > 0) {
            updated[tokenId] = timerValue - 1;
            hasChanges = true;
          } else if (timerValue === 0) {
            hasChanges = true;
          }
        }

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => {
      if (immortalIntervalRef.current) {
        clearInterval(immortalIntervalRef.current);
      }
    };
  }, [immortalTimers]);

  // Cooldown timer effect with HP regeneration
  useEffect(() => {
    if (Object.keys(cooldownTimers).length === 0) return;

    cooldownIntervalRef.current = setInterval(() => {
      setCooldownTimers((prev) => {
        const updated: Record<string, number> = {};
        let hasChanges = false;

        for (const tokenId in prev) {
          const timerValue = prev[tokenId];
          if (timerValue === undefined || timerValue === null) continue;

          if (timerValue > 0) {
            updated[tokenId] = timerValue - 1;
            hasChanges = true;
          } else if (timerValue === 0) {
            hasChanges = true;
          }
        }

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => {
      if (cooldownIntervalRef.current) {
        clearInterval(cooldownIntervalRef.current);
      }
    };
  }, [cooldownTimers]);

  return {
    cooldownTimers,
    recoveryTimers,
    breedingTimers,
    transferTimers,
    listedTimers,
    immortalTimers,
    setCooldownTimers,
    setRecoveryTimers,
    setBreedingTimers,
    setTransferTimers,
    setListedTimers,
    setImmortalTimers,
    setInitialTimers,
    formatTime,
    isBreeding,
    isFaint,
    getBreedingTimeRemaining,
    getRecoveryTimeRemaining,
    getTransferTimeRemaining,
    getListedTimeRemaining,
    getImmortalTimeRemaining,
  };
}