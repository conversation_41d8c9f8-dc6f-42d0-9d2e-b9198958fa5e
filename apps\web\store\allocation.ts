import { toast } from "sonner";
import { create } from "zustand";

type Delegate = {
  walletAddress: string;
  amount: number;
  isRemoved?: boolean;
};

type SelectedAccount = {
  walletAddress?: string;
  amount?: number;
};

type Breakdown = {
  legendaryCount: number;
  quantity: number;
  totalAllocation: number;
  type?: string;
};

type AllocationState = {
  isPending: boolean;
  error: string | null;
  totalAllocation: number;
  totalChickens: number;
  totalDelegated: number;
  selectedAccount?: SelectedAccount;
  delegates: Delegate[];
  breakDown: Breakdown[];
};

type Actions = {
  getAlloc: () => Promise<void>;
  setSelectedAcct: (data: SelectedAccount) => void;
  addOrUpdate: (
    address: string,
    amount: number,
    isRemoved?: boolean
  ) => Promise<void>;
  calculateTotalDelegated: () => number;
};

type StoreState = AllocationState & Actions;

const initialState: AllocationState = {
  isPending: true,
  error: null,
  totalAllocation: 0,
  totalChickens: 0,
  totalDelegated: 0,
  delegates: [],
  breakDown: [],
};

const API_ENDPOINTS = {
  csrf: "/csrf-token",
} as const;

const getCsrfToken = async () => {
  const { csrfToken } = await handleApiCall(API_ENDPOINTS.csrf);
  return csrfToken;
};

const handleApiCall = async (url: string, options?: RequestInit) => {
  const response = await fetch(url, options);
  if (!response.ok) {
    throw new Error(`API call failed: ${response.statusText}`);
  }
  return response.json();
};

const useAllocationStore = create<StoreState>()((set, get) => ({
  ...initialState,
  setSelectedAcct: (data) => {
    set((state) => ({
      selectedAccount: {
        ...state.selectedAccount,
        ...data,
      },
    }));
  },
  calculateTotalDelegated: () => {
    const { delegates } = get();
    return delegates
      .filter((delegate) => !delegate.isRemoved)
      .reduce((total, delegate) => total + delegate.amount, 0);
  },

  addOrUpdate: async (address: string, amount: number, isRemoved = false) => {
    try {
      set({ isPending: true, error: null });
      const csrfToken = await getCsrfToken();
      const addr = address.toLowerCase();

      const response = await fetch("/api/delegate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken,
        },
        body: JSON.stringify({ address: addr, amount, isRemoved }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update delegation");
      }

      // Update local state
      set((state) => {
        const existingDelegateIndex = state.delegates.findIndex(
          (d) => d.walletAddress === address
        );

        let newDelegates = [...state.delegates];

        if (existingDelegateIndex >= 0) {
          // Update existing delegate
          newDelegates[existingDelegateIndex] = {
            walletAddress: addr, // Ensure walletAddress is always set
            amount,
            isRemoved,
          };
        } else {
          // Add new delegate
          newDelegates.push({
            walletAddress: addr,
            amount,
            isRemoved,
          });
        }

        const totalDelegated = newDelegates
          .filter((delegate) => !delegate.isRemoved)
          .reduce((total, delegate) => total + delegate.amount, 0);

        return {
          ...state,
          delegates: newDelegates,
          totalDelegated,
        };
      });

      // Refresh allocation data
      await get().getAlloc();
    } catch (error) {
      toast.error("Failed to update allocation", {
        description: error instanceof Error ? error.message : "Unknown error",
        position: "top-center",
      });
    } finally {
      set({ isPending: false });
    }
  },

  getAlloc: async () => {
    try {
      set({ isPending: true, error: null });

      const response = await fetch("/api/delegate", {
        cache: "no-store",
        method: "GET",
      });

      if (!response.ok) {
        throw new Error("Failed to fetch allocation data");
      }

      const data = await response.json();

      // Ensure proper type mapping
      const delegates: Delegate[] = data.data.delegates.map(
        (delegate: any) => ({
          walletAddress: delegate.address,
          amount: delegate.amount,
          isRemoved: delegate.isRemoved || false,
        })
      );

      const totalDelegated = delegates
        .filter((delegate) => !delegate.isRemoved)
        .reduce((total, delegate) => total + delegate.amount, 0);

      set({
        totalChickens: data.data.totalChickens,
        totalAllocation: data.data.totalAllocation,
        delegates,
        breakDown: data.data.breakDown,
        totalDelegated,
        error: null,
      });
    } catch (error) {
      toast.error(`${error instanceof Error ? error.message : "Unknown error"}`, {
        description: "Please reload your browser.",
        position: "top-center",
      });
    } finally {
      set({ isPending: false });
    }
  },
}));

export default useAllocationStore;
