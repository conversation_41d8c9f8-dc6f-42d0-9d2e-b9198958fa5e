import { NextRequest, NextResponse } from "next/server";
import { Address } from "viem";
import { delay } from "@/utils/delay";
import { isEggByTokenId } from "@/lib/utils/chicken-attributes";

type ResponseData = {
  erc721Tokens: {
    total: number;
    results: TokenData[];
  };
};

interface SearchCriteria {
  name: string;
  values: string;
}

interface TokenData {
  tokenAddress: string;
  tokenId: string;
  owner: string;
  image: string;
  name?: string;
  cdnImage?: string;
  attributes?: {
    trait_type: string;
    value: string | number;
  }[];
}

async function _getNfts(
  owner: string,
  from: number = 0,
  contractAddress: Address,
  criteria?: { name: string; values: string }
): Promise<ResponseData> {
  const graphqlEndpoint = process.env.RONIN_MARKETPLACE_GQL as string;

  // Build the GraphQL query with criteria
  const data = {
    operationName: "GetERC721TokensList",
    variables: {
      from,
      size: 50,
      owner: owner,
      tokenAddress: contractAddress,
      criteria: criteria ? [criteria] : undefined,
    },
    query: `query GetERC721TokensList($tokenAddress: String, $owner: String, $from: Int!, $size: Int!, $criteria: [SearchCriteria!]) {
      erc721Tokens(
        tokenAddress: $tokenAddress
        owner: $owner
        from: $from
        size: $size
        criteria: $criteria
      ) {
        total
        results {
          tokenAddress
          tokenId
          owner
          name
          image
          cdnImage
          attributes
        }
      }
    }`,
  };

  const headers = {
    "Content-Type": "application/json",
  };

  const response = await fetch(graphqlEndpoint, {
    method: "POST",
    headers,
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch NFTs: ${response.status}`);
  }

  const responseData = await response.json();
  return responseData.data;
}

export async function GET(req: NextRequest) {
  const searchParams = req.nextUrl.searchParams;
  const address = searchParams.get("address");

  if (!address) {
    return NextResponse.json(
      { error: "Address parameter is required" },
      { status: 400 }
    );
  }

  try {
    const eggs: {
      tokenId: string;
      address: string;
      image: string;
      name?: string;
      type?: string;
      attributes?: {
        trait_type: string;
        value: string | number;
      }[];
    }[] = [];

    // Only fetch from legacy contract since eggs are only in the legacy contract
    const legacyAddress = process.env.LEGACY_CONTRACT as Address;
    const legacyThreshold = parseInt(process.env.LEGACY_THRESHOLD || "11110");
    const DELAY_BETWEEN_REQUESTS = 1000; // 1 second delay

    // Fetch eggs using type criteria
    const criteria = {
      name: "type",
      values: "egg",
    };

    let from = 0;
    let hasMore = true;

    while (hasMore) {
      const fetchNft = await _getNfts(
        address.toLowerCase(),
        from,
        legacyAddress,
        criteria
      );

      // Process current batch
      fetchNft.erc721Tokens.results.forEach((item) => {
        // Double check that it's an egg by token ID as well
        const tokenId = parseInt(item.tokenId);
        if (isEggByTokenId(tokenId, legacyThreshold)) {
          eggs.push({
            tokenId: item.tokenId,
            address: item.owner,
            image: item.image,
            name: item.name,
            type: "Egg",
            attributes: item.attributes,
          });
        }
      });

      // If we got less results than requested, we're done
      if (fetchNft.erc721Tokens.results.length < 50) {
        hasMore = false;
      } else {
        // Update from for next batch
        from += 50;
        // Add delay before next request
        await delay(DELAY_BETWEEN_REQUESTS);
      }
    }

    return NextResponse.json({ eggs, total: eggs.length });
  } catch (error) {
    console.error("Error fetching eggs:", error);
    return NextResponse.json(
      { error: "Failed to fetch eggs" },
      { status: 500 }
    );
  }
}
