import { IChickenMetadata, IAttribute } from "@/lib/types/chicken.types";

// Default threshold for legacy chicken contract eggs
const DEFAULT_CHICKEN_LEGACY_THRESHOLD = 11110;

/**
 * Safely extracts an attribute value from chicken metadata with proper type handling
 * @param metadata Chicken metadata containing attributes
 * @param traitType The trait type to look for
 * @param defaultValue Default value to return if attribute is not found
 * @returns The properly typed attribute value
 */
export function getAttributeValue<T>(
  metadata: IChickenMetadata | undefined,
  traitType: string,
  defaultValue: T
): T {
  if (!metadata?.attributes) return defaultValue;

  const attribute = metadata.attributes.find(
    (attr) => attr.trait_type === traitType
  );

  if (!attribute) return defaultValue;

  // Handle different display types
  if (
    attribute.display_type === "number" ||
    typeof attribute.value === "number"
  ) {
    return Number(attribute.value || 0) as unknown as T;
  }

  if (attribute.display_type === "date") {
    return new Date(attribute.value as string | number) as unknown as T;
  }

  // Default string handling
  return attribute.value as unknown as T;
}

/**
 * Checks if a chicken is an egg based on its metadata
 * @param metadata Chicken metadata
 * @returns boolean indicating if the chicken is an egg
 * @deprecated Use isEggByTokenId instead for better performance
 */
export function isEggType(metadata: IChickenMetadata | undefined): boolean {
  if (!metadata?.attributes) return false;

  const typeAttribute = metadata.attributes.find(
    (attr) => attr.trait_type === "Type"
  );

  return typeAttribute?.value === "Egg";
}

/**
 * Checks if a chicken is an egg based on its tokenId
 * @param tokenId The token ID to check
 * @param threshold The threshold for legacy chicken contract eggs (default: 11110)
 * @returns boolean indicating if the tokenId represents an egg
 */
export function isEggByTokenId(
  tokenId: number,
  threshold: number = DEFAULT_CHICKEN_LEGACY_THRESHOLD
): boolean {
  // Eggs are in the legacy contract starting from the threshold
  return tokenId >= threshold;
}

/**
 * Gets the chicken type from metadata
 * @param metadata Chicken metadata
 * @returns The chicken type or undefined if not found
 */
export function getChickenType(
  metadata: IChickenMetadata | undefined
): string | undefined {
  if (!metadata?.attributes) return undefined;

  const typeAttribute = metadata.attributes.find(
    (attr) => attr.trait_type === "Type"
  );

  return typeAttribute?.value as string | undefined;
}

/**
 * Safely gets the daily feathers value from chicken metadata
 * @param metadata Chicken metadata
 * @returns The daily feathers value or 0 if not found
 */
export function getDailyFeathers(
  metadata: IChickenMetadata | undefined
): number {
  // Eggs don't have daily feathers
  if (isEggType(metadata)) return 0;

  return getAttributeValue(metadata, "Daily Feathers", 0);
}

/**
 * Safely gets the breed count value from chicken metadata
 * @param metadata Chicken metadata
 * @returns The breed count value or 0 if not found
 */
export function getBreedCount(metadata: IChickenMetadata | undefined): number {
  // Eggs don't have breed count
  if (isEggType(metadata)) return 0;

  return getAttributeValue(metadata, "Breed Count", 0);
}

/**
 * Gets all relevant attributes for a chicken based on its type
 * @param metadata Chicken metadata
 * @returns An object with all relevant attributes
 */
export function getChickenAttributes(metadata: IChickenMetadata | undefined) {
  if (!metadata) return null;

  // Handle egg type differently
  if (isEggType(metadata)) {
    return {
      type: "Egg",
      dailyFeathers: 0,
      breedCount: 0,
    };
  }

  // For regular chickens, extract all relevant attributes
  const type = getAttributeValue<string>(metadata, "Type", "");

  return {
    type,
    dailyFeathers: getDailyFeathers(metadata),
    breedCount: getBreedCount(metadata),
    generation: getAttributeValue(metadata, "Generation", ""),
    gender: getAttributeValue(metadata, "Gender", ""),
    gritAttack: getAttributeValue(metadata, "Grit Attack", 0),
    gritDefense: getAttributeValue(metadata, "Grit Defense", 0),
    gritHealth: getAttributeValue(metadata, "Grit Health", 0),
    gritSpeed: getAttributeValue(metadata, "Grit Speed", 0),
    innateAttack: getAttributeValue(metadata, "Innate Attack", 0),
    innateDefense: getAttributeValue(metadata, "Innate Defense", 0),
    innateHealth: getAttributeValue(metadata, "Innate Health", 0),
    innateSpeed: getAttributeValue(metadata, "Innate Speed", 0),
    instinct: getAttributeValue(metadata, "Instinct", ""),
    legendaryCount: getAttributeValue(metadata, "Legendary Count", 0),
  };
}
