"use client";

import axios from "axios";
import { CHICKEN_API } from "./useChickenMetadataBatch";
import { IGenesResponse } from "../types/genes.types";
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";

const fetchChickenGenes = async (tokenId: number) => {
  const { data } = await axios.get(`${CHICKEN_API}/genes/${tokenId}`);
  return data as IGenesResponse;
};

/**
 * Fetch genes for multiple chickens using batch API if available, otherwise concurrent individual calls
 */
const fetchBatchChickenGenes = async (
  tokenIds: number[]
): Promise<IGenesResponse[]> => {
  if (!CHICKEN_API) {
    throw new Error("Chicken API URL not configured");
  }

  if (!tokenIds || tokenIds.length === 0) {
    return [];
  }

  try {
    // Try batch endpoint first
    const { data } = await axios.post(`${CHICKEN_API}/genes/batch`, {
      tokenIds: tokenIds,
    });
    return data as IGenesResponse[];
  } catch (error) {
    console.warn(
      "Batch genes API not available, using concurrent individual calls:",
      error
    );

    // Fallback to concurrent individual calls
    return Promise.all(tokenIds.map(fetchChickenGenes));
  }
};

const useChickenGenes = (tokenIds: number[]) => {
  const fetchGenes = () => {
    if (!CHICKEN_API) {
      throw new Error("Chicken API URL not configured");
    }

    if (!tokenIds || tokenIds.length === 0) {
      return [];
    }

    return fetchBatchChickenGenes(tokenIds);
  };

  const genesQuery = useQuery({
    queryKey: ["chickenGenes", tokenIds],
    queryFn: fetchGenes,
    enabled: !!tokenIds && tokenIds.length > 0,
  });

  const genesMap = useMemo(() => {
    if (!genesQuery.data || !tokenIds) {
      return {};
    }

    return tokenIds.reduce(
      (map, tokenId, index) => {
        map[tokenId] = genesQuery.data[index] as IGenesResponse;
        return map;
      },
      {} as Record<number, IGenesResponse>
    );
  }, [genesQuery.data, tokenIds]);

  return {
    genesQuery,
    genesMap,
    isLoading: genesQuery.isLoading,
    error: genesQuery.error,
    isError: genesQuery.isError,
  };
};

export { fetchChickenGenes, fetchBatchChickenGenes };
export default useChickenGenes;
