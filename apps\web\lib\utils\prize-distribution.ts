// Prize distribution calculation utility
// Using decimal arithmetic for high accuracy prize calculations

interface PrizeDistribution {
  basePrizePercentage: number;
  growingPotPercentage: number;
  totalPercentage: number;
}

/**
 * Calculate prize distribution percentages for a given rank
 * Uses decimal arithmetic for higher accuracy
 */
export function calculatePrizeDistribution(rank: number): PrizeDistribution {
  // Convert percentages to decimal values for precise calculations
  let basePrizeDecimal: number;
  let growingPotDecimal: number;

  if (rank === 1) {
    basePrizeDecimal = 0.06; // 6%
    growingPotDecimal = 0.015; // 1.5%
  } else if (rank === 2) {
    basePrizeDecimal = 0.04; // 4%
    growingPotDecimal = 0.015; // 1.5%
  } else if (rank === 3) {
    basePrizeDecimal = 0.03; // 3%
    growingPotDecimal = 0.015; // 1.5%
  } else if (rank === 4) {
    basePrizeDecimal = 0.025; // 2.5%
    growingPotDecimal = 0.0125; // 1.25%
  } else if (rank === 5) {
    basePrizeDecimal = 0.02; // 2%
    growingPotDecimal = 0.0125; // 1.25%
  } else if (rank === 6) {
    basePrizeDecimal = 0.018; // 1.8%
    growingPotDecimal = 0.0125; // 1.25%
  } else if (rank === 7) {
    basePrizeDecimal = 0.016; // 1.6%
    growingPotDecimal = 0.0125; // 1.25%
  } else if (rank === 8) {
    basePrizeDecimal = 0.014; // 1.4%
    growingPotDecimal = 0.0125; // 1.25%
  } else if (rank === 9) {
    basePrizeDecimal = 0.012; // 1.2%
    growingPotDecimal = 0.0125; // 1.25%
  } else if (rank === 10) {
    basePrizeDecimal = 0.01; // 1%
    growingPotDecimal = 0.01; // 1%
  } else if (rank >= 11 && rank <= 50) {
    basePrizeDecimal = 0.0018875; // 0.18875%
    growingPotDecimal = 0.01175; // 1.175%
  } else if (rank >= 51 && rank <= 200) {
    basePrizeDecimal = 0.00133; // 0.133%
    growingPotDecimal = 0.001667; // 0.1667%
  } else if (rank >= 201 && rank <= 500) {
    basePrizeDecimal = 0.0008; // 0.08%
    growingPotDecimal = 0.000333; // 0.0333%
  } else if (rank >= 501 && rank <= 1000) {
    basePrizeDecimal = 0.00048; // 0.048%
    growingPotDecimal = 0.0001; // 0.01%
  } else {
    // No prize for ranks above 1000
    basePrizeDecimal = 0;
    growingPotDecimal = 0;
  }

  // Calculate total percentage
  const totalDecimal = basePrizeDecimal + growingPotDecimal;

  // Convert back to percentage values for display
  return {
    basePrizePercentage: basePrizeDecimal * 100,
    growingPotPercentage: growingPotDecimal * 100,
    totalPercentage: totalDecimal * 100,
  };
}

/**
 * Calculate the actual prize amount for a given rank
 * @param rank - The rank position
 * @param basePrize - Base prize amount (default: 10,000,000)
 * @param growingPot - Growing pot amount
 * @returns Object containing base prize share, growing pot share, and total prize
 */
export function calculatePrizeAmount(
  rank: number,
  basePrize: number = 10000000,
  growingPot: number = 0
): {
  basePrizeShare: number;
  growingPotShare: number;
  totalPrize: number;
} {
  const distribution = calculatePrizeDistribution(rank);

  // Use decimal arithmetic for precise calculations
  const basePrizeShare = basePrize * (distribution.basePrizePercentage / 100);
  const growingPotShare = growingPot * (distribution.growingPotPercentage / 100);
  const totalPrize = basePrizeShare + growingPotShare;

  return {
    basePrizeShare,
    growingPotShare,
    totalPrize,
  };
}

/**
 * Format prize amount for display
 * @param amount - The prize amount
 * @returns Formatted string with full values (no shorthand)
 */
export function formatPrizeAmount(amount: number): string {
  if (amount >= 1) {
    return amount.toLocaleString(undefined, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });
  } else if (amount > 0) {
    return amount.toFixed(2);
  }
  return "0";
}

/**
 * Check if a rank is eligible for prizes
 * @param rank - The rank position
 * @returns Boolean indicating if rank is eligible
 */
export function isEligibleForPrize(rank: number): boolean {
  return rank >= 1 && rank <= 1000;
}