"use client";

import { hookstate, useHookstate } from "@hookstate/core";
import { devtools } from "@hookstate/devtools";
import { useOptimizedEggLoading } from "./useOptimizedEggLoading";
import { useEggHatching } from "./useEggHatching";

const initialState = {};
const optimizedHatchingState = hookstate(
  initialState,
  devtools({ key: "optimizedHatchingState" })
);

/**
 * Optimized hatching hook that uses contract-based egg loading
 * and the ninuno-rewards API for filtering breeding vs hatching phases
 */
export const useOptimizedHatching = () => {
  const state = useHookstate(optimizedHatchingState);
  
  // Use the optimized egg loading hook
  const eggLoadingQuery = useOptimizedEggLoading("", {
    pageSize: 20,
    filterPhase: "all",
    sortBy: "tokenId",
    sortOrder: "asc",
  });

  // Use the existing egg hatching functionality
  const { hatchEgg, isHatching } = useEggHatching(eggLoadingQuery.eggInfoMap);

  return {
    state,
    
    // Egg data
    allEggTokenIds: eggLoadingQuery.allEggTokenIds,
    breedingPhaseEggs: eggLoadingQuery.breedingPhaseEggs,
    hatchingPhaseEggs: eggLoadingQuery.hatchingPhaseEggs,
    currentPageEggTokenIds: eggLoadingQuery.currentPageEggTokenIds,
    
    // Maps for efficient lookup
    eggInfoMap: eggLoadingQuery.eggInfoMap,
    metadataMap: eggLoadingQuery.metadataMap,
    
    // Hatching functionality
    hatchEgg,
    isHatching,
    
    // Pagination
    currentPage: eggLoadingQuery.currentPage,
    totalPages: eggLoadingQuery.totalPages,
    totalCount: eggLoadingQuery.totalCount,
    hasMore: eggLoadingQuery.hasMore,
    goToNextPage: eggLoadingQuery.goToNextPage,
    goToPreviousPage: eggLoadingQuery.goToPreviousPage,
    goToPage: eggLoadingQuery.goToPage,
    resetPage: eggLoadingQuery.resetPage,
    
    // Loading and error states
    isLoading: eggLoadingQuery.isLoading,
    error: eggLoadingQuery.error,
    isConnected: eggLoadingQuery.isConnected,
    address: eggLoadingQuery.address,
    
    // Stats
    stats: eggLoadingQuery.stats,
    
    // Filter options
    filterPhase: eggLoadingQuery.filterPhase,
    sortBy: eggLoadingQuery.sortBy,
    sortOrder: eggLoadingQuery.sortOrder,
  };
};

/**
 * Hook for optimized breeding phase eggs with pagination
 */
export const useOptimizedBreedingPhaseEggs = (searchQuery: string = "") => {
  return useOptimizedEggLoading(searchQuery, {
    pageSize: 20,
    filterPhase: "breeding",
    sortBy: "tokenId",
    sortOrder: "asc",
  });
};

/**
 * Hook for optimized hatching phase eggs with pagination
 */
export const useOptimizedHatchingPhaseEggs = (searchQuery: string = "") => {
  return useOptimizedEggLoading(searchQuery, {
    pageSize: 20,
    filterPhase: "hatching",
    sortBy: "hatchTime",
    sortOrder: "asc",
  });
};

export default optimizedHatchingState;
