"use client";

import axios from "@/lib/api";
import { useQuery } from "@tanstack/react-query";

// Define the interface for breeding fee data
interface IBreedingFee {
  id: number;
  count: number;
  cock: number;
  cockUsd: number;
  feathers: number;
  createdAt: string;
  updatedAt: string;
}

interface IBreedingFeesResponse {
  status: number;
  data: IBreedingFee[];
}

// Fetcher function
const fetchBreedingFees = async () => {
  const { data } = await axios.get<IBreedingFeesResponse>("/breeding-fees");
  return data;
};

// Helper function to get the breeding fee for a specific count
const getBreedingFeeForCount = (
  fees: IBreedingFee[],
  breedCount: number | undefined,
  maxCountFee: number = 7
) => {
  // Handle undefined breed count
  if (breedCount === undefined) {
    return fees.find((fee) => fee.count === 0);
  }

  // If breed count exceeds the max, use the max count fee
  const count = breedCount >= maxCountFee ? maxCountFee : breedCount;
  return (
    fees.find((fee) => fee.count === count) ||
    fees.find((fee) => fee.count === 0)
  );
};

// Hook
const useBreedingFees = (
  parent1BreedCount?: number | number[],
  parent2BreedCount?: number | number[]
) => {
  const breedingFeesQuery = useQuery({
    queryKey: ["breeding-fees"],
    queryFn: fetchBreedingFees,
  });

  // Calculate the base breeding fee (count 0) as fallback
  const baseBreedingFee = breedingFeesQuery.data?.data.find(
    (fee) => fee.count === 0
  );

  // Calculate breeding fees based on parent breed counts if available
  const calculateBreedingFees = () => {
    if (!breedingFeesQuery.data?.data) {
      return { cockFee: 0, featherFee: 0 };
    }

    const fees = breedingFeesQuery.data.data;

    // If no parent breed counts provided, return base fee
    if (parent1BreedCount === undefined || parent2BreedCount === undefined) {
      return {
        cockFee: baseBreedingFee?.cock || 0,
        featherFee: baseBreedingFee?.feathers || 0,
      };
    }

    // Handle array of breed counts (mass breeding)
    if (Array.isArray(parent1BreedCount) && Array.isArray(parent2BreedCount)) {
      let totalCockFee = 0;
      let totalFeatherFee = 0;

      // Calculate fees for each pair and sum them up
      for (
        let i = 0;
        i < Math.min(parent1BreedCount.length, parent2BreedCount.length);
        i++
      ) {
        const parent1Fee = getBreedingFeeForCount(fees, parent1BreedCount[i]);
        const parent2Fee = getBreedingFeeForCount(fees, parent2BreedCount[i]);

        totalCockFee += (parent1Fee?.cock || 0) + (parent2Fee?.cock || 0);
        totalFeatherFee +=
          (parent1Fee?.feathers || 0) + (parent2Fee?.feathers || 0);
      }

      return { cockFee: totalCockFee, featherFee: totalFeatherFee };
    }

    // Handle single breed counts (manual breeding)
    // Get fees for each parent based on their breed count
    const parent1Fee = getBreedingFeeForCount(
      fees,
      parent1BreedCount as number
    );
    const parent2Fee = getBreedingFeeForCount(
      fees,
      parent2BreedCount as number
    );

    // Calculate total fees (sum of both parents)
    const cockFee = (parent1Fee?.cock || 0) + (parent2Fee?.cock || 0);
    const featherFee =
      (parent1Fee?.feathers || 0) + (parent2Fee?.feathers || 0);

    return { cockFee, featherFee };
  };

  const { cockFee, featherFee } = calculateBreedingFees();

  return {
    breedingFeesQuery,
    baseBreedingFee,
    cockFee,
    featherFee,
  };
};

export default useBreedingFees;
