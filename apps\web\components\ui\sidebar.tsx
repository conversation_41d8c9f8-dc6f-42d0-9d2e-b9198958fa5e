"use client";

import * as React from "react";

import {
  IconChevronDown,
  IconChevronRight,
  IconHamburger,
  IconSidebarFill,
} from "justd-icons";
import type {
  DisclosureProps,
  LinkProps,
  LinkRenderProps,
} from "react-aria-components";
import {
  composeRenderProps,
  Disclosure,
  DisclosurePanel,
  Link,
  Text,
} from "react-aria-components";
import { tv } from "tailwind-variants";

import { Button, ButtonPrimitive } from "./button";
import { cn, useMediaQuery } from "./primitive";
import { Sheet } from "./sheet";
import { Tooltip } from "./tooltip";
import { Badge } from "./badge";

type SidebarContextProps = {
  state: "expanded" | "collapsed";
  open: boolean;
  setOpen: (open: boolean) => void;
  openMobile: boolean;
  setOpenMobile: (open: boolean) => void;
  isMobile: boolean;
  toggleSidebar: () => void;
};

const SidebarContext = React.createContext<SidebarContextProps | null>(null);

function useSidebar() {
  const context = React.useContext(SidebarContext);
  if (!context) {
    throw new Error("useSidebar must be used within a Sidebar.");
  }

  return context;
}

const Provider = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & {
    defaultOpen?: boolean;
    isOpen?: boolean;
    onOpenChange?: (open: boolean) => void;
  }
>(
  (
    {
      defaultOpen = true,
      isOpen: openProp,
      onOpenChange: setOpenProp,
      className,
      children,
      ...props
    },
    ref
  ) => {
    const isMobile = useMediaQuery("(max-width: 768px)");
    const [openMobile, setOpenMobile] = React.useState(false);

    const [_open, _setOpen] = React.useState(defaultOpen);
    const open = openProp ?? _open;
    const setOpen = React.useCallback(
      (value: boolean | ((value: boolean) => boolean)) => {
        if (setOpenProp) {
          return setOpenProp?.(
            typeof value === "function" ? value(open) : value
          );
        }

        _setOpen(value);

        document.cookie = `sidebar:state=${open}; path=/; max-age=${60 * 60 * 24 * 7}`;
      },
      [setOpenProp, open]
    );

    const toggleSidebar = React.useCallback(() => {
      return isMobile
        ? setOpenMobile((open) => !open)
        : setOpen((open) => !open);
    }, [isMobile, setOpen, setOpenMobile]);

    React.useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === "b" && (event.metaKey || event.ctrlKey)) {
          event.preventDefault();
          toggleSidebar();
        }
      };

      window.addEventListener("keydown", handleKeyDown);
      return () => window.removeEventListener("keydown", handleKeyDown);
    }, [toggleSidebar]);

    const state = open ? "expanded" : "collapsed";

    const contextValue = React.useMemo<SidebarContextProps>(
      () => ({
        state,
        open,
        setOpen,
        isMobile,
        openMobile,
        setOpenMobile,
        toggleSidebar,
      }),
      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]
    );

    return (
      <SidebarContext.Provider value={contextValue}>
        <div
          className={cn(
            "group/sidebar-wrapper [--sidebar-width:16.5rem] [--sidebar-width-icon:3rem] flex min-h-svh w-full text-fg dark:has-[[data-intent=inset]]:bg-bg has-[[data-intent=inset]]:bg-secondary/50",
            className
          )}
          ref={ref}
          {...props}
        >
          {children}
        </div>
      </SidebarContext.Provider>
    );
  }
);
Provider.displayName = "Provider";

const Inset = ({ className, ...props }: React.ComponentProps<"main">) => {
  return (
    <main
      data-slot="sidebar-inset"
      className={cn([
        [
          "relative flex min-h-svh max-w-full flex-1 flex-col bg-bg",
          "md:peer-data-[intent=inset]:ml-0 md:peer-data-[intent=inset]:bg-tertiary md:peer-data-[intent=inset]:rounded-xl",
          "peer-data-[intent=inset]:overflow-hidden peer-data-[intent=inset]:border peer-data-[intent=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[intent=inset]:my-2 md:peer-data-[intent=inset]:mr-2",
        ],
        className,
      ])}
      {...props}
    />
  );
};

const Sidebar = ({
  side = "left",
  intent = "sidebar",
  collapsible = "offcanvas",
  className,
  children,
  ...props
}: React.ComponentProps<"div"> & {
  side?: "left" | "right";
  intent?: "sidebar" | "floating" | "inset";
  collapsible?: "offcanvas" | "dock" | "none";
}) => {
  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();

  if (collapsible === "none") {
    return (
      <div
        className={cn(
          "flex h-full w-[--sidebar-width] flex-col bg-tertiary text-fg ",
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }

  if (isMobile) {
    return (
      <Sheet isOpen={openMobile} onOpenChange={setOpenMobile} {...props}>
        <Sheet.Content
          aria-label="Sidebar"
          data-slot="sidebar"
          data-mobile="true"
          classNames={{
            content: "bg-bg text-fg [&>button]:hidden",
          }}
          isStack={intent === "floating"}
          side={side}
        >
          <Sheet.Body className="p-0 sm:p-0">{children}</Sheet.Body>
        </Sheet.Content>
      </Sheet>
    );
  }
  return (
    <div
      className="group peer hidden md:block"
      data-state={state}
      data-collapsible={state === "collapsed" ? collapsible : ""}
      data-intent={intent}
      data-side={side}
    >
      <div
        className={cn(
          "duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear",
          "group-data-[collapsible=offcanvas]:w-0",
          "group-data-[side=right]:rotate-180",
          intent === "floating" || intent === "inset"
            ? "group-data-[collapsible=dock]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]"
            : "group-data-[collapsible=dock]:w-[--sidebar-width-icon]"
        )}
      />
      <div
        className={cn(
          "duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex",
          side === "left"
            ? "left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]"
            : "right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",
          intent === "floating" || intent === "inset"
            ? "p-2 group-data-[collapsible=dock]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]"
            : "group-data-[collapsible=dock]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",
          className
        )}
        {...props}
      >
        <div
          data-slot="sidebar"
          className={cn(
            "flex h-full w-full flex-col bg-tertiary group-data-[intent=inset]:bg-transparent group-data-[intent=floating]:rounded-lg group-data-[intent=floating]:border group-data-[intent=floating]:border-border group-data-[intent=floating]:bg-secondary/50",
            intent === "inset" || state === "collapsed"
              ? "[&_[data-slot=sidebar-header]]:border-transparent [&_[data-slot=sidebar-footer]]:border-transparent"
              : "[&_[data-slot=sidebar-header]]:border-b [&_[data-slot=sidebar-footer]]:border-t"
          )}
        >
          {children}
        </div>
      </div>
    </div>
  );
};

const itemStyles = tv({
  base: [
    "group relative flex w-full cursor-pointer items-center gap-x-2 overflow-hidden rounded-lg px-2.5 py-2 text-sidebar-fg/70 outline-hidden sm:text-sm",
    "**:data-[slot=menu-trigger]:absolute **:data-[slot=menu-trigger]:right-0 **:data-[slot=menu-trigger]:flex **:data-[slot=menu-trigger]:h-full **:data-[slot=menu-trigger]:w-[calc(var(--sidebar-width)-90%)] **:data-[slot=menu-trigger]:items-center **:data-[slot=menu-trigger]:justify-end **:data-[slot=menu-trigger]:pr-2.5",
    "**:data-[slot=menu-trigger]:hidden",
    "**:data-[slot=avatar]:*:size-4 **:data-[slot=avatar]:size-4 **:data-[slot=icon]:size-4 **:data-[slot=avatar]:shrink-0 **:data-[slot=icon]:shrink-0",
    "**:data-[slot=menu-trigger]:bg-gradient-to-l **:data-[slot=menu-trigger]:from-(--sidebar-accent) **:data-[slot=menu-trigger]:from-65%",
    "in-data-[sidebar-intent=fleet]:rounded-none",
  ],
  variants: {
    collapsed: {
      true: "size-9 justify-center gap-x-0 p-0",
    },
    isCurrent: {
      true: "text-primary font-semibold hover:text-primary/60",
    },
    isActive: {
      true: "bg-(--sidebar-accent) text-sidebar-fg **:data-[slot=menu-trigger]:flex",
    },
    isDisabled: {
      true: "cursor-default opacity-50",
    },
  },
});

interface SidebarItemProps
  extends Omit<React.ComponentProps<typeof Link>, "children"> {
  isCurrent?: boolean;
  tooltip?: React.ReactNode | string;
  children?:
    | React.ReactNode
    | ((
        values: LinkRenderProps & {
          defaultChildren: React.ReactNode;
          isCollapsed: boolean;
        }
      ) => React.ReactNode);
  badge?: string | number | undefined;
}

const Item = ({
  isCurrent,
  tooltip,
  children,
  badge,
  className,
  ref,
  ...props
}: SidebarItemProps) => {
  const { state, isMobile } = useSidebar();
  const isCollapsed = state === "collapsed" && !isMobile;
  const link = (
    <Link
      ref={ref}
      data-sidebar-item="true"
      aria-current={isCurrent ? "page" : undefined}
      className={composeRenderProps(className, (cls, renderProps) =>
        itemStyles({
          ...renderProps,
          isCurrent,
          collapsed: isCollapsed,
          isActive:
            renderProps.isPressed ||
            renderProps.isFocusVisible ||
            renderProps.isHovered,
          className: cls,
        })
      )}
      {...props}
    >
      {(values) => (
        <>
          {typeof children === "function"
            ? children({ ...values, isCollapsed })
            : children}

          {badge &&
            (state !== "collapsed" ? (
              <Badge
                shape="square"
                intent="primary"
                data-slot="sidebar-badge"
                className={cn(
                  "-translate-y-1/2 absolute inset-ring-1 inset-ring-primary/20 inset-y-1/2 right-1.5 h-5.5 w-auto text-[10px] transition-colors group-data-current:inset-ring-transparent",
                  isCurrent && [
                    "bg-[color-mix(in_oklab,var(--color-primary)_20%,white_20%)] text-primary-fg dark:bg-[color-mix(in_oklab,var(--color-primary)_20%,white_15%)] dark:text-current ",
                    "group-data-hovered:bg-[color-mix(in_oklab,var(--color-primary)_25%,white_30%)]",
                    "dark:group-data-hovered:bg-[color-mix(in_oklab,var(--color-primary)_25%,white_20%)]",
                  ]
                )}
              >
                {badge}
              </Badge>
            ) : (
              <div
                aria-hidden
                className="absolute top-1 right-1 size-1.5 rounded-full bg-primary"
              />
            ))}
        </>
      )}
    </Link>
  );

  return isCollapsed && tooltip ? (
    <Tooltip delay={0}>
      {link}
      <Tooltip.Content
        className="**:data-[slot=icon]:hidden **:data-[slot=sidebar-label-mask]:hidden"
        intent="inverse"
        showArrow={false}
        placement="right"
      >
        {tooltip}
      </Tooltip.Content>
    </Tooltip>
  ) : (
    link
  );
};

const Content = ({ className, ...props }: React.ComponentProps<"div">) => {
  const { state } = useSidebar();
  return (
    <div
      data-sidebar-content="true"
      className={cn(
        "flex min-h-0 flex-1 scroll-mb-96 flex-col overflow-auto",
        state === "collapsed" && "items-center",
        className
      )}
      {...props}
    />
  );
};

const navStyles = tv({
  base: "bg-tertiary md:bg-bg w-full justify-between sm:justify-start h-[3.57rem] px-4 border-b flex items-center gap-x-2",
  variants: {
    isSticky: {
      true: "sticky top-0 z-40",
    },
  },
});

interface NavProps extends React.ComponentProps<"nav"> {
  isSticky?: boolean;
}

const Nav = ({ isSticky = false, className, ...props }: NavProps) => {
  return (
    <nav
      data-slot="sidebar-nav"
      {...props}
      className={navStyles({ isSticky, className })}
    />
  );
};

const Trigger = ({
  className,
  onPress,
  ...props
}: React.ComponentProps<typeof Button>) => {
  const { toggleSidebar } = useSidebar();
  return (
    <Button
      aria-label={props["aria-label"] || "Toggle Sidebar"}
      data-slot="sidebar-trigger"
      appearance="plain"
      size="square-petite"
      className={className}
      onPress={(event) => {
        onPress?.(event);
        toggleSidebar();
      }}
      {...props}
    >
      <IconSidebarFill className="md:inline hidden" />
      <IconHamburger className="md:hidden inline" />
      <span className="sr-only">Toggle Sidebar</span>
    </Button>
  );
};

const header = tv({
  base: "flex flex-col mb-2",
  variants: {
    collapsed: {
      false: "px-5 py-4",
      true: "px-5 py-4 md:p-0 md:size-9 mt-1 group-data-[intent=floating]:mt-2 md:rounded-lg md:hover:bg-muted md:mx-auto md:justify-center md:items-center",
    },
  },
});

const Header = ({
  className,
  ...props
}: React.HtmlHTMLAttributes<HTMLDivElement>) => {
  const { state } = React.useContext(SidebarContext)!;
  return (
    <div
      data-slot="sidebar-header"
      {...props}
      className={header({ collapsed: state === "collapsed", className })}
      {...props}
    />
  );
};

const footer = tv({
  base: "flex flex-col mt-auto",
  variants: {
    collapsed: {
      false: [
        "p-2 [&_[data-slot=menu-trigger]>[data-slot=avatar]]:-ml-1.5 [&_[data-slot=menu-trigger]]:w-full [&_[data-slot=menu-trigger]]:hover:bg-muted [&_[data-slot=menu-trigger]]:justify-start [&_[data-slot=menu-trigger]]:flex [&_[data-slot=menu-trigger]]:items-center",
      ],
      true: "size-12 p-1 [&_[data-slot=menu-trigger]]:size-9 justify-center items-center",
    },
  },
});

const Footer = ({
  className,
  ...props
}: React.HtmlHTMLAttributes<HTMLDivElement>) => {
  const { state } = React.useContext(SidebarContext)!;
  return (
    <div
      {...props}
      data-slot="sidebar-footer"
      className={footer({ collapsed: state === "collapsed", className })}
      {...props}
    />
  );
};

interface CollapsibleProps extends DisclosureProps {
  children: React.ReactNode;
  title?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
  icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const Section = ({
  className,
  ...props
}: React.ComponentProps<"div"> & { title?: string }) => {
  const { state } = useSidebar();
  return (
    <div
      data-sidebar-section="true"
      className={cn(
        "flex flex-col gap-y-0.5 in-data-[sidebar-intent=fleet]:px-0 px-2",
        className
      )}
      {...props}
    >
      {state !== "collapsed" && "title" in props && (
        <Header className="group-data-[sidebar-collapsible=dock]/sidebar-container:-mt-8 mb-1 flex shrink-0 items-center rounded-md px-2.5 font-medium text-sidebar-fg/70 text-xs outline-none ring-sidebar-ring transition-[margin,opa] duration-200 ease-linear data-focus-visible:ring-2 *:data-[slot=icon]:size-4 *:data-[slot=icon]:shrink-0 group-data-[sidebar-collapsible=dock]/sidebar-container:opacity-0">
          {props.title}
        </Header>
      )}
      {props.children}
    </div>
  );
};

const Rail = ({ className, ...props }: React.ComponentProps<"button">) => {
  const { toggleSidebar } = useSidebar();

  return (
    <button
      data-slot="sidebar-rail"
      aria-label="Toggle Sidebar"
      tabIndex={-1}
      onClick={toggleSidebar}
      title="Toggle Sidebar"
      className={cn(
        "absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-transparent group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex",
        "[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize",
        "[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize",
        "group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-tertiary",
        "[[data-side=left][data-collapsible=offcanvas]_&]:-right-2",
        "[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",
        className
      )}
      {...props}
    />
  );
};

type SidebarLabelProps = React.ComponentProps<typeof Text>;
const Label = ({ className, ref, ...props }: SidebarLabelProps) => {
  const { state, isMobile } = useSidebar();
  const collapsed = state === "collapsed" && !isMobile;
  if (!collapsed) {
    return (
      <Text
        ref={ref}
        slot="label"
        className={cn(
          "flex w-full flex-1 overflow-hidden whitespace-nowrap",
          className
        )}
        {...props}
      >
        {props.children}
      </Text>
    );
  }
  return null;
};

Sidebar.Provider = Provider;
Sidebar.Inset = Inset;
Sidebar.Header = Header;
Sidebar.Nav = Nav;
Sidebar.Content = Content;
Sidebar.Footer = Footer;
Sidebar.Item = Item;
Sidebar.Section = Section;
Sidebar.Rail = Rail;
Sidebar.Trigger = Trigger;
Sidebar.Label = Label;

export { Sidebar, useSidebar };
