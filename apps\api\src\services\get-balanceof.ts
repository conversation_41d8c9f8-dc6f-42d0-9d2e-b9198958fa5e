import ChickenContract from "../abi/Chicken.abi.json";
import Legacy<PERSON>bi from "../abi/Legacy.abi.json";
import { Address, createPublicClient, http } from "viem";
import { ronin, saigon } from "viem/chains";
import { env } from "../env";

const publicClient = createPublicClient({
  chain: env.ENV === "production" ? ronin : saigon,
  transport: http(env.RONIN_RPC),
});

export async function getTokenBalance(address: string) {
  try {
    const balance = await publicClient.readContract({
      address: env.CHICKEN_CONTRACT as Address,
      abi: ChickenContract,
      functionName: "balanceOf",
      args: [address],
    }) as bigint;

    const legacyBal = await publicClient.readContract({
      address: env.LEGACY_CONTRACT as Address,
      abi: LegacyAbi,
      functionName: "balanceOf",
      args: [address],
    }) as bigint;

    let bal = 0n;

    if (balance) {
      bal += balance;
    }

    if (legacyBal) {
      bal += legacyBal;
    }
    
    return bal;
  } catch (error) {
    throw error;
  }
}
