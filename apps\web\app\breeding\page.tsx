"use client";

import AppNavbar from "@/components/shared/navbar";
import { Suspense } from "react";
import AppInitializer from "@/providers/app/initializer";
import { BreedingTab } from "@/features/breeding";
import { BreedingMaintenance } from "@/components/maintenance";

// Loading fallback component
function BreedingTabsLoading() {
  return (
    <div className="relative max-w-[1680px] w-full mx-auto my-6 px-4 md:px-6 lg:px-8">
      <div className="animate-pulse flex flex-col gap-6">
        <div className="h-12 bg-stone-800 rounded-md w-full max-w-md mx-auto"></div>
        <div className="h-64 bg-stone-800 rounded-lg w-full"></div>
        <div className="h-32 bg-stone-800 rounded-lg w-full"></div>
      </div>
    </div>
  );
}

export default function BreedingPage() {
  // Check if breeding maintenance mode is enabled
  const isMaintenanceMode =
    process.env.NEXT_PUBLIC_BREEDING_MAINTENANCE_MODE === "true";

  return (
    <AppInitializer>
      <div className="min-h-screen relative bg-gradient-to-b from-stone-900 via-black to-stone-900 overflow-hidden">
        <div className="sticky top-0 z-20 bg-stone-900/80 backdrop-blur-sm border-b border-primary/10">
          <AppNavbar />
        </div>

        {isMaintenanceMode ? (
          <BreedingMaintenance />
        ) : (
          <Suspense fallback={<BreedingTabsLoading />}>
            <div className="relative max-w-[1680px] w-full mx-auto my-6 px-4 md:px-6 lg:px-8">
              <BreedingTab />
            </div>
          </Suspense>
        )}
      </div>
    </AppInitializer>
  );
}
