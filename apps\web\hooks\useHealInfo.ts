import { useCallback, useEffect, useState } from "react";
import { ChickenStats } from "@/types/chicken.type";

interface HealInfo {
  healsRemaining: number;
  maxHeals: number;
  resetTime: string;
}

export function useHealInfo(address: string | null) {
  const [healInfo, setHealInfo] = useState<HealInfo | null>(null);
  const [healInfoLoading, setHealInfoLoading] = useState(false);

  const fetchHealInfo = useCallback(async () => {
    if (!address) return;

    setHealInfoLoading(true);
    try {
      const response = await fetch(
        `https://chicken-api-ivory.vercel.app/api/game/heal?address=${address}`
      );

      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setHealInfo({
          healsRemaining: data.healsRemaining,
          maxHeals: data.maxHeals,
          resetTime: "00:00:00 UTC",
        });
      } else {
        console.error("Error fetching heal info:", data.error);
        setHealInfo(null);
      }
    } catch (error) {
      console.error("Error fetching heal info:", error);
      setHealInfo(null);
    } finally {
      setHealInfoLoading(false);
    }
  }, [address]);

  // Function to handle healing
  const handleHeal = useCallback(
    async (tokenId: string, setChickenStats: React.Dispatch<React.SetStateAction<Record<string, ChickenStats>>>) => {
      if (!address) return false;

      try {
        const res = await fetch("/csrf-token");
        if (!res.ok) {
          throw new Error(`API call failed: ${res.statusText}`);
        }
        const { csrfToken } = await res.json();

        const response = await fetch("/api/proxy/heal", {
          method: "POST",
          headers: {
            "X-CSRF-Token": csrfToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            id: parseInt(tokenId, 10),
            address: address,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.error || `API responded with status: ${response.status}`
          );
        }

        const data = await response.json();

        if (data.success) {
          // Update chicken stats with the healed data
          setChickenStats((prev) => ({
            ...prev,
            [tokenId]: {
              ...prev[tokenId],
              hp: prev[tokenId]?.maxHp || data.chicken.stats.currentHp,
              maxHp: prev[tokenId]?.maxHp || data.chicken.stats.hp,
              level: data.chicken.level,
              attack: data.chicken.stats.attack,
              defense: data.chicken.stats.defense,
              speed: data.chicken.stats.speed,
              ferocity: data.chicken.stats.ferocity,
              cockrage: data.chicken.stats.cockrage,
              evasion: data.chicken.stats.evasion,
              boosters: data.chicken.boosters || prev[tokenId]?.boosters || {},
            },
          }));

          // Update heal info
          setHealInfo((prev) =>
            prev
              ? {
                  ...prev,
                  healsRemaining: data.healsRemaining,
                }
              : null
          );

          return true;
        } else {
          console.error("Healing failed:", data.error);
          return false;
        }
      } catch (error) {
        console.error("Error healing chicken:", error);
        throw error;
      }
    },
    [address]
  );

  useEffect(() => {
    // Fetch heal info on component mount
    if (address) {
      fetchHealInfo();
    }

    // Set up a refresh interval (every 5 minutes)
    const refreshInterval = setInterval(
      () => {
        if (address) {
          fetchHealInfo();
        }
      },
      5 * 60 * 1000
    );

    return () => clearInterval(refreshInterval);
  }, [address, fetchHealInfo]);

  return {
    healInfo,
    healInfoLoading,
    fetchHealInfo,
    handleHeal,
  };
}