"use client";

import { useQuery } from "@tanstack/react-query";
import { IBattleStats } from "../types/battle.types";

/**
 * Fetch battle stats for a single chicken using batch API
 */
const fetchChickenBattleStats = async (
  tokenId: number
): Promise<IBattleStats> => {
  try {
    // Use batch API even for single chicken to maintain consistency
    const gameApiUrl =
      process.env.NEXT_PUBLIC_CHICKEN_API_URL ||
      "https://chicken-api-ivory.vercel.app/api";
    const response = await fetch(`${gameApiUrl}/game/batch`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        ids: [tokenId],
      }),
    });
    if (!response.ok) {
      throw new Error(`Failed to fetch battle stats: ${response.status}`);
    }
    const batchData = await response.json();
    const data = batchData.chickens?.[0] || {
      wins: 0,
      losses: 0,
      draws: 0,
      level: 1,
      state: "normal",
      stats: {},
    };
    return {
      wins: data.wins || 0,
      losses: data.losses || 0,
      draws: data.draws || 0,
      level: data.level,
      state: data.state || "normal",
      recoverDate: data.recoverDate,
      stats: data.stats,
    };
  } catch (error) {
    console.warn(`Failed to fetch battle stats for chicken ${tokenId}:`, error);
    return {
      wins: 0,
      losses: 0,
      draws: 0,
      state: "normal",
    };
  }
};

/**
 * Hook to fetch battle stats for a single chicken
 */
export const useBattleStats = (tokenId: number | null) => {
  return useQuery({
    queryKey: ["battleStats", tokenId],
    queryFn: () => fetchChickenBattleStats(tokenId!),
    enabled: !!tokenId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

/**
 * Fetch battle stats for multiple chickens using the batch API
 */
const fetchBulkBattleStats = async (
  tokenIds: number[]
): Promise<Record<number, IBattleStats>> => {
  if (tokenIds.length === 0) return {};

  try {
    const gameApiUrl =
      process.env.NEXT_PUBLIC_CHICKEN_API_URL ||
      "https://chicken-api-ivory.vercel.app/api";
    const response = await fetch(`${gameApiUrl}/game/batch`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        ids: tokenIds,
      }),
    });

    if (!response.ok) {
      throw new Error(`Batch API responded with status: ${response.status}`);
    }

    const data = await response.json();
    const statsMap: Record<number, IBattleStats> = {};

    // Transform the batch response to our format
    // The API returns { chickens: [...], found: number, missing: [], total: number }
    const chickensData = data.chickens || [];

    tokenIds.forEach((tokenId) => {
      const chickenData = chickensData.find(
        (chicken: { id: number }) => chicken.id === tokenId
      );
      if (chickenData) {
        statsMap[tokenId] = {
          wins: chickenData.wins || 0,
          losses: chickenData.losses || 0,
          draws: chickenData.draws || 0,
          level: chickenData.level,
          state: chickenData.state || "normal",
          recoverDate: chickenData.recoverDate,
          stats: chickenData.stats,
        };
      } else {
        // Default stats if no data found
        statsMap[tokenId] = {
          wins: 0,
          losses: 0,
          draws: 0,
          state: "normal",
        };
      }
    });

    return statsMap;
  } catch (error) {
    console.warn(
      "Failed to fetch bulk battle stats, falling back to individual calls:",
      error
    );

    // Fallback to individual calls if batch fails
    const statsPromises = tokenIds.map(async (tokenId) => ({
      tokenId,
      stats: await fetchChickenBattleStats(tokenId),
    }));

    const results = await Promise.allSettled(statsPromises);
    const statsMap: Record<number, IBattleStats> = {};

    results.forEach((result, index) => {
      const tokenId = tokenIds[index];
      if (tokenId !== undefined) {
        if (result.status === "fulfilled") {
          statsMap[tokenId] = result.value.stats;
        } else {
          // Default stats if fetch failed
          statsMap[tokenId] = {
            wins: 0,
            losses: 0,
            draws: 0,
            state: "normal",
          };
        }
      }
    });

    return statsMap;
  }
};

/**
 * Hook to fetch battle stats for multiple chickens using batch API
 */
export const useBulkBattleStats = (tokenIds: number[]) => {
  return useQuery({
    queryKey: ["bulkBattleStats", tokenIds],
    queryFn: () => fetchBulkBattleStats(tokenIds),
    enabled: tokenIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

/**
 * Export the fetch functions for direct use
 */
export { fetchChickenBattleStats, fetchBulkBattleStats };
