import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ address: string }> }
) {
  const { address } = await params;

  if (address) {
    const req = await fetch(
      `${process.env.HONO_API_ENDPOINT}/api/auth/nonce/${address}`
    );

    if (req.ok) {
      const data = await req.json();

      return NextResponse.json(data, { status: 200 });
    }
  }
  return NextResponse.json(
    {
      status: false,
      responseCode: 400,
      message: "Bad request",
    },
    { status: 400 }
  );
}
