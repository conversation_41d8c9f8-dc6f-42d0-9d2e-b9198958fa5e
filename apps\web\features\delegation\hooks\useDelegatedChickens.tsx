"use client";

import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { useStateContext } from "@/providers/app/state";
import { DelegationAPI } from "../api/delegation.api";
import { IChickenDelegationInfo } from "../types/delegation.types";
import useChickenMetadata from "@/features/breeding/tab/breeding/hooks/useChickenMetadata";
import { IAttribute, IChickenMetadata } from "@/lib/types/chicken.types";

// Extended interface for delegated chickens that includes delegation info
export interface IDelegatedChicken {
  tokenId: number;
  image: string;
  metadata?: IChickenMetadata;
  dailyFeathers?: number;
  breedCount?: number;
  type?: string;
  level?: number;
  winRate?: number;
  // Delegation specific fields
  delegatedTask: number;
  rewardDistribution: number;
  sharedRewardAmount: number | null;
  renterAddress: string;
  ownerAddress: string;
  legendaryCount: number;
  isDelegated: true; // Always true for delegated chickens
}

// Interface for battle stats
interface IBattleStats {
  wins: number;
  losses: number;
  draws?: number;
  level?: number;
  state?: string; // "normal", "faint", "dead", "breeding"
  recoverDate?: string;
  stats?: {
    attack?: number;
    defense?: number;
    speed?: number;
    currentHp?: number;
    hp?: number;
    cockrage?: number;
    ferocity?: number;
    evasion?: number;
    instinct?: string;
  };
}

// Individual battle stats function removed - now using batch API only

export function useDelegatedChickens() {
  const { address, isConnected } = useStateContext();

  // Fetch delegated chickens for current user (as delegatee/renter)
  const delegatedChickensQuery = useQuery({
    queryKey: ["delegatedChickens", address],
    queryFn: async () => {
      if (!address) return [];
      const response = await DelegationAPI.getChickensByWallet(address);
      return response.data || [];
    },
    enabled: !!address && isConnected,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Extract token IDs from delegated chickens
  const delegatedTokenIds = useMemo(() => {
    return delegatedChickensQuery.isSuccess
      ? delegatedChickensQuery.data.map((chicken) => chicken.tokenId)
      : [];
  }, [delegatedChickensQuery.isSuccess, delegatedChickensQuery.data]);

  // Fetch metadata for delegated chickens
  const metadataQuery = useChickenMetadata(delegatedTokenIds);

  // Fetch battle stats for delegated chickens using batch API
  const battleStatsQuery = useQuery({
    queryKey: ["delegatedChickensBattleStats", delegatedTokenIds],
    queryFn: async () => {
      if (delegatedTokenIds.length === 0) return {};

      try {
        // Use the batch API instead of individual calls
        const gameApiUrl =
          process.env.NEXT_PUBLIC_CHICKEN_API_URL ||
          "https://chicken-api-ivory.vercel.app/api";
        const response = await fetch(`${gameApiUrl}/game/batch`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ids: delegatedTokenIds,
          }),
        });

        if (!response.ok) {
          throw new Error(
            `Batch API responded with status: ${response.status}`
          );
        }

        const data = await response.json();
        const statsMap: Record<number, IBattleStats> = {};

        // Transform the batch response to our format
        const chickensData = data.chickens || [];
        chickensData.forEach((chicken: any) => {
          if (chicken && chicken.id) {
            statsMap[chicken.id] = {
              wins: chicken.wins || 0,
              losses: chicken.losses || 0,
              draws: chicken.draws || 0,
              level: chicken.level,
              state: chicken.state || "normal",
              recoverDate: chicken.recoverDate,
              stats: chicken.stats,
            };
          }
        });

        // Add default stats for missing chickens
        delegatedTokenIds.forEach((tokenId) => {
          if (!statsMap[tokenId]) {
            statsMap[tokenId] = {
              wins: 0,
              losses: 0,
              draws: 0,
              state: "normal",
            };
          }
        });

        return statsMap;
      } catch (error) {
        console.error("Failed to fetch batch battle stats:", error);
        // Return default stats for all chickens if batch fails
        const defaultStatsMap: Record<number, IBattleStats> = {};
        delegatedTokenIds.forEach((tokenId) => {
          defaultStatsMap[tokenId] = {
            wins: 0,
            losses: 0,
            draws: 0,
            state: "normal",
          };
        });
        return defaultStatsMap;
      }
    },
    enabled: delegatedTokenIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Transform delegated chickens data to match inventory interface
  const delegatedChickens = useMemo(() => {
    if (
      !delegatedChickensQuery.isSuccess ||
      !metadataQuery.metadataQuery.isSuccess
    ) {
      return [];
    }

    const metadataMap = metadataQuery.metadataMap;
    const battleStatsMap = battleStatsQuery.data || {};

    return delegatedChickensQuery.data.map(
      (delegationInfo: IChickenDelegationInfo) => {
        const chickenMetadata = metadataMap[delegationInfo.tokenId];
        const battleStats = battleStatsMap[delegationInfo.tokenId] || {
          wins: 0,
          losses: 0,
        };

        // Extract attributes from metadata
        const typeAttribute = chickenMetadata?.attributes.find(
          (attr: IAttribute) => attr.trait_type === "Type"
        );
        const levelAttribute = chickenMetadata?.attributes.find(
          (attr: IAttribute) => attr.trait_type === "Level"
        );
        const breedCountAttribute = chickenMetadata?.attributes.find(
          (attr: IAttribute) => attr.trait_type === "Breed Count"
        );

        // Calculate win rate from battle stats
        const totalBattles = battleStats.wins + battleStats.losses;
        const winRate =
          totalBattles > 0
            ? Math.round((battleStats.wins / totalBattles) * 100)
            : 0;

        const delegatedChicken: IDelegatedChicken = {
          tokenId: delegationInfo.tokenId,
          image:
            delegationInfo.image ||
            chickenMetadata?.image ||
            `https://chicken-api-ivory.vercel.app/api/image/${delegationInfo.tokenId}.png`,
          metadata: chickenMetadata,
          type: typeAttribute?.value as string,
          dailyFeathers: delegationInfo.dailyFeathers || 0,
          level: Number(levelAttribute?.value) || 1,
          breedCount: Number(breedCountAttribute?.value) || 0,
          winRate,
          // Delegation specific fields
          delegatedTask: delegationInfo.delegatedTask,
          rewardDistribution: delegationInfo.rewardDistribution,
          sharedRewardAmount: delegationInfo.sharedRewardAmount,
          renterAddress: delegationInfo.renterAddress,
          ownerAddress: delegationInfo.ownerAddress,
          legendaryCount: delegationInfo.legendaryCount,
          isDelegated: true,
        };

        return delegatedChicken;
      }
    );
  }, [
    delegatedChickensQuery.isSuccess,
    delegatedChickensQuery.data,
    metadataQuery.metadataQuery.isSuccess,
    metadataQuery.metadataMap,
    battleStatsQuery.data,
  ]);

  return {
    delegatedChickens,
    isLoading:
      delegatedChickensQuery.isLoading || metadataQuery.metadataQuery.isLoading,
    isFetching:
      delegatedChickensQuery.isFetching ||
      metadataQuery.metadataQuery.isFetching,
    error: delegatedChickensQuery.error || metadataQuery.metadataQuery.error,
    isConnected,
  };
}
