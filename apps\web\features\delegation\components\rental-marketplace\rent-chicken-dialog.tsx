"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "ui";
import Image from "next/image";
import { <PERSON>, Coins, User, Zap, Al<PERSON><PERSON>riangle, Shield } from "lucide-react";
import {
  IRentalWithMetadata,
  formatRentalDuration,
  formatRoninPrice,
  REWARD_DISTRIBUTION_LABELS,
  DELEGATED_TASK_LABELS,
} from "../../types/delegation.types";
import { RentalStatusBadge } from "../shared/rental-status-badge";

interface IRentChickenDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  rental: IRentalWithMetadata | null;
  onConfirmRent?: (rental: IRentalWithMetadata) => void;
  loading?: boolean;
}

export function RentChickenDialog({
  isOpen,
  onOpenChange,
  rental,
  onConfirmRent,
  loading = false,
}: IRentChickenDialogProps) {
  const [agreed, setAgreed] = useState(false);

  if (!rental) return null;

  const chickenType = String(
    rental.chickenMetadata?.attributes?.find(
      (attr) => attr.trait_type === "Type"
    )?.value || "Unknown"
  );

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "genesis":
        return "bg-purple-500/20 text-purple-400";
      case "legacy":
        return "bg-blue-500/20 text-blue-400";
      case "ordinary":
        return "bg-gray-500/20 text-gray-400";
      default:
        return "bg-gray-500/20 text-gray-400";
    }
  };

  const handleConfirm = () => {
    if (onConfirmRent && agreed) {
      onConfirmRent(rental);
    }
  };

  const handleClose = () => {
    setAgreed(false);
    onOpenChange(false);
  };

  // Calculate insurance and total costs
  const rentalPrice = parseFloat(rental.roninPrice) / 1e18;
  const hasInsurance = rental.insurancePrice && rental.insurancePrice !== "0";
  const insuranceAmount = hasInsurance
    ? parseFloat(rental.insurancePrice!) / 1e18
    : 0;
  const totalCostWithInsurance = rentalPrice + insuranceAmount;

  // Check if chicken needs insurance
  const chickenNeedsInsurance =
    chickenType.toLowerCase() === "legacy" ||
    chickenType.toLowerCase() === "ordinary";

  return (
    <Modal isOpen={isOpen} onOpenChange={handleClose}>
      <Modal.Content size="2xl">
        <Modal.Header>
          <Modal.Title>Rent Chicken</Modal.Title>
          <Modal.Description>
            Review the rental details and confirm your rental
          </Modal.Description>
        </Modal.Header>

        <Modal.Body className="space-y-6">
          {/* Chicken Info */}
          <div className="flex gap-4 p-4 bg-stone-800 rounded-lg">
            <div className="relative w-20 h-20 rounded-lg overflow-hidden bg-stone-700">
              {rental.chickenMetadata?.image ? (
                <Image
                  src={rental.chickenMetadata.image}
                  alt={
                    rental.chickenMetadata.name ||
                    `Chicken #${rental.chickenTokenId}`
                  }
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-500">
                  <Zap className="w-8 h-8" />
                </div>
              )}
            </div>

            <div className="flex-1">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h3 className="font-semibold text-white text-lg">
                    {rental.chickenMetadata?.name ||
                      `Chicken #${rental.chickenTokenId}`}
                  </h3>
                  <p className="text-gray-400">#{rental.chickenTokenId}</p>
                </div>
                <div className="flex gap-2">
                  <RentalStatusBadge
                    status={rental.status}
                    roninPrice={rental.roninPrice}
                  />
                  <span
                    className={`text-xs px-2 py-1 rounded-full ${getTypeColor(chickenType)}`}
                  >
                    {chickenType}
                  </span>
                </div>
              </div>

              <div className="text-sm text-gray-400">
                {rental.chickenMetadata?.description}
              </div>
            </div>
          </div>

          {/* Rental Terms */}
          <div className="space-y-4">
            <h4 className="font-semibold text-white">Rental Terms</h4>

            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center justify-between p-3 bg-stone-800 rounded-lg">
                <span className="text-gray-400 flex items-center gap-2">
                  <Coins className="w-4 h-4" />
                  Daily Rate
                </span>
                <span className="text-white font-semibold text-lg">
                  {(() => {
                    const durationInDays = rental.rentalPeriod / 86400;
                    const dailyRate =
                      durationInDays > 0
                        ? rentalPrice / durationInDays
                        : rentalPrice;
                    return `${dailyRate.toFixed(4)} RON/day`;
                  })()}
                </span>
              </div>

              <div className="flex items-center justify-between p-3 bg-stone-800 rounded-lg">
                <span className="text-gray-400 flex items-center gap-2">
                  <Coins className="w-4 h-4" />
                  Rental Cost
                </span>
                <span className="text-white font-semibold text-lg">
                  {rentalPrice.toFixed(4)} RON
                </span>
              </div>

              {/* Insurance Information */}
              {(hasInsurance || chickenNeedsInsurance) && (
                <div className="flex items-center justify-between p-3 bg-stone-800 rounded-lg">
                  <span className="text-gray-400 flex items-center gap-2">
                    <Shield className="w-4 h-4" />
                    Insurance
                  </span>
                  {hasInsurance ? (
                    <span className="text-blue-400 font-semibold text-lg">
                      {insuranceAmount.toFixed(4)} RON
                    </span>
                  ) : (
                    <span className="text-yellow-400 font-semibold text-lg">
                      Not covered
                    </span>
                  )}
                </div>
              )}

              {/* Total Cost with Insurance */}
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-500/20 to-amber-500/20 border border-yellow-500/30 rounded-lg">
                <span className="text-yellow-400 flex items-center gap-2 font-semibold">
                  <Coins className="w-4 h-4" />
                  Total Cost
                </span>
                <span className="text-yellow-400 font-bold text-xl">
                  {totalCostWithInsurance.toFixed(4)} RON
                </span>
              </div>

              <div className="flex items-center justify-between p-3 bg-stone-800 rounded-lg">
                <span className="text-gray-400 flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Duration
                </span>
                <span className="text-white font-semibold">
                  {formatRentalDuration(rental.rentalPeriod)}
                </span>
              </div>

              <div className="flex items-center justify-between p-3 bg-stone-800 rounded-lg">
                <span className="text-gray-400 flex items-center gap-2">
                  <User className="w-4 h-4" />
                  Owner
                </span>
                <span className="text-white font-mono text-sm">
                  {rental.ownerAddress.slice(0, 8)}...
                  {rental.ownerAddress.slice(-6)}
                </span>
              </div>

              {rental.dailyFeathers && (
                <div className="flex items-center justify-between p-3 bg-stone-800 rounded-lg">
                  <span className="text-gray-400">Daily Feathers</span>
                  <span className="text-yellow-400 font-semibold">
                    {rental.dailyFeathers}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Delegation Details */}
          <div className="space-y-3">
            <h4 className="font-semibold text-white">Delegation Details</h4>

            <div className="p-4 bg-stone-800 rounded-lg space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Activities</span>
                <span className="text-white">
                  {DELEGATED_TASK_LABELS[rental.delegatedTask]}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-400">Daily Rub Distribution</span>
                <span className="text-white">
                  {REWARD_DISTRIBUTION_LABELS[rental.rewardDistribution]}
                </span>
              </div>

              {rental.sharedRewardAmount && (
                <div className="flex justify-between">
                  <span className="text-gray-400">Your Daily Rub Rewards</span>
                  <span className="text-yellow-400 font-semibold">
                    {rental.sharedRewardAmount} 🪶
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Warning */}
          <div className="flex gap-3 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
            <AlertTriangle className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
            <div className="text-sm">
              <p className="text-yellow-400 font-medium mb-1">
                Important Notice
              </p>
              <p className="text-gray-300">
                By renting this chicken, you agree to pay the full rental amount
                {hasInsurance && " plus insurance"} upfront. The rental period
                starts immediately upon confirmation and cannot be extended or
                refunded.
                {chickenNeedsInsurance && !hasInsurance && (
                  <span className="block mt-2 text-yellow-300 font-medium">
                    ⚠️ This {chickenType} chicken can die in battle and has no
                    insurance coverage.
                  </span>
                )}
              </p>
            </div>
          </div>

          {/* Agreement Checkbox */}
          <div className="flex items-start gap-3">
            <input
              type="checkbox"
              id="rental-agreement"
              checked={agreed}
              onChange={(e) => setAgreed(e.target.checked)}
              className="mt-1 w-4 h-4 text-yellow-500 bg-stone-700 border-stone-600 rounded focus:ring-yellow-500"
            />
            <label htmlFor="rental-agreement" className="text-sm text-gray-300">
              I understand and agree to the rental terms. I confirm that I have
              sufficient RON balance ({totalCostWithInsurance.toFixed(4)} RON)
              to complete this transaction
              {hasInsurance && " including insurance"} and understand that the
              payment is non-refundable.
            </label>
          </div>
        </Modal.Body>

        <Modal.Footer>
          <Button
            appearance="outline"
            onPress={handleClose}
            isDisabled={loading}
          >
            Cancel
          </Button>
          <Button
            intent="primary"
            onPress={handleConfirm}
            isDisabled={!agreed || loading}
            className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold"
          >
            {loading
              ? "Processing..."
              : `Rent for ${totalCostWithInsurance.toFixed(4)} RON`}
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
}
