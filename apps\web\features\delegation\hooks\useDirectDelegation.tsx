import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAccount, useWalletClient, usePublicClient } from "wagmi";
import { toast } from "sonner";
import { Address } from "viem";

import { DelegationAPI } from "../api/delegation.api";
import { useChickenApproval } from "./useChickenApproval";
import { rentalAbi } from "@/providers/web3/abi/rental-abi";
import { chain } from "@/providers/web3/web3-provider";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";

import type {
  ICreateRentalFormData,
  ICreateRentalResponse,
  IListChickenResponse,
} from "../types/delegation.types";

/**
 * Hook for handling direct delegation with proper contract interactions
 * This includes both escrow (listChickenForRent) and activation (rentChicken) steps
 */
export const useDirectDelegation = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const { address } = useAccount();
  const { data: walletClient } = useWalletClient();
  const publicClient = usePublicClient();
  const queryClient = useQueryClient();
  const { blockchainQuery } = useBlockchain();
  const { Disconnect } = useStateContext();
  const { checkApprovalStatus, executeApproval } = useChickenApproval();

  // Get rental contract address from blockchain config
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  /**
   * Execute the complete direct delegation process:
   * 1. Call API to create rental and get signature
   * 2. Check and handle chicken NFT approval
   * 3. Execute listChickenForRent (escrow)
   * 4. Execute rentChicken with 0 payment (activation)
   * 5. Handle success/error states
   */
  const executeDirectDelegation = async (formData: ICreateRentalFormData) => {
    try {
      if (!address || !walletClient || !publicClient) {
        toast.error("Cannot create delegation", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return { success: false, error: "Wallet not connected" };
      }

      if (!rentalAddress) {
        toast.error("Cannot create delegation", {
          description: "Rental contract not configured",
          position: "top-right",
        });
        return { success: false, error: "Contract not configured" };
      }

      // Validate that this is a direct delegation
      if (!formData.isDirectDelegation || !formData.renterAddress) {
        throw new Error("Invalid direct delegation data");
      }

      setIsProcessing(true);

      // Step 1: Create rental and get signature from API
      toast.info("Creating delegation...", {
        description: "Getting signature from server",
        position: "top-center",
      });

      const response: ICreateRentalResponse =
        await DelegationAPI.createRental(formData);

      if (response.status !== 1 || !response.data) {
        throw new Error("Failed to create delegation");
      }

      // The API response should contain the signature data for listing
      const delegationData =
        response.data as unknown as IListChickenResponse["data"];

      const {
        chickenTokenId,
        rentalId,
        roninPrice, // Should be "0" for direct delegation
        insurancePrice, // Should be "0" for direct delegation
        rentalPeriod,
        ownerAddress,
        signature,
      } = delegationData;

      // Step 2: Check and handle NFT approval
      toast.info("Checking NFT approval...", {
        description: "Ensuring contract can transfer your chicken",
        position: "top-center",
      });

      const isApproved = await checkApprovalStatus(chickenTokenId);

      if (!isApproved) {
        toast.info("Approval required", {
          description: "Please approve the chicken for delegation first",
          position: "top-center",
        });

        const approvalResult = await executeApproval(chickenTokenId);

        if (!approvalResult.success) {
          throw new Error("Chicken approval failed or was cancelled");
        }
      }

      // Convert prices to BigInt (should be 0 for direct delegation)
      const ethPriceInWei = BigInt(roninPrice);
      const insurancePriceInWei = BigInt(insurancePrice);

      // Step 3: Execute listChickenForRent (escrow)
      toast.info("Transferring chicken to escrow...", {
        description: "Please confirm the first transaction in your wallet",
        position: "top-center",
      });

      // Simulate the listing transaction
      const listSimulateReq = await publicClient.simulateContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "listChickenForRent",
        args: [
          BigInt(chickenTokenId),
          BigInt(rentalId),
          ethPriceInWei,
          insurancePriceInWei,
          BigInt(rentalPeriod),
          signature as `0x${string}`,
        ],
        chain,
        account: address,
      });

      // Estimate gas for listing
      const listGasEstimate = await publicClient.estimateContractGas({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "listChickenForRent",
        args: [
          BigInt(chickenTokenId),
          BigInt(rentalId),
          ethPriceInWei,
          insurancePriceInWei,
          BigInt(rentalPeriod),
          signature as `0x${string}`,
        ],
        account: address,
      });

      // Execute listing transaction
      const listHash = await walletClient.writeContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "listChickenForRent",
        args: [
          BigInt(chickenTokenId),
          BigInt(rentalId),
          ethPriceInWei,
          insurancePriceInWei,
          BigInt(rentalPeriod),
          signature as `0x${string}`,
        ],
        gas: listGasEstimate + BigInt(50000), // Add buffer
        chain,
        account: address,
      });

      // Wait for listing confirmation
      toast.info("Confirming escrow transaction...", {
        description: "Waiting for blockchain confirmation",
        position: "top-center",
      });

      const listReceipt = await publicClient.waitForTransactionReceipt({
        hash: listHash,
        confirmations: 1,
      });

      if (listReceipt.status !== "success") {
        throw new Error("Escrow transaction failed");
      }

      // Step 4: For direct delegation, we need to handle activation differently
      // Since the renter needs to execute the rentChicken call, we'll provide
      // the necessary data for them to complete the activation

      toast.success("Delegation setup complete!", {
        description:
          "Chicken is now in escrow. Share the delegation details with the delegatee to activate.",
        position: "top-center",
      });

      // Prepare activation data for the delegatee
      const activationData = {
        rentId: BigInt(rentalId),
        chickenId: BigInt(chickenTokenId),
        ethPrice: ethPriceInWei, // 0 for direct delegation
        insurancePrice: insurancePriceInWei, // 0 for direct delegation
        renterAddress: formData.renterAddress as Address,
        ownerAddress: ownerAddress as Address,
        signature: signature as `0x${string}`,
      };

      // Invalidate queries
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["my-rentals"] });
        queryClient.invalidateQueries({ queryKey: ["chickens"] });
        queryClient.invalidateQueries({ queryKey: ["my-rentals", address] });
        queryClient.invalidateQueries({ queryKey: ["chickens", address] });
        queryClient.invalidateQueries({ queryKey: ["chickenRentalStatuses"] });
      }, 500);

      return {
        success: true,
        data: delegationData,
        activationData,
        listHash,
        listReceipt,
        message: "Delegation created successfully",
      };
    } catch (error: unknown) {
      console.error("Direct delegation error:", error);

      let errorMessage = "Failed to create delegation";
      let errorDescription = "Please try again";

      if (error instanceof Error) {
        if (error.message.includes("User rejected")) {
          errorMessage = "Transaction cancelled";
          errorDescription = "You cancelled the transaction";
        } else if (error.message.includes("insufficient funds")) {
          errorMessage = "Insufficient funds";
          errorDescription = "You don't have enough ETH for gas fees";
        } else {
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        position: "top-center",
      });

      return { success: false, error };
    } finally {
      setIsProcessing(false);
    }
  };

  // Mutation for React Query integration
  const directDelegationMutation = useMutation({
    mutationFn: executeDirectDelegation,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Direct delegation mutation error:", error);
    },
  });

  return {
    executeDirectDelegation,
    directDelegationMutation,
    isProcessing,
    rentalAddress,
  };
};
