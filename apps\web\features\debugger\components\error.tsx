"use client";

import React from "react";

interface IErrorProps {
  message: string;
}

export default function Error({ message }: IErrorProps): React.ReactNode {
  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6">
      <div className="flex items-start p-4 bg-red-50 rounded-lg border border-red-100">
        <div className="mr-3 mt-0.5">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-red-500"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <div>
          <h3 className="text-sm font-medium text-red-800 mb-1">
            Error loading data
          </h3>
          <p className="text-red-600">{message}</p>
          <p className="text-xs text-red-500 mt-2">
            Please try again or check your connection
          </p>
        </div>
      </div>
    </div>
  );
}
