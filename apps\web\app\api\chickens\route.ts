import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const c = await cookies();
  const jwt = c.get("jwt")?.value;

  if (!jwt) {
    return NextResponse.json(
      {
        status: false,
        responseCode: 401,
        message: "Authentication required",
      },
      { status: 401 }
    );
  }

  try {
    const apiEndpoint = process.env.HONO_API_ENDPOINT;

    if (!apiEndpoint) {
      throw new Error("HONO_API_ENDPOINT environment variable is not defined");
    }

    const url = `${apiEndpoint}/api/me/chickens`;
    console.log(`Fetching from: ${url}`);

    const res = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${jwt}`,
      },
      cache: "no-store",
    });

    const data = await res.json();

    if (!res.ok) {
      console.error("API response error:", data);
      return NextResponse.json(
        {
          status: false,
          responseCode: res.status,
          message: data.message || "Error fetching chickens",
          errors: data.errors || [],
        },
        { status: res.status }
      );
    }

    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error("Error in chickens API route:", error);

    return NextResponse.json(
      {
        status: false,
        responseCode: 500,
        message: "Failed to fetch chickens",
        errors: [(error as Error).message],
      },
      { status: 500 }
    );
  }
}
