"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "ui";
import Image from "next/image";
import {
  Clock,
  User,
  Users,
  Zap,
  Copy,
  ExternalLink,
  Crown,
  Star,
  Store,
  Calendar,
  Coins,
  Shield,
} from "lucide-react";
import { IDelegatedChicken } from "@/features/delegation/hooks/useDelegatedChickens";
import {
  REWARD_DISTRIBUTION_LABELS,
  GAME_REWARD_DISTRIBUTION_LABELS,
  DELEGATED_TASK_LABELS,
  EDelegatedTaskType,
  IRental,
} from "@/features/delegation/types/delegation.types";
import { CooldownTimer } from "@/components/common/cooldown-timer";
import { useStateContext } from "@/providers/app/state";
import { useState, useEffect } from "react";
import { DelegationAPI } from "@/features/delegation/api/delegation.api";
import { getAddressExplorerUrl } from "@/lib/utils/explorer";

interface IDelegationDetailsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  delegatedChicken: IDelegatedChicken | null;
}

export function DelegationDetailsDialog({
  isOpen,
  onOpenChange,
  delegatedChicken,
}: IDelegationDetailsDialogProps) {
  const { address } = useStateContext();
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);
  const [rentalDetails, setRentalDetails] = useState<IRental | null>(null);
  const [isLoadingRental, setIsLoadingRental] = useState(false);

  // Fetch rental details when dialog opens
  useEffect(() => {
    if (isOpen && delegatedChicken) {
      const fetchRentalDetails = async () => {
        setIsLoadingRental(true);
        try {
          const response = await DelegationAPI.getChickenRental(
            delegatedChicken.tokenId
          );
          if (response.status === 1 && response.data) {
            setRentalDetails(response.data);
          }
        } catch (error) {
          console.error("Failed to fetch rental details:", error);
        } finally {
          setIsLoadingRental(false);
        }
      };

      fetchRentalDetails();
    }
  }, [isOpen, delegatedChicken]);

  if (!delegatedChicken) return null;

  const chicken = delegatedChicken;
  const chickenType =
    chicken.metadata?.attributes?.find((attr) => attr.trait_type === "Type")
      ?.value || "Unknown";

  const getTypeColor = (type: string | number) => {
    const typeStr = String(type).toLowerCase();
    switch (typeStr) {
      case "genesis":
        return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      case "legacy":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "ordinary":
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const getTypeIcon = (type: string | number) => {
    const typeStr = String(type).toLowerCase();
    switch (typeStr) {
      case "genesis":
        return <Crown className="w-4 h-4" />;
      case "legacy":
        return <Star className="w-4 h-4" />;
      case "ordinary":
        return <Zap className="w-4 h-4" />;
      default:
        return <Zap className="w-4 h-4" />;
    }
  };

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedAddress(type);
      setTimeout(() => setCopiedAddress(null), 2000);
    } catch (err) {
      console.error("Failed to copy address:", err);
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 8)}...${address.slice(-6)}`;
  };

  const openInExplorer = (address: string) => {
    window.open(getAddressExplorerUrl(address), "_blank");
  };

  // Determine which address to show based on current user
  const isOwner = address?.toLowerCase() === chicken.ownerAddress.toLowerCase();

  // Determine if this is a listing or active delegation/rental
  const isListed = rentalDetails?.status === 0; // Status 0 = AVAILABLE (listed in market)
  const isActiveRental = rentalDetails?.status === 1; // Status 1 = RENTED (active)

  // For listed chickens, don't show renter address since there isn't one yet
  const displayAddress = isListed
    ? chicken.ownerAddress
    : isOwner
      ? chicken.renterAddress
      : chicken.ownerAddress;
  const displayLabel = isListed ? "Owner" : isOwner ? "Delegated To" : "Owner";

  // Calculate remaining seconds from rental expiration
  const remainingSeconds = rentalDetails?.expiresAt
    ? Math.max(
        0,
        Math.floor(
          (new Date(rentalDetails.expiresAt).getTime() - Date.now()) / 1000
        )
      )
    : 0;

  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
      <Modal.Content size="lg">
        <Modal.Header>
          <Modal.Title>
            {isListed ? "Marketplace Listing" : "Delegation Details"}
          </Modal.Title>
          <Modal.Description>
            {isListed
              ? "View listing information and terms"
              : "View delegation information and terms"}
          </Modal.Description>
        </Modal.Header>

        <Modal.Body className="space-y-4 pb-6">
          {/* Hero Section - Chicken Card */}
          <div className="relative bg-gradient-to-br from-stone-800 to-stone-900 rounded-lg p-4 border border-stone-700">
            <div className="flex gap-4">
              <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-stone-700 flex-shrink-0">
                <Image
                  src={chicken.image}
                  alt={chicken.metadata?.name || `Chicken #${chicken.tokenId}`}
                  fill
                  className="object-cover"
                />
              </div>

              <div className="flex-1 space-y-2">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="font-semibold text-white text-lg">
                      {chicken.metadata?.name || `Chicken #${chicken.tokenId}`}
                    </h3>
                  </div>
                  <span
                    className={`text-xs px-2 py-1 rounded-full border flex items-center gap-1.5 ${getTypeColor(chickenType)}`}
                  >
                    {getTypeIcon(chickenType)}
                    {chickenType}
                  </span>
                </div>

                {/* Quick Stats */}
                <div className="flex gap-3">
                  <div className="flex items-center gap-1.5 text-yellow-400">
                    <span>🪶</span>
                    <span className="font-semibold text-sm">
                      {chicken.dailyFeathers}
                    </span>
                    <span className="text-gray-400 text-xs">daily</span>
                  </div>
                  {chicken.legendaryCount > 0 && (
                    <div className="flex items-center gap-1.5 text-purple-400">
                      <Zap className="w-3 h-3" />
                      <span className="font-semibold text-sm">
                        {chicken.legendaryCount}
                      </span>
                      <span className="text-gray-400 text-xs">legendary</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Time Remaining / Listing Status */}
          {isLoadingRental ? (
            <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/30 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="p-1.5 bg-blue-500/20 rounded">
                  <Clock className="w-4 h-4 text-blue-400" />
                </div>
                <h4 className="font-semibold text-white">Loading...</h4>
              </div>
              <div className="text-center">
                <div className="text-blue-400 text-sm">
                  Loading rental details...
                </div>
              </div>
            </div>
          ) : isListed ? (
            <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/30 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="p-1.5 bg-green-500/20 rounded">
                  <Store className="w-4 h-4 text-green-400" />
                </div>
                <h4 className="font-semibold text-white">Listing Status</h4>
              </div>
              <div className="text-center">
                <div className="text-green-400 text-lg font-semibold">
                  Available for Rent
                </div>
                <p className="text-gray-400 text-xs mt-1">
                  This chicken is listed in the rental marketplace
                </p>
              </div>
            </div>
          ) : (
            (remainingSeconds > 0 ||
              (rentalDetails && !rentalDetails.expiresAt)) && (
              <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/30 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <div className="p-1.5 bg-blue-500/20 rounded">
                    <Clock className="w-4 h-4 text-blue-400" />
                  </div>
                  <h4 className="font-semibold text-white">Time Remaining</h4>
                </div>
                <div className="text-center">
                  {remainingSeconds > 0 ? (
                    <>
                      <CooldownTimer
                        remainingSeconds={remainingSeconds}
                        className="text-2xl font-bold text-blue-400"
                      />
                      <p className="text-gray-400 text-xs mt-1">
                        Until delegation expires
                      </p>
                    </>
                  ) : (
                    <>
                      <div className="text-blue-400 text-lg font-semibold">
                        No Expiration
                      </div>
                      <p className="text-gray-400 text-xs mt-1">
                        This delegation has no time limit
                      </p>
                    </>
                  )}
                </div>
              </div>
            )
          )}

          {/* Rental/Delegation Configuration */}
          <div className="space-y-3">
            <h4 className="font-semibold text-white flex items-center gap-2">
              <div className="w-1 h-4 bg-yellow-400 rounded-full"></div>
              {isListed ? "Listing Configuration" : "Delegation Configuration"}
            </h4>

            {/* Rental Price for Listed Chickens */}
            {isListed && rentalDetails && (
              <div className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/30 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-yellow-500/20 rounded">
                      <Coins className="w-4 h-4 text-yellow-400" />
                    </div>
                    <div>
                      <p className="text-gray-300 text-sm">
                        Daily Rental Price
                      </p>
                      <p className="text-yellow-400 font-bold text-lg">
                        {rentalDetails.roninPrice === "0"
                          ? "Free"
                          : `${parseFloat(rentalDetails.roninPrice) / 1e18} RON`}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-gray-400 text-xs">Duration</p>
                    <p className="text-white font-semibold">
                      {Math.floor(rentalDetails.rentalPeriod / 86400)} days
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Insurance Information */}
            {rentalDetails &&
              (() => {
                const hasInsurance =
                  rentalDetails.insurancePrice &&
                  rentalDetails.insurancePrice !== "0";
                const chickenType = chicken.type?.toLowerCase();
                const chickenNeedsInsurance =
                  chickenType === "legacy" || chickenType === "ordinary";

                // Only show insurance section if chicken needs insurance or has insurance
                if (hasInsurance || chickenNeedsInsurance) {
                  const insuranceAmount = hasInsurance
                    ? parseFloat(rentalDetails.insurancePrice!) / 1e18
                    : 0;

                  return (
                    <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/30 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="p-1.5 bg-blue-500/20 rounded">
                            <Shield className="w-4 h-4 text-blue-400" />
                          </div>
                          <div>
                            <p className="text-gray-300 text-sm">
                              Insurance Coverage
                            </p>
                            <p
                              className={`font-bold text-lg ${hasInsurance ? "text-blue-400" : "text-yellow-400"}`}
                            >
                              {hasInsurance
                                ? `${insuranceAmount.toFixed(4)} RON`
                                : "Not covered"}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-gray-400 text-xs">Status</p>
                          <p
                            className={`font-semibold text-sm ${hasInsurance ? "text-blue-400" : "text-yellow-400"}`}
                          >
                            {hasInsurance
                              ? "Protected"
                              : chickenNeedsInsurance
                                ? "Can die"
                                : "Genesis"}
                          </p>
                        </div>
                      </div>
                      {!hasInsurance && chickenNeedsInsurance && (
                        <div className="mt-2 text-xs text-yellow-300 bg-yellow-500/10 border border-yellow-500/30 rounded p-2">
                          ⚠️ This chicken can die during delegation. No
                          insurance coverage provided.
                        </div>
                      )}
                      {hasInsurance && (
                        <div className="mt-2 text-xs text-blue-300 bg-blue-500/10 border border-blue-500/30 rounded p-2">
                          🛡️ Owner will receive insurance payout if chicken dies
                          during delegation.
                        </div>
                      )}
                    </div>
                  );
                }
                return null;
              })()}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {/* Activities Card */}
              <div className="bg-gradient-to-br from-stone-800 to-stone-900 rounded-lg p-3 border border-stone-700">
                <div className="flex items-center gap-2 mb-2">
                  <div className="p-1 bg-green-500/20 rounded">
                    <Zap className="w-3 h-3 text-green-400" />
                  </div>
                  <span className="text-gray-300 text-sm">Activities</span>
                </div>
                <p className="text-white font-semibold">
                  {
                    DELEGATED_TASK_LABELS[
                      chicken.delegatedTask as keyof typeof DELEGATED_TASK_LABELS
                    ]
                  }
                </p>
              </div>

              {/* Daily Rub Distribution Card */}
              <div className="bg-gradient-to-br from-stone-800 to-stone-900 rounded-lg p-3 border border-stone-700">
                <div className="flex items-center gap-2 mb-2">
                  <div className="p-1 bg-purple-500/20 rounded">
                    <Users className="w-3 h-3 text-purple-400" />
                  </div>
                  <span className="text-gray-300 text-sm">
                    Daily Rub Distribution
                  </span>
                </div>
                {chicken.rewardDistribution === 3 &&
                chicken.sharedRewardAmount ? (
                  <Tooltip delay={0}>
                    <Tooltip.Trigger>
                      <p className="text-white font-semibold cursor-help">
                        {
                          REWARD_DISTRIBUTION_LABELS[
                            chicken.rewardDistribution as keyof typeof REWARD_DISTRIBUTION_LABELS
                          ]
                        }{" "}
                        ({chicken.sharedRewardAmount} 🪶/day)
                      </p>
                    </Tooltip.Trigger>
                    <Tooltip.Content>
                      <div className="text-sm">
                        <div className="font-semibold mb-1">
                          Daily Rub Distribution:
                        </div>
                        <div>
                          Renter gets: {chicken.sharedRewardAmount} Daily Rub
                          rewards/day
                        </div>
                        <div>
                          Owner gets:{" "}
                          {(chicken.dailyFeathers || 0) -
                            chicken.sharedRewardAmount}{" "}
                          Daily Rub rewards/day
                        </div>
                      </div>
                    </Tooltip.Content>
                  </Tooltip>
                ) : (
                  <p className="text-white font-semibold">
                    {
                      REWARD_DISTRIBUTION_LABELS[
                        chicken.rewardDistribution as keyof typeof REWARD_DISTRIBUTION_LABELS
                      ]
                    }
                  </p>
                )}
              </div>
            </div>

            {/* Reward Share */}
            {chicken.sharedRewardAmount && (
              <div className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/30 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="p-1 bg-yellow-500/20 rounded">
                      <Star className="w-3 h-3 text-yellow-400" />
                    </div>
                    <span className="text-gray-300 text-sm">
                      Your Daily Rub Rewards
                    </span>
                  </div>
                  <span className="text-yellow-400 font-bold text-lg">
                    {chicken.sharedRewardAmount} 🪶
                  </span>
                </div>
              </div>
            )}

            {/* Listing Date for Listed Chickens */}
            {isListed && rentalDetails && (
              <div className="bg-gradient-to-br from-stone-800 to-stone-900 rounded-lg p-3 border border-stone-700">
                <div className="flex items-center gap-2 mb-2">
                  <div className="p-1 bg-gray-500/20 rounded">
                    <Calendar className="w-3 h-3 text-gray-400" />
                  </div>
                  <span className="text-gray-300 text-sm">Listed On</span>
                </div>
                <p className="text-white font-semibold">
                  {new Date(rentalDetails.createdAt).toLocaleDateString(
                    "en-US",
                    {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    }
                  )}
                </p>
              </div>
            )}

            {/* Game Reward Distribution - Only show when gameplay is enabled */}
            {rentalDetails &&
              (rentalDetails.delegatedTask === EDelegatedTaskType.GAMEPLAY ||
                rentalDetails.delegatedTask === EDelegatedTaskType.BOTH) &&
              rentalDetails.gameRewardDistribution && (
                <div className="bg-gradient-to-r from-purple-500/10 to-indigo-500/10 border border-purple-500/30 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="p-1 bg-purple-500/20 rounded">
                        <span className="text-purple-400 text-xs">🎮</span>
                      </div>
                      <span className="text-gray-300 text-sm">
                        Game Rewards
                      </span>
                    </div>
                    <span className="text-purple-400 font-bold">
                      {
                        GAME_REWARD_DISTRIBUTION_LABELS[
                          rentalDetails.gameRewardDistribution
                        ]
                      }
                    </span>
                  </div>
                  <div className="mt-2 text-xs text-gray-400">
                    Who receives crystals, shards, and corn from gameplay
                  </div>
                </div>
              )}
          </div>

          {/* Address Information */}
          <div className="space-y-3">
            <h4 className="font-semibold text-white flex items-center gap-2">
              <div className="w-1 h-4 bg-blue-400 rounded-full"></div>
              {isListed ? "Owner Information" : "Address Information"}
            </h4>

            <div className="bg-gradient-to-br from-stone-800 to-stone-900 rounded-lg p-4 border border-stone-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-1.5 bg-blue-500/20 rounded">
                    {isOwner ? (
                      <Users className="w-3 h-3 text-blue-400" />
                    ) : (
                      <User className="w-3 h-3 text-blue-400" />
                    )}
                  </div>
                  <div>
                    <p className="text-gray-300 text-xs">{displayLabel}</p>
                    <p className="text-white font-mono font-semibold">
                      {formatAddress(displayAddress)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <button
                    onClick={() =>
                      copyToClipboard(displayAddress, displayLabel)
                    }
                    className="p-1.5 hover:bg-stone-700 rounded transition-colors group"
                    title="Copy address"
                  >
                    <Copy className="w-3 h-3 text-gray-400 group-hover:text-white" />
                  </button>
                  <button
                    onClick={() => openInExplorer(displayAddress)}
                    className="p-1.5 hover:bg-stone-700 rounded transition-colors group"
                    title="View in explorer"
                  >
                    <ExternalLink className="w-3 h-3 text-gray-400 group-hover:text-white" />
                  </button>
                </div>
              </div>
              {copiedAddress === displayLabel && (
                <div className="mt-2 p-2 bg-green-500/10 border border-green-500/30 rounded">
                  <p className="text-green-400 text-xs flex items-center gap-1.5">
                    <span className="w-1.5 h-1.5 bg-green-400 rounded-full"></span>
                    Address copied!
                  </p>
                </div>
              )}
            </div>
          </div>
        </Modal.Body>
      </Modal.Content>
    </Modal>
  );
}
