"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Loading } from "@/components/shared/loading";
import { usePreAlphaLeaderboard } from "@/hooks/usePreAlphaLeaderboard";
import { ChevronDown, ChevronUp, Info, ShoppingCart } from "lucide-react";
import { useStateContext } from "@/providers/app/state";
import useAuthStore from "@/store/auth";
// import { calculatePrizeAmount, formatPrizeAmount, isEligibleForPrize } from "@/lib/utils/prize-distribution"; // Temporarily disabled for Preseason 0
import { createPublicClient, http, erc20Abi, formatUnits, Address } from "viem";
import { ronin, saigon } from "viem/chains";
import { getMarketplaceUrl } from "@/lib/utils/explorer";

// Updated type to match the new API response
interface ChickenLeaderboardItem {
  rank: number;
  tokenId: number;
  name: string;
  mmr: number;
  wins: number;
  losses: number;
  draws: number;
  totalGames: number;
  type: string;
  lastUpdated: string;
  owner: string;
}

interface LeaderboardPagination {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

interface LeaderboardResponse {
  leaderboard: ChickenLeaderboardItem[];
  pagination: LeaderboardPagination;
  message?: string;
}

// Define column type for type safety
type Column = {
  key: keyof ChickenLeaderboardItem | string;
  label: string;
  className?: string;
  format?: (val: any, item?: ChickenLeaderboardItem) => React.ReactNode;
};

export default function ChickenLeaderboard() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { address: connectedWallet } = useStateContext();
  const { rns: connectedRns } = useAuthStore();

  const [page, setPage] = useState(parseInt(searchParams.get("page") || "1"));
  const [limit, setLimit] = useState(20);
  const [searchId, setSearchId] = useState(searchParams.get("tokenId") || "");
  const [isSearching, setIsSearching] = useState(!!searchParams.get("tokenId"));
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());
  // const [prizePoolData, setPrizePoolData] = useState<{
  //   basePrize: number;
  //   growingPot: number;
  // }>({ basePrize: 10000000, growingPot: 0 }); // Temporarily disabled for Preseason 0

  // Season end configuration - Temporarily disabled for Preseason 0
  // const isSeasonEnded = process.env.NEXT_PUBLIC_SEASON_ENDED === 'true';
  // const finalRewards = {
  //   crafting: parseInt(process.env.NEXT_PUBLIC_FINAL_CRAFTING_REWARDS || '0'),
  //   breeding: parseInt(process.env.NEXT_PUBLIC_FINAL_BREEDING_REWARDS || '0'),
  //   healIap: parseInt(process.env.NEXT_PUBLIC_FINAL_HEAL_IAP_REWARDS || '0'),
  // };

  // Custom hook to fetch data from the API
  const { data, isLoading, error } =
    usePreAlphaLeaderboard<LeaderboardResponse>("chickens", page, limit, {
      order: "desc", // Always sort by highest MMR first
      tokenId: searchId || undefined,
      minGames: "20", // Hardcoded to always be 20
    });

  const leaderboard = data?.leaderboard || [];
  const totalPages = data?.pagination?.pages || 1;
  const totalItems = data?.pagination?.total || 0;
  const apiMessage = data?.message;

  // Prize pool constants - Temporarily disabled for Preseason 0
  // const BASE_PRIZE = 10000000; // 10 million base prize
  // const CHAIN_ID = process.env.NEXT_PUBLIC_CHAINDID
  //   ? parseInt(process.env.NEXT_PUBLIC_CHAINDID)
  //   : 2021;
  // const COCK_TOKEN_ADDRESS = process.env.NEXT_PUBLIC_COCK_ADDRESS as Address;
  // const CRAFTING_TREASURY_ADDRESS = process.env
  //   .NEXT_PUBLIC_CRAFTING_TREASURY_ADDRESS as Address;
  // const BREEDING_TREASURY_ADDRESS = process.env
  //   .NEXT_PUBLIC_BREEDING_TREASURY_ADDRESS as Address;

  // Fetch growing pot from treasuries - Temporarily disabled for Preseason 0
  // const fetchGrowingPot = useCallback(async (): Promise<number> => {
  //   try {
  //     if (
  //       !COCK_TOKEN_ADDRESS ||
  //       !CRAFTING_TREASURY_ADDRESS ||
  //       !BREEDING_TREASURY_ADDRESS
  //     ) {
  //       return 0;
  //     }

  //     const chain = CHAIN_ID === 2020 ? ronin : saigon;
  //     const publicClient = createPublicClient({
  //       chain,
  //       transport: http(),
  //     });

  //     const [craftingBalance, breedingBalance] = await Promise.all([
  //       publicClient.readContract({
  //         address: COCK_TOKEN_ADDRESS,
  //         abi: erc20Abi,
  //         functionName: "balanceOf",
  //         args: [CRAFTING_TREASURY_ADDRESS],
  //       }) as Promise<bigint>,
  //       publicClient.readContract({
  //         address: COCK_TOKEN_ADDRESS,
  //         abi: erc20Abi,
  //         functionName: "balanceOf",
  //         args: [BREEDING_TREASURY_ADDRESS],
  //       }) as Promise<bigint>,
  //     ]);

  //     const craftingInTokens = parseFloat(formatUnits(craftingBalance, 18));
  //     const breedingInTokens = parseFloat(formatUnits(breedingBalance, 18));

  //     const craftingContribution = craftingInTokens * 0.4;
  //     const breedingContribution = breedingInTokens * 0.4;
  //     const totalContribution = craftingContribution + breedingContribution;

  //     return totalContribution;
  //   } catch (error) {
  //     console.error("Error fetching treasury balances:", error);
  //     return 0;
  //   }
  // }, [COCK_TOKEN_ADDRESS, CRAFTING_TREASURY_ADDRESS, BREEDING_TREASURY_ADDRESS, CHAIN_ID]);

  // Fetch prize pool data on component mount - Temporarily disabled for Preseason 0
  // useEffect(() => {
  //   if (isSeasonEnded) {
  //     // Use final accumulated rewards when season has ended
  //     const totalFinalRewards = finalRewards.crafting + finalRewards.breeding + finalRewards.healIap;
  //     setPrizePoolData({ basePrize: BASE_PRIZE, growingPot: totalFinalRewards });
  //   } else {
  //     fetchGrowingPot().then((growingPot) => {
  //       setPrizePoolData({ basePrize: BASE_PRIZE, growingPot });
  //     });
  //   }
  // }, [fetchGrowingPot, BASE_PRIZE, isSeasonEnded, finalRewards]);

  useEffect(() => {
    const params = new URLSearchParams();
    params.set("page", page.toString());
    if (searchId) params.set("tokenId", searchId);
    // minGames is not included in URL params as it's always 20
    router.replace(`/leaderboard?${params.toString()}`, { scroll: false });
  }, [page, searchId, router]);

  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleLimitChange = useCallback(
    (e: React.ChangeEvent<HTMLSelectElement>) => {
      setLimit(parseInt(e.target.value));
      setPage(1);
    },
    []
  );

  const handleSearch = useCallback(() => {
    if (searchId) {
      setIsSearching(true);
      setPage(1);
    }
  }, [searchId]);

  const clearSearch = useCallback(() => {
    setSearchId("");
    setIsSearching(false);
    setPage(1);
  }, []);

  const toggleRowExpansion = useCallback((tokenId: number) => {
    setExpandedRows((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(tokenId)) {
        newSet.delete(tokenId);
      } else {
        newSet.add(tokenId);
      }
      return newSet;
    });
  }, []);

  const downloadCsv = useCallback(() => {
    const params = new URLSearchParams();
    params.set("format", "csv");
    params.set("order", "desc");
    if (searchId) params.set("tokenId", searchId);
    params.set("minGames", "20");

    window.open(
      `https://chicken-api-ivory.vercel.app/api/stats/prealpha/leaderboard/chickens?${params.toString()}`,
      "_blank"
    );
  }, [searchId]);

  const paginationItems = useMemo(() => {
    const items = [];
    const maxVisiblePages = 5;

    items.push(
      <button
        key="first"
        onClick={() => handlePageChange(1)}
        className={`px-3 py-1 rounded-md ${
          page === 1
            ? "bg-primary text-black font-bold"
            : "bg-stone-800 text-white hover:bg-stone-700"
        }`}
      >
        1
      </button>
    );

    const startPage = Math.max(2, page - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 2);

    if (startPage > 2) {
      items.push(
        <span key="ellipsis1" className="px-2 text-white">
          ...
        </span>
      );
    }

    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`px-3 py-1 rounded-md ${
            page === i
              ? "bg-primary text-black font-bold"
              : "bg-stone-800 text-white hover:bg-stone-700"
          }`}
        >
          {i}
        </button>
      );
    }

    if (endPage < totalPages - 1) {
      items.push(
        <span key="ellipsis2" className="px-2 text-white">
          ...
        </span>
      );
    }

    if (totalPages > 1) {
      items.push(
        <button
          key="last"
          onClick={() => handlePageChange(totalPages)}
          className={`px-3 py-1 rounded-md ${
            page === totalPages
              ? "bg-primary text-black font-bold"
              : "bg-stone-800 text-white hover:bg-stone-700"
          }`}
        >
          {totalPages}
        </button>
      );
    }

    return items;
  }, [page, totalPages, handlePageChange]);

  // Note: getMarketplaceUrl is now imported from @/lib/utils/explorer

  // Get inventory URL
  const getInventoryUrl = (tokenId: number) => {
    return `https://app.sabongsaga.com/inventory/chickens/${tokenId}`;
  };

  // Format Ronin address for display
  const formatAddress = (address: string) => {
    if (!address || address === "Unknown") return "Unknown";

    // Check if it's an RNS-style name (contains a dot)
    if (address.includes(".")) return address;

    // Otherwise format as Ethereum address
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  // Main visible columns (always shown)
  const mainColumns = useMemo<Column[]>(() => {
    return [
      {
        key: "rank",
        label: "Rank",
        className: "text-right",
        format: (value, item) => {
          const rankStyle = getRankStyling(value);
          return (
            <div className="flex items-center justify-end gap-1">
              {rankStyle.icon && <span>{rankStyle.icon}</span>}
              <span className={value <= 3 ? rankStyle.text : "text-gray-200"}>
                {value}
              </span>
            </div>
          );
        },
      },
      {
        key: "name",
        label: "Name",
        className: "text-white",
      },
      {
        key: "type",
        label: "Type",
        className: "text-center",
        format: (value) => {
          const normalizedType = value.toLowerCase();
          let bgColor = "bg-stone-700";
          let textColor = "text-white";

          if (normalizedType === "genesis") {
            bgColor = "bg-orange-600";
            textColor = "text-white";
          } else if (normalizedType === "legacy") {
            bgColor = "bg-green-600";
            textColor = "text-white";
          } else if (normalizedType === "ordinary") {
            bgColor = "bg-blue-600";
            textColor = "text-white";
          }

          const properCaseValue =
            value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();

          return (
            <span
              className={`px-2 py-1 text-xs rounded-full ${bgColor} ${textColor}`}
            >
              {properCaseValue}
            </span>
          );
        },
      },
      {
        key: "owner",
        label: "RNS/Owner",
        format: (value) => (
          <div className="truncate max-w-[120px] text-cyan-300" title={value}>
            {formatAddress(value)}
          </div>
        ),
      },
      {
        key: "mmr",
        label: "MMR",
        className: "text-white font-bold text-right",
      },
      // Prize pool temporarily disabled for Preseason 0
      // {
      //   key: "prizeShare",
      //   label: "Prize in $COCK",
      //   className: "text-yellow-400 font-bold text-right",
      //   format: (_, item) => {
      //     if (!item || !isEligibleForPrize(item.rank)) {
      //       return <span className="text-gray-500">-</span>;
      //     }
      //
      //     const prizeAmount = calculatePrizeAmount(
      //       item.rank,
      //       prizePoolData.basePrize,
      //       prizePoolData.growingPot
      //     );
      //
      //     return (
      //       <span>{formatPrizeAmount(prizeAmount.totalPrize)}</span>
      //     );
      //   },
      // },
    ];
  }, []); // Removed prizePoolData dependency for Preseason 0

  // Additional columns (shown in expanded view)
  const expandableColumns = useMemo<Column[]>(() => {
    return [
      { key: "totalGames", label: "Total Games", className: "text-purple-400" },
      { key: "wins", label: "Wins", className: "text-green-400" },
      { key: "losses", label: "Losses", className: "text-red-400" },
      { key: "draws", label: "Draws", className: "text-blue-400" },
      {
        key: "winRate",
        label: "Win Rate",
        className: "text-yellow-400",
        format: (_, item) => {
          if (!item) return "0%";
          const totalGames = item.wins + item.losses + item.draws;
          const winRate =
            totalGames > 0 ? Math.round((item.wins / totalGames) * 100) : 0;
          return `${winRate}%`;
        },
      },
    ];
  }, []);

  // Function to check if entry belongs to connected user
  const isUserEntry = useCallback(
    (owner: string) => {
      if (!owner) return false;

      // Check if owner matches the connected wallet address
      if (
        connectedWallet &&
        owner.toLowerCase() === connectedWallet.toLowerCase()
      ) {
        return true;
      }

      // Check if owner matches the connected user's RNS name
      if (connectedRns && owner === connectedRns) {
        return true;
      }

      return false;
    },
    [connectedWallet, connectedRns]
  );

  // Function to get rank styling for prestige
  const getRankStyling = (rank: number) => {
    if (rank === 1) {
      return {
        border: "border-2 border-yellow-400",
        glow: "shadow-inner shadow-yellow-400/60",
        text: "text-yellow-400",
        icon: "👑",
      };
    } else if (rank === 2) {
      return {
        border: "border-2 border-gray-200",
        glow: "shadow-inner shadow-gray-200/50",
        text: "text-gray-200",
        icon: "🥈",
      };
    } else if (rank === 3) {
      return {
        border: "border-2 border-orange-400",
        glow: "shadow-inner shadow-orange-400/50",
        text: "text-orange-400",
        icon: "🥉",
      };
    }
    return {
      border: "",
      glow: "",
      text: "text-gray-200",
      icon: "",
    };
  };

  return (
    <div className="leaderboard-container">
      {/* Filters */}
      <div className="flex flex-wrap justify-between items-center mb-6 gap-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* Chicken ID Search */}
          <div className="flex items-center space-x-2">
            <label htmlFor="searchId" className="text-white">
              Chicken ID:
            </label>
            <input
              id="searchId"
              type="number"
              value={searchId}
              onChange={(e) => setSearchId(e.target.value)}
              className="bg-stone-800 text-white border border-stone-700 rounded-md px-3 py-1 w-24"
              placeholder="ID"
              onKeyDown={(e) => {
                if (e.key === "Enter") handleSearch();
              }}
            />
            <button
              onClick={handleSearch}
              className="bg-stone-700 text-white px-3 py-1 rounded-md hover:bg-stone-600 transition-colors duration-200"
            >
              Search
            </button>
            {isSearching && (
              <button
                onClick={clearSearch}
                className="bg-red-700 text-white px-3 py-1 rounded-md hover:bg-red-600 transition-colors duration-200"
              >
                Clear
              </button>
            )}
          </div>
        </div>

        {/* Results per page and CSV download */}
        <div className="flex items-center space-x-4">
          {!isSearching && (
            <select
              value={limit}
              onChange={handleLimitChange}
              className="bg-stone-800 text-white border border-stone-700 rounded-md px-3 py-1"
            >
              {[10, 20, 50, 100].map((val) => (
                <option key={val} value={val}>
                  {val} per page
                </option>
              ))}
            </select>
          )}

          {/* <button
            onClick={downloadCsv}
            className="bg-primary text-black px-4 py-2 rounded-md font-medium hover:bg-yellow-500 transition-colors duration-200 flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
              />
            </svg>
            Download CSV
          </button> */}
        </div>
      </div>

      {/* Filter indicators */}
      <div className="flex flex-wrap gap-2 mb-4">
        {isSearching && (
          <div className="bg-stone-700 text-white p-3 rounded-md flex justify-between items-center flex-1">
            <span>
              Showing results for Chicken ID: <strong>{searchId}</strong>
            </span>
            <button
              onClick={clearSearch}
              className="bg-red-700 text-white px-3 py-1 rounded-md hover:bg-red-600 transition-colors duration-200 text-sm"
            >
              Clear
            </button>
          </div>
        )}

        <div className="bg-stone-700 text-white p-3 rounded-md flex justify-between items-center flex-1">
          <span className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 text-primary"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
              />
            </svg>
            Showing chickens with at least{" "}
            <strong className="mx-1">20 games</strong>
          </span>
        </div>

        {isLoading && (
          <div className="bg-cyan-900 text-white p-3 rounded-md flex items-center flex-1">
            <svg
              className="animate-spin h-5 w-5 mr-2 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            <span>Loading data including blockchain owner information...</span>
          </div>
        )}
      </div>

      {/* API Message */}
      {apiMessage && (
        <div className="bg-blue-900 text-white p-3 rounded-md mb-4 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 text-blue-300"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          {apiMessage}
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="bg-red-900 text-white p-4 rounded-md mb-6 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 text-red-300"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          Failed to load leaderboard data. Please try again later.
        </div>
      )}

      {/* Content */}
      <p className="text-xs text-stone-400 mb-4">
        <span className="text-stone-500">Note:</span> This is Preseason 0 - no
        prize pool is available for this season. Rankings are for competitive
        purposes only.
      </p>
      {isLoading ? (
        <Loading />
      ) : (
        <>
          {/* Responsive Table */}
          <div className="space-y-4">
            {/* Desktop Table View - Hidden on mobile */}
            <div className="hidden md:block">
              <div className="overflow-x-auto">
                <table className="min-w-full bg-stone-800 rounded-lg overflow-hidden">
                  <thead className="bg-stone-900">
                    <tr>
                      {mainColumns.map((col, index) => (
                        <th
                          key={col.key}
                          className={`px-4 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider ${
                            col.key === "rank"
                              ? "w-16"
                              : col.key === "mmr"
                                ? "w-24"
                                : ""
                            // col.key === "prizeShare" ? "w-32" : "" // Removed for Preseason 0
                          } ${index > 0 ? "border-l border-stone-700/30" : ""}`}
                        >
                          {col.label}
                        </th>
                      ))}
                      <th className="px-4 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider w-32 border-l border-stone-700/30">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {leaderboard.length > 0 ? (
                      leaderboard.map(
                        (item: ChickenLeaderboardItem, index: number) => (
                          <React.Fragment key={`chicken-${item.tokenId}`}>
                            <tr
                              className={`${
                                item.rank <= 3
                                  ? `${getRankStyling(item.rank).border} ${getRankStyling(item.rank).glow}`
                                  : isUserEntry(item.owner)
                                    ? "border-2 border-white shadow-inner shadow-white/20"
                                    : index > 0
                                      ? "border-t border-stone-700"
                                      : ""
                              } ${
                                index % 2 === 0
                                  ? "bg-stone-800"
                                  : "bg-stone-850"
                              } hover:bg-stone-700 transition-colors duration-150 cursor-pointer`}
                              onClick={() => toggleRowExpansion(item.tokenId)}
                            >
                              {mainColumns.map((col) => {
                                const value =
                                  item[col.key as keyof ChickenLeaderboardItem];
                                return (
                                  <td
                                    key={col.key}
                                    className={`px-4 py-3 whitespace-nowrap text-sm ${col.className || "text-gray-200"} ${
                                      col.key === "rank"
                                        ? "w-16"
                                        : col.key === "mmr"
                                          ? "w-24"
                                          : ""
                                      // col.key === "prizeShare" ? "w-32" : "" // Removed for Preseason 0
                                    }`}
                                  >
                                    {col.format
                                      ? col.format(value, item)
                                      : value}
                                  </td>
                                );
                              })}
                              <td className="px-4 py-3 whitespace-nowrap text-center w-32">
                                <div className="flex items-center justify-center gap-3">
                                  <a
                                    href={getInventoryUrl(item.tokenId)}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-400 hover:text-blue-300 transition-colors p-1"
                                    onClick={(e) => e.stopPropagation()}
                                    title="View Details"
                                  >
                                    <Info size={18} />
                                  </a>
                                  <a
                                    href={getMarketplaceUrl(item.tokenId)}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-yellow-400 hover:text-yellow-300 transition-colors p-1"
                                    onClick={(e) => e.stopPropagation()}
                                    title="View in Marketplace"
                                  >
                                    <ShoppingCart size={18} />
                                  </a>
                                  <button
                                    className="text-primary hover:text-yellow-300 transition-colors p-1"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleRowExpansion(item.tokenId);
                                    }}
                                    title={
                                      expandedRows.has(item.tokenId)
                                        ? "Collapse details"
                                        : "Expand details"
                                    }
                                  >
                                    {expandedRows.has(item.tokenId) ? (
                                      <ChevronUp size={18} />
                                    ) : (
                                      <ChevronDown size={18} />
                                    )}
                                  </button>
                                </div>
                              </td>
                            </tr>
                            {expandedRows.has(item.tokenId) && (
                              <tr className="bg-stone-750">
                                <td
                                  colSpan={mainColumns.length + 1}
                                  className="px-4 py-4"
                                >
                                  <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                                    {expandableColumns.map((col) => {
                                      const value =
                                        item[
                                          col.key as keyof ChickenLeaderboardItem
                                        ];
                                      return (
                                        <div
                                          key={col.key}
                                          className="bg-stone-700 rounded-lg p-3"
                                        >
                                          <div className="text-xs font-medium text-gray-400 uppercase tracking-wider mb-1">
                                            {col.label}
                                          </div>
                                          <div
                                            className={`font-bold ${col.className || "text-gray-200"}`}
                                          >
                                            {col.format
                                              ? col.format(value, item)
                                              : value}
                                          </div>
                                        </div>
                                      );
                                    })}
                                  </div>
                                </td>
                              </tr>
                            )}
                          </React.Fragment>
                        )
                      )
                    ) : (
                      <tr>
                        <td
                          colSpan={mainColumns.length + 1}
                          className="px-4 py-8 text-center text-gray-400"
                        >
                          {isSearching
                            ? `No chicken found with ID: ${searchId} that has played at least 20 games`
                            : `No chickens found that have played at least 20 games`}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Mobile Card View */}
            <div className="md:hidden space-y-4">
              {leaderboard.length > 0 ? (
                leaderboard.map(
                  (item: ChickenLeaderboardItem, index: number) => {
                    const rankStyle = getRankStyling(item.rank);
                    return (
                      <div
                        key={`chicken-mobile-${item.tokenId}`}
                        className={`bg-stone-800 rounded-lg overflow-hidden ${
                          item.rank <= 3
                            ? `${rankStyle.border} ${rankStyle.glow}`
                            : isUserEntry(item.owner)
                              ? "border-2 border-white shadow-inner shadow-white/20"
                              : ""
                        }`}
                      >
                        <div
                          className="p-4 cursor-pointer hover:bg-stone-700 transition-colors duration-150"
                          onClick={() => toggleRowExpansion(item.tokenId)}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1 mr-4">
                              <div className="flex items-center gap-1 mb-2">
                                {rankStyle.icon && (
                                  <span>{rankStyle.icon}</span>
                                )}
                                <span
                                  className={`text-sm font-medium ${item.rank <= 3 ? rankStyle.text : "text-gray-200"}`}
                                >
                                  #{item.rank}
                                </span>
                              </div>
                              <div className="flex items-center gap-2 mb-1">
                                <h3 className="text-lg font-semibold text-white">
                                  {item.name}
                                </h3>
                                <span
                                  className={`px-2 py-1 text-xs rounded-full ${
                                    item.type.toLowerCase() === "genesis"
                                      ? "bg-orange-600"
                                      : item.type.toLowerCase() === "legacy"
                                        ? "bg-green-600"
                                        : item.type.toLowerCase() === "ordinary"
                                          ? "bg-blue-600"
                                          : "bg-stone-700"
                                  } text-white`}
                                >
                                  {item.type.charAt(0).toUpperCase() +
                                    item.type.slice(1).toLowerCase()}
                                </span>
                              </div>
                              <p className="text-sm text-cyan-300">
                                {formatAddress(item.owner)}
                              </p>
                            </div>
                            <div className="flex flex-col items-end gap-3">
                              <span className="text-white font-bold text-lg">
                                {item.mmr} MMR
                              </span>
                              <div className="flex items-center gap-3">
                                <a
                                  href={getInventoryUrl(item.tokenId)}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-400 hover:text-blue-300 transition-colors p-1"
                                  title="View Details"
                                >
                                  <Info size={18} />
                                </a>
                                <a
                                  href={getMarketplaceUrl(item.tokenId)}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-yellow-400 hover:text-yellow-300 transition-colors p-1"
                                  title="View in Marketplace"
                                >
                                  <ShoppingCart size={18} />
                                </a>
                                <button className="text-primary hover:text-yellow-300 transition-colors p-1">
                                  {expandedRows.has(item.tokenId) ? (
                                    <ChevronUp size={18} />
                                  ) : (
                                    <ChevronDown size={18} />
                                  )}
                                </button>
                              </div>
                            </div>
                          </div>

                          {/* Prize Share Row - Temporarily disabled for Preseason 0 */}
                          {/* <div className="flex justify-end mt-2">
                            {isEligibleForPrize(item.rank) ? (
                              <div className="text-right">
                                <div className="text-xs text-gray-400 mb-1">
                                  Prize in $COCK
                                </div>
                                <span className="text-yellow-400 font-bold">
                                  {formatPrizeAmount(
                                    calculatePrizeAmount(
                                      item.rank,
                                      prizePoolData.basePrize,
                                      prizePoolData.growingPot
                                    ).totalPrize
                                  )}
                                </span>
                              </div>
                            ) : (
                              <span className="text-gray-500 text-sm">
                                No prize
                              </span>
                            )}
                          </div> */}
                        </div>
                        {expandedRows.has(item.tokenId) && (
                          <div className="px-4 pb-4 border-t border-stone-700">
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-4">
                              {expandableColumns.map((col) => {
                                const value =
                                  item[col.key as keyof ChickenLeaderboardItem];
                                return (
                                  <div
                                    key={col.key}
                                    className="bg-stone-700 rounded-lg p-3"
                                  >
                                    <div className="text-xs font-medium text-gray-400 uppercase tracking-wider mb-1">
                                      {col.label}
                                    </div>
                                    <div
                                      className={`font-bold ${col.className || "text-gray-200"}`}
                                    >
                                      {col.format
                                        ? col.format(value, item)
                                        : value}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  }
                )
              ) : (
                <div className="bg-stone-800 rounded-lg p-8 text-center text-gray-400">
                  {isSearching
                    ? `No chicken found with ID: ${searchId} that has played at least 20 games`
                    : `No chickens found that have played at least 20 games`}
                </div>
              )}
            </div>
          </div>

          {/* Pagination - only show if not searching for a specific ID */}
          {!isSearching && totalPages > 1 && (
            <div className="flex justify-center mt-6 space-x-2">
              <button
                onClick={() => handlePageChange(Math.max(1, page - 1))}
                disabled={page === 1}
                className="px-3 py-1 rounded-md bg-stone-800 text-white hover:bg-stone-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                &laquo;
              </button>
              {paginationItems}
              <button
                onClick={() => handlePageChange(Math.min(totalPages, page + 1))}
                disabled={page === totalPages}
                className="px-3 py-1 rounded-md bg-stone-800 text-white hover:bg-stone-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                &raquo;
              </button>
            </div>
          )}

          {/* Results summary */}
          <div className="mt-4 text-center text-sm text-gray-400">
            Showing {leaderboard.length} of {totalItems} entries
          </div>
        </>
      )}
    </div>
  );
}
