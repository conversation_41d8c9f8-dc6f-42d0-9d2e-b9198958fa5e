import { IChickenMetadata } from "@/lib/types/chicken.types";

// Constants for filter defaults
export const RENTAL_FILTER_DEFAULTS = {
  PRICE_RANGE: {
    MIN: 0,
    MAX: Number.MAX_SAFE_INTEGER, // No upper limit
  },
  DURATION_RANGE: {
    MIN: 1,
    MAX: Number.MAX_SAFE_INTEGER, // No upper limit
  },
} as const;

// Marketplace fee configuration
// Note: FEE_PERCENTAGE is now fetched from the rental contract
// Use useMarketplaceFee hook to get the current fee percentage
export const MARKETPLACE_CONFIG = {
  // Fallback fee percentage if contract read fails
  FALLBACK_FEE_PERCENTAGE: 2.5,
} as const;

// Enums based on API documentation
export enum ERentalStatus {
  AVAILABLE = 0,
  RENTED = 1,
  EXPIRED = 2,
  CANCELLED = 3,
}

export enum ERewardDistributionType {
  DELEGATOR_ONLY = 1, // All rewards go to delegator
  DELEGATEE_ONLY = 2, // All rewards go to delegatee
  SHARED = 3, // Rewards shared based on sharedRewardAmount
}

export enum EGameRewardDistributionType {
  DELEGATOR_ONLY = 1, // Game rewards go to delegator (chicken owner)
  DELEGATEE_ONLY = 2, // Game rewards go to delegatee (renter)
}

export enum EDelegatedTaskType {
  DAILY_RUB = 1, // Only daily rub delegation
  GAMEPLAY = 2, // Only gameplay delegation
  BOTH = 3, // Both daily rub and gameplay
}

// Core interfaces based on API documentation
export interface IRental {
  id: number;
  chickenTokenId: number;
  ownerAddress: string;
  renterAddress: string | null;
  roninPrice: string; // bigint as string in wei
  insurancePrice?: string; // bigint as string in wei
  rentalPeriod: number; // Duration in seconds
  rentedAt: string | null; // ISO date string
  expiresAt: string | null; // ISO date string
  status: ERentalStatus;
  signature: string | null;
  rewardDistribution: ERewardDistributionType;
  gameRewardDistribution: EGameRewardDistributionType;
  delegatedTask: EDelegatedTaskType;
  sharedRewardAmount: number | null; // Daily feathers amount for delegatee
  insuranceClaimed?: boolean; // Whether insurance has been claimed
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

// Extended rental with chicken metadata for UI display
export interface IRentalWithMetadata extends IRental {
  chickenMetadata?: IChickenMetadata;
  dailyFeathers?: number;
  legendaryCount?: number;
}

// API Request/Response types
export interface ICreateRentalRequest {
  chickenTokenId: number;
  roninPrice: string; // wei format
  rentalPeriod: number; // seconds
  rewardDistribution?: ERewardDistributionType;
  gameRewardDistribution?: EGameRewardDistributionType;
  delegatedTask?: EDelegatedTaskType;
  renterAddress?: string; // Required when roninPrice is "0"
  sharedRewardAmount?: number; // Required when rewardDistribution is SHARED - daily feathers for delegatee
  insurancePrice?: string; // wei format
}

export interface ICreateRentalResponse {
  status: number;
  message: string;
  data?: IRental;
}

export interface IRentChickenRequest {
  rentalId: number;
}

export interface IRentChickenResponse {
  status: number;
  data: {
    rentalId: number;
    chickenTokenId: number;
    roninPrice: string;
    insurancePrice: string;
    renterAddress: string;
    renterWallet: string;
    ownerAddress: string;
    signature: string;
  };
}

export interface IListChickenResponse {
  status: number;
  message: string;
  data: {
    chickenTokenId: number;
    rentalId: number;
    roninPrice: string;
    insurancePrice: string;
    rentalPeriod: number;
    ownerAddress: string;
    signature: string;
  };
}

export interface IAvailableRentalsResponse {
  status: number;
  data: {
    meta: {
      total: number;
      perPage: number;
      currentPage: number;
      lastPage: number;
      firstPage: number;
      firstPageUrl: string;
      lastPageUrl: string;
      nextPageUrl: string | null;
      previousPageUrl: string | null;
    };
    data: IRental[];
  };
}

export interface IMyRentalsResponse {
  status: number;
  data: {
    ownedRentals: IRental[];
    rentedChickens: IRental[];
    expiredRentalsWithInsurance: IRental[];
  };
}

export interface IRentalHistoryResponse {
  status: number;
  data: {
    meta: {
      total: number;
      perPage: number;
      currentPage: number;
      lastPage: number;
      firstPage: number;
      firstPageUrl: string;
      lastPageUrl: string;
      nextPageUrl: string | null;
      previousPageUrl: string | null;
    };
    data: IRentalHistoryEvent[];
  };
}

// UI-specific types
export interface IRentalFilters {
  priceRange: {
    min: number;
    max: number;
  };
  durationRange: {
    min: number; // days
    max: number; // days
  };

  featherRewardDistribution: ERewardDistributionType[];
  gameRewardDistribution: EGameRewardDistributionType[];
  delegatedTask: EDelegatedTaskType[];
  sortBy: "price" | "duration" | "created" | "feathers" | "tokenId";
  sortOrder: "asc" | "desc";
}

// API filter parameters for server-side filtering
export interface IRentalApiFilters {
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  minDuration?: number;
  maxDuration?: number;
  featherRewardDistribution?: string; // comma-separated values
  gameRewardDistribution?: string; // comma-separated values
  delegatedTask?: string; // comma-separated values
  sortBy?: "created_at" | "ronin_price" | "rental_period" | "chicken_token_id";
  sortOrder?: "asc" | "desc";
}

export interface ICreateRentalFormData {
  chickenTokenId: number | null;
  isDirectDelegation: boolean;
  renterAddress: string;
  roninPrice: string;
  rentalPeriod: number;
  rewardDistribution: ERewardDistributionType;
  gameRewardDistribution: EGameRewardDistributionType;
  delegatedTask: EDelegatedTaskType;
  sharedRewardAmount: number;
  insurancePrice?: string;
}

// Bulk rental form data for multiple chickens
export interface IBulkCreateRentalFormData {
  selectedChickenIds: number[];
  isBulkMode: boolean;
  isDirectDelegation: boolean;
  renterAddress: string;
  roninPrice: string;
  rentalPeriod: number;
  rewardDistribution: ERewardDistributionType;
  gameRewardDistribution: EGameRewardDistributionType;
  delegatedTask: EDelegatedTaskType;
  sharedRewardAmount: number;
  insurancePrice?: string;
  // Individual overrides for specific chickens
  individualOverrides?: Record<number, Partial<ICreateRentalFormData>>;
}

// Individual chicken rental configuration
export interface IChickenRentalConfig {
  chickenTokenId: number;
  roninPrice: string;
  rentalPeriod: number;
  rewardDistribution: ERewardDistributionType;
  delegatedTask: EDelegatedTaskType;
  sharedRewardAmount: number;
  renterAddress?: string;
  insurancePrice?: string;
}

// Bulk rental creation request
export interface IBulkCreateRentalRequest {
  rentals: IChickenRentalConfig[];
}

// Bulk rental creation response
export interface IBulkCreateRentalResponse {
  status: number;
  message: string;
  data: Array<{
    chickenTokenId: number;
    success: boolean;
    message?: string;
    data?: {
      chickenTokenId: number;
      rentalId: number;
      roninPrice: string;
      insurancePrice: string;
      rentalPeriod: number;
      ownerAddress: string;
      signature: string;
    };
  }>;
}

// Chicken selection state for bulk operations
export interface IChickenSelection {
  [chickenTokenId: number]: boolean;
}

// Selected chicken with metadata for display
export interface ISelectedChickenInfo {
  tokenId: number;
  image: string;
  metadata?: IChickenMetadata;
  isAvailable: boolean;
  type: string;
  config?: Partial<IChickenRentalConfig>;
}

// Chicken delegation info for wallet query
export interface IChickenDelegationInfo {
  delegatedTask: EDelegatedTaskType;
  rewardDistribution: ERewardDistributionType;
  sharedRewardAmount: number | null;
  renterAddress: string;
  ownerAddress: string;
  tokenId: number;
  image: string;
  dailyFeathers: number;
  legendaryCount: number;
}

// Status display helpers
export const RENTAL_STATUS_LABELS: Record<ERentalStatus, string> = {
  [ERentalStatus.AVAILABLE]: "Available",
  [ERentalStatus.RENTED]: "Rented",
  [ERentalStatus.EXPIRED]: "Expired",
  [ERentalStatus.CANCELLED]: "Cancelled",
};

export const REWARD_DISTRIBUTION_LABELS: Record<
  ERewardDistributionType,
  string
> = {
  [ERewardDistributionType.DELEGATOR_ONLY]: "Delegator",
  [ERewardDistributionType.DELEGATEE_ONLY]: "Delegatee",
  [ERewardDistributionType.SHARED]: "Shared",
};

export const GAME_REWARD_DISTRIBUTION_LABELS: Record<
  EGameRewardDistributionType,
  string
> = {
  [EGameRewardDistributionType.DELEGATOR_ONLY]: "Owner",
  [EGameRewardDistributionType.DELEGATEE_ONLY]: "Renter",
};

export const DELEGATED_TASK_LABELS: Record<EDelegatedTaskType, string> = {
  [EDelegatedTaskType.DAILY_RUB]: "Daily Rub",
  [EDelegatedTaskType.GAMEPLAY]: "Gameplay",
  [EDelegatedTaskType.BOTH]: "Full Access",
};

// Utility functions
export const formatRentalDuration = (seconds: number): string => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);

  if (days > 0) {
    return `${days} day${days > 1 ? "s" : ""}`;
  }
  return `${hours} hour${hours > 1 ? "s" : ""}`;
};

export const formatRoninPrice = (weiPrice: string): string => {
  const price = parseFloat(weiPrice) / 1e18;
  return price.toFixed(4);
};

export const isRentalExpired = (rental: IRental): boolean => {
  if (!rental.expiresAt) return false;
  return new Date(rental.expiresAt) < new Date();
};

// Marketplace fee calculation utilities
export const calculateMarketplaceFee = (
  totalAmount: number,
  feePercentage: number
): number => {
  return (totalAmount * feePercentage) / 100;
};

export const calculateEarningsAfterFees = (
  totalAmount: number,
  feePercentage: number
): number => {
  const fee = calculateMarketplaceFee(totalAmount, feePercentage);
  return totalAmount - fee;
};

export const formatMarketplaceFeePercentage = (
  feePercentage: number
): string => {
  return `${feePercentage}%`;
};

export const getRentalTimeRemaining = (rental: IRental): string | null => {
  if (!rental.expiresAt) return null;

  const now = new Date();
  const expires = new Date(rental.expiresAt);
  const diff = expires.getTime() - now.getTime();

  if (diff <= 0) return "Expired";

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) return `${days}d ${hours}h`;
  if (hours > 0) return `${hours}h ${minutes}m`;
  return `${minutes}m`;
};

export const getRentalRemainingSeconds = (rental: IRental): number => {
  if (!rental.expiresAt) return 0;

  const now = new Date();
  const expires = new Date(rental.expiresAt);
  const diff = expires.getTime() - now.getTime();

  return diff > 0 ? Math.floor(diff / 1000) : 0;
};

// Enhanced History Event Types
export enum ERentalHistoryEventType {
  LISTED = "listed",
  UNLISTED = "unlisted",
  RENTED = "rented",
  CANCELLED = "cancelled",
  INSURANCE_CLAIMED = "insurance_claimed",
  TERMS_UPDATED = "terms_updated",
}

export interface IRentalHistoryEventData {
  // For LISTED events
  originalPrice?: string;
  originalTerms?: {
    rewardDistribution?: number;
    delegatedTask?: number;
    sharedRewardAmount?: number;
  };

  // For PRICE_UPDATED events
  oldPrice?: string;
  newPrice?: string;

  // For RENTED events
  renterAddress?: string;
  rentalDuration?: number;

  // For EXPIRED events
  expiredAt?: string;

  // For INSURANCE_CLAIMED events
  insuranceAmount?: string;
  claimReason?: string;

  // For blockchain events
  blockNumber?: string;
  transactionHash?: string;

  // Additional context
  notes?: string;
}

// API response interface with snake_case fields (as returned by the backend)
export interface IRentalApiResponse {
  id: number;
  chicken_token_id: number;
  owner_address: string;
  renter_address: string | null;
  ronin_price: string;
  insurance_price?: string;
  rental_period: number;
  rented_at: string | null;
  expires_at: string | null;
  status: number;
  signature: string | null;
  reward_distribution: number;
  game_reward_distribution: number;
  delegated_task: number;
  shared_reward_amount: number | null;
  insurance_claimed?: boolean;
  created_at: string;
  updated_at: string;
  rub_streak_benefactor?: number;
  legendary_feather_benefactor?: number;
}

export interface IRentalHistoryEvent {
  id: number;
  rental_id: number;
  event_type: ERentalHistoryEventType;
  actor_address: string;
  event_data: IRentalHistoryEventData | null;
  block_number: string | null;
  transaction_hash: string | null;
  description: string | null;
  created_at: string;
  updated_at: string;
  rental?: IRentalApiResponse; // Nested rental data from preload (snake_case)
}

export interface IRentalHistoryFilters {
  eventTypes?: ERentalHistoryEventType[];
  chickenTokenId?: number;
  dateFrom?: string;
  dateTo?: string;
  actorAddress?: string;
  page?: number;
  pageSize?: number;
}
