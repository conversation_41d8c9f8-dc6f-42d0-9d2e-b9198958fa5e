"use client";

import { useQuery } from "@tanstack/react-query";
import { useStateContext } from "@/providers/app/state";
import {
  IGameRewardsResponse,
  IChickenWithRewards,
  IGameRewardItem,
} from "../types/inventory.types";
import { rewardMetadataMap } from "../mock/mock-data";

const GAME_REWARDS_API_URL =
  "https://chicken-api-ivory.vercel.app/api/game/rewards";

/**
 * Hook to fetch and process game rewards organized by chicken
 * Similar to ninuno rewards pattern but for game rewards
 */
export const usePerChickenRewards = () => {
  const { address } = useStateContext();

  const { data, isLoading, error, refetch } = useQuery<IChickenWithRewards[]>({
    queryKey: ["perChickenRewards", address],
    queryFn: async (): Promise<IChickenWithRewards[]> => {
      if (!address) {
        throw new Error("No wallet address available");
      }

      console.log("🐔 Fetching per-chicken rewards for address:", address);
      const apiUrl = `${GAME_REWARDS_API_URL}?address=${address}`;

      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error(`Failed to fetch rewards: ${response.statusText}`);
      }

      const apiData: IGameRewardsResponse = await response.json();
      console.log("📦 Raw API response for per-chicken:", apiData);

      if (!apiData.success) {
        throw new Error("API returned unsuccessful response");
      }

      // Transform rewards by chicken
      const chickensWithRewards: IChickenWithRewards[] = [];

      Object.entries(apiData.data.rewardsByChicken).forEach(
        ([chickenId, rewards]) => {
          // Filter unclaimed rewards for this chicken
          const unclaimedRewards = rewards.filter((reward) => !reward.claimed);

          if (unclaimedRewards.length === 0) {
            return; // Skip chickens with no unclaimed rewards
          }

          // Calculate total rewards and group by type
          const rewardsByType: { [type: string]: number } = {};
          let totalRewards = 0;

          unclaimedRewards.forEach((reward) => {
            const metadata = rewardMetadataMap.get(reward.rewardId);
            if (metadata) {
              const quantity = reward.quantity || 1;
              const type = metadata.type;

              rewardsByType[type] = (rewardsByType[type] || 0) + quantity;
              totalRewards += quantity;
            }
          });

          // Create chicken with rewards object
          const chickenWithRewards: IChickenWithRewards = {
            chickenId,
            tokenId: parseInt(chickenId),
            name: `Chicken #${chickenId}`,
            image: `https://chicken-api-ivory.vercel.app/api/image/${chickenId}.png`,
            totalRewards,
            rewardsByType,
            rewards: unclaimedRewards,
            hasUnclaimedRewards: unclaimedRewards.length > 0,
            lastUpdated: new Date().toISOString(),
          };

          chickensWithRewards.push(chickenWithRewards);
        }
      );

      // Sort by total rewards (descending)
      chickensWithRewards.sort((a, b) => b.totalRewards - a.totalRewards);

      console.log("🐔 Processed chickens with rewards:", chickensWithRewards);
      return chickensWithRewards;
    },
    enabled: !!address,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  return {
    chickensWithRewards: data || [],
    isLoading,
    error,
    refetch,
    hasChickensWithRewards: (data?.length || 0) > 0,
    totalChickens: data?.length || 0,
    address, // For debugging
  };
};
