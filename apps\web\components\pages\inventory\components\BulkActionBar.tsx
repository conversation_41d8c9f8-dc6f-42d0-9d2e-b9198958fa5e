"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { X, CheckSquare, Square } from "lucide-react";
import { IBulkActionSummary } from "@/features/delegation/hooks/useBulkActionSelection";

interface BulkActionBarProps {
  isVisible: boolean;
  summary: IBulkActionSummary;
  isProcessing?: boolean;
  onCancelDelegation: () => void;
  onUnlistFromMarket: () => void;
  onSelectAllCancellable: () => void;
  onSelectAllUnlistable: () => void;
  onClearSelection: () => void;
  onClose: () => void;
}

export function BulkActionBar({
  isVisible,
  summary,
  isProcessing = false,
  onCancelDelegation,
  onUnlistFromMarket,
  onSelectAllCancellable,
  onSelectAllUnlistable,
  onClearSelection,
  onClose,
}: BulkActionBarProps) {
  if (!isVisible) return null;

  const hasSelection = summary.totalSelected > 0;
  const canCancelAny = summary.canCancelDelegation > 0;
  const canUnlistAny = summary.canUnlistFromMarket > 0;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-stone-900/95 backdrop-blur-sm border-t border-stone-700 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 py-4">
        <div className="flex items-center justify-between gap-4">
          {/* Selection Summary */}
          <div className="flex items-center gap-4">
            <div className="text-white font-medium">
              {summary.totalSelected > 0 ? (
                <span>
                  {summary.totalSelected} chicken{summary.totalSelected !== 1 ? "s" : ""} selected
                </span>
              ) : (
                <span className="text-stone-400">No chickens selected</span>
              )}
            </div>
            
            {summary.totalSelected > 0 && (
              <div className="flex items-center gap-2 text-sm text-stone-300">
                {summary.canCancelDelegation > 0 && (
                  <span className="bg-orange-600/20 text-orange-400 px-2 py-1 rounded">
                    {summary.canCancelDelegation} delegated
                  </span>
                )}
                {summary.canUnlistFromMarket > 0 && (
                  <span className="bg-red-600/20 text-red-400 px-2 py-1 rounded">
                    {summary.canUnlistFromMarket} listed
                  </span>
                )}
                {summary.ineligible > 0 && (
                  <span className="bg-stone-600/20 text-stone-400 px-2 py-1 rounded">
                    {summary.ineligible} ineligible
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            {/* Quick Selection Buttons */}
            {!hasSelection && (
              <>
                <Button
                  size="small"
                  appearance="outline"
                  className="text-orange-400 border-orange-600/50 hover:border-orange-500 hover:bg-orange-600/10"
                  onPress={onSelectAllCancellable}
                  isDisabled={isProcessing}
                >
                  <CheckSquare className="h-4 w-4 mr-1" />
                  Select Delegated
                </Button>
                <Button
                  size="small"
                  appearance="outline"
                  className="text-red-400 border-red-600/50 hover:border-red-500 hover:bg-red-600/10"
                  onPress={onSelectAllUnlistable}
                  isDisabled={isProcessing}
                >
                  <CheckSquare className="h-4 w-4 mr-1" />
                  Select Listed
                </Button>
              </>
            )}

            {/* Action Buttons */}
            {hasSelection && (
              <>
                {canCancelAny && (
                  <Button
                    size="small"
                    intent="warning"
                    className="bg-orange-600/20 hover:bg-orange-600/30 text-orange-400 border border-orange-600/50 hover:border-orange-500"
                    onPress={onCancelDelegation}
                    isDisabled={isProcessing}
                  >
                    Cancel Delegation ({summary.canCancelDelegation})
                  </Button>
                )}
                
                {canUnlistAny && (
                  <Button
                    size="small"
                    intent="danger"
                    className="bg-red-600/20 hover:bg-red-600/30 text-red-400 border border-red-600/50 hover:border-red-500"
                    onPress={onUnlistFromMarket}
                    isDisabled={isProcessing}
                  >
                    Unlist from Market ({summary.canUnlistFromMarket})
                  </Button>
                )}

                <Button
                  size="small"
                  appearance="outline"
                  className="text-stone-400 border-stone-600 hover:border-stone-500"
                  onPress={onClearSelection}
                  isDisabled={isProcessing}
                >
                  <Square className="h-4 w-4 mr-1" />
                  Clear
                </Button>
              </>
            )}

            {/* Close Button */}
            <Button
              size="small"
              appearance="outline"
              className="text-stone-400 border-stone-600 hover:border-stone-500"
              onPress={onClose}
              isDisabled={isProcessing}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Processing Indicator */}
        {isProcessing && (
          <div className="mt-3 pt-3 border-t border-stone-700">
            <div className="flex items-center gap-2 text-sm text-stone-300">
              <div className="animate-spin h-4 w-4 border-2 border-stone-600 border-t-orange-400 rounded-full"></div>
              <span>Processing bulk action...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
