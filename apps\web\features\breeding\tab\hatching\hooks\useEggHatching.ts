"use client";

import axios from "@/lib/api";
import { useStateContext } from "@/providers/app/state";
import { queryClient } from "@/providers/web3/web3-provider";
import { useState } from "react";
import { toast } from "sonner";
import { IEggInfo } from "../types/egg-info.types";
import { isEggReadyToHatch } from "../utils/egg.utils";

export const hatchEggApi = async (tokenId: number) => {
  const { data } = await axios.post(`/breedings/hatch`, {
    chickenTokenId: tokenId,
  });
  return data;
};

/**
 * Hook for hatching eggs
 * @param eggInfoMap The egg info map
 * @returns Hatching-related functions and state
 */
export function useEggHatching(eggInfoMap: Record<number, IEggInfo>) {
  const [isHatching, setIsHatching] = useState<number | null>(null);
  const { ConnectRecentWallet } = useStateContext();

  /**
   * Hatches an egg
   * @param tokenId The token ID of the egg to hatch
   * @param onSuccess Optional callback for when hatching succeeds
   */
  const hatchEgg = async (
    tokenId: number,
    onSuccess?: (
      eggId: number,
      parentIds: { parent1: number; parent2: number }
    ) => void
  ) => {
    // Check if the egg is ready to hatch
    const eggInfo = eggInfoMap[tokenId];
    if (!isEggReadyToHatch(eggInfo)) {
      toast.error("This egg is not ready to hatch yet");
      return;
    }

    try {
      await ConnectRecentWallet();

      setIsHatching(tokenId);

      // Call the hatch endpoint
      await hatchEggApi(tokenId);

      toast.success("Egg hatched successfully!");

      // Update the egg info to mark it as hatched
      const updatedEggInfoMap = { ...eggInfoMap };
      if (updatedEggInfoMap[tokenId]) {
        updatedEggInfoMap[tokenId] = {
          ...updatedEggInfoMap[tokenId],
          is_hatched: 1,
        };
      }

      // Get the parent IDs for the success dialog
      const eggInfo = eggInfoMap[tokenId];
      if (eggInfo && onSuccess) {
        onSuccess(tokenId, {
          parent1: eggInfo.chicken_left_token_id,
          parent2: eggInfo.chicken_right_token_id,
        });
      }

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({
        queryKey: [
          "chickens",
          "eggInfo",
          "chickenMetadata",
          "breedingPhaseEggs",
          "hatchingPhaseEggs",
        ],
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : typeof error === "object" && error !== null && "response" in error
            ? // @ts-expect-error - We know this is an axios error
              error.response?.data?.message
            : "Failed to hatch egg. Please try again.";

      toast.error(errorMessage);
    } finally {
      setIsHatching(null);
    }
  };

  return {
    hatchEgg,
    isHatching,
  };
}
