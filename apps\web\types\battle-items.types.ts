export interface BattleItem {
  id: number;
  name: string;
  description: string;
  image: string;
}

export type BattleItemSlot = number | -1; // -1 represents skipped slot

export interface BattleItemSelection {
  round1: BattleItemSlot;
  round2: BattleItemSlot;
  round3: BattleItemSlot;
}

export interface BattleItemSelectionState {
  selectedItems: BattleItemSlot[];
  setItem: (slot: number, itemId: BattleItemSlot) => void;
  clearItem: (slot: number) => void;
  resetSelection: () => void;
  getAvailableItems: () => BattleItem[];
  isItemSelected: (itemId: number) => boolean;
  getIncompleteSlots: () => number[];
}