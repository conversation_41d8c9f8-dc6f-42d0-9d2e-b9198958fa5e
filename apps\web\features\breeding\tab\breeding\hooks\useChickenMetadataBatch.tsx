"use client";

import { IChickenMetadata } from "@/lib/types/chicken.types";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useMemo } from "react";

export const CHICKEN_API = process.env.NEXT_PUBLIC_CHICKEN_API_URL || "";

const fetchChickenMetadataBatch = async (tokenIds: number[]) => {
  const { data } = await axios.post(`${CHICKEN_API}/batch`, {
    ids: tokenIds.map(String), // Convert numbers to strings for API
  });
  return data.results.map((item: any) => item.data) as IChickenMetadata[];
};

const useChickenMetadataBatch = (tokenIds: number[]) => {
  const fetchMetadatas = () => {
    if (!CHICKEN_API) {
      throw new Error("Chicken API URL not configured");
    }

    if (!tokenIds || tokenIds.length === 0) {
      return [];
    }

    return fetchChickenMetadataBatch(tokenIds);
  };

  const metadataQuery = useQuery({
    queryKey: ["chickenMetadataBatch", tokenIds],
    queryFn: fetchMetadatas,
    enabled: !!tokenIds && tokenIds.length > 0,
  });

  // create metadata mapping
  const metadataMap = useMemo(() => {
    if (!metadataQuery.data || !tokenIds) {
      return {};
    }
    return tokenIds.reduce(
      (map, tokenId, index) => {
        map[tokenId] = metadataQuery.data[index] as IChickenMetadata;
        return map;
      },
      {} as Record<number, IChickenMetadata>
    );
  }, [metadataQuery.data, tokenIds]);

  return {
    metadataQuery,
    metadataMap,
    isLoading: metadataQuery.isLoading || metadataQuery.isFetching,
    error: metadataQuery.error,
    isError: metadataQuery.isError,
  };
};

export { fetchChickenMetadataBatch };
export default useChickenMetadataBatch;