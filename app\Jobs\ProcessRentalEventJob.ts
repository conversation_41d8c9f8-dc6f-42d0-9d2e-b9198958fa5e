import Env from '@ioc:Adonis/Core/Env'
import { JobContract } from '@ioc:Rocketseat/Bull'
import Rental, { RentalStatus } from 'App/Models/Rental'
import RentalEvent, { RentalEventProcessedStatus } from 'App/Models/RentalEvent'
import RentalHistoryEvent from 'App/Models/RentalHistoryEvent'
import { DateTime } from 'luxon'

export default class ProcessRentalEventJob implements JobContract {
  public key = `ProcessRentalEventJob-${Env.get('NODE_ENV')}`

  public async handle(job) {
    const { data } = job
    const rentalEvent = data

    try {
      const findEvent = await RentalEvent.query()
        .where('transactionHash', rentalEvent.transactionHash)
        .where('logIndex', rentalEvent.logIndex)
        .first()

      if (findEvent && findEvent.processed === RentalEventProcessedStatus.PROCESSED) {
        console.log('SKIP (Event already processed):', rentalEvent.transactionHash)
        return {
          status: 0,
          message: 'Event already processed',
        }
      }

      if (findEvent && findEvent.processed === RentalEventProcessedStatus.PENDING) {
        console.log('Reprocessing rental event:', rentalEvent.transactionHash)

        // Process the event
        const { isSuccess } = await this.processData(findEvent)

        // After processing
        if (isSuccess) {
          findEvent.processed = RentalEventProcessedStatus.PROCESSED
          await findEvent.save()

          console.log('DONE:', findEvent.transactionHash)
          return {
            status: 1,
            message: `DONE: ${findEvent.transactionHash}`,
          }
        }
      } else {
        console.log('Processing rental event:', rentalEvent.transactionHash)
        const createEvent = await RentalEvent.create({
          address: rentalEvent.address,
          blockHash: rentalEvent.blockHash,
          blockNumber: rentalEvent.blockNumber,
          data: rentalEvent.data,
          logIndex: rentalEvent.logIndex,
          transactionHash: rentalEvent.transactionHash,
          transactionIndex: rentalEvent.transactionIndex,
          removed: rentalEvent.removed,
          args: rentalEvent.args,
          eventName: rentalEvent.eventName,
          processed: RentalEventProcessedStatus.PENDING,
        })

        // Process the event
        const { isSuccess } = await this.processData(createEvent)

        // After processing
        if (isSuccess) {
          createEvent.processed = RentalEventProcessedStatus.PROCESSED
          await createEvent.save()

          console.log('DONE:', createEvent.transactionHash)
          return {
            status: 1,
            message: `DONE: ${createEvent.transactionHash}`,
          }
        }
      }

      return {
        status: 0,
        message: 'Failed to process event',
      }
    } catch (error) {
      console.log('Error processing rental event:', error)
      return {
        status: 0,
        message: error.message,
      }
    }
  }

  private async processData(rentalEvent: RentalEvent) {
    switch (rentalEvent.eventName) {
      case 'ChickenRented':
        return this.processChickenRentedEvent(rentalEvent)
      case 'ChickenUnlistedForRent':
        return this.processChickenUnlistedForRentEvent(rentalEvent)
      case 'ChickenListedForRent':
        return this.processChickenListedForRentEvent(rentalEvent)
      case 'InsuranceClaimed':
        return this.processInsuranceClaimedEvent(rentalEvent)
      default:
        return {
          isSuccess: false,
        }
    }
  }

  private async processChickenListedForRentEvent(rentalEvent: RentalEvent) {
    try {
      // Find the rental by ID from the event
      const rental = await Rental.find(Number(rentalEvent.args.rentId))

      if (!rental) {
        throw new Error(`Rental with ID ${rentalEvent.args.rentId} not found`)
      }

      // Verify the rental status
      if (rental.status !== RentalStatus.AVAILABLE) {
        // Update rental status if it's not already marked as available
        rental.status = RentalStatus.AVAILABLE
        await rental.save()
      }

      // For direct delegation (price = 0), only create a delegated event, not a listed event
      if (rental.roninPrice === BigInt(0)) {
        rental.status = RentalStatus.RENTED
        await rental.save()

        // Create history event for direct delegation (price = 0)
        await RentalHistoryEvent.createRentedEvent(
          rental.id,
          rental.renterAddress || '',
          rental.rentalPeriod,
          rentalEvent.blockNumber.toString(),
          rentalEvent.transactionHash
        )
      } else {
        // For paid delegation, create a listing event
        await RentalHistoryEvent.createListedEvent(
          rental.id,
          rental.ownerAddress,
          rental.roninPrice.toString(),
          {
            rewardDistribution: rental.rewardDistribution,
            delegatedTask: rental.delegatedTask,
            sharedRewardAmount: rental.sharedRewardAmount,
          },
          rentalEvent.blockNumber.toString(),
          rentalEvent.transactionHash
        )
      }

      return {
        isSuccess: true,
      }
    } catch (error) {
      console.log('Error processing rental event data:', error)
      return {
        isSuccess: false,
      }
    }
  }

  private async processChickenUnlistedForRentEvent(rentalEvent: RentalEvent) {
    try {
      // Find the rental by ID from the event
      const rental = await Rental.find(Number(rentalEvent.args.rentId))

      if (!rental) {
        throw new Error(`Rental with ID ${rentalEvent.args.rentId} not found`)
      }

      // Determine if this is a cancellation (was rented) or unlisting (was available)
      const wasRented = rental.status === RentalStatus.RENTED

      // Update rental status to cancelled
      rental.status = RentalStatus.CANCELLED
      await rental.save()

      if (wasRented) {
        // If the rental was active (rented), this is a cancellation
        await RentalHistoryEvent.createCancelledEvent(
          rental.id,
          rental.ownerAddress, // Owner is the one who cancels
          rentalEvent.blockNumber.toString(),
          rentalEvent.transactionHash
        )
      } else {
        // If the rental was just available (not rented), this is an unlisting
        await RentalHistoryEvent.createUnlistedEvent(
          rental.id,
          rental.ownerAddress, // Owner is the one who unlists
          rentalEvent.blockNumber.toString(),
          rentalEvent.transactionHash
        )
      }

      return {
        isSuccess: true,
      }
    } catch (error) {
      console.log('Error processing rental event data:', error)
      return {
        isSuccess: false,
      }
    }
  }

  private async processChickenRentedEvent(rentalEvent: RentalEvent) {
    try {
      // Find the rental by ID from the event
      const rental = await Rental.find(Number(rentalEvent.args.rentId))

      if (!rental) {
        throw new Error(`Rental with ID ${rentalEvent.args.rentId} not found`)
      }

      // Verify the rental status
      if (rental.status !== RentalStatus.RENTED) {
        // Update rental status if it's not already marked as rented
        rental.status = RentalStatus.RENTED
        rental.renterAddress = rentalEvent.args.renter
        rental.rentedAt = DateTime.now()
        rental.expiresAt = DateTime.fromMillis(Number(rentalEvent.args.expiresAt) * 1000)
        await rental.save()
      }

      // Create history event for rental (only if not a direct delegation)
      if (rental.roninPrice > BigInt(0)) {
        await RentalHistoryEvent.createRentedEvent(
          rental.id,
          rentalEvent.args.renter || '',
          rental.rentalPeriod,
          rentalEvent.blockNumber.toString(),
          rentalEvent.transactionHash
        )
      }

      return {
        isSuccess: true,
      }
    } catch (error) {
      console.log('Error processing rental event data:', error)
      return {
        isSuccess: false,
      }
    }
  }

  private async processInsuranceClaimedEvent(rentalEvent: RentalEvent) {
    try {
      // Find the rental by ID from the event
      const rental = await Rental.find(Number(rentalEvent.args.rentId))

      if (!rental) {
        throw new Error(`Rental with ID ${rentalEvent.args.rentId} not found`)
      }

      // Check if insurance has already been marked as claimed
      if (!rental.insuranceClaimed) {
        // Update rental to mark insurance as claimed
        rental.insuranceClaimed = true
        await rental.save()

        console.log(`Insurance claimed for rental ${rental.id} by ${rentalEvent.args.claimer}`)
      }

      // Create history event for insurance claim
      await RentalHistoryEvent.createInsuranceClaimedEvent(
        rental.id,
        rentalEvent.args.claimer || '',
        rental.insurancePrice.toString(),
        rentalEvent.blockNumber.toString(),
        rentalEvent.transactionHash
      )

      return {
        isSuccess: true,
      }
    } catch (error) {
      console.log('Error processing insurance claimed event:', error)
      return {
        isSuccess: false,
      }
    }
  }
}
