"use client";

import { Modal } from "@/components/ui/modal";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, AlertTriangle, RefreshCw } from "lucide-react";

interface BulkActionResult {
  success: boolean;
  hash?: string;
  successfulCount: number;
  totalRequested: number;
  failedCount: number;
  error?: string;
}

interface BulkActionResultModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  actionType: "cancel-delegation" | "unlist-market";
  result: BulkActionResult | null;
  onRetry?: () => void;
  onClose: () => void;
}

export function BulkActionResultModal({
  isOpen,
  onOpenChange,
  actionType,
  result,
  onRetry,
  onClose,
}: BulkActionResultModalProps) {
  if (!result) return null;

  const actionConfig = {
    "cancel-delegation": {
      title: "Delegation Cancellation Results",
      successMessage: "delegations cancelled",
      failureMessage: "delegations failed",
      icon: "🔄",
    },
    "unlist-market": {
      title: "Market Unlisting Results",
      successMessage: "chickens unlisted",
      failureMessage: "chickens failed to unlist",
      icon: "🏪",
    },
  };

  const config = actionConfig[actionType];
  const hasFailures = result.failedCount > 0;
  const hasSuccesses = result.successfulCount > 0;
  const isPartialSuccess = hasSuccesses && hasFailures;
  const isCompleteSuccess = hasSuccesses && !hasFailures;
  const isCompleteFailure = !hasSuccesses && hasFailures;

  const getStatusIcon = () => {
    if (isCompleteSuccess)
      return <CheckCircle className="h-8 w-8 text-green-400" />;
    if (isCompleteFailure) return <XCircle className="h-8 w-8 text-red-400" />;
    if (isPartialSuccess)
      return <AlertTriangle className="h-8 w-8 text-yellow-400" />;
    return <CheckCircle className="h-8 w-8 text-green-400" />;
  };

  const getStatusTitle = () => {
    if (isCompleteSuccess) return "Success!";
    if (isCompleteFailure) return "Failed";
    if (isPartialSuccess) return "Partially Completed";
    return "Completed";
  };

  const getStatusColor = () => {
    if (isCompleteSuccess) return "text-green-400";
    if (isCompleteFailure) return "text-red-400";
    if (isPartialSuccess) return "text-yellow-400";
    return "text-green-400";
  };

  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
      <Modal.Content>
        <Modal.Header>
          <Modal.Title className="flex items-center gap-3">
            <span className="text-2xl">{config.icon}</span>
            {config.title}
          </Modal.Title>
        </Modal.Header>

        <Modal.Body className="space-y-6">
          {/* Status Summary */}
          <div className="text-center space-y-4">
            <div className="flex justify-center">{getStatusIcon()}</div>
            <div>
              <h3 className={`text-xl font-bold ${getStatusColor()}`}>
                {getStatusTitle()}
              </h3>
              <p className="text-stone-300 mt-1">
                {isCompleteSuccess &&
                  `All ${result.successfulCount} ${config.successMessage} successfully`}
                {isCompleteFailure &&
                  `All ${result.failedCount} ${config.failureMessage}`}
                {isPartialSuccess &&
                  `${result.successfulCount} ${config.successMessage}, ${result.failedCount} ${config.failureMessage}`}
              </p>
            </div>
          </div>

          {/* Detailed Results */}
          <div className="bg-stone-800/50 rounded-lg p-4 space-y-3">
            <h4 className="text-white font-medium">Summary</h4>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="bg-stone-700/50 rounded-lg p-3">
                <div className="text-2xl font-bold text-white">
                  {result.totalRequested}
                </div>
                <div className="text-stone-300 text-sm">Total</div>
              </div>
              <div className="bg-green-600/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-green-400">
                  {result.successfulCount}
                </div>
                <div className="text-green-300 text-sm">Successful</div>
              </div>
              <div className="bg-red-600/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-red-400">
                  {result.failedCount}
                </div>
                <div className="text-red-300 text-sm">Failed</div>
              </div>
            </div>
          </div>

          {/* Transaction Hash */}
          {result.success && result.hash && (
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <h4 className="text-blue-200 font-medium mb-2">
                Transaction Hash
              </h4>
              <div className="flex items-center gap-2">
                <code className="bg-stone-900/50 px-3 py-2 rounded text-blue-300 text-sm font-mono flex-1 break-all">
                  {result.hash}
                </code>
                <Button
                  size="small"
                  appearance="outline"
                  className="text-blue-400 border-blue-500/50 hover:border-blue-400"
                  onPress={() => {
                    navigator.clipboard.writeText(result.hash!);
                  }}
                >
                  Copy
                </Button>
              </div>
            </div>
          )}

          {/* Error Details */}
          {result.error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <h4 className="text-red-200 font-medium mb-2">Error Details</h4>
              <p className="text-red-300 text-sm">{result.error}</p>
            </div>
          )}

          {/* Next Steps */}
          {hasFailures && (
            <div className="bg-amber-500/10 border border-amber-500/20 rounded-lg p-4">
              <h4 className="text-amber-200 font-medium mb-2">Next Steps</h4>
              <p className="text-amber-300/80 text-sm mb-3">
                Some operations failed. This could be due to network issues, gas
                problems, or changes in chicken status. You can try again for
                the failed items.
              </p>
              {onRetry && (
                <Button
                  size="small"
                  className="bg-amber-600/20 hover:bg-amber-600/30 text-amber-400 border border-amber-600/50"
                  onPress={onRetry}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry Failed Operations
                </Button>
              )}
            </div>
          )}

          {/* Success Message */}
          {isCompleteSuccess && (
            <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 text-center">
              <p className="text-green-200">
                🎉 All operations completed successfully! Your chickens have
                been updated.
              </p>
            </div>
          )}
        </Modal.Body>

        <Modal.Footer>
          <Button
            className="w-full bg-stone-700 hover:bg-stone-600 text-white"
            onPress={onClose}
          >
            Close
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
}
