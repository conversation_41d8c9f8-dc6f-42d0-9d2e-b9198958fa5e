import { IChickenMetadata } from "@/lib/types/chicken.types";
import { IGenesResponse } from "@/features/breeding/tab/breeding/types/genes.types";

/**
 * Enum for chicken types in the Sabong Saga ecosystem
 */
export enum EChickenType {
  ORDINARY = "Ordinary",
  LEGACY = "Legacy",
  GENESIS = "Genesis",
}

/**
 * Interface for chicken battle statistics
 */
export interface IChickenBattleStats {
  wins: number;
  losses: number;
  draws?: number;
  level?: number;
  state?: "normal" | "faint" | "dead" | "breeding";
  recoverDate?: string;
  stats?: {
    attack?: number;
    defense?: number;
    speed?: number;
    currentHp?: number;
    hp?: number;
    maxHp?: number;
    cockrage?: number;
    ferocity?: number;
    evasion?: number;
    instinct?: string;
  };
}

/**
 * Interface for chicken rental/delegation status
 */
export interface IChickenRentalStatus {
  isAvailable: boolean;
  rentalStatus?:
    | "available"
    | "listed"
    | "rented"
    | "delegated"
    | "expired"
    | "cancelled";
  statusLabel?: string;
}

/**
 * Interface for chicken cooldown information
 */
export interface IChickenCooldownInfo {
  cooldownTime: number; // Unix timestamp
  isOnCooldown: boolean;
  remainingTime?: number; // Milliseconds remaining
}

/**
 * Comprehensive interface for chicken information
 */
export interface IChickenInfo {
  // Basic information
  tokenId: number;
  type: EChickenType;
  image: string;
  
  // Metadata information
  metadata?: IChickenMetadata;
  
  // Genes information
  genes?: IGenesResponse;
  
  // Battle and game information
  battleStats?: IChickenBattleStats;
  level?: number;
  winRate?: number;
  
  // Breeding information
  breedCount?: number;
  cooldownInfo?: IChickenCooldownInfo;
  
  // Economic information
  dailyFeathers?: number;
  
  // Rental/delegation information
  rentalStatus?: IChickenRentalStatus;
  isAvailable?: boolean;
  
  // Derived properties
  isOnCooldown?: boolean;
  isDead?: boolean;
  isFaint?: boolean;
  isBreeding?: boolean;
}

/**
 * Interface for chicken info fetcher response
 */
export interface IChickenInfoResponse {
  chickens: IChickenInfo[];
  chickensByType: {
    ordinary: IChickenInfo[];
    legacy: IChickenInfo[];
    genesis: IChickenInfo[];
  };
  totalCount: number;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Interface for chicken token IDs by type
 */
export interface IChickenTokenIdsByType {
  ordinary: number[];
  legacy: number[];
  genesis: number[];
  all: number[];
}

/**
 * Interface for bulk chicken data maps
 */
export interface IChickenDataMaps {
  metadataMap: Record<number, IChickenMetadata>;
  genesMap: Record<number, IGenesResponse>;
  battleStatsMap: Record<number, IChickenBattleStats>;
  breedCountMap: Record<number, number>;
  cooldownMap: Record<number, number>;
  rentalStatusMap: Record<number, IChickenRentalStatus>;
}

/**
 * Interface for chicken info fetcher options
 */
export interface IChickenInfoOptions {
  includeMetadata?: boolean;
  includeGenes?: boolean;
  includeBattleStats?: boolean;
  includeBreedCount?: boolean;
  includeCooldown?: boolean;
  includeRentalStatus?: boolean;
  enableAutoRefresh?: boolean;
  refreshInterval?: number;
}
