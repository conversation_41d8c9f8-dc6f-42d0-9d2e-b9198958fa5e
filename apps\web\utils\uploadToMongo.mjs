// utils/uploadToMongo.mjs
import { parse } from "csv-parse";
import * as fs from "fs/promises";
import path from "path";
import { fileURLToPath } from "url";
import { MongoClient } from "mongodb";
import dotenv from "dotenv";
import { dirname } from "path";

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function connectToMongo() {
  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  return client;
}

async function uploadToMongo() {
  let client;
  try {
    // Read the CSV file
    const csvPath = path.join(process.cwd(), "data", "allocations.csv");
    const fileContent = await fs.readFile(csvPath, "utf-8");

    // Parse CSV
    const records = await new Promise((resolve, reject) => {
      parse(
        fileContent,
        {
          columns: true,
          skip_empty_lines: true,
          // Disable automatic type casting
          cast: false,
        },
        (err, records) => {
          if (err) reject(err);
          else resolve(records);
        }
      );
    });

    // Connect to MongoDB
    client = await connectToMongo();
    const db = client.db(process.env.MONGODB_DB);
    const collection = db.collection("allocations");

    console.log(`Processing ${records.length} records...`);

    // Process each record
    const operations = records.map((record) => {
      // Debug log
      console.log("Processing raw record:", record);

      // Safely parse JSON fields
      let breakdown;
      try {
        breakdown =
          typeof record.Breakdown === "string"
            ? JSON.parse(record.Breakdown)
            : record.Breakdown;
      } catch (e) {
        console.warn(
          `Failed to parse breakdown for address ${record.Address}, using empty array`
        );
        breakdown = [];
      }

      // Ensure address is a string
      const address = String(record.Address).toLowerCase();

      const parsedRecord = {
        address,
        totalChickens: parseInt(record["Total Chickens"]) || 0,
        totalAllocations: parseInt(record["Total Allocations"]) || 0,
        breakdown,
        timestamp: new Date(record.Timestamp),
        lastUpdated: new Date(record["Last Updated"]),
      };

      // Log the processed record
      console.log("Processed record:", {
        address: parsedRecord.address,
        totalChickens: parsedRecord.totalChickens,
        totalAllocations: parsedRecord.totalAllocations,
      });

      return {
        updateOne: {
          filter: { address: parsedRecord.address },
          update: { $set: parsedRecord },
          upsert: true,
        },
      };
    });

    // Execute bulk operations in batches
    const BATCH_SIZE = 100;
    for (let i = 0; i < operations.length; i += BATCH_SIZE) {
      const batch = operations.slice(i, i + BATCH_SIZE);
      console.log(
        `Processing batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(operations.length / BATCH_SIZE)}`
      );
      await collection.bulkWrite(batch);
    }

    console.log("Upload completed successfully!");

    // Create indexes
    console.log("Creating indexes...");
    await collection.createIndex({ address: 1 }, { unique: true });
    await collection.createIndex({ totalAllocations: 1 });
    await collection.createIndex({ lastUpdated: 1 });

    console.log("Indexes created successfully!");
  } catch (error) {
    console.error("Error uploading to MongoDB:", error);
    if (error.records) {
      console.error("Problem record:", error.records[0]);
    }
    throw error;
  } finally {
    if (client) {
      await client.close();
    }
  }
}

// Run the upload
console.time("Upload Time");
uploadToMongo()
  .then(() => {
    console.timeEnd("Upload Time");
    console.log("Process completed successfully");
  })
  .catch((error) => {
    console.error("Error:", error);
    process.exit(1);
  });
