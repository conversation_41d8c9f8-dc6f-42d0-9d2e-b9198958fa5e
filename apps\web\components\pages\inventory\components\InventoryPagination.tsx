import { ChevronLeft, ChevronRight } from "lucide-react";

interface InventoryPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function InventoryPagination({
  currentPage,
  totalPages,
  onPageChange,
}: InventoryPaginationProps) {
  if (totalPages <= 1) return null;

  return (
    <div className="flex justify-center items-center gap-2 mt-8">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`p-2 rounded-lg ${
          currentPage === 1
            ? "bg-stone-700 text-stone-500 cursor-not-allowed"
            : "bg-stone-700 text-white hover:bg-stone-600"
        }`}
        aria-label="Previous page"
      >
        <ChevronLeft className="h-5 w-5" />
      </button>

      <div className="flex items-center gap-1">
        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
          // Show pages around current page
          let pageToShow: number;
          if (totalPages <= 5) {
            pageToShow = i + 1;
          } else if (currentPage <= 3) {
            pageToShow = i + 1;
          } else if (currentPage >= totalPages - 2) {
            pageToShow = totalPages - 4 + i;
          } else {
            pageToShow = currentPage - 2 + i;
          }

          return (
            <button
              key={pageToShow}
              onClick={() => onPageChange(pageToShow)}
              className={`h-8 w-8 flex items-center justify-center rounded-lg ${
                currentPage === pageToShow
                  ? "bg-blue-600 text-white"
                  : "bg-stone-700 text-white hover:bg-stone-600"
              }`}
            >
              {pageToShow}
            </button>
          );
        })}

        {totalPages > 5 && currentPage < totalPages - 2 && (
          <>
            <span className="text-white">...</span>
            <button
              onClick={() => onPageChange(totalPages)}
              className="h-8 w-8 flex items-center justify-center rounded-lg bg-stone-700 text-white hover:bg-stone-600"
            >
              {totalPages}
            </button>
          </>
        )}
      </div>

      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`p-2 rounded-lg ${
          currentPage === totalPages
            ? "bg-stone-700 text-stone-500 cursor-not-allowed"
            : "bg-stone-700 text-white hover:bg-stone-600"
        }`}
        aria-label="Next page"
      >
        <ChevronRight className="h-5 w-5" />
      </button>
    </div>
  );
}