"use client";

import { useMemo } from "react";
import useChickenGenes from "./useChickenGenes";

/**
 * Hook to load genes only for selected chickens in breeding
 * This optimizes performance by only loading genes when chickens are actually selected
 */
export function useSelectedChickenGenes(selectedChickenIds: number[]) {
  // Only load genes for selected chickens
  const genesQuery = useChickenGenes(selectedChickenIds);

  // Create a map for easy lookup
  const selectedGenesMap = useMemo(() => {
    return genesQuery.genesMap || {};
  }, [genesQuery.genesMap]);

  return {
    selectedGenesMap,
    isLoading: genesQuery.isLoading,
    error: genesQuery.error,
    // Helper function to get genes for a specific chicken
    getGenesForChicken: (tokenId: number) => selectedGenesMap[tokenId],
  };
}
