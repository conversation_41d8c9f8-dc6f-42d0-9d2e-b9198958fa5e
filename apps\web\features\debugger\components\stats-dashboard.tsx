"use client";

import React from "react";

interface IStatsData {
  // Basic counts
  totalChickens: number;
  totalEggs: number;
  totalNullAttributes: number;
  typeDistribution: Record<string, number>;

  // Average stats
  averageStats: {
    innateAttack: number;
    innateDefense: number;
    innateHealth: number;
    innateSpeed: number;
    gritAttack: number;
    gritDefense: number;
    gritHealth: number;
    gritSpeed: number;
  };

  // Optional additional stats
  totalDailyFeathers?: number;
  chickensInCooldown?: number;
  readyToHatchEggs?: number;
  incubatingEggs?: number;
  breedingPhaseEggs?: number;
  hatchingPhaseEggs?: number;
}

interface IStatsDashboardProps {
  stats: IStatsData;
}

export default function StatsDashboard({
  stats,
}: IStatsDashboardProps): React.ReactNode {
  return (
    <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-100 mb-6">
      <h2 className="text-lg font-semibold mb-4 text-primary">
        Chicken Statistics Dashboard
      </h2>

      {/* Counts */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mb-6">
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-md transition-all">
          <h3 className="text-sm font-medium text-gray-700 mb-2">
            Total Chickens
          </h3>
          <p className="text-2xl font-bold text-primary">
            {stats.totalChickens}
          </p>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-md transition-all">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Total Eggs</h3>
          <p className="text-2xl font-bold text-amber-600">{stats.totalEggs}</p>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-md transition-all">
          <h3 className="text-sm font-medium text-gray-700 mb-2">
            Null Attributes
          </h3>
          <p className="text-2xl font-bold text-gray-600">
            {stats.totalNullAttributes}
          </p>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-md transition-all">
          <h3 className="text-sm font-medium text-gray-700 mb-2">
            Total Items
          </h3>
          <p className="text-2xl font-bold text-indigo-600">
            {stats.totalChickens + stats.totalEggs + stats.totalNullAttributes}
          </p>
        </div>
      </div>

      {/* Additional Statistics Sections */}
      <div className="grid grid-cols-1 gap-6">
        {/* Breeding Stats */}
        {stats.chickensInCooldown !== undefined && (
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 shadow-sm">
            <h3 className="text-base font-medium text-gray-700 mb-3">
              Breeding Statistics
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
                <h4 className="text-xs font-medium text-gray-600 mb-1">
                  In Cooldown
                </h4>
                <p className="text-lg font-semibold text-purple-500">
                  {stats.chickensInCooldown}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Egg Stats */}
        {(stats.readyToHatchEggs !== undefined ||
          stats.incubatingEggs !== undefined ||
          stats.breedingPhaseEggs !== undefined ||
          stats.hatchingPhaseEggs !== undefined) && (
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 shadow-sm">
            <h3 className="text-base font-medium text-gray-700 mb-3">
              Egg Statistics
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {stats.breedingPhaseEggs !== undefined && (
                <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
                  <h4 className="text-xs font-medium text-gray-600 mb-1">
                    Breeding Phase
                  </h4>
                  <p className="text-lg font-semibold text-emerald-500">
                    {stats.breedingPhaseEggs}
                  </p>
                </div>
              )}
              {stats.hatchingPhaseEggs !== undefined && (
                <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
                  <h4 className="text-xs font-medium text-gray-600 mb-1">
                    Hatching Phase
                  </h4>
                  <p className="text-lg font-semibold text-emerald-500">
                    {stats.hatchingPhaseEggs}
                  </p>
                </div>
              )}
              {stats.readyToHatchEggs !== undefined && (
                <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
                  <h4 className="text-xs font-medium text-gray-600 mb-1">
                    Ready to Hatch
                  </h4>
                  <p className="text-lg font-semibold text-green-500">
                    {stats.readyToHatchEggs}
                  </p>
                </div>
              )}
              {stats.incubatingEggs !== undefined && (
                <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
                  <h4 className="text-xs font-medium text-gray-600 mb-1">
                    Total Incubating
                  </h4>
                  <p className="text-lg font-semibold text-teal-500">
                    {stats.incubatingEggs}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Economic Stats */}
        {stats.totalDailyFeathers !== undefined && (
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 shadow-sm">
            <h3 className="text-base font-medium text-gray-700 mb-3">
              Economic Statistics
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
                <h4 className="text-xs font-medium text-gray-600 mb-1">
                  Total Daily Feathers
                </h4>
                <p className="text-lg font-semibold text-amber-500">
                  {stats.totalDailyFeathers}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Average Stats */}
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 shadow-sm mb-6">
        <h3 className="text-base font-medium text-gray-700 mb-3">
          Average Chicken Stats
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
            <h4 className="text-xs font-medium text-gray-600 mb-1">
              Innate Attack
            </h4>
            <p className="text-lg font-semibold text-red-500">
              {stats.averageStats.innateAttack}
            </p>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
            <h4 className="text-xs font-medium text-gray-600 mb-1">
              Innate Defense
            </h4>
            <p className="text-lg font-semibold text-blue-500">
              {stats.averageStats.innateDefense}
            </p>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
            <h4 className="text-xs font-medium text-gray-600 mb-1">
              Innate Health
            </h4>
            <p className="text-lg font-semibold text-green-500">
              {stats.averageStats.innateHealth}
            </p>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
            <h4 className="text-xs font-medium text-gray-600 mb-1">
              Innate Speed
            </h4>
            <p className="text-lg font-semibold text-yellow-500">
              {stats.averageStats.innateSpeed}
            </p>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
            <h4 className="text-xs font-medium text-gray-600 mb-1">
              Grit Attack
            </h4>
            <p className="text-lg font-semibold text-red-500">
              {stats.averageStats.gritAttack}
            </p>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
            <h4 className="text-xs font-medium text-gray-600 mb-1">
              Grit Defense
            </h4>
            <p className="text-lg font-semibold text-blue-500">
              {stats.averageStats.gritDefense}
            </p>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
            <h4 className="text-xs font-medium text-gray-600 mb-1">
              Grit Health
            </h4>
            <p className="text-lg font-semibold text-green-500">
              {stats.averageStats.gritHealth}
            </p>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-100 hover:shadow-sm transition-all">
            <h4 className="text-xs font-medium text-gray-600 mb-1">
              Grit Speed
            </h4>
            <p className="text-lg font-semibold text-yellow-500">
              {stats.averageStats.gritSpeed}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export type { IStatsData };
