"use client";

import React from "react";
import { Address } from "viem";
import { Button } from "@/components/ui";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { format, formatDistanceToNow } from "date-fns";

interface ITokenInfoDisplayProps {
  tokenId: string;
  owner: Address | undefined;
  contractType: string | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  onViewOwnerData: (address: string) => void;
  isEgg?: boolean;
  eggInfo?: {
    phase: string;
    inBreedingPhase: boolean;
    inHatchingPhase: boolean;
    readyToHatch: boolean;
    hatched: boolean;
    timeRemaining: number;
    progress: number;
    parentLeft: number;
    parentRight: number;
    generation: number;
    hatchedAt: Date;
    breedingEndTime: Date;
    createdAt: Date;
    hatched_time?: number;
  } | null;
}

export default function TokenInfoDisplay({
  tokenId,
  owner,
  contractType,
  isLoading,
  isError,
  error,
  onViewOwnerData,
  isEgg = false,
  eggInfo = null,
}: ITokenInfoDisplayProps): React.ReactNode {
  if (isLoading) {
    return (
      <Card className="p-6 bg-white rounded-xl shadow-sm border border-gray-100">
        <h2 className="text-lg font-semibold mb-4 text-primary flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
          </svg>
          Token Information
        </h2>
        <div className="space-y-4">
          <div className="bg-gray-50 p-3 rounded-lg">
            <p className="text-sm text-gray-500 mb-1">Token ID</p>
            <p className="font-medium text-gray-900">{tokenId}</p>
          </div>
          <div className="bg-gray-50 p-3 rounded-lg">
            <p className="text-sm text-gray-500 mb-1">Owner Address</p>
            <Skeleton className="h-6 w-full max-w-md bg-gray-200" />
          </div>
          <div className="bg-gray-50 p-3 rounded-lg">
            <p className="text-sm text-gray-500 mb-1">Contract Type</p>
            <Skeleton className="h-6 w-32 bg-gray-200" />
          </div>
        </div>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card className="p-6 bg-white rounded-xl shadow-sm border border-gray-100">
        <h2 className="text-lg font-semibold mb-4 text-red-500 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
          Error
        </h2>
        <div className="bg-red-50 p-4 rounded-lg border border-red-100">
          <p className="text-red-600 font-medium">
            {error?.message || "Failed to fetch token information"}
          </p>
          <p className="mt-2 text-sm text-red-500">
            This could be because the token ID doesn&apos;t exist or there was
            an error connecting to the blockchain.
          </p>
        </div>
      </Card>
    );
  }

  if (!owner) {
    return null;
  }

  // Format time remaining in a human-readable format
  const formatTimeRemaining = (seconds: number): string => {
    if (seconds <= 0) return "0 seconds";

    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    let result = "";
    if (days > 0) result += `${days}d `;
    if (hours > 0) result += `${hours}h `;
    if (minutes > 0) result += `${minutes}m `;
    if (remainingSeconds > 0) result += `${remainingSeconds}s`;

    return result.trim();
  };

  return (
    <Card className="p-6 bg-white rounded-xl shadow-sm border border-gray-100">
      <h2 className="text-lg font-semibold mb-4 text-primary flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 mr-2"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
        </svg>
        Token Information
      </h2>
      <div className="space-y-4">
        <div className="bg-gray-50 p-3 rounded-lg">
          <p className="text-sm text-gray-500 mb-1">Token ID</p>
          <p className="font-medium text-gray-900">{tokenId}</p>
        </div>
        <div className="bg-gray-50 p-3 rounded-lg">
          <p className="text-sm text-gray-500 mb-1">Owner Address</p>
          <p className="font-medium break-all text-gray-900">{owner}</p>
        </div>
        <div className="bg-gray-50 p-3 rounded-lg">
          <p className="text-sm text-gray-500 mb-1">Contract Type</p>
          <p className="font-medium text-gray-900">{contractType} Chicken</p>
        </div>

        {/* Egg-specific information */}
        {isEgg && eggInfo && (
          <div className="mt-4 border-t pt-4 border-gray-100">
            <h3 className="text-md font-semibold mb-3 text-primary flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-2"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path
                  fillRule="evenodd"
                  d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                  clipRule="evenodd"
                />
              </svg>
              Egg Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-500 mb-1">Current Phase</p>
                <p className="font-medium text-gray-900">{eggInfo.phase}</p>
              </div>

              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-500 mb-1">Generation</p>
                <p className="font-medium text-gray-900">
                  {eggInfo.generation}
                </p>
              </div>

              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-500 mb-1">Parent Left (ID)</p>
                <p className="font-medium text-gray-900">
                  {eggInfo.parentLeft}
                </p>
              </div>

              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-500 mb-1">Parent Right (ID)</p>
                <p className="font-medium text-gray-900">
                  {eggInfo.parentRight}
                </p>
              </div>

              {eggInfo.inBreedingPhase && (
                <div className="bg-gray-50 p-3 rounded-lg col-span-1 md:col-span-2">
                  <p className="text-sm text-gray-500 mb-1">
                    Breeding Phase Progress
                  </p>
                  <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                    <div
                      className="bg-primary h-2.5 rounded-full"
                      style={{ width: `${eggInfo.progress}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500">
                    {eggInfo.timeRemaining > 0
                      ? `${formatTimeRemaining(eggInfo.timeRemaining)} remaining`
                      : "Ready for hatching phase"}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Breeding ends: {format(eggInfo.breedingEndTime, "PPpp")}
                  </p>
                </div>
              )}

              {eggInfo.inHatchingPhase && (
                <div className="bg-gray-50 p-3 rounded-lg col-span-1 md:col-span-2">
                  <p className="text-sm text-gray-500 mb-1">
                    Hatching Phase Progress
                  </p>
                  <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                    <div
                      className="bg-primary h-2.5 rounded-full"
                      style={{ width: `${eggInfo.progress}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500">
                    {eggInfo.timeRemaining > 0
                      ? `${formatTimeRemaining(eggInfo.timeRemaining)} remaining`
                      : "Ready to hatch"}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Can hatch at: {format(eggInfo.hatchedAt, "PPpp")}
                  </p>
                </div>
              )}

              {eggInfo.readyToHatch && (
                <div className="bg-gray-50 p-3 rounded-lg col-span-1 md:col-span-2 border-l-4 border-green-500">
                  <p className="text-sm font-medium text-green-600 mb-1">
                    Ready to Hatch!
                  </p>
                  <p className="text-xs text-gray-500">
                    This egg can now be hatched into a chicken.
                  </p>
                </div>
              )}

              {eggInfo.hatched && (
                <div className="bg-gray-50 p-3 rounded-lg col-span-1 md:col-span-2 border-l-4 border-blue-500">
                  <p className="text-sm font-medium text-blue-600 mb-1">
                    Hatched
                  </p>
                  <p className="text-xs text-gray-500">
                    This egg has been hatched into a chicken.
                  </p>
                  {eggInfo.hatched_time && (
                    <p className="text-xs text-gray-500 mt-1">
                      Hatched at:{" "}
                      {format(new Date(eggInfo.hatched_time * 1000), "PPpp")}
                    </p>
                  )}
                </div>
              )}

              <div className="bg-gray-50 p-3 rounded-lg col-span-1 md:col-span-2">
                <p className="text-sm text-gray-500 mb-1">Created</p>
                <p className="text-xs text-gray-500">
                  {formatDistanceToNow(eggInfo.createdAt, { addSuffix: true })}
                </p>
                <p className="text-xs text-gray-500">
                  {format(eggInfo.createdAt, "PPpp")}
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="pt-2">
          <Button
            intent="primary"
            onPress={() => onViewOwnerData(owner)}
            className="mt-2 w-full sm:w-auto"
          >
            <div className="flex items-center justify-center space-x-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                  clipRule="evenodd"
                />
              </svg>
              <span>View Owner&apos;s Data</span>
            </div>
          </Button>
        </div>
      </div>
    </Card>
  );
}
