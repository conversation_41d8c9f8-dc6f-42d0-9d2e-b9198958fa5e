import { Badge } from "@/components/ui";
import { ItemData } from "@/utils/crafting-helpers";

interface CraftingBadgesProps {
  item: ItemData;
  recipe: any;
  cornRecipe: any;
  enoughBalance: boolean;
  enoughRecipeMaterials: boolean;
  isCornCraftingValid: boolean;
}

export const CraftingBadges: React.FC<CraftingBadgesProps> = ({
  item,
  recipe,
  cornRecipe,
  enoughBalance,
  enoughRecipeMaterials,
  isCornCraftingValid,
}) => {
  const isRandomItem = item?.isRandom;

  if (isRandomItem && item.craftable) {
    if (enoughBalance) {
      return (
        <Badge
          intent="success"
          className="text-xs px-1 py-0.5 whitespace-nowrap"
        >
          Craftable
        </Badge>
      );
    } else {
      return (
        <Badge className="text-xs px-1 py-0.5 bg-yellow-500/15 text-yellow-700 dark:bg-yellow-500/10 dark:text-yellow-400 whitespace-nowrap">
          Need Items
        </Badge>
      );
    }
  } else if (recipe || cornRecipe || item.craftable) {
    const canCraftRecipe = recipe && enoughRecipeMaterials;
    const canCraftCorn = cornRecipe && isCornCraftingValid;

    if (canCraftRecipe || canCraftCorn) {
      return (
        <Badge
          intent="success"
          className="text-xs px-1 py-0.5 whitespace-nowrap"
        >
          Craftable
        </Badge>
      );
    } else {
      return (
        <Badge className="text-xs px-1 py-0.5 bg-yellow-500/15 text-yellow-700 dark:bg-yellow-500/10 dark:text-yellow-400 whitespace-nowrap">
          Need Items
        </Badge>
      );
    }
  } else {
    return (
      <Badge
        intent="secondary"
        className="text-xs px-1 py-0.5 whitespace-nowrap"
      >
        No Recipe
      </Badge>
    );
  }
};