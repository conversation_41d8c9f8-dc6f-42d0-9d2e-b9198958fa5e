"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";
import { chain } from "@/providers/web3/web3-provider";
import { rentalAbi } from "@/providers/web3/abi/rental-abi";
import { DelegationAPI } from "../api/delegation.api";
import { IRentChickenResponse } from "../types/delegation.types";

/**
 * Hook for handling the complete chicken rental process
 * Follows the pattern from useClaimBalance for blockchain interactions
 */
export const useRentChicken = () => {
  const { publicClient, walletClient, address, Disconnect } = useStateContext();
  const { blockchainQuery } = useBlockchain();
  const queryClient = useQueryClient();

  const [isRenting, setIsRenting] = useState(false);

  // Get rental contract address from blockchain config
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  /**
   * Execute the complete rental process:
   * 1. Call API to get signature
   * 2. Execute blockchain transaction
   * 3. Handle success/error states
   */
  const executeRentChicken = async (
    rentalId: number,
    ownerAddress: Address
  ) => {
    try {
      if (!address || !walletClient) {
        toast.error("Cannot rent chicken", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return;
      }

      if (!rentalAddress) {
        toast.error("Cannot rent chicken", {
          description: "Rental contract not configured",
          position: "top-right",
        });
        return;
      }

      setIsRenting(true);

      // Step 1: Get signature from API
      toast.info("Preparing rental transaction...", {
        description: "Getting signature from server",
        position: "top-center",
      });

      const rentData: IRentChickenResponse =
        await DelegationAPI.rentChicken(rentalId);

      if (rentData.status !== 1 || !rentData.data) {
        throw new Error("Failed to get rental signature from server");
      }

      const {
        rentalId: contractRentalId,
        chickenTokenId,
        roninPrice,
        insurancePrice,
        renterAddress,
        ownerAddress: rentalOwnerAddress,
        signature,
      } = rentData.data;

      // Convert prices from wei string to bigint
      const ethPriceInWei = BigInt(roninPrice);
      const insurancePriceInWei = BigInt(insurancePrice);
      const totalPayment = ethPriceInWei + insurancePriceInWei;

      // Step 2: Simulate the contract call
      toast.info("Simulating transaction...", {
        description: "Checking transaction validity",
        position: "top-center",
      });

      // Prepare the RentChickenParams struct
      const rentParams = {
        rentId: BigInt(contractRentalId),
        chickenId: BigInt(chickenTokenId),
        ethPrice: ethPriceInWei,
        insurancePrice: insurancePriceInWei,
        renterAddress: renterAddress as Address,
        ownerAddress: rentalOwnerAddress as Address,
        signature: signature as `0x${string}`,
      };

      console.log(
        "Rent parameters:",
        rentParams,
        "Total payment:",
        totalPayment
      );

      const simulateReq = await publicClient.simulateContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "rentChicken",
        args: [rentParams],
        value: totalPayment, // Send ETH + insurance with the transaction
        chain,
        account: address,
      });

      // Step 3: Estimate gas for the transaction
      const gasEstimate = await publicClient.estimateContractGas({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "rentChicken",
        args: [rentParams],
        value: totalPayment,
        account: address,
      });

      // Add 15% buffer to gas estimate (rental transactions might be more complex)
      const gasWithBuffer = (gasEstimate * 115n) / 100n;

      const request = simulateReq.request;
      request.gas = gasWithBuffer;

      // Step 4: Execute the transaction
      toast.info("Sending rental transaction...", {
        description: "Please confirm in your wallet",
        position: "top-center",
      });

      const hash = await walletClient.writeContract(request);

      toast.info("Rental transaction sent", {
        description: `Transaction hash: ${hash}`,
        position: "top-center",
      });

      // Step 5: Wait for transaction to be mined
      const receipt = await publicClient.waitForTransactionReceipt({ hash });

      if (receipt.status === "success") {
        toast.success("Chicken rented successfully!", {
          description: "You can now use the rented chicken",
          position: "top-center",
        });

        // Show a secondary notification about data update delay
        toast.info("Updating marketplace data...", {
          description: "Changes will reflect in a few seconds",
          position: "top-center",
          duration: 8000, // Show for 8 seconds
        });

        // Add delay before invalidating queries to allow backend to process the blockchain event
        setTimeout(async () => {
          // First, invalidate and refetch chickenTokenIds (blockchain data) if needed
          await queryClient.invalidateQueries({
            queryKey: ["chickenTokenIds", "legacy", address],
          });
          await queryClient.invalidateQueries({
            queryKey: ["chickenTokenIds", "genesis", address],
          });

          // Then invalidate rental-related queries
          queryClient.invalidateQueries({ queryKey: ["available-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals", address] });
          queryClient.invalidateQueries({
            queryKey: ["chickenRentalStatuses"],
          });

          // Show confirmation that data has been updated
          toast.success("Marketplace data updated!", {
            description: "Rental listings have been refreshed",
            position: "top-center",
            duration: 3000,
          });
        }, 10000); // 10 second delay

        return { success: true, hash, receipt };
      } else {
        throw new Error("Transaction failed on-chain");
      }
    } catch (error) {
      console.error("Rent chicken failed:", error);

      let errorMessage = "Failed to rent chicken";
      let errorDescription = "An unexpected error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;

        // Handle specific error cases
        if (error.message.includes("User rejected")) {
          errorDescription = "Transaction was cancelled by user";
        } else if (error.message.includes("insufficient funds")) {
          errorDescription = "Insufficient funds to complete the rental";
        } else if (error.message.includes("ErrInvalidSignature")) {
          errorDescription = "Invalid rental signature. Please try again.";
        } else if (error.message.includes("ErrRentIdAlreadyUsed")) {
          errorDescription = "This chicken has already been rented";
        } else if (error.message.includes("ErrInvalidPayment")) {
          errorDescription = "Invalid payment amount";
        } else {
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        position: "top-center",
      });

      return { success: false, error };
    } finally {
      setIsRenting(false);
    }
  };

  // Mutation for React Query integration
  const rentChickenMutation = useMutation({
    mutationFn: ({
      rentalId,
      ownerAddress,
    }: {
      rentalId: number;
      ownerAddress: Address;
    }) => executeRentChicken(rentalId, ownerAddress),
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Rent chicken mutation error:", error);
    },
  });

  return {
    executeRentChicken,
    rentChickenMutation,
    isRenting,
    rentalAddress,
  };
};
