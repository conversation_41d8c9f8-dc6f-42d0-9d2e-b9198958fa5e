import marketplace from "../models/marketplace.js";
import { listedNfts } from "../services/getLatestListed.js";
import { fetchOpAllCollections } from "../services/getLatestOpListed.js";
import { UTCTimestamp } from "../utils/utc-timestamp.js";

export async function RecordListed() {
  try {
    const epoch = UTCTimestamp();
    const allListedNft = await listedNfts();
    const allOpListedNft = await fetchOpAllCollections();

    const combinedData = [...allListedNft, ...allOpListedNft];
    const docsWithEpoch = combinedData.map((doc) => ({
      address: doc.address,
      tokenId: doc.id,
      listedAt: doc.startedAt,
      epoch: epoch,
    }));

    const operations = docsWithEpoch.map((doc) => ({
      updateOne: {
        filter: { tokenId: doc.tokenId, epoch },
        update: { $setOnInsert: doc },
        upsert: true,
      },
    }));

    const result = await marketplace.bulkWrite(operations);

    console.log(`Inserted/Updated documents: ${result.modifiedCount}`);
  } catch (error) {
    const knownError = error as Error;
    console.log(`Something went wrong: ${knownError.message}`);
  }
}
