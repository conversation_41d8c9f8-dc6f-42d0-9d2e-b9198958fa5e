import Image from "next/image";
import { ChickenStats } from "@/types/chicken.type";
import { ChickenHpBar } from "./ChickenHpBar";
import { ChickenStatusBadges } from "./ChickenStatusBadges";
import { ChickenOverlays } from "./ChickenOverlays";
import { ChickenTimers } from "./ChickenTimers";
import { ChickenActions } from "./ChickenActions";

type ChickenType =
  | "owned"
  | "delegated-to-me"
  | "rented-to-me"
  | "delegated-out"
  | "rented-out"
  | "listed-in-market";

interface Chicken {
  tokenId: string;
  image: string;
  attributes?: {
    Type?: string[];
  };
  chickenType?: ChickenType;
  isDelegated?: boolean;
  isRented?: boolean;
  isDisabled?: boolean;
  delegationInfo?: any;
  rentalInfo?: any;
}

interface ChickenCardProps {
  chicken: Chicken;
  stats?: ChickenStats;
  isStatsLoading: boolean;
  isFavorite: boolean;
  isEgg: boolean;
  isFaint: boolean;
  isDead: boolean;
  isBreeding: boolean;
  isListed: boolean;
  wasTransferred: boolean;
  isImmortal: boolean;
  cooldownTimers: Record<string, number>;
  recoveryTimers: Record<string, number>;
  breedingTimers: Record<string, number>;
  listedTimers: Record<string, number>;
  transferTimers: Record<string, number>;
  immortalTimers: Record<string, number>;
  formatTime: (
    seconds: number,
    type:
      | "cooldown"
      | "recovery"
      | "breeding"
      | "listed"
      | "transfer"
      | "immortal"
  ) => string;
  onChickenClick: (tokenId: string) => void;
  onToggleFavorite: (e: React.MouseEvent, tokenId: string) => void;
  onBattle: (e: React.MouseEvent, tokenId: string) => void;
  onHeal: (e: React.MouseEvent, tokenId: string) => void;
  onFeed: (e: React.MouseEvent, tokenId: string) => void;
  onBreed: (e: React.MouseEvent, tokenId: string) => void;
  onMenuAction: (e: React.MouseEvent, action: string, tokenId: string) => void;
  // Bulk selection props
  isBulkMode?: boolean;
  isSelected?: boolean;
  canSelect?: boolean;
  onToggleSelection?: (tokenId: string) => void;
}

export function ChickenCard({
  chicken,
  stats,
  isStatsLoading,
  isFavorite,
  isEgg,
  isFaint,
  isDead,
  isBreeding,
  isListed,
  wasTransferred,
  isImmortal,
  cooldownTimers,
  recoveryTimers,
  breedingTimers,
  listedTimers,
  transferTimers,
  immortalTimers,
  formatTime,
  onChickenClick,
  onToggleFavorite,
  onBattle,
  onHeal,
  onFeed,
  onBreed,
  onMenuAction,
  isBulkMode = false,
  isSelected = false,
  canSelect = true,
  onToggleSelection,
}: ChickenCardProps) {
  // Priority order: dead → faint → transferred → listed → breeding
  const isDisabled =
    isDead || isFaint || wasTransferred || isListed || isBreeding;

  return (
    <div
      className={`bg-stone-800 border rounded-lg p-4 shadow-md cursor-pointer transition-all relative ${
        isSelected && isBulkMode
          ? "border-blue-400 bg-blue-500/10"
          : isFavorite
            ? "border-yellow-500"
            : isListed
              ? "border-orange-500 shadow-orange-500/20"
              : isBreeding
                ? "border-pink-500"
                : wasTransferred
                  ? "border-purple-500"
                  : "border-stone-700 hover:border-stone-500"
      } ${isDisabled ? "opacity-80" : ""} ${
        isImmortal
          ? "animate-pulse-subtle bg-gradient-to-br from-stone-800 via-stone-800 to-amber-900/20"
          : isListed
            ? "bg-gradient-to-br from-stone-800 via-stone-800 to-orange-900/10"
            : ""
      }`}
      style={
        isImmortal
          ? {
              boxShadow:
                "0 0 20px rgba(251, 191, 36, 0.3), inset 0 0 20px rgba(251, 191, 36, 0.1)",
            }
          : isListed
            ? {
                boxShadow:
                  "0 0 15px rgba(251, 146, 60, 0.2), inset 0 0 15px rgba(251, 146, 60, 0.05)",
              }
            : {}
      }
      onClick={() => {
        if (isBulkMode && canSelect && onToggleSelection) {
          onToggleSelection(chicken.tokenId);
        } else {
          onChickenClick(chicken.tokenId);
        }
      }}
    >
      {/* Timers */}
      <ChickenTimers
        tokenId={chicken.tokenId}
        isFaint={isFaint}
        isDead={isDead}
        isBreeding={isBreeding}
        isListed={isListed}
        wasTransferred={wasTransferred}
        isImmortal={isImmortal}
        isEgg={isEgg}
        cooldownTimers={cooldownTimers}
        recoveryTimers={recoveryTimers}
        breedingTimers={breedingTimers}
        listedTimers={listedTimers}
        transferTimers={transferTimers}
        immortalTimers={immortalTimers}
        formatTime={formatTime}
        hp={stats?.hp}
        maxHp={stats?.maxHp}
      />

      {/* Status Badges */}
      <ChickenStatusBadges
        tokenId={chicken.tokenId}
        isFavorite={isFavorite}
        isImmortal={isImmortal}
        chickenType={chicken.chickenType}
        onToggleFavorite={onToggleFavorite}
      />

      {/* Chicken Image */}
      <div className="aspect-square relative mb-3 overflow-hidden rounded-lg">
        {chicken.image ? (
          <>
            {/* Loading placeholder */}
            <div
              className="absolute inset-0 animate-pulse bg-stone-700/30 rounded z-10"
              id={`placeholder-${chicken.tokenId}`}
            ></div>

            <Image
              src={chicken.image}
              alt={`Chicken #${chicken.tokenId}`}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
              className={`object-cover z-20 transition-all duration-300 ${
                isFaint || isDead
                  ? "grayscale"
                  : isBreeding
                    ? "sepia-[0.3] hue-rotate-[300deg] saturate-[1.8]" // Pink tint
                    : wasTransferred
                      ? "sepia-[0.4] hue-rotate-[260deg] saturate-[1.5]" // Purple tint
                      : isImmortal
                        ? "sepia-[0.2] hue-rotate-[45deg] saturate-[1.3] brightness-[1.1]" // Golden tint
                        : ""
              }`}
              onLoadingComplete={() => {
                // Hide the placeholder when image loads
                const placeholder = document.getElementById(
                  `placeholder-${chicken.tokenId}`
                );
                if (placeholder) {
                  placeholder.style.display = "none";
                }
              }}
              loading="lazy"
            />
          </>
        ) : (
          <div className="w-full h-full animate-pulse space-y-4 flex items-center justify-center">
            <div className="space-y-2 w-full">
              <div className="h-24 bg-stone-700/30 rounded mx-auto"></div>
              <div className="h-8 bg-stone-700/30 rounded w-3/4 mx-auto"></div>
              <div className="h-4 bg-stone-700/30 rounded w-1/2 mx-auto"></div>
            </div>
          </div>
        )}

        {/* Overlays */}
        <ChickenOverlays
          tokenId={chicken.tokenId}
          isFaint={isFaint}
          isDead={isDead}
          isBreeding={isBreeding}
          isListed={isListed}
          wasTransferred={wasTransferred}
          isImmortal={isImmortal}
          recoveryTimers={recoveryTimers}
          breedingTimers={breedingTimers}
          listedTimers={listedTimers}
          transferTimers={transferTimers}
          immortalTimers={immortalTimers}
          formatTime={formatTime}
        />
      </div>

      {/* Level and HP Bar - only show for non-eggs and non-disabled chickens */}
      {!isEgg && !isDisabled && (
        <div className="w-full mt-2 mb-3">
          <div className="flex justify-between items-center text-base font-semibold mb-1">
            <span className="text-white">{stats?.mmr || 0} MMR</span>
            <span>Lvl {stats?.level || 1}</span>
          </div>
          <ChickenHpBar
            tokenId={chicken.tokenId}
            stats={stats}
            isLoading={isStatsLoading}
          />
        </div>
      )}

      {/* Level only for disabled chickens - no HP bar */}
      {!isEgg && isDisabled && (
        <div className="w-full mt-2 mb-3">
          <div className="flex justify-between items-center text-base font-semibold">
            <span className="text-white">{stats?.mmr || 0} MMR</span>
            <span>Lvl {stats?.level || 1}</span>
          </div>
        </div>
      )}

      {/* Chicken ID and Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="font-medium text-white">Chicken #{chicken.tokenId}</div>
        <ChickenActions
          tokenId={chicken.tokenId}
          isEgg={isEgg}
          isFaint={isFaint}
          isDead={isDead}
          isBreeding={isBreeding}
          isListed={isListed}
          wasTransferred={wasTransferred}
          hp={stats?.hp}
          maxHp={stats?.maxHp}
          chickenType={chicken.chickenType}
          isDisabled={chicken.isDisabled}
          onBattle={onBattle}
          onHeal={onHeal}
          onFeed={onFeed}
          onBreed={onBreed}
          onMenuAction={onMenuAction}
        />
      </div>
    </div>
  );
}
