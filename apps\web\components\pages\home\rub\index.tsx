"use client";

import CardsLoaders from "@/components/shared/cards-loaders";
import { useStateContext } from "@/providers/app/state";
import useChickenStore from "@/store/chicken";
import { useEffect } from "react";
import { CantFetch } from "./canFetch";
import { NotAuthenticatedRubContent } from "./not-authenticated";
import { RubContent } from "./rubContent";

export default function Rub() {
  const { isConnected, loading } = useStateContext();
  const {
    getChickens,
    chickens,
    isPending: isPendingChick,
  } = useChickenStore();

  useEffect(() => {
    // Only fetch chickens if the user is authenticated
    if (isConnected) {
      getChickens(isConnected);
    }
  }, []);

  if (loading.value || isPendingChick) {
    return <CardsLoaders />;
  }

  if (!isConnected) {
    return <NotAuthenticatedRubContent />;
  }

  if (typeof chickens === "undefined") {
    return <CantFetch />;
  }

  return <RubContent chickens={chickens} />;
}
