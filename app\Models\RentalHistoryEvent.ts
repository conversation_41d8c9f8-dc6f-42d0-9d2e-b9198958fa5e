/* eslint-disable @typescript-eslint/naming-convention */
import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import Rental from './Rental'

export enum RentalHistoryEventType {
  LISTED = 'listed',
  UNLISTED = 'unlisted',
  RENTED = 'rented',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
  INSURANCE_CLAIMED = 'insurance_claimed',
  PRICE_UPDATED = 'price_updated',
  TERMS_UPDATED = 'terms_updated',
}

export interface IRentalHistoryEventData {
  // For LISTED events
  originalPrice?: string
  originalTerms?: {
    rewardDistribution?: number
    delegatedTask?: number
    sharedRewardAmount?: number | null
  }

  // For PRICE_UPDATED events
  oldPrice?: string
  newPrice?: string

  // For RENTED events
  renterAddress?: string
  rentalDuration?: number

  // For EXPIRED events
  expiredAt?: string

  // For INSURANCE_CLAIMED events
  insuranceAmount?: string
  claimReason?: string

  // For blockchain events
  blockNumber?: string
  transactionHash?: string

  // Additional context
  notes?: string
}

export default class RentalHistoryEvent extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public rentalId: number

  @column()
  public eventType: RentalHistoryEventType

  @column()
  public actorAddress: string

  @column({
    prepare: (value: IRentalHistoryEventData) => JSON.stringify(value),
    consume: (value: string) => {
      return value && typeof value === 'string' ? JSON.parse(value) : value
    },
  })
  public eventData: IRentalHistoryEventData | null

  @column()
  public blockNumber: string | null

  @column()
  public transactionHash: string | null

  @column()
  public description: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // Relationships
  @belongsTo(() => Rental)
  public rental: BelongsTo<typeof Rental>

  // Helper methods
  public static async createEvent(
    rentalId: number,
    eventType: RentalHistoryEventType,
    actorAddress: string,
    eventData?: IRentalHistoryEventData,
    description?: string
  ): Promise<RentalHistoryEvent> {
    return await RentalHistoryEvent.create({
      rentalId,
      eventType,
      actorAddress,
      eventData: eventData || null,
      blockNumber: eventData?.blockNumber || null,
      transactionHash: eventData?.transactionHash || null,
      description,
    })
  }

  public static async createListedEvent(
    rentalId: number,
    ownerAddress: string,
    price: string,
    terms: any,
    blockNumber?: string,
    transactionHash?: string
  ): Promise<RentalHistoryEvent> {
    // Convert wei price to human-readable RON format
    const priceInRon = (parseFloat(price) / 1e18).toFixed(4)

    return await this.createEvent(
      rentalId,
      RentalHistoryEventType.LISTED,
      ownerAddress,
      {
        originalPrice: price,
        originalTerms: terms,
        blockNumber,
        transactionHash,
      },
      `Chicken listed for delegation at ${priceInRon} RON`
    )
  }

  public static async createRentedEvent(
    rentalId: number,
    renterAddress: string,
    rentalDuration: number,
    blockNumber?: string,
    transactionHash?: string
  ): Promise<RentalHistoryEvent> {
    return await this.createEvent(
      rentalId,
      RentalHistoryEventType.RENTED,
      renterAddress,
      {
        renterAddress,
        rentalDuration,
        blockNumber,
        transactionHash,
      },
      `Chicken delegated to ${renterAddress}`
    )
  }

  public static async createCancelledEvent(
    rentalId: number,
    actorAddress: string,
    blockNumber?: string,
    transactionHash?: string
  ): Promise<RentalHistoryEvent> {
    return await this.createEvent(
      rentalId,
      RentalHistoryEventType.CANCELLED,
      actorAddress,
      {
        blockNumber,
        transactionHash,
      },
      `Delegation cancelled`
    )
  }

  public static async createUnlistedEvent(
    rentalId: number,
    actorAddress: string,
    blockNumber?: string,
    transactionHash?: string
  ): Promise<RentalHistoryEvent> {
    return await this.createEvent(
      rentalId,
      RentalHistoryEventType.UNLISTED,
      actorAddress,
      {
        blockNumber,
        transactionHash,
      },
      `Chicken unlisted from marketplace`
    )
  }

  public static async createExpiredEvent(
    rentalId: number,
    expiredAt: string
  ): Promise<RentalHistoryEvent> {
    return await this.createEvent(
      rentalId,
      RentalHistoryEventType.EXPIRED,
      '', // System event, no specific actor
      {
        expiredAt,
      },
      `Delegation expired`
    )
  }

  public static async createInsuranceClaimedEvent(
    rentalId: number,
    claimerAddress: string,
    insuranceAmount: string,
    blockNumber?: string,
    transactionHash?: string
  ): Promise<RentalHistoryEvent> {
    return await this.createEvent(
      rentalId,
      RentalHistoryEventType.INSURANCE_CLAIMED,
      claimerAddress,
      {
        insuranceAmount,
        blockNumber,
        transactionHash,
      },
      `Insurance claimed for ${insuranceAmount} RON`
    )
  }
}
