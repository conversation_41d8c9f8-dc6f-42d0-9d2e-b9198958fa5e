import { formatEther } from "viem";
import {
  CornAllocation,
  RecipeMaterial,
  CornMaterial,
  CraftingPool,
  ItemData,
  getCurrentBalance,
  isSufficientMaterial,
  getTotalAllocated,
  isCornAllocationValid,
  isType2RandomPool,
  getType2RequirementsForRandomPool,
  getAcceptedResourceIdsForRandomPool,
  getRecipeForItem,
  getCornRecipeForItem,
  isType2CornRecipe,
  getType2Requirements,
  getAcceptedResourceIds,
} from "./crafting-helpers";

export interface ValidationContext {
  cock: bigint;
  feathers: number;
  legendaryFeathers: number;
  foodBalances: Record<number, bigint>;
  quantity: number;
}

// Recipe-based crafting validation
export const validateRecipeMaterials = (
  recipe: { materials: RecipeMaterial[] },
  context: ValidationContext
): boolean => {
  return recipe.materials.every((material) => {
    return isSufficientMaterial(
      material,
      context.quantity,
      context.cock,
      context.feathers,
      context.legendaryFeathers,
      context.foodBalances
    );
  });
};

// Variable/corn crafting validation
export const validateCornCrafting = (
  cornRecipe: any,
  cornAllocation: CornAllocation,
  context: ValidationContext
): boolean => {
  if (!cornRecipe) return false;

  // Check if it's type 2 (corn allocation)
  if (isType2CornRecipe(cornRecipe)) {
    const required = getType2Requirements(cornRecipe);
    const acceptedIds = getAcceptedResourceIds(cornRecipe);
    if (!required || acceptedIds.length === 0) return false;

    const totalAllocated = getTotalAllocated(cornAllocation);
    const totalNeeded = required * context.quantity;

    // Check if allocation is exactly what's needed
    const correctAllocation = totalAllocated === totalNeeded;

    // Check if allocation doesn't exceed available balances and uses only accepted resources
    const validAllocation = isCornAllocationValid(
      cornAllocation,
      acceptedIds,
      context.foodBalances
    );

    // Also check other (non-type 2) materials
    const otherMaterials = cornRecipe.recipes.filter(
      (r: CornMaterial) => r.tokenType !== 2
    );
    const otherMaterialsValid = otherMaterials.every((recipe: CornMaterial) => {
      return isSufficientMaterial(
        recipe,
        context.quantity,
        context.cock,
        context.feathers,
        context.legendaryFeathers,
        context.foodBalances
      );
    });

    return correctAllocation && validAllocation && otherMaterialsValid;
  }

  // For non-type 2 recipes, check all materials
  return cornRecipe.recipes.every((recipe: CornMaterial) => {
    return isSufficientMaterial(
      recipe,
      context.quantity,
      context.cock,
      context.feathers,
      context.legendaryFeathers,
      context.foodBalances
    );
  });
};

// Random crafting validation
export const validateRandomCrafting = (
  craftingPools: Record<number, CraftingPool>,
  cornAllocation: CornAllocation,
  context: ValidationContext,
  poolId: number = 0
): boolean => {
  const pool = craftingPools[poolId];
  if (!pool) return false;

  // Check if it's type 2 (resource allocation)
  if (isType2RandomPool(poolId, craftingPools)) {
    const required = getType2RequirementsForRandomPool(poolId, craftingPools);
    const acceptedIds = getAcceptedResourceIdsForRandomPool(poolId, craftingPools);
    if (!required || acceptedIds.length === 0) return false;

    const totalAllocated = getTotalAllocated(cornAllocation);
    const totalNeeded = required * context.quantity;

    // Check if allocation is exactly what's needed
    const correctAllocation = totalAllocated === totalNeeded;

    // Check if allocation doesn't exceed available balances and uses only accepted resources
    const validAllocation = isCornAllocationValid(
      cornAllocation,
      acceptedIds,
      context.foodBalances
    );

    // Also check other (non-type 2) materials
    const otherMaterials = pool.materials.filter(
      (material: any) => material.tokenType !== 2
    );
    const otherMaterialsValid = otherMaterials.every((material: any) => {
      const currentBalance = getCurrentBalance(
        {
          tokenType: material.tokenType,
          tokenAddress: material.tokenAddress,
          tokenId: material.tokenId,
          amount: material.amount,
        },
        context.cock,
        context.feathers,
        context.legendaryFeathers,
        context.foodBalances
      );
      const requiredAmount = material.amount * BigInt(context.quantity);
      return currentBalance >= requiredAmount;
    });

    return correctAllocation && validAllocation && otherMaterialsValid;
  }

  // For non-type 2 random pools, check all materials
  return pool.materials.every((material: any) => {
    const currentBalance = getCurrentBalance(
      {
        tokenType: material.tokenType,
        tokenAddress: material.tokenAddress,
        tokenId: material.tokenId,
        amount: material.amount,
      },
      context.cock,
      context.feathers,
      context.legendaryFeathers,
      context.foodBalances
    );
    const requiredAmount = material.amount * BigInt(context.quantity);
    return currentBalance >= requiredAmount;
  });
};

// Legacy random cookie validation (fallback)
export const validateLegacyRandomCookie = (
  randomCockPrice: bigint,
  randomFeatherPrice: bigint,
  context: ValidationContext
): boolean => {
  if (!randomCockPrice || !randomFeatherPrice) return false;
  
  const cockCost = Number(formatEther(randomCockPrice)) * context.quantity;
  const featherCost = Number(randomFeatherPrice) * context.quantity;
  
  return (
    Number(formatEther(context.cock)) >= cockCost &&
    context.feathers >= featherCost
  );
};

// Main validation function
export const validateCrafting = (
  item: ItemData,
  craftingMethod: "recipe" | "corn",
  craftableItems: Array<{ tokenId: number; materials: RecipeMaterial[]; exists: boolean }>,
  craftableCookies: Array<{
    recipes: CornMaterial[];
    tokenId: bigint;
    acceptedResourceIds: bigint[];
  }>,
  craftingPools: Record<number, CraftingPool>,
  cornAllocation: CornAllocation,
  context: ValidationContext,
  randomCockPrice?: bigint,
  randomFeatherPrice?: bigint
): boolean => {
  // Handle random items
  if (item.isRandom) {
    const isValid = validateRandomCrafting(craftingPools, cornAllocation, context, 0);
    
    // Fallback to legacy validation if random crafting validation fails
    if (!isValid && randomCockPrice && randomFeatherPrice) {
      return validateLegacyRandomCookie(randomCockPrice, randomFeatherPrice, context);
    }
    
    return isValid;
  }

  // Handle regular items
  if (craftingMethod === "recipe") {
    const recipe = getRecipeForItem(item.id, craftableItems);
    return recipe ? validateRecipeMaterials(recipe, context) : false;
  } else {
    const cornRecipe = getCornRecipeForItem(item.id, craftableCookies);
    return cornRecipe ? validateCornCrafting(cornRecipe, cornAllocation, context) : false;
  }
};

// Error message generation
export const getValidationErrorMessage = (
  item: ItemData,
  craftingMethod: "recipe" | "corn",
  craftingPools: Record<number, CraftingPool>,
  cornAllocation: CornAllocation,
  context: ValidationContext,
  craftableCookies: Array<{
    recipes: CornMaterial[];
    tokenId: bigint;
    acceptedResourceIds: bigint[];
  }>
): string => {
  // For random items with type 2 materials
  if (item.isRandom && isType2RandomPool(0, craftingPools)) {
    const required = getType2RequirementsForRandomPool(0, craftingPools);
    const acceptedIds = getAcceptedResourceIdsForRandomPool(0, craftingPools);
    const totalAllocated = getTotalAllocated(cornAllocation);
    const totalNeeded = required ? required * context.quantity : 0;

    if (acceptedIds.length === 0) {
      return "No accepted resources configured";
    } else if (totalAllocated < totalNeeded) {
      return `Need ${(totalNeeded - totalAllocated).toLocaleString()} more resources`;
    } else if (totalAllocated > totalNeeded) {
      return `Too many resources allocated (${(totalAllocated - totalNeeded).toLocaleString()} excess)`;
    } else if (!isCornAllocationValid(cornAllocation, acceptedIds, context.foodBalances)) {
      return "Allocation exceeds available balance or uses non-accepted resources";
    } else {
      // Check other materials
      const pool = craftingPools[0];
      if (pool) {
        const otherMaterials = pool.materials.filter((material: any) => material.tokenType !== 2);
        const insufficientOther = otherMaterials.some((material: any) => {
          const currentBalance = getCurrentBalance(
            {
              tokenType: material.tokenType,
              tokenAddress: material.tokenAddress,
              tokenId: material.tokenId,
              amount: material.amount,
            },
            context.cock,
            context.feathers,
            context.legendaryFeathers,
            context.foodBalances
          );
          const requiredAmount = material.amount * BigInt(context.quantity);
          return currentBalance < requiredAmount;
        });
        if (insufficientOther) {
          return "Insufficient additional materials";
        }
      }
    }
    return "Invalid resource allocation";
  }

  // For type 2 variable recipes
  if (craftingMethod === "corn") {
    const cornRecipe = getCornRecipeForItem(item.id, craftableCookies);
    if (cornRecipe && isType2CornRecipe(cornRecipe)) {
      const required = getType2Requirements(cornRecipe);
      const acceptedIds = getAcceptedResourceIds(cornRecipe);
      const totalAllocated = getTotalAllocated(cornAllocation);
      const totalNeeded = required ? required * context.quantity : 0;

      if (acceptedIds.length === 0) {
        return "No accepted resources configured for this recipe";
      } else if (totalAllocated < totalNeeded) {
        return `Need ${(totalNeeded - totalAllocated).toLocaleString()} more resources`;
      } else if (totalAllocated > totalNeeded) {
        return `Too many resources allocated (${(totalAllocated - totalNeeded).toLocaleString()} excess)`;
      } else if (!isCornAllocationValid(cornAllocation, acceptedIds, context.foodBalances)) {
        return "Allocation exceeds available balance or uses non-accepted resources";
      } else {
        const otherMaterials = cornRecipe.recipes.filter((r: CornMaterial) => r.tokenType !== 2);
        const insufficientOther = otherMaterials.some((recipe: CornMaterial) => {
          return !isSufficientMaterial(
            recipe,
            context.quantity,
            context.cock,
            context.feathers,
            context.legendaryFeathers,
            context.foodBalances
          );
        });
        if (insufficientOther) {
          return "Insufficient additional materials";
        }
      }
      return "Invalid resource allocation";
    }
  }

  return "Not enough materials";
};