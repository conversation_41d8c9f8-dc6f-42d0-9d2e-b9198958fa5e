"use client";

import { useState } from "react";
import {
  AdminLayout,
  IBreedingCooldown,
  ICreateBreedingCooldownPayload,
  IUpdateBreedingCooldownPayload,
  useBreedingCooldowns,
  useCreateBreedingCooldown,
  useDeleteBreedingCooldown,
  useUpdateBreedingCooldown,
} from "@/features/admin";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Popover } from "@/components/ui/popover";

export default function BreedingCooldownsPage() {
  const { data: cooldowns, isLoading } = useBreedingCooldowns();
  const createMutation = useCreateBreedingCooldown();
  const updateMutation = useUpdateBreedingCooldown();
  const deleteMutation = useDeleteBreedingCooldown();

  const [createForm, setCreateForm] = useState<ICreateBreedingCooldownPayload>({
    count: 0,
    cooldown: 0,
  });

  // We don't need to track editingCooldown state anymore since we're using popovers
  const [updateForm, setUpdateForm] = useState<IUpdateBreedingCooldownPayload>({
    breedingCooldownId: 0,
    count: 0,
    cooldown: 0,
  });

  // Handle create form changes
  const handleCreateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCreateForm((prev) => ({
      ...prev,
      [name]: value === "" ? 0 : parseInt(value, 10),
    }));
  };

  // Handle update form changes
  const handleUpdateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUpdateForm((prev) => ({
      ...prev,
      [name]: value === "" ? 0 : parseInt(value, 10),
    }));
  };

  // Start editing a cooldown
  const startEditing = (cooldown: IBreedingCooldown) => {
    setUpdateForm({
      breedingCooldownId: cooldown.id,
      count: cooldown.count,
      cooldown: cooldown.cooldown,
    });
  };

  // Cancel editing
  const cancelEditing = () => {
    // Reset form values
    setUpdateForm({
      breedingCooldownId: 0,
      count: 0,
      cooldown: 0,
    });
  };

  // Submit create form
  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createMutation.mutateAsync(createForm);
      toast.success("Breeding cooldown created successfully");
      setCreateForm({ count: 0, cooldown: 0 });
    } catch (error) {
      toast.error("Failed to create breeding cooldown");
      console.error(error);
    }
  };

  // Submit update form
  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await updateMutation.mutateAsync(updateForm);
      toast.success("Breeding cooldown updated successfully");

      // Close the popover after successful update
      const closeButton = document.querySelector(
        '[aria-label="Dismiss"]'
      ) as HTMLButtonElement;
      if (closeButton) closeButton.click();
    } catch (error) {
      toast.error("Failed to update breeding cooldown");
      console.error(error);
    }
  };

  // State to track which cooldown is being considered for deletion
  const [deletingCooldownId, setDeletingCooldownId] = useState<number | null>(
    null
  );

  // Delete a cooldown
  const handleDelete = async (id: number) => {
    try {
      await deleteMutation.mutateAsync(id);
      toast.success("Breeding cooldown deleted successfully");
      // Reset the deleting state
      setDeletingCooldownId(null);
    } catch (error) {
      toast.error("Failed to delete breeding cooldown");
      console.error(error);
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">Breeding Cooldowns</h1>
          <p className="text-gray-400">
            Manage breeding cooldown periods based on breed count
          </p>
        </div>

        {/* Create Form */}
        <div className="bg-[#1E1E1E] rounded-lg p-6 border border-[#333]">
          <h2 className="text-xl font-semibold mb-4">Create New Cooldown</h2>
          <form onSubmit={handleCreate} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="count"
                  className="block mb-2 text-sm font-medium"
                >
                  Breed Count
                </label>
                <input
                  type="number"
                  id="count"
                  name="count"
                  value={createForm.count}
                  onChange={handleCreateChange}
                  className="w-full px-4 py-2 bg-[#2D2D2D] border border-[#444] rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  min="0"
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="cooldown"
                  className="block mb-2 text-sm font-medium"
                >
                  Cooldown (days)
                </label>
                <input
                  type="number"
                  id="cooldown"
                  name="cooldown"
                  value={createForm.cooldown}
                  onChange={handleCreateChange}
                  className="w-full px-4 py-2 bg-[#2D2D2D] border border-[#444] rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  min="0"
                  required
                />
              </div>
            </div>
            <Button
              intent="primary"
              type="submit"
              isDisabled={createMutation.isPending}
            >
              {createMutation.isPending ? "Creating..." : "Create Cooldown"}
            </Button>
          </form>
        </div>

        {/* Cooldowns Table */}
        <div className="bg-[#1E1E1E] rounded-lg p-6 border border-[#333]">
          <h2 className="text-xl font-semibold mb-4">Existing Cooldowns</h2>

          {isLoading ? (
            <p>Loading cooldowns...</p>
          ) : cooldowns && cooldowns.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-[#2D2D2D] text-left">
                    <th className="px-4 py-2 border-b border-[#444]">
                      Breed Count
                    </th>
                    <th className="px-4 py-2 border-b border-[#444]">
                      Cooldown (days)
                    </th>
                    <th className="px-4 py-2 border-b border-[#444]">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {cooldowns.map((cooldown) => (
                    <tr
                      key={cooldown.id}
                      className="border-b border-[#333] hover:bg-[#2A2A2A]"
                    >
                      <td className="px-4 py-3">{cooldown.count}</td>
                      <td className="px-4 py-3">{cooldown.cooldown}</td>
                      <td className="px-4 py-3">
                        <div className="flex space-x-2">
                          <Popover>
                            {/* @ts-expect-error - The type error is a false positive, this pattern works correctly */}
                            <Popover.Trigger asChild>
                              <Button
                                intent="secondary"
                                size="extra-small"
                                onPress={() => startEditing(cooldown)}
                              >
                                Edit
                              </Button>
                            </Popover.Trigger>
                            <Popover.Content className="p-6 w-[400px] bg-[#1E1E1E] border-[#333]">
                              <div className="space-y-4">
                                <h2 className="text-xl font-semibold mb-4">
                                  Edit Cooldown #{cooldown.id}
                                </h2>
                                <form
                                  onSubmit={handleUpdate}
                                  className="space-y-4"
                                >
                                  <div className="grid grid-cols-1 gap-4">
                                    <div>
                                      <label
                                        htmlFor="edit-count"
                                        className="block mb-2 text-sm font-medium"
                                      >
                                        Breed Count
                                      </label>
                                      <input
                                        type="number"
                                        id="edit-count"
                                        name="count"
                                        value={updateForm.count}
                                        onChange={handleUpdateChange}
                                        className="w-full px-4 py-2 bg-[#2D2D2D] border border-[#444] rounded-md focus:outline-none focus:ring-2 focus:ring-primary h-12"
                                        min="0"
                                        required
                                      />
                                    </div>
                                    <div>
                                      <label
                                        htmlFor="edit-cooldown"
                                        className="block mb-2 text-sm font-medium"
                                      >
                                        Cooldown (days)
                                      </label>
                                      <input
                                        type="number"
                                        id="edit-cooldown"
                                        name="cooldown"
                                        value={updateForm.cooldown}
                                        onChange={handleUpdateChange}
                                        className="w-full px-4 py-2 bg-[#2D2D2D] border border-[#444] rounded-md focus:outline-none focus:ring-2 focus:ring-primary h-12"
                                        min="0"
                                        required
                                      />
                                    </div>
                                  </div>
                                  <div className="flex space-x-4 mt-6">
                                    <Button
                                      intent="primary"
                                      type="submit"
                                      isDisabled={updateMutation.isPending}
                                      className="flex-1"
                                    >
                                      {updateMutation.isPending
                                        ? "Updating..."
                                        : "Update Cooldown"}
                                    </Button>
                                    <Button
                                      intent="secondary"
                                      type="button"
                                      onPress={() => {
                                        cancelEditing();
                                        // Find and click the close button programmatically
                                        const closeButton =
                                          document.querySelector(
                                            '[aria-label="Dismiss"]'
                                          ) as HTMLButtonElement;
                                        if (closeButton) closeButton.click();
                                      }}
                                      className="flex-1"
                                    >
                                      Cancel
                                    </Button>
                                  </div>
                                </form>
                              </div>
                            </Popover.Content>
                          </Popover>
                          <Popover>
                            {/* @ts-expect-error - The type error is a false positive, this pattern works correctly */}
                            <Popover.Trigger asChild>
                              <Button
                                intent="danger"
                                size="extra-small"
                                onPress={() =>
                                  setDeletingCooldownId(cooldown.id)
                                }
                                isDisabled={deleteMutation.isPending}
                              >
                                Delete
                              </Button>
                            </Popover.Trigger>
                            <Popover.Content className="p-4 w-[300px] bg-[#1E1E1E] border-[#333]">
                              <div className="space-y-4">
                                <h3 className="text-lg font-semibold">
                                  Delete Confirmation
                                </h3>
                                <p className="text-sm text-gray-400">
                                  Are you sure you want to delete this cooldown?
                                </p>
                                <div className="flex space-x-3 justify-end">
                                  <Button
                                    intent="secondary"
                                    size="small"
                                    onPress={() => {
                                      setDeletingCooldownId(null);
                                      // Find and click the close button programmatically
                                      const closeButton =
                                        document.querySelector(
                                          '[aria-label="Dismiss"]'
                                        ) as HTMLButtonElement;
                                      if (closeButton) closeButton.click();
                                    }}
                                  >
                                    Cancel
                                  </Button>
                                  <Button
                                    intent="danger"
                                    size="small"
                                    onPress={() => handleDelete(cooldown.id)}
                                    isDisabled={deleteMutation.isPending}
                                  >
                                    {deleteMutation.isPending
                                      ? "Deleting..."
                                      : "Delete"}
                                  </Button>
                                </div>
                              </div>
                            </Popover.Content>
                          </Popover>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p>No cooldowns found.</p>
          )}
        </div>

        {/* Edit Form is now in a popover */}
      </div>
    </AdminLayout>
  );
}
