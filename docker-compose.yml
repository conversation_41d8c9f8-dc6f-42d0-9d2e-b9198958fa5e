version: "3.8" # Using latest stable version

services:
  api:
    container_name: sabongsaga-api
    build:
      context: .
      dockerfile: ./apps/api/Dockerfile
      args:
        NODE_ENV: production
    restart: always
    ports:
      - "3002:3002"
    networks:
      - app_network
    env_file:
      - ./apps/api/.env
  marketplace:
    container_name: sabongsaga-marketplace
    build:
      context: .
      dockerfile: ./apps/marketplace/Dockerfile
      args:
        NODE_ENV: production
    restart: always
    ports:
      - "3004:3004"
    networks:
      - app_network
    env_file:
      - ./apps/marketplace/.env
  web:
    container_name: sabongsaga-web
    build:
      context: .
      dockerfile: ./apps/web/Dockerfile
      args:
        NODE_ENV: production
    restart: always
    ports:
      - "3000:3000"
    networks:
      - app_network
    env_file:
      - ./apps/web/.env # Fixed: Changed from api/.env to web/.env

networks:
  app_network:
    external: true
