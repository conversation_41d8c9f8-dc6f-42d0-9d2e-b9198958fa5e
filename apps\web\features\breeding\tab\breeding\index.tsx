"use client";

import BreedingButton from "./components/breeding-button";
import { BreedingCost } from "./components/breeding-cost";
import BreedingPairsContainer from "./components/breeding-pairs-container";
import BreedingReferral from "./components/breeding-referral";
import BreedingResultDialog from "./components/breeding-result.dialog";
import { BreedingSelector } from "./components/breeding-selector";
import { useOptimizedBreeding } from "./hooks/useBreeding";

export default function Breeding() {
  const { state } = useOptimizedBreeding();

  return (
    <div className="flex flex-col gap-4 md:gap-6 px-2 md:px-0">
      <div className="grid gap-8">
        <BreedingSelector
          selectedKey={state.breedOption.value}
          onSelectionChange={(v) => state.breedOption.set(v)}
        />

        <BreedingPairsContainer />

        <BreedingCost
          breedOption={state.breedOption.value}
          manualBreedingPair={state.manualBreedingPair}
          massBreedingPairs={state.massBreedingPairs}
        />

        <BreedingReferral />
        <BreedingButton />

        <BreedingResultDialog />
      </div>
    </div>
  );
}
