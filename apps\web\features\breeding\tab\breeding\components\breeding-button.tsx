"use client";

import { But<PERSON> } from "@/components/ui";
import useBlockchain from "@/lib/hooks/useBlockchain";
import useBreedingFees from "@/lib/hooks/useBreedingFees";
import useTokens from "@/lib/hooks/useTokens";
import { useStateContext } from "@/providers/app/state";
import { queryClient } from "@/providers/web3/web3-provider";
import useAuthStore from "@/store/auth";
import { useMemo } from "react";
import { Address } from "viem";
import { useOptimizedBreeding } from "../hooks/useBreeding";
import useBreedingStore from "../store/breeding";

const getReadableErrorMessage = (error: unknown): string => {
  if (!(error instanceof Error)) return "An unexpected error occurred";

  const msg = error.message.toLowerCase();

  if (msg.includes("user rejected")) {
    return "You rejected the transaction in your wallet";
  }
  if (msg.includes("insufficient funds")) {
    return "You don't have enough funds to complete this breeding";
  }
  if (msg.includes("provider not found")) {
    return "Please install or unlock your wallet";
  }
  if (msg.includes("no address received")) {
    return "Connection failed - no address received";
  }
  if (msg.includes("timeout")) {
    return "Connection timed out";
  }

  return "Failed to breed chickens. Please try again";
};

export default function BreedingButton() {
  const { state } = useOptimizedBreeding();
  const tokens = useTokens();
  const {
    isConnected,
    publicClient,
    address,
    loadingBreeding,
    loadingBreedingMessage,
    ConnectRecentWallet,
  } = useStateContext();

  const { blockchainQuery } = useBlockchain();
  const { startBreed, startBreedBatch } = useBreedingStore({
    chicken_genesis_address:
      blockchainQuery.data?.chicken_genesis_address ?? ("" as Address),
    chicken_legacy_address:
      blockchainQuery.data?.chicken_legacy_address ?? ("" as Address),
    cock_address: blockchainQuery.data?.cock_address ?? ("" as Address),
    items_address: blockchainQuery.data?.items_address ?? ("" as Address),
    breeding_address: blockchainQuery.data?.breeding_address ?? ("" as Address),
    resources_address:
      blockchainQuery.data?.resources_address ?? ("" as Address),
  })();
  const { fetchBalances } = useAuthStore();

  // Get breed counts from selected chickens
  let parent1BreedCount: number | number[] = 0;
  let parent2BreedCount: number | number[] = 0;

  if (state.breedOption.value === "manual" && state.manualBreedingPair.value) {
    // For manual breeding, use single breed counts
    parent1BreedCount =
      state.manualBreedingPair.parent1Data.value?.breedCount || 0;
    parent2BreedCount =
      state.manualBreedingPair.parent2Data.value?.breedCount || 0;
  } else if (
    state.breedOption.value === "mass" &&
    state.massBreedingPairs.length > 0
  ) {
    // For mass breeding, calculate the total cost for all pairs
    // We'll fetch the fees for each pair and sum them up in the hook
    const pairs = state.massBreedingPairs.get();

    // Create arrays to hold all breed counts
    const parent1BreedCounts: number[] = [];
    const parent2BreedCounts: number[] = [];

    // Collect breed counts from all pairs
    pairs.forEach((pair) => {
      if (pair.parent1Data && pair.parent2Data) {
        parent1BreedCounts.push(pair.parent1Data.breedCount || 0);
        parent2BreedCounts.push(pair.parent2Data.breedCount || 0);
      }
    });

    // Use arrays of breed counts for mass breeding
    if (parent1BreedCounts.length > 0 && parent2BreedCounts.length > 0) {
      parent1BreedCount = parent1BreedCounts;
      parent2BreedCount = parent2BreedCounts;
    }
  }

  // Use the breeding fees hook at the component level
  const { cockFee, featherFee } = useBreedingFees(
    parent1BreedCount,
    parent2BreedCount
  );

  // Check if user has enough tokens for breeding
  const hasEnoughTokens = useMemo(() => {
    // If no chickens are selected, return false
    if (
      state.breedOption.value === "manual" &&
      (!state.manualBreedingPair.parent1.value ||
        !state.manualBreedingPair.parent2.value)
    ) {
      return false;
    }

    if (
      state.breedOption.value === "mass" &&
      state.massBreedingPairs
        .get()
        .some((pair) => !pair.parent1 || !pair.parent2)
    ) {
      return false;
    }

    // Check if user has enough tokens based on the calculated fees
    const hasEnoughCock = tokens.cock.formatted >= cockFee;
    const hasEnoughFeathers = tokens.feather.formatted >= featherFee;

    return hasEnoughCock && hasEnoughFeathers;
  }, [
    state.breedOption,
    state.manualBreedingPair,
    state.massBreedingPairs,
    tokens,
    cockFee,
    featherFee,
  ]);

  const handleBreed = async () => {
    try {
      await ConnectRecentWallet();

      loadingBreedingMessage.set(
        "Breeding’s not gae if you say 'no homo' first, OK? 😉"
      );
      loadingBreeding.set(true);

      if (state.breedOption.value === "manual") {
        await startBreed(
          state.manualBreedingPair.parent1.value,
          state.manualBreedingPair.parent2.value,
          state.savedReferralCode.value
        );
      } else {
        await startBreedBatch(
          state.massBreedingPairs.get().map((pair) => ({
            chickenLeftTokenId: pair.parent1,
            chickenRightTokenId: pair.parent2,
          })),
          state.savedReferralCode.value
        );
      }

      // Reset breeding pair states
      if (state.breedOption.value === "manual") {
        state.manualBreedingPair.set({
          id: 1,
          parent1: 0,
          parent2: 0,
          breedingItem: 0,
          parent1Data: undefined,
          parent2Data: undefined,
        });
      } else {
        state.massBreedingPairs.set([
          {
            id: 1,
            parent1: 0,
            parent2: 0,
            breedingItem: 0,
            parent1Data: undefined,
            parent2Data: undefined,
          },
        ]);
      }

      // Refetch balances and chickens data
      await Promise.all([
        fetchBalances(isConnected, publicClient, address),
        queryClient.invalidateQueries({
          queryKey: ["chickenTokenIds"],
        }),
        queryClient.invalidateQueries({
          queryKey: ["ninunoRewards"],
        }),
      ]);

      if (blockchainQuery.data?.cock_address) {
        queryClient.invalidateQueries({
          queryKey: ["readContract"],
        });
      }

      state.dialog.breed.set({
        isOpen: true,
        type: "success",
        title: "Breeding Successful!",
        description: "Check Hatching to see your new egg! 🥚",
      });
    } catch (error) {
      state.dialog.breed.set({
        isOpen: true,
        type: "error",
        title: "Breeding Failed",
        description: getReadableErrorMessage(error),
      });
    } finally {
      loadingBreeding.set(false);
    }
  };

  return (
    <div className="grid pb-4 place-items-center gap-4">
      <Button
        className="bg-yellow-500 w-full sm:w-auto sm:px-40 hover:bg-yellow-600 text-black font-bold py-3 rounded-lg max-w-[90vw] md:max-w-none"
        onPress={handleBreed}
        isDisabled={
          (state.breedOption.value === "manual"
            ? !state.manualBreedingPair.parent1.value ||
              !state.manualBreedingPair.parent2.value
            : state.massBreedingPairs
                .get()
                .some((pair) => !pair.parent1 || !pair.parent2)) ||
          !hasEnoughTokens
        }
        // isDisabled={true}
      >
        BREED
      </Button>
    </div>
  );
}
