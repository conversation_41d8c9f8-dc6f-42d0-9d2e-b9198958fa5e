"use client";

import React from "react";
import { Card, Badge } from "ui";
import {
  Check,
  Users,
  Clock,
  Coins,
  Shield,
  Calculator,
  Key,
  ChevronDown,
  ChevronUp,
  Info,
} from "lucide-react";
import { cn } from "@/utils/classes";
import {
  ICreateRentalFormData,
  EDelegatedTaskType,
  GAME_REWARD_DISTRIBUTION_LABELS,
  ISelectedChickenInfo,
} from "../../types/delegation.types";

interface IRentalSummaryReceiptProps {
  formData: ICreateRentalFormData;
  selectedChickensInfo: ISelectedChickenInfo[];
  isBulkMode: boolean;
  selectedChicken?: any;
  formatPrice: (price: string) => string;
  calculateInsuranceAmount?: () => number;
  calculateTotalRenterCost?: () => number;
  chickenNeedsInsurance?: () => boolean;
  isCustomDuration?: boolean;
  customDuration?: { days: number };
  durationOptions?: Array<{ value: number; label: string }>;
}

export function RentalSummaryReceipt({
  formData,
  selectedChickensInfo,
  isBulkMode,
  selectedChicken,
  formatPrice,
  calculateInsuranceAmount,
  calculateTotalRenterCost,
  chickenNeedsInsurance,
  isCustomDuration,
  customDuration,
  durationOptions,
}: IRentalSummaryReceiptProps) {
  const [showCalculations, setShowCalculations] = React.useState(false);

  // Calculate values for bulk mode
  const dailyRate = parseFloat(formData.roninPrice || "0");
  const durationInDays = formData.rentalPeriod / 86400;
  const totalChickens = isBulkMode ? selectedChickensInfo.length : 1;

  // Calculate chicken types
  const chickensNeedingInsurance = isBulkMode
    ? selectedChickensInfo.filter((chicken) => {
        if (!chicken.metadata?.attributes) return false;
        const typeAttribute = chicken.metadata.attributes.find(
          (attr: any) => attr.trait_type === "Type"
        );
        return (
          typeAttribute?.value === "Legacy" ||
          typeAttribute?.value === "Ordinary"
        );
      })
    : chickenNeedsInsurance?.()
      ? [selectedChicken]
      : [];

  const genesisCount = totalChickens - chickensNeedingInsurance.length;
  const legacyCount = chickensNeedingInsurance.length;

  // Calculate costs
  const rentalCostPerChicken = dailyRate * durationInDays;
  const totalRentalCost = rentalCostPerChicken * totalChickens;

  const customInsurance = parseFloat(formData.insurancePrice || "0");
  const insurancePerChicken = customInsurance;
  const totalInsurance = insurancePerChicken * chickensNeedingInsurance.length;

  const finalTotal = isBulkMode
    ? totalRentalCost + totalInsurance
    : calculateTotalRenterCost?.() || 0;

  // Format duration display
  const getDurationDisplay = () => {
    if (isCustomDuration) {
      return customDuration?.days && customDuration.days > 0
        ? `${customDuration.days} day${customDuration.days > 1 ? "s" : ""}`
        : "Not set";
    }
    return (
      durationOptions?.find((d) => d.value === formData.rentalPeriod)?.label ||
      "Not set"
    );
  };

  // Get access level display
  const getAccessDisplay = () => {
    switch (formData.delegatedTask) {
      case EDelegatedTaskType.BOTH:
        return "Full Access";
      case EDelegatedTaskType.DAILY_RUB:
        return "Daily Rub Only";
      default:
        return "Gameplay Only";
    }
  };

  return (
    <div className="space-y-4">
      {/* Status Header */}
      <div className="relative overflow-hidden">
        <div className="flex items-center justify-center gap-2 p-4 bg-gradient-to-r from-green-500/10 via-green-400/10 to-green-500/10 border border-green-500/30 rounded-lg backdrop-blur-sm">
          <div className="flex items-center justify-center w-8 h-8 bg-green-500/20 rounded-full">
            <Check className="w-5 h-5 text-green-400" />
          </div>
          <span className="text-green-400 font-semibold text-lg">
            Ready to Create
          </span>
        </div>
        {/* Subtle glow effect */}
        <div className="absolute inset-0 bg-green-400/5 rounded-lg blur-xl -z-10"></div>
      </div>

      {/* Quick Summary Cards - Only for rental listings */}
      {!formData.isDirectDelegation && (
        <div className="grid grid-cols-3 gap-3">
          <div className="text-center p-3 bg-stone-700/30 rounded-lg border border-stone-600/30">
            <p className="text-gray-400 text-xs mb-1">Chickens</p>
            <p className="text-white font-bold text-lg">{totalChickens}</p>
          </div>
          <div className="text-center p-3 bg-stone-700/30 rounded-lg border border-stone-600/30">
            <p className="text-gray-400 text-xs mb-1">Duration</p>
            <p className="text-white font-bold text-lg">{durationInDays}d</p>
          </div>
          <div className="text-center p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/30">
            <p className="text-gray-400 text-xs mb-1">Total</p>
            <p className="text-yellow-400 font-bold text-lg">
              {finalTotal.toFixed(2)}
            </p>
            <p className="text-gray-500 text-xs">RON</p>
          </div>
        </div>
      )}

      {/* Selection Summary */}
      <Card className="bg-gradient-to-br from-stone-800/60 to-stone-900/40 border-stone-600/50 shadow-lg">
        <Card.Header className="pb-3">
          <div className="flex items-center gap-2">
            <div className="flex items-center justify-center w-6 h-6 bg-blue-500/20 rounded-full">
              <Users className="w-4 h-4 text-blue-400" />
            </div>
            <span className="text-white font-medium">Selection Summary</span>
          </div>
        </Card.Header>
        <Card.Content className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 bg-stone-700/30 rounded-lg border border-stone-600/30">
              <p className="text-gray-400 text-sm mb-1">Chickens</p>
              <p className="text-white font-semibold text-lg">
                {isBulkMode
                  ? `${selectedChickensInfo.length} selected`
                  : `#${selectedChicken?.tokenId || "Not selected"}`}
              </p>
            </div>
            <div className="p-3 bg-stone-700/30 rounded-lg border border-stone-600/30">
              <p className="text-gray-400 text-sm mb-1">Type</p>
              <Badge
                intent={formData.isDirectDelegation ? "info" : "primary"}
                shape="square"
                className="text-sm"
              >
                {formData.isDirectDelegation
                  ? "Direct Delegation"
                  : "Rental Listing"}
              </Badge>
            </div>
          </div>

          {isBulkMode && (legacyCount > 0 || genesisCount > 0) && (
            <div className="pt-2 border-t border-stone-600">
              <p className="text-gray-400 text-sm mb-2">Chicken Types</p>
              <div className="flex gap-2 flex-wrap">
                {legacyCount > 0 && (
                  <Badge intent="warning" shape="square">
                    {legacyCount} Legacy/Ordinary
                  </Badge>
                )}
                {genesisCount > 0 && (
                  <Badge intent="success" shape="square">
                    {genesisCount} Genesis
                  </Badge>
                )}
              </div>
            </div>
          )}
        </Card.Content>
      </Card>

      {/* Rental Details */}
      {!formData.isDirectDelegation && (
        <Card className="bg-gradient-to-br from-yellow-900/20 to-orange-900/10 border-yellow-600/30 shadow-lg">
          <Card.Header className="pb-3">
            <div className="flex items-center gap-2">
              <div className="flex items-center justify-center w-6 h-6 bg-yellow-500/20 rounded-full">
                <Coins className="w-4 h-4 text-yellow-400" />
              </div>
              <span className="text-white font-medium">Rental Details</span>
            </div>
          </Card.Header>
          <Card.Content className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="p-3 bg-yellow-500/5 rounded-lg border border-yellow-600/20">
                <div className="flex items-center gap-1 mb-1">
                  <p className="text-gray-400 text-sm">Daily Rate</p>
                  <Info className="w-3 h-3 text-gray-500" />
                </div>
                <p className="text-yellow-300 font-bold text-xl">
                  {dailyRate.toFixed(4)} RON
                </p>
                <p className="text-gray-500 text-xs">per chicken per day</p>
              </div>
              <div className="p-3 bg-blue-500/5 rounded-lg border border-blue-600/20">
                <p className="text-gray-400 text-sm mb-1">Duration</p>
                <p className="text-blue-300 font-semibold text-lg">
                  {getDurationDisplay()}
                </p>
                <p className="text-gray-500 text-xs">
                  {durationInDays} day{durationInDays !== 1 ? "s" : ""}
                </p>
              </div>
            </div>

            <div className="p-3 bg-green-500/5 rounded-lg border border-green-600/20">
              <div className="flex items-center gap-2 mb-2">
                <div className="flex items-center justify-center w-5 h-5 bg-green-500/20 rounded-full">
                  <Key className="w-3 h-3 text-green-400" />
                </div>
                <span className="text-gray-400 text-sm">Access Level</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-green-400" />
                <span className="text-green-300 font-medium">
                  {getAccessDisplay()}
                </span>
              </div>
            </div>

            {/* Game Reward Distribution - Only show when gameplay is enabled */}
            {(formData.delegatedTask === EDelegatedTaskType.GAMEPLAY ||
              formData.delegatedTask === EDelegatedTaskType.BOTH) && (
              <div className="p-3 bg-purple-500/5 rounded-lg border border-purple-600/20">
                <div className="flex items-center gap-2 mb-2">
                  <div className="flex items-center justify-center w-5 h-5 bg-purple-500/20 rounded-full">
                    <span className="text-purple-400 text-xs">🎮</span>
                  </div>
                  <span className="text-gray-400 text-sm">Game Rewards</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-purple-400" />
                  <span className="text-purple-300 font-medium">
                    {
                      GAME_REWARD_DISTRIBUTION_LABELS[
                        formData.gameRewardDistribution
                      ]
                    }
                  </span>
                </div>
                <p className="text-gray-500 text-xs mt-1">
                  Who receives crystals, shards, and corn from gameplay
                </p>
              </div>
            )}
          </Card.Content>
        </Card>
      )}

      {/* Cost Breakdown */}
      {!formData.isDirectDelegation && (
        <Card className="bg-gradient-to-br from-purple-900/20 to-indigo-900/10 border-purple-600/30 shadow-lg">
          <Card.Header className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="flex items-center justify-center w-6 h-6 bg-purple-500/20 rounded-full">
                  <Calculator className="w-4 h-4 text-purple-400" />
                </div>
                <span className="text-white font-medium">Cost Breakdown</span>
              </div>
              <button
                onClick={() => setShowCalculations(!showCalculations)}
                className="flex items-center gap-1 px-2 py-1 text-gray-400 hover:text-white hover:bg-purple-500/10 rounded-md transition-all duration-200"
              >
                <span className="text-xs font-medium">Details</span>
                {showCalculations ? (
                  <ChevronUp className="w-3 h-3" />
                ) : (
                  <ChevronDown className="w-3 h-3" />
                )}
              </button>
            </div>
          </Card.Header>
          <Card.Content className="space-y-4">
            {/* Rental Cost */}
            <div className="p-3 bg-blue-500/5 rounded-lg border border-blue-600/20">
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-2">
                  <Coins className="w-4 h-4 text-blue-400" />
                  <span className="text-gray-300 font-medium">Rental Cost</span>
                </div>
                <span className="text-blue-300 font-bold text-lg">
                  {isBulkMode
                    ? totalRentalCost.toFixed(4)
                    : rentalCostPerChicken.toFixed(4)}{" "}
                  RON
                </span>
              </div>

              {showCalculations && (
                <div className="text-xs text-gray-400 space-y-1 pl-6 border-l-2 border-blue-500/20">
                  <p className="flex items-center gap-1">
                    <span className="w-1 h-1 bg-blue-400 rounded-full"></span>
                    {dailyRate.toFixed(4)} RON/day × {durationInDays} day
                    {durationInDays !== 1 ? "s" : ""} ={" "}
                    <span className="text-blue-300 font-medium">
                      {rentalCostPerChicken.toFixed(4)} RON per chicken
                    </span>
                  </p>
                  {isBulkMode && (
                    <p className="flex items-center gap-1">
                      <span className="w-1 h-1 bg-blue-400 rounded-full"></span>
                      {rentalCostPerChicken.toFixed(4)} RON × {totalChickens}{" "}
                      chickens ={" "}
                      <span className="text-blue-300 font-medium">
                        {totalRentalCost.toFixed(4)} RON
                      </span>
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Insurance */}
            {(chickensNeedingInsurance.length > 0 || isBulkMode) && (
              <div className="p-3 bg-orange-500/5 rounded-lg border border-orange-600/20">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center gap-2">
                    <Shield className="w-4 h-4 text-orange-400" />
                    <span className="text-gray-300 font-medium">Insurance</span>
                  </div>
                  <span className="text-orange-300 font-bold text-lg">
                    {totalInsurance.toFixed(4)} RON
                  </span>
                </div>

                {showCalculations && (
                  <div className="text-xs text-gray-400 space-y-1 pl-6 border-l-2 border-orange-500/20">
                    {legacyCount > 0 && (
                      <p className="flex items-center gap-1">
                        <span className="w-1 h-1 bg-orange-400 rounded-full"></span>
                        Legacy/Ordinary: {legacyCount} ×{" "}
                        {insurancePerChicken.toFixed(4)} RON ={" "}
                        <span className="text-orange-300 font-medium">
                          {(legacyCount * insurancePerChicken).toFixed(4)} RON
                        </span>
                      </p>
                    )}
                    {genesisCount > 0 && (
                      <p className="flex items-center gap-1">
                        <span className="w-1 h-1 bg-green-400 rounded-full"></span>
                        Genesis: {genesisCount} × 0.0000 RON ={" "}
                        <span className="text-green-300 font-medium">
                          0.0000 RON
                        </span>
                      </p>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Total */}
            <div className="relative overflow-hidden">
              <div className="p-4 bg-gradient-to-r from-yellow-500/10 via-yellow-400/10 to-yellow-500/10 border-2 border-yellow-500/30 rounded-lg backdrop-blur-sm">
                <div className="flex justify-between items-center">
                  <span className="text-yellow-300 font-semibold text-lg">
                    Total
                  </span>
                  <span className="text-yellow-400 font-bold text-2xl">
                    {finalTotal.toFixed(4)} RON
                  </span>
                </div>

                {showCalculations && (
                  <div className="text-xs text-yellow-200/70 mt-2 flex items-center gap-1">
                    <span className="w-1 h-1 bg-yellow-400 rounded-full"></span>
                    {isBulkMode
                      ? totalRentalCost.toFixed(4)
                      : rentalCostPerChicken.toFixed(4)}{" "}
                    RON rental + {totalInsurance.toFixed(4)} RON insurance
                  </div>
                )}
              </div>
              {/* Subtle glow effect */}
              <div className="absolute inset-0 bg-yellow-400/5 rounded-lg blur-xl -z-10"></div>
            </div>
          </Card.Content>
        </Card>
      )}
    </div>
  );
}
