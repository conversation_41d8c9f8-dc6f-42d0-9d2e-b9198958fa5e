import connectMongoDb from "@/services/mongo-client";
import {
  AllocationData,
  AllocationBreakdown,
  DelegateData,
} from "@/types/allocation.type";

import { NextRequest, NextResponse } from "next/server";

type ReturnedData = {
  address: string;
  totalChickens: number;
  totalAllocations: number;
  totalDelegated: number;
  breakdown: AllocationBreakdown[];
  delegated: DelegateData[];
};

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const address = searchParams.get("address")?.toLowerCase();

  if (!address) {
    return NextResponse.json(
      { error: "Address parameter is required" },
      { status: 400 }
    );
  }

  let client;
  try {
    // Connect to MongoDB
    client = await connectMongoDb();
    const db = client.db(process.env.MONGODB_DB);

    const [allocationData, delegatees, delegates] = await Promise.all([
      db.collection<AllocationData>("allocations").findOne({ address }),
      db
        .collection<DelegateData>("delegates")
        .find({ delegatedBy: address, isRemoved: false })
        .toArray(),
      db
        .collection<DelegateData>("delegates")
        .find({ address, isRemoved: false })
        .toArray(),
    ]);

    const totalDelegated = delegatees.reduce(
      (sum, delegate) => sum + delegate.amount,
      0
    );

    const delegate = delegates.reduce(
      (sum, delegate) => sum + delegate.amount,
      0
    );

    const returnedData: ReturnedData = {
      address,
      totalChickens: 0,
      totalAllocations: 0,
      totalDelegated: 0,
      breakdown: [],
      delegated: [],
    };

    if (allocationData) {
      returnedData.totalAllocations += allocationData.totalAllocations;
      returnedData.breakdown = allocationData.breakdown;
      returnedData.totalChickens = allocationData.totalChickens
    }

    if (delegates) {
      returnedData.delegated = delegates;
    }

    if (delegate) {
      returnedData.totalAllocations += delegate;
    }

    if (totalDelegated) {
      returnedData.totalDelegated += totalDelegated;
    }
    

    return NextResponse.json(returnedData, { status: 200 });
  } catch (error) {
    console.error("Error fetching allocation data:", error);
    return NextResponse.json(
      { error: "Failed to fetch allocation data" },
      { status: 500 }
    );
  } finally {
    if (client) {
      await client.close();
    }
  }
}
