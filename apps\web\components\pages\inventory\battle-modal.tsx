import Image from "next/image";
import { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import {
  Swords,
  Star,
  Crosshair,
  Shield,
  Zap,
  Activity,
  AlertTriangle,
  Trophy,
  Loader2,
  Flame,
  Axe,
  Wind,
  Skull,
  Heart,
  Award,
  CheckCircle,
  Clock,
  Users,
  X,
} from "lucide-react";
import useMatchmakingStore from "@/store/match-making";
import useAuthStore from "@/store/auth";
import {
  BattleItemSlots,
  BattleItemGrid,
  IncompleteSelectionModal,
} from "@/components/shared/battle-items";
import { BATTLE_ITEMS, getBattleItemById } from "@/data/battle-items";

// Server URL - in production use environment variable
const SERVER_URL =
  process.env.NEXT_PUBLIC_GAME_SERVER_URL || "ws://localhost:2567";

type ChickenStats = {
  level?: number;
  attack?: number;
  defense?: number;
  speed?: number;
  ferocity?: number;
  cockrage?: number;
  evasion?: number;
  hp?: number;
  maxHp?: number;
  currentHp?: number;
  boosters?: {
    attack?: number;
    defense?: number;
    speed?: number;
    hp?: number;
    cockrage?: number;
    ferocity?: number;
    evasion?: number;
  };
};

type BattleModalProps = {
  isOpen: boolean;
  onClose: () => void;
  chickenId: string;
  chickenImage?: string;
  chickenStats: ChickenStats;
};

export default function BattleModal({
  isOpen,
  onClose,
  chickenId,
  chickenImage,
  chickenStats,
}: BattleModalProps) {
  // Matchmaking state
  const {
    isConnected,
    isConnecting,
    connectionError,
    isInQueue,
    queuePosition,
    waitingCount,
    matchFound,
    matchCode,
    gameUrl,
    currentPhase,
    statusMessage,
    setSelectedFighter,
    setAddress,
    setBattleVerification,
    connectToServer,
    joinMatchmaking,
    leaveMatchmaking,
    resetMatchState,
    resetBattleItems,
    getIncompleteSlots,
    selectedBattleItems,
    setBattleItem,
    clearBattleItem,
    isItemSelected,
    getNextAvailableSlot,
  } = useMatchmakingStore();

  const { address } = useAuthStore();

  // Local state for UI stages
  const [stage, setStage] = useState<
    | "ready"
    | "connecting"
    | "authenticating"
    | "verifying_ownership"
    | "loading_chicken"
    | "queuing"
    | "matching"
    | "creating_battle"
    | "matched"
  >("ready");

  // State for incomplete selection modal
  const [showIncompleteModal, setShowIncompleteModal] = useState(false);

  // Set the selected fighter ID when component mounts
  useEffect(() => {
    if (isOpen && chickenId && address) {
      // Convert chickenId from string to number for the matchmaking system
      setSelectedFighter(parseInt(chickenId, 10));
      setAddress(address);
    }
  }, [isOpen, chickenId, address, setSelectedFighter]);

  // Handle connection and matchmaking flow
  useEffect(() => {
    if (isConnecting) {
      setStage("connecting");
    } else if (isInQueue) {
      // Use the server's current phase for more accurate state
      const serverPhase = currentPhase;
      if (serverPhase === "authenticating") {
        setStage("authenticating");
      } else if (serverPhase === "verifying_ownership") {
        setStage("verifying_ownership");
      } else if (serverPhase === "loading_chicken") {
        setStage("loading_chicken");
      } else if (serverPhase === "matching") {
        setStage("matching");
      } else if (serverPhase === "creating_battle") {
        setStage("creating_battle");
      } else {
        setStage("queuing");
      }
    } else if (matchFound) {
      setStage("matched");
    }
  }, [isConnecting, isInQueue, matchFound, currentPhase]);

  // Handle match found - redirect to game
  useEffect(() => {
    if (matchFound && matchCode && gameUrl) {
      // Use setTimeout to ensure UI updates before redirect
      const redirectTimer = setTimeout(() => {
        // Store token in localStorage (or could use cookies)

        // Redirect to game
        window.location.href = `${gameUrl}?code=${encodeURIComponent(matchCode)}`;
      }, 1500);

      return () => clearTimeout(redirectTimer);
    }
  }, [matchFound, matchCode, gameUrl]);

  // Cleanup on unmount or close
  useEffect(() => {
    return () => {
      // Cleanup on component unmount
      if (!isOpen) {
        // If user closes modal while in queue, leave the queue
        if (isInQueue) {
          leaveMatchmaking();
        }
        // Reset state
        resetMatchState();
        resetBattleItems();
        setStage("ready");
      }
    };
  }, [isOpen, isInQueue, leaveMatchmaking, resetMatchState]);

  // Start battle with Battle API verification
  const handleStartBattle = async () => {
    // Check if there are incomplete battle item selections
    const incompleteSlots = getIncompleteSlots();
    if (incompleteSlots.length > 0) {
      setShowIncompleteModal(true);
      return;
    }

    // Proceed with battle start
    await startBattleProcess();
  };

  // Separate function to handle the actual battle start process
  const startBattleProcess = async () => {
    // First establish connection to the server
    if (!isConnected) {
      await connectToServer(SERVER_URL);
    }

    // Then join matchmaking queue
    joinMatchmaking();
  };

  // Handle battle item selection/deselection
  const handleItemClick = (itemId: number) => {
    // Check if item is already selected
    if (isItemSelected(itemId)) {
      // Deselect the item by finding its slot and clearing it
      const slotIndex = selectedBattleItems.findIndex(
        (item) => item === itemId
      );
      if (slotIndex !== -1) {
        clearBattleItem(slotIndex);
      }
      return;
    }

    // Find next available slot for selection
    const nextSlot = getNextAvailableSlot();
    if (nextSlot !== null) {
      setBattleItem(nextSlot, itemId);
    }
  };

  const handleCancel = () => {
    // Leave matchmaking if in queue
    if (isInQueue) {
      leaveMatchmaking();
    }

    // Reset state
    resetMatchState();
    resetBattleItems();
    setStage("ready");

    // Close modal
    onClose();
  };

  if (!isOpen) return null;

  // Helper function to get HP color based on percentage
  const getHpColor = (current: number = 0, max: number = 100) => {
    const percentage = (current / max) * 100;
    if (percentage > 70) return "bg-green-500";
    if (percentage > 30) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Normalize stats to handle different property names
  const normalizedStats = {
    level: chickenStats.level || 1,
    attack: chickenStats.attack || 0,
    defense: chickenStats.defense || 0,
    speed: chickenStats.speed || 0,
    ferocity: chickenStats.ferocity || 0,
    cockrage: chickenStats.cockrage || 0,
    evasion: chickenStats.evasion || 0,
    currentHp: chickenStats.currentHp || chickenStats.hp || 100,
    maxHp: chickenStats.maxHp || 100,
    boosters: chickenStats.boosters || {},
  };

  if (!isOpen) return null;

  const modalContent = (
    <div
      className="fixed top-0 left-0 right-0 bottom-0 bg-black/80 flex items-center justify-center p-4 backdrop-blur-sm"
      onClick={onClose}
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100vw",
        height: "100vh",
        zIndex: 99999,
        margin: 0,
        padding: "16px",
      }}
    >
      <div
        className={`bg-gray-800 border border-gray-700 rounded-lg shadow-xl w-full max-h-[95vh] overflow-hidden animate-fadeIn flex flex-col ${
          stage === "ready" ? "max-w-7xl" : "max-w-lg"
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="bg-gradient-to-r from-red-600 to-red-700 p-4 border-b border-red-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Swords className="h-5 w-5 text-white mr-3" />
              <h3 className="text-xl font-bold text-white">
                {stage === "ready" && "Prepare for Battle"}
                {stage === "connecting" && "Connecting to Server"}
                {stage === "authenticating" && "Authenticating..."}
                {stage === "verifying_ownership" && "Verifying Ownership..."}
                {stage === "loading_chicken" && "Loading Chicken Data..."}
                {stage === "queuing" && "Finding Opponent"}
                {stage === "matching" && "Analyzing Matches..."}
                {stage === "creating_battle" && "Creating Battle..."}
                {stage === "matched" && "Match Found!"}
              </h3>
            </div>

            {stage === "ready" && (
              <div className="text-white text-sm font-medium">
                {selectedBattleItems.filter((item) => item !== -1).length}/3
                items selected
              </div>
            )}
          </div>

          {stage === "ready" && (
            <div className="mt-2 text-red-100 text-sm">
              <CheckCircle className="h-4 w-4 mr-2 inline" />
              Select up to 3 battle items (optional) and chicken stats are shown
              for strategy
            </div>
          )}

          {/* Other stages descriptions */}
          {stage !== "ready" && (
            <div className="mt-2 flex items-center text-sm text-red-200">
              {stage === "connecting" && (
                <>
                  <Clock className="h-4 w-4 mr-1 animate-spin" /> Establishing
                  connection...
                </>
              )}
              {stage === "authenticating" && (
                <>
                  <Clock className="h-4 w-4 mr-1 animate-spin" /> Authenticating
                  player...
                </>
              )}
              {stage === "verifying_ownership" && (
                <>
                  <Clock className="h-4 w-4 mr-1 animate-spin" /> Verifying
                  chicken ownership...
                </>
              )}
              {stage === "loading_chicken" && (
                <>
                  <Clock className="h-4 w-4 mr-1 animate-spin" /> Loading
                  chicken data...
                </>
              )}
              {stage === "queuing" && (
                <>
                  <Users className="h-4 w-4 mr-1" /> Position {queuePosition} of{" "}
                  {waitingCount} players
                </>
              )}
              {stage === "matching" && (
                <>
                  <Clock className="h-4 w-4 mr-1 animate-spin" /> Analyzing
                  player ratings...
                </>
              )}
              {stage === "creating_battle" && (
                <>
                  <Clock className="h-4 w-4 mr-1 animate-spin" /> Creating
                  battle room...
                </>
              )}
              {stage === "matched" && (
                <>
                  <Trophy className="h-4 w-4 mr-1" /> Opponent found! Preparing
                  battle...
                </>
              )}
              {/* Show server status message if available */}
              {statusMessage && (
                <span className="ml-2 text-xs text-red-300">
                  ({statusMessage})
                </span>
              )}
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="p-6 flex-1 overflow-y-auto">
          {connectionError && (
            <div className="bg-red-900/30 border border-red-700/50 rounded-lg p-4 mb-6">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-red-400 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-red-300 font-medium">Connection Error</p>
                  <p className="text-red-200/80 text-sm mt-1">
                    {connectionError}
                  </p>
                </div>
              </div>
            </div>
          )}

          {stage === "ready" && (
            <div className="grid grid-cols-1 lg:grid-cols-6 gap-6 h-full">
              {/* Left Panel - Your Fighter (Even Narrower) */}
              <div className="lg:col-span-2 bg-slate-800/50 rounded-lg border border-slate-600/50 p-4 self-start">
                <div className="flex items-center mb-3">
                  <Star className="h-4 w-4 text-yellow-400 mr-2" />
                  <h3 className="text-base font-semibold text-white">
                    Your Fighter
                  </h3>
                </div>

                {/* Chicken Image and Basic Info - Side by Side */}
                <div className="flex items-center space-x-3 mb-4">
                  <div className="relative w-20 h-20 rounded-lg overflow-hidden bg-gray-700 flex-shrink-0 border-2 border-orange-500">
                    {chickenImage ? (
                      <Image
                        src={chickenImage}
                        alt={`Chicken #${chickenId}`}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <span className="text-gray-500 text-xs">No image</span>
                      </div>
                    )}
                  </div>

                  <div className="flex-1">
                    <h4 className="text-base font-bold text-yellow-400">
                      Chicken #{chickenId}
                    </h4>
                    <p className="text-gray-400 text-sm">
                      Level {normalizedStats.level}
                    </p>
                  </div>
                </div>

                {/* Compact Health Bar */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-green-400 text-xs flex items-center">
                      <Activity className="h-3 w-3 mr-1" />
                      HP
                    </span>
                    <span className="text-white text-xs">
                      {Math.round(normalizedStats.currentHp)}/
                      {Math.round(normalizedStats.maxHp)}
                    </span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full transition-all duration-500"
                      style={{
                        width: `${(normalizedStats.currentHp / normalizedStats.maxHp) * 100}%`,
                      }}
                    />
                  </div>
                </div>

                {/* Compact Stats - 2 Column Grid */}
                <div className="grid grid-cols-2 gap-x-3 gap-y-1.5 text-xs">
                  <div className="flex items-center justify-between">
                    <span className="text-red-400 flex items-center">
                      <Crosshair className="h-3 w-3 mr-1" />
                      ATK
                    </span>
                    <span className="text-white font-medium">
                      {Math.round(normalizedStats.attack)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-blue-400 flex items-center">
                      <Shield className="h-3 w-3 mr-1" />
                      DEF
                    </span>
                    <span className="text-white font-medium">
                      {Math.round(normalizedStats.defense)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-yellow-400 flex items-center">
                      <Zap className="h-3 w-3 mr-1" />
                      SPD
                    </span>
                    <span className="text-white font-medium">
                      {Math.round(normalizedStats.speed)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-orange-400 flex items-center">
                      <Axe className="h-3 w-3 mr-1" />
                      FER
                    </span>
                    <span className="text-white font-medium">
                      {Math.round(normalizedStats.ferocity)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-purple-400 flex items-center">
                      <Flame className="h-3 w-3 mr-1" />
                      CKR
                    </span>
                    <span className="text-white font-medium">
                      {Math.round(normalizedStats.cockrage)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-green-400 flex items-center">
                      <Wind className="h-3 w-3 mr-1" />
                      EVA
                    </span>
                    <span className="text-white font-medium">
                      {Math.round(normalizedStats.evasion)}
                    </span>
                  </div>
                </div>

                {/* Compact Battle Info */}
                <div className="mt-4 space-y-2">
                  <div className="flex items-center text-xs">
                    <Trophy className="h-3 w-3 text-yellow-400 mr-1.5" />
                    <span className="text-gray-300">
                      Win for materials & leaderboard points
                    </span>
                  </div>
                  <div className="flex items-center text-xs">
                    <AlertTriangle className="h-3 w-3 text-yellow-400 mr-1.5" />
                    <span className="text-gray-300">
                      Risk: Chickens can faint or die without Ambrosia
                    </span>
                  </div>
                </div>
              </div>

              {/* Right Panel - Battle Items (Much Larger) */}
              <div className="lg:col-span-4 bg-slate-800/50 rounded-lg border border-slate-600/50 p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <Shield className="h-5 w-5 text-blue-400 mr-2" />
                    <h3 className="text-lg font-semibold text-white">
                      Battle Items (
                      {selectedBattleItems.filter((item) => item !== -1).length}
                      /3) - Optional
                    </h3>
                  </div>
                </div>

                <p className="text-gray-400 text-sm mb-4">
                  Selected:{" "}
                  <span className="text-blue-400">
                    Optional - Choose up to 3 items for strategic advantage
                  </span>
                  <br />
                  <span className="text-yellow-300 text-xs">
                    Only one item will be active per round
                  </span>
                </p>

                {/* Selection Slots - Horizontal layout like in image */}
                <div className="flex space-x-4 mb-6">
                  {[0, 1, 2].map((slotIndex) => {
                    const itemId = selectedBattleItems[slotIndex];
                    const item =
                      itemId !== -1 && itemId !== undefined
                        ? getBattleItemById(itemId)
                        : null;

                    return (
                      <div
                        key={slotIndex}
                        className="w-16 h-16 border-2 border-dashed border-gray-500 rounded-lg flex items-center justify-center bg-gray-700/50 relative"
                      >
                        {item ? (
                          <div className="relative w-full h-full">
                            <Image
                              src={item.image}
                              alt={item.name}
                              fill
                              className="object-cover rounded"
                            />
                            <button
                              onClick={() => clearBattleItem(slotIndex)}
                              className="absolute -top-1 -right-1 bg-red-600 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs hover:bg-red-700"
                            >
                              ×
                            </button>
                          </div>
                        ) : (
                          <span className="text-gray-400 font-bold text-lg">
                            {slotIndex + 1}
                          </span>
                        )}
                      </div>
                    );
                  })}
                </div>

                {/* Battle Items Grid - Scrollable */}
                <div className="h-96 overflow-y-auto pr-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {BATTLE_ITEMS.map((item) => {
                      const isSelected = isItemSelected(item.id);
                      const isDisabled =
                        !isSelected && getNextAvailableSlot() === null;

                      return (
                        <div
                          key={item.id}
                          className={`
                            rounded-lg p-3 border cursor-pointer transition-all duration-200 relative
                            ${
                              isSelected
                                ? "border-yellow-400 bg-yellow-400/15 shadow-lg shadow-yellow-400/20"
                                : "bg-slate-900 border-slate-700 hover:border-slate-500 hover:bg-slate-800/50"
                            }
                            ${isDisabled ? "opacity-50 cursor-not-allowed" : ""}
                          `}
                          onClick={
                            isDisabled
                              ? undefined
                              : () => handleItemClick(item.id)
                          }
                        >
                          {/* Selection indicator */}
                          {isSelected && (
                            <div className="absolute top-1 right-1 bg-yellow-400 text-black rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">
                              ✓
                            </div>
                          )}
                          <div className="flex items-start space-x-3">
                            <div className="w-12 h-12 md:w-12 md:h-12 bg-gray-600 rounded flex-shrink-0 relative">
                              <Image
                                src={item.image}
                                alt={item.name}
                                fill
                                className="object-cover rounded"
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4
                                className={`font-medium text-sm md:truncate ${
                                  isSelected ? "text-yellow-100" : "text-white"
                                }`}
                              >
                                {item.name}
                              </h4>
                              <p
                                className={`text-xs mt-1 leading-relaxed ${
                                  isSelected
                                    ? "text-yellow-200/90"
                                    : "text-gray-300"
                                } md:line-clamp-2`}
                              >
                                {item.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {[
            "connecting",
            "authenticating",
            "verifying_ownership",
            "loading_chicken",
            "queuing",
            "matching",
            "creating_battle",
          ].includes(stage) && (
            <div className="py-8">
              <div className="flex flex-col items-center justify-center">
                {/* Loading animation */}
                <div className="relative w-20 h-20 mb-6">
                  <div className="absolute inset-0 rounded-full border-4 border-r-transparent border-red-500 animate-spin"></div>
                  <div className="absolute inset-3 rounded-full border-4 border-l-transparent border-amber-500 animate-spin animation-delay-150"></div>
                </div>

                <h3 className="text-xl font-bold text-amber-400 mb-3">
                  {stage === "connecting" && "Connecting to Server..."}
                  {stage === "authenticating" && "Authenticating..."}
                  {stage === "verifying_ownership" && "Verifying Ownership..."}
                  {stage === "loading_chicken" && "Loading Chicken Data..."}
                  {stage === "queuing" && "Finding an Opponent..."}
                  {stage === "matching" && "Analyzing Matches..."}
                  {stage === "creating_battle" && "Creating Battle..."}
                </h3>

                {stage === "queuing" && (
                  <div className="text-center mb-6">
                    <div className="bg-gray-700 rounded-lg p-4 mb-4">
                      <div className="flex items-center justify-center space-x-4 text-sm">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-amber-400">
                            {queuePosition}
                          </div>
                          <div className="text-gray-400">Your Position</div>
                        </div>
                        <div className="text-gray-600">/</div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-400">
                            {waitingCount}
                          </div>
                          <div className="text-gray-400">Total Waiting</div>
                        </div>
                      </div>
                    </div>
                    <p className="text-gray-400 text-sm">
                      Estimated wait time:{" "}
                      <span className="text-amber-400">
                        ~{Math.max(1, Math.ceil(queuePosition / 2))} minute
                        {Math.ceil(queuePosition / 2) !== 1 ? "s" : ""}
                      </span>
                    </p>
                  </div>
                )}

                <p className="text-gray-400 text-sm text-center max-w-xs mb-6">
                  {stage === "connecting" &&
                    "Establishing connection to the battle server. Please wait a moment."}
                  {stage === "authenticating" &&
                    "Verifying your authentication token..."}
                  {stage === "verifying_ownership" &&
                    "Confirming you own the selected chicken..."}
                  {stage === "loading_chicken" &&
                    "Loading your chicken's stats and battle readiness..."}
                  {stage === "queuing" &&
                    "We're matching you with an opponent of similar strength. This may take a few moments."}
                  {stage === "matching" &&
                    "Analyzing player ratings to create balanced matches..."}
                  {stage === "creating_battle" &&
                    "Setting up the battle room for your match..."}
                </p>
              </div>
            </div>
          )}

          {stage === "matched" && (
            <div className="py-8">
              <div className="flex flex-col items-center justify-center">
                {/* Success animation */}
                <div className="w-20 h-20 mb-6 relative">
                  <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-50"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Trophy className="h-10 w-10 text-yellow-400" />
                  </div>
                </div>

                <h3 className="text-xl font-bold text-green-400 mb-3">
                  Match Found!
                </h3>

                <p className="text-gray-300 text-center max-w-xs mb-8">
                  An opponent has been found. Redirecting you to the battlefield
                  in a moment...
                </p>

                <div className="flex items-center">
                  <Loader2 className="h-5 w-5 text-amber-400 animate-spin mr-2" />
                  <span className="text-amber-400">
                    Redirecting to battle...
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {(stage === "ready" ||
          stage === "connecting" ||
          stage === "authenticating" ||
          stage === "verifying_ownership" ||
          stage === "loading_chicken" ||
          stage === "queuing" ||
          stage === "matching" ||
          stage === "creating_battle") && (
          <div className="p-6 border-t border-gray-700 bg-gray-800 mt-auto">
            <div className="flex justify-end gap-3">
              <button
                className={`px-6 py-2.5 rounded-lg transition-colors flex items-center justify-center text-sm font-medium ${
                  stage === "ready"
                    ? "bg-gray-600 text-white hover:bg-gray-500 border border-gray-500"
                    : "bg-red-700 text-white hover:bg-red-600 border border-red-600"
                }`}
                onClick={handleCancel}
              >
                <X className="h-4 w-4 mr-2" />
                {stage === "ready" ? "Cancel" : "Leave Queue"}
              </button>
              {stage === "ready" && (
                <button
                  className="px-6 py-2.5 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 transition-colors flex items-center justify-center shadow-lg shadow-red-900/30 text-sm font-medium"
                  onClick={handleStartBattle}
                >
                  <Swords className="h-4 w-4 mr-2" />
                  Enter Battle
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Incomplete Selection Modal */}
      <IncompleteSelectionModal
        isOpen={showIncompleteModal}
        onClose={() => setShowIncompleteModal(false)}
        onConfirm={async () => {
          setShowIncompleteModal(false);
          await startBattleProcess();
        }}
      />
    </div>
  );

  // Use portal to render at document body level to avoid z-index conflicts
  return typeof document !== "undefined"
    ? createPortal(modalContent, document.body)
    : null;
}
