"use client";

import {
  AdminLayout,
  useAdmin,
  useBreedingCooldowns,
  useBreedingFees,
} from "@/features/admin";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function AdminDashboard() {
  const { userData } = useAdmin();
  const { data: cooldowns, isLoading: isLoadingCooldowns } =
    useBreedingCooldowns();
  const { data: fees, isLoading: isLoadingFees } = useBreedingFees();

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">Admin Dashboard</h1>
          <p className="text-gray-400">
            Welcome back,{" "}
            {userData?.blockchainAddress
              ? userData.blockchainAddress.slice(0, 8) + "..."
              : "Admin"}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Breeding Cooldowns Card */}
          <div className="bg-[#1E1E1E] rounded-lg p-6 border border-[#333]">
            <h2 className="text-xl font-semibold mb-4">Breeding Cooldowns</h2>
            {isLoadingCooldowns ? (
              <p>Loading cooldowns...</p>
            ) : (
              <>
                <p className="mb-4">
                  {cooldowns?.length || 0} cooldown configurations available
                </p>
                <Button intent="primary" size="small">
                  <Link href="/admin/breeding-cooldowns">Manage Cooldowns</Link>
                </Button>
              </>
            )}
          </div>

          {/* Breeding Fees Card */}
          <div className="bg-[#1E1E1E] rounded-lg p-6 border border-[#333]">
            <h2 className="text-xl font-semibold mb-4">Breeding Fees</h2>
            {isLoadingFees ? (
              <p>Loading fees...</p>
            ) : (
              <>
                <p className="mb-4">
                  {fees?.length || 0} fee configurations available
                </p>
                <Button intent="primary" size="small">
                  <Link href="/admin/breeding-fees">Manage Fees</Link>
                </Button>
              </>
            )}
          </div>
        </div>

        <div className="bg-[#1E1E1E] rounded-lg p-6 border border-[#333]">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="flex flex-wrap gap-4">
            <Button intent="secondary" size="small">
              <Link href="/admin/breeding-cooldowns">Manage Cooldowns</Link>
            </Button>
            <Button intent="secondary" size="small">
              <Link href="/admin/breeding-fees">Manage Fees</Link>
            </Button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
