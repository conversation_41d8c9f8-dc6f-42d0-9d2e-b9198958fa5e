import { publicClient } from "@/lib/publicClient";
import { breedingAbi } from "@/providers/web3/abi/breeding-abi";
import { cockAbi } from "@/providers/web3/abi/cock-abi";
import { itemAbi } from "@/providers/web3/abi/item-abi";
import { resourcesAbi } from "@/providers/web3/abi/resources-abi";
import useAuthStore from "@/store/auth";
import { ConnectorErrorType } from "@sky-mavis/tanto-connect";
import { toast } from "sonner";
import {
  Address,
  ContractFunctionExecutionError,
  formatEther,
  parseEventLogs,
  UserRejectedRequestError,
  WriteContractParameters,
} from "viem";
import { ronin, saigon } from "viem/chains";
import { create } from "zustand";
import axios from "@/lib/api";
import { globalStatePersist } from "@/lib/store/persist";

const CHAIN_ID = Number(process.env.NEXT_PUBLIC_CHAINDID || "2021");
const chain = CHAIN_ID === 2020 ? ronin : saigon;

type BreedingState = {
  isPending: boolean;
  approvingCock: boolean;
  approvingFeathers: boolean;
  approvingResources: boolean;
};

type PrepareBreedResponse = {
  data: {
    address: string;
    chickenLeftTokenId: string;
    chickenRightTokenId: string;
    totalAmount: string;
    amountToVault: string;
    amountToNinuno: string;
    breedingCooldownTime: string;
    feathersData: string[][];
    resourcesData: string[][];
    signature: `0x${string}`;
  };
};

type PrepareBreedBatchResponse = {
  data: {
    address: string;
    chickenLeftTokenIds: string[];
    chickenRightTokenIds: string[];
    totalAmounts: string[];
    amountsToVault: string[];
    amountsToNinuno: string[];
    breedingCooldownTimes: string[];
    feathersData: string[][][];
    resourcesData: string[][][];
    signatures: `0x${string}`[];
  };
};

type Actions = {
  approveCock: (amt: bigint) => Promise<void>;
  approveFeathers: () => Promise<void>;
  approveResources: () => Promise<void>;
  startBreed: (
    parent1: number,
    parent2: number,
    referralCode: string
  ) => Promise<void>;
  startBreedBatch: (
    pairs: {
      chickenLeftTokenId: number;
      chickenRightTokenId: number;
    }[],
    referralCode: string
  ) => Promise<void>;
  breed: (
    parent1: bigint,
    parent2: bigint,
    totalAmount: bigint,
    amountsToVault: bigint,
    amountsToNinuno: bigint,
    breedingCooldownTime: bigint,
    feathersData: bigint[][],
    resourcesData: bigint[][],
    signature: `0x${string}`,
    referralCode: string
  ) => Promise<void>;
  breedBatch: (
    parent1s: bigint[],
    parent2s: bigint[],
    totalAmounts: bigint[],
    amountsToVault: bigint[],
    amountsToNinuno: bigint[],
    breedingCooldownTimes: bigint[],
    feathersData: bigint[][][],
    resourcesData: bigint[][][],
    signatures: `0x${string}`[],
    referralCode: string
  ) => Promise<void>;
};

type Contracts = {
  chicken_genesis_address: Address;
  chicken_legacy_address: Address;
  cock_address: Address;
  items_address: Address;
  breeding_address: Address;
  resources_address: Address;
};

type StoreState = BreedingState & Actions & Contracts;

const handleError = (
  error: unknown,
  defaultMessage: string,
  operation: string
) => {
  if (error instanceof UserRejectedRequestError) {
    toast.error("Transaction rejected", {
      description: "You rejected the transaction in your wallet",
      position: "top-right",
    });
    throw error;
  }
  if (error instanceof ContractFunctionExecutionError) {
    toast.error(error.name, {
      description: error.shortMessage,
      position: "top-right",
    });
    throw error;
  }

  // Handle specific error types
  if (error instanceof Error) {
    if (error.name === ConnectorErrorType.PROVIDER_NOT_FOUND) {
      window.open("https://wallet.roninchain.com", "_blank");
      toast.error("Wallet not found", {
        description: "Please install Ronin Wallet to continue",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("user rejected")) {
      toast.error("Transaction rejected", {
        description: "You rejected the transaction in your wallet",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("insufficient funds")) {
      toast.error("Insufficient funds", {
        description: "You don't have enough funds to complete this transaction",
        position: "top-right",
      });
      throw error;
    }
  } else {
    const err = error as Error;

    // Fallback for non-Error objects
    toast.error(operation, {
      description: defaultMessage,
      position: "top-right",
    });
    throw err;
  }
};

const useBreedingStore = ({
  chicken_genesis_address,
  chicken_legacy_address,
  cock_address,
  items_address,
  breeding_address,
  resources_address,
}: {
  chicken_genesis_address: Address;
  chicken_legacy_address: Address;
  cock_address: Address;
  items_address: Address;
  breeding_address: Address;
  resources_address: Address;
}) => {
  return create<StoreState>()((set, get) => ({
    chicken_genesis_address,
    chicken_legacy_address,
    cock_address,
    items_address,
    breeding_address,
    resources_address,
    isPending: false,
    approvingCock: false,
    approvingFeathers: false,
    approvingResources: false,

    approveCock: async (amount) => {
      const stateContext = window.stateContext;
      if (!stateContext || !stateContext.address) {
        toast.error("Cannot approve ERC20", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }
      const { address, publicClient, walletClient } = stateContext;

      if (!address || !walletClient) {
        toast.error("Cannot approve ERC20", {
          description: "Wallet not connected",
          position: "top-right",
        });
        return;
      }

      try {
        const cockAllowance = await publicClient.readContract({
          address: get().cock_address,
          abi: cockAbi,
          functionName: "allowance",
          args: [address, get().breeding_address],
        });

        if (cockAllowance >= amount) {
          toast.success("ERC20 approval successful", {
            description: `Approved ${formatEther(amount)} COCK tokens for spending`,
            position: "top-right",
          });
          return;
        }

        set({ approvingCock: true });

        toast.info("Preparing approval transaction...", {
          description: `Approving ${formatEther(amount)} COCK tokens for spending`,
          position: "top-right",
        });

        const { request } = await publicClient.simulateContract({
          address: get().cock_address,
          abi: cockAbi,
          functionName: "approve",
          args: [get().breeding_address, amount],
          chain, // Explicitly specify the chain
          account: address, // Explicitly specify the account
        });
        // Approve amount for ERC20 token
        const hash = await walletClient!.writeContract(request);

        toast.info("Approval transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-right",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          toast.success("ERC20 approval successful", {
            description: `Approved ${formatEther(amount)} COCK tokens for spending`,
            position: "top-right",
          });
        } else {
          toast.error("ERC20 approval failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-right",
          });
        }
      } catch (error) {
        handleError(error, "Failed to approve ERC20 token", "ERC20 approval");
      } finally {
        set({ approvingCock: false });
      }
    },

    approveFeathers: async () => {
      const stateContext = window.stateContext;
      if (!stateContext || !stateContext.address) {
        toast.error("Cannot approve ERC20", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }
      const { address, publicClient, walletClient } = stateContext;

      if (!address || !walletClient) {
        toast.error("Cannot approve ERC1155", {
          description: "Wallet not connected",
          position: "top-right",
        });
        return;
      }

      try {
        const isApprovedForAll = await publicClient.readContract({
          address: get().items_address,
          abi: itemAbi,
          functionName: "isApprovedForAll",
          args: [address, get().breeding_address],
        });

        if (isApprovedForAll) {
          toast.success("ERC1155 approval successful", {
            description:
              "Successfully approved all Feathers tokens for spending",
            position: "top-right",
          });
          return;
        }

        set({ approvingFeathers: true });
        toast.info("Preparing approval transaction...", {
          description: "Setting approval for all Feathers tokens",
          position: "top-right",
        });

        // Get the current chain
        const chain = CHAIN_ID === 2020 ? ronin : saigon;

        // Set approval for all for ERC1155 token
        const hash = await walletClient.writeContract({
          address: get().items_address,
          abi: itemAbi,
          functionName: "setApprovalForAll",
          args: [get().breeding_address, true],
          chain, // Explicitly specify the chain
          account: address, // Explicitly specify the account
        });

        toast.info("Approval transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-right",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          toast.success("ERC1155 approval successful", {
            description:
              "Successfully approved all Feathers tokens for spending",
            position: "top-right",
          });
        } else {
          toast.error("ERC1155 approval failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-right",
          });
        }
      } catch (error) {
        handleError(
          error,
          "Failed to approve ERC1155 token",
          "ERC1155 approval"
        );
      } finally {
        set({ approvingFeathers: false });
      }
    },

    approveResources: async () => {
      const stateContext = window.stateContext;
      if (!stateContext || !stateContext.address) {
        toast.error("Cannot approve ERC20", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }
      const { address, publicClient, walletClient } = stateContext;

      if (!address || !walletClient) {
        toast.error("Cannot approve ERC1155", {
          description: "Wallet not connected",
          position: "top-right",
        });
        return;
      }

      try {
        const isApprovedForAll = await publicClient.readContract({
          address: get().resources_address,
          abi: resourcesAbi,
          functionName: "isApprovedForAll",
          args: [address, get().breeding_address],
        });

        if (isApprovedForAll) {
          toast.success("ERC1155 approval successful", {
            description:
              "Successfully approved all Resources tokens for spending",
            position: "top-right",
          });
          return;
        }

        set({ approvingResources: true });
        toast.info("Preparing approval transaction...", {
          description: "Setting approval for all Resources tokens",
          position: "top-right",
        });

        // Get the current chain
        const chain = CHAIN_ID === 2020 ? ronin : saigon;

        // Set approval for all for ERC1155 token
        const hash = await walletClient.writeContract({
          address: get().resources_address,
          abi: resourcesAbi,
          functionName: "setApprovalForAll",
          args: [get().breeding_address, true],
          chain, // Explicitly specify the chain
          account: address, // Explicitly specify the account
        });

        toast.info("Approval transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-right",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          toast.success("ERC1155 approval successful", {
            description:
              "Successfully approved all Resources tokens for spending",
            position: "top-right",
          });
        } else {
          toast.error("ERC1155 approval failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-right",
          });
        }
      } catch (error) {
        handleError(
          error,
          "Failed to approve ERC1155 token",
          "ERC1155 approval"
        );
      } finally {
        set({ approvingResources: false });
      }
    },

    startBreed: async (parent1, parent2, referralCode = "") => {
      const stateContext = window.stateContext;
      if (!stateContext || !stateContext.address) {
        toast.error("Cannot approve ERC20", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }
      const { address, walletClient } = stateContext;

      if (!address || !walletClient) {
        toast.error("Cannot breed chickens", {
          description: "Wallet not connected",
          position: "top-right",
        });
        throw Error("Breeding preparation failed");
      }

      try {
        set({ isPending: true });

        const { data } = await axios.post<PrepareBreedResponse>(
          "/breedings/initiate-breeding",
          {
            address,
            chickenLeftTokenId: parent1,
            chickenRightTokenId: parent2,
          }
        );

        globalStatePersist.breeding.message.set(
          `Don't mind us. Just focus on your wallet interaction 😁`
        );

        await get().breed(
          BigInt(data.data.chickenLeftTokenId),
          BigInt(data.data.chickenRightTokenId),
          BigInt(data.data.totalAmount),
          BigInt(data.data.amountToVault),
          BigInt(data.data.amountToNinuno),
          BigInt(data.data.breedingCooldownTime),
          data.data.feathersData.map((featherData) =>
            featherData.map((feather) => BigInt(feather))
          ),
          data.data.resourcesData.map((resourceData) =>
            resourceData.map((resource) => BigInt(resource))
          ),
          data.data.signature,
          referralCode
        );
      } catch (error) {
        handleError(error, "Failed to prepare breeding", "breeding");
      } finally {
        set({ isPending: false });
        globalStatePersist.breeding.loading.set(false);
        globalStatePersist.breeding.message.set(
          `Breeding’s not gae if you say 'no homo' first, OK? 😉`
        );
      }
    },

    startBreedBatch: async (pairs, referralCode = "") => {
      const stateContext = window.stateContext;
      if (!stateContext || !stateContext.address) {
        toast.error("Cannot approve ERC20", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }
      const { address, walletClient } = stateContext;

      if (!address || !walletClient) {
        toast.error("Cannot breed chickens", {
          description: "Wallet not connected",
          position: "top-right",
        });
        throw Error("Breeding preparation failed");
      }

      try {
        set({ isPending: true });

        const { data } = await axios.post<PrepareBreedBatchResponse>(
          "/breedings/initiate-bulk-breeding",
          {
            address,
            data: pairs,
          }
        );

        globalStatePersist.breeding.message.set(
          `Don't mind us. Just focus on your wallet interaction 😁`
        );

        await get().breedBatch(
          data.data.chickenLeftTokenIds.map((item) => BigInt(item)),
          data.data.chickenRightTokenIds.map((item) => BigInt(item)),
          data.data.totalAmounts.map((item) => BigInt(`${item}`)),
          data.data.amountsToVault.map((item) => BigInt(`${item}`)),
          data.data.amountsToNinuno.map((item) => BigInt(`${item}`)),
          data.data.breedingCooldownTimes.map((item) => BigInt(item)),
          data.data.feathersData.map((arr) =>
            arr.map((n) => n.map((o) => BigInt(o)))
          ),
          data.data.resourcesData.map((arr) =>
            arr.map((n) => n.map((o) => BigInt(o)))
          ),
          data.data.signatures,
          referralCode
        );
      } catch (error) {
        handleError(error, "Failed to prepare breeding", "breeding");
      } finally {
        set({ isPending: false });
        globalStatePersist.breeding.loading.set(false);
        globalStatePersist.breeding.message.set(
          `Breeding’s not gae if you say 'no homo' first, OK? 😉`
        );
      }
    },

    breed: async (
      parent1,
      parent2,
      totalAmount,
      amountsToVault,
      amountsToNinuno,
      breedingCooldownTime,
      feathersData,
      resourcesData,
      signature,
      referralCode
    ) => {
      const { fetchBalances } = useAuthStore.getState();
      const stateContext = window.stateContext;
      if (!stateContext || !stateContext.address) {
        toast.error("Cannot approve ERC20", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }
      const {
        address,
        walletClient,
        Disconnect: logout,
        isConnected,
      } = stateContext;

      if (!address || !walletClient) {
        toast.error("Cannot breed chickens", {
          description: "Wallet not connected",
          position: "top-right",
        });
        await logout();
        return;
      }

      try {
        await get().approveCock(totalAmount);
        await get().approveFeathers();
        await get().approveResources();

        let simulationSuccess = false;
        let request: WriteContractParameters | undefined;
        let simulationError;

        try {
          const simulateReq = await publicClient.simulateContract({
            address: get().breeding_address,
            abi: breedingAbi,
            functionName: "breed",
            args: [
              {
                chickenLeftTokenId: parent1,
                chickenRightTokenId: parent2,
                totalAmount,
                amountToVault: amountsToVault,
                amountToNinuno: amountsToNinuno,
                breedingCooldownTime,
                feathersData,
                resourcesData,
              },
              signature,
              referralCode,
            ],
            chain,
            account: address,
          });

          request = simulateReq.request;
          simulationSuccess = true;
        } catch (error) {
          simulationError = error;
          request = undefined;
        }

        if (!simulationSuccess || !request) {
          throw simulationError;
        }

        // Estimate gas for the transaction
        const gasEstimate = await publicClient.estimateContractGas({
          address: get().breeding_address,
          abi: breedingAbi,
          functionName: "breed",
          args: [
            {
              chickenLeftTokenId: parent1,
              chickenRightTokenId: parent2,
              totalAmount,
              amountToVault: amountsToVault,
              amountToNinuno: amountsToNinuno,
              breedingCooldownTime,
              feathersData,
              resourcesData,
            },
            signature,
            referralCode,
          ],
          account: address,
        });

        // Add 10% buffer to gas estimate
        const gasWithBuffer = (gasEstimate * 115n) / 100n;

        // Add gas to request
        request.gas = gasWithBuffer;

        // Continue with transaction if simulation succeeded
        const hash = await walletClient.writeContract(request);

        toast.info("Breeding transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-center",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          // Refresh balances after successful mint
          await fetchBalances(isConnected, publicClient, address);

          const logs = parseEventLogs({
            abi: breedingAbi,
            eventName: "Breed",
            logs: receipt.logs,
          });

          console.log(logs);
        } else {
          toast.error("Breeding transaction failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-center",
          });
          throw Error("Breeding transaction failed");
        }
      } catch (error) {
        handleError(error, "Failed to craft foods", "breeding");
      }
    },

    breedBatch: async (
      parent1s,
      parent2s,
      totalAmounts,
      amountsToVault,
      amountsToNinuno,
      breedingCooldownTimes,
      feathersData,
      resourcesData,
      signatures,
      referralCode
    ) => {
      const { fetchBalances } = useAuthStore.getState();
      const stateContext = window.stateContext;
      if (!stateContext || !stateContext.address) {
        toast.error("Cannot approve ERC20", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }
      const {
        address,
        walletClient,
        Disconnect: logout,
        isConnected,
      } = stateContext;

      if (!address || !walletClient) {
        toast.error("Cannot breed chickens", {
          description: "Wallet not connected",
          position: "top-right",
        });
        await logout();
        return;
      }

      try {
        set({ isPending: true });

        await get().approveCock(totalAmounts.reduce((a, b) => a + b, 0n));
        await get().approveFeathers();
        await get().approveResources();

        let simulationSuccess = false;
        let request: WriteContractParameters | undefined;
        let simulationError;

        try {
          const simulateReq = await publicClient.simulateContract({
            address: get().breeding_address,
            abi: breedingAbi,
            functionName: "breedBatch",
            args: [
              {
                chickenLeftTokenIds: parent1s,
                chickenRightTokenIds: parent2s,
                totalAmounts: totalAmounts,
                amountsToVault: amountsToVault,
                amountsToNinuno: amountsToNinuno,
                breedingCooldownTimes: breedingCooldownTimes,
                feathersData: feathersData,
                resourcesData: resourcesData,
                signatures,
              },
              referralCode,
            ],
            chain,
            account: address,
          });

          request = simulateReq.request;
          simulationSuccess = true;
        } catch (error) {
          simulationError = error;
          request = undefined;
        }

        if (!simulationSuccess || !request) {
          throw simulationError;
        }

        // Estimate gas for the transaction
        const gasEstimate = await publicClient.estimateContractGas({
          address: get().breeding_address,
          abi: breedingAbi,
          functionName: "breedBatch",
          args: [
            {
              chickenLeftTokenIds: parent1s,
              chickenRightTokenIds: parent2s,
              totalAmounts: totalAmounts,
              amountsToVault: amountsToVault,
              amountsToNinuno: amountsToNinuno,
              breedingCooldownTimes: breedingCooldownTimes,
              feathersData: feathersData,
              resourcesData: resourcesData,
              signatures,
            },
            referralCode,
          ],
          account: address,
        });

        // Add 10% buffer to gas estimate
        const gasWithBuffer = (gasEstimate * 115n) / 100n;

        // Add gas to request
        request.gas = gasWithBuffer;

        // Continue with transaction if simulation succeeded
        const hash = await walletClient.writeContract(request);

        toast.info("Breeding transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-center",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          // Refresh balances after successful mint
          await fetchBalances(isConnected, publicClient, address);

          const logs = parseEventLogs({
            abi: breedingAbi,
            eventName: "BatchBreed",
            logs: receipt.logs,
          });

          console.log(logs);
        } else {
          toast.error("Breeding transaction failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-center",
          });
          throw Error("Breeding transaction failed");
        }
      } catch (error) {
        handleError(error, "Failed to craft foods", "breeding");
      } finally {
        set({ isPending: false });
        globalStatePersist.breeding.loading.set(false);
        globalStatePersist.breeding.message.set(
          `Breeding’s not gae if you say 'no homo' first, OK? 😉`
        );
      }
    },
  }));
};

export default useBreedingStore;
