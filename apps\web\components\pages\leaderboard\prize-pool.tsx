"use client";

import React, { useEffect, useState, useMemo } from "react";
import { createPortal } from "react-dom";
import { createPublicClient, http, erc20Abi, formatUnits, Address } from "viem";
import { ronin, saigon } from "viem/chains";
import Link from "next/link";

const BASE_PRIZE = 10000000; // 10 million base prize

// Constants from environment
const CHAIN_ID = process.env.NEXT_PUBLIC_CHAINDID
  ? parseInt(process.env.NEXT_PUBLIC_CHAINDID)
  : 2021;

const COCK_TOKEN_ADDRESS = process.env.NEXT_PUBLIC_COCK_ADDRESS as Address;
const CRAFTING_TREASURY_ADDRESS = process.env
  .NEXT_PUBLIC_CRAFTING_TREASURY_ADDRESS as Address;
const BREEDING_TREASURY_ADDRESS = process.env
  .NEXT_PUBLIC_BREEDING_TREASURY_ADDRESS as Address;

// Season end configuration - moved outside component to prevent re-creation
const isSeasonEnded = process.env.NEXT_PUBLIC_SEASON_ENDED === 'true';
const FINAL_CRAFTING_REWARDS = parseInt(process.env.NEXT_PUBLIC_FINAL_CRAFTING_REWARDS || '0');
const FINAL_BREEDING_REWARDS = parseInt(process.env.NEXT_PUBLIC_FINAL_BREEDING_REWARDS || '0');
const FINAL_HEAL_IAP_REWARDS = parseInt(process.env.NEXT_PUBLIC_FINAL_HEAL_IAP_REWARDS || '0');

function TotalPrizeAmount({ compact = false, onClick }: { compact?: boolean; onClick?: () => void }) {
  const [growingPot, setGrowingPot] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch 40% from crafting treasury + 40% from breeding treasury
  async function fetchGrowingPot(): Promise<number> {
    try {
      // Validate required environment variables
      if (
        !COCK_TOKEN_ADDRESS ||
        !CRAFTING_TREASURY_ADDRESS ||
        !BREEDING_TREASURY_ADDRESS
      ) {
        throw new Error(
          "Missing required environment variables for treasury addresses"
        );
      }

      // Create public client for Ronin/Saigon network
      const chain = CHAIN_ID === 2020 ? ronin : saigon;
      const publicClient = createPublicClient({
        chain,
        transport: http(),
      });

      const [craftingBalance, breedingBalance] = await Promise.all([
        publicClient.readContract({
          address: COCK_TOKEN_ADDRESS,
          abi: erc20Abi,
          functionName: "balanceOf",
          args: [CRAFTING_TREASURY_ADDRESS],
        }) as Promise<bigint>,
        publicClient.readContract({
          address: COCK_TOKEN_ADDRESS,
          abi: erc20Abi,
          functionName: "balanceOf",
          args: [BREEDING_TREASURY_ADDRESS],
        }) as Promise<bigint>,
      ]);

      // Convert from wei to readable amounts
      const craftingInTokens = parseFloat(formatUnits(craftingBalance, 18));
      const breedingInTokens = parseFloat(formatUnits(breedingBalance, 18));

      // Calculate percentages: 40% from crafting and breeding
      const craftingContribution = craftingInTokens * 0.4;
      const breedingContribution = breedingInTokens * 0.4;
      const totalContribution = craftingContribution + breedingContribution;

      return totalContribution;
    } catch (error) {
      console.error("Error fetching treasury balances:", error);
      return 0; // Return 0 as fallback
    }
  }

  useEffect(() => {
    let isMounted = true;
    
    if (isSeasonEnded) {
      // Use final accumulated rewards when season has ended
      const totalFinalRewards = FINAL_CRAFTING_REWARDS + FINAL_BREEDING_REWARDS + FINAL_HEAL_IAP_REWARDS;
      if (isMounted) {
        setGrowingPot(totalFinalRewards);
        setLoading(false);
      }
    } else {
      fetchGrowingPot()
        .then((value) => {
          if (isMounted) {
            setGrowingPot(value);
            setLoading(false);
          }
        })
        .catch(() => {
          if (isMounted) {
            setGrowingPot(null);
            setLoading(false);
          }
        });
    }
    
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array since constants don't change

  if (loading) {
    return (
      <div className="animate-pulse flex items-center gap-2">
        <div
          className={`${compact ? "h-6 w-32" : "h-8 w-48"} bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-lg`}
        ></div>
      </div>
    );
  }

  if (growingPot === null) {
    return <span className="text-red-400 text-sm">Error loading</span>;
  }

  const total = BASE_PRIZE + growingPot;
  const formattedTotal = total.toLocaleString(undefined, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        {/* <span className="text-xs font-medium text-stone-400">Prize Pool:</span> */}
        <span 
          className={`text-sm font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent ${
            isSeasonEnded && onClick ? 'cursor-pointer hover:from-yellow-300 hover:to-orange-400 transition-all' : ''
          }`}
          onClick={isSeasonEnded ? onClick : undefined}
        >
          {isSeasonEnded ? formattedTotal : `${formattedTotal}++`} $COCK
        </span>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center gap-2">
      <div className="text-center">
        <div 
          className={`text-2xl md:text-3xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent ${
            isSeasonEnded && onClick ? 'cursor-pointer hover:from-yellow-300 hover:via-orange-400 hover:to-red-400 transition-all' : ''
          }`}
          onClick={isSeasonEnded ? onClick : undefined}
        >
          {isSeasonEnded ? formattedTotal : `${formattedTotal}++`} $COCK
        </div>
      </div>
    </div>
  );
}

export default function PrizePoolHeader() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [showRewardsModal, setShowRewardsModal] = useState(false);


  useEffect(() => {
    const handleScroll = () => {
      // Consider scrolled when user scrolls more than 100px
      setIsScrolled(window.scrollY > 100);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && showRewardsModal) {
        setShowRewardsModal(false);
      }
    };

    if (showRewardsModal) {
      window.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [showRewardsModal]);

  if (isScrolled) {
    // Compact sliver mode when scrolled
    return (
      <div className="fixed top-0 left-0 right-0 z-[60] bg-black/98 backdrop-blur-sm border-b border-stone-700/50 transition-all duration-300">
        <div className="w-full px-4 py-1">
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <span className="text-yellow-400 text-xs">🏆</span>
                <span className={`text-xs font-medium uppercase tracking-wide ${isSeasonEnded ? 'text-red-400' : 'text-stone-400'}`}>
                  {isSeasonEnded ? 'Season Ended' : 'Preseason 0'}
                </span>
              </div>
              <div className="w-px h-3 bg-stone-600"></div>
              <TotalPrizeAmount compact onClick={() => setShowRewardsModal(true)} />
            </div>
            <Link
              href="/inventory/chickens"
              className="flex items-center gap-1.5 px-3 py-1 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 rounded-full text-xs font-medium text-white transition-all duration-200 hover:scale-105"
            >
              <span>⚔️</span>
              <span className="hidden sm:inline">Play Now</span>
              <span className="sm:hidden">Play</span>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Full mode when at top
  return (
    <div className="sticky top-[72px] z-40 bg-gradient-to-b from-black/95 to-stone-900/95 backdrop-blur-sm border-b border-stone-700/50">
      <div className="w-full px-4 py-3">
        <div className="max-w-7xl mx-auto">
          {/* Mobile: Centered layout */}
          <div className="md:hidden text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <span className="text-yellow-400">🏆</span>
              <span className="text-sm font-semibold text-yellow-300 uppercase tracking-wider">
                {isSeasonEnded ? 'Final Prize Pool' : 'Total Prize Pool'}
              </span>
            </div>
            <TotalPrizeAmount onClick={() => setShowRewardsModal(true)} />
            <div className="flex items-center justify-center gap-3 mt-2">
              <div className={`text-xs ${isSeasonEnded ? 'text-red-400' : 'text-stone-400'}`}>
                {isSeasonEnded ? 'Season Ended' : 'Preseason 0'}
              </div>
              <Link
                href="/inventory/chickens"
                className="flex items-center gap-1.5 px-3 py-1.5 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 rounded-full text-xs font-medium text-white transition-all duration-200 hover:scale-105"
              >
                <span>⚔️</span>
                <span>Play Now</span>
              </Link>
            </div>
          </div>

          {/* Desktop: Horizontal layout */}
          <div className="hidden md:flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-yellow-400 text-lg">🏆</span>
                <span className="text-sm font-semibold text-yellow-300 uppercase tracking-wider">
                  {isSeasonEnded ? 'Final Prize Pool (Season Ended)' : 'Preseason 0'}
                </span>
              </div>
              <div className="w-px h-6 bg-stone-600"></div>
              <TotalPrizeAmount onClick={() => setShowRewardsModal(true)} />
            </div>

            <Link
              href="/inventory/chickens"
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 rounded-full text-sm font-medium text-white transition-all duration-200 hover:scale-105 shadow-lg"
            >
              <span>⚔️</span>
              <span>Play Now</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Final Rewards Modal - STYLED VERSION */}
      {showRewardsModal && isSeasonEnded && typeof document !== 'undefined' && createPortal(
        <div 
          className="fixed top-0 left-0 w-full h-full z-[9999] bg-black/80 backdrop-blur-sm"
          onClick={() => setShowRewardsModal(false)}
        >
          <div 
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-stone-900 border border-stone-700 rounded-xl max-w-4xl w-[90%] max-h-[90vh] overflow-y-auto shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex justify-between items-center p-6 border-b border-stone-700">
              <h3 className="text-2xl font-bold text-white tracking-wide">
                FINAL ACCUMULATED REWARDS
              </h3>
              <button
                onClick={() => setShowRewardsModal(false)}
                className="text-stone-400 hover:text-white transition-colors p-1"
                aria-label="Close modal"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Reward cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="bg-stone-800 rounded-xl p-6 border border-green-500/30 hover:border-green-500/50 transition-colors">
                  <div className="text-3xl md:text-4xl font-bold text-green-400 mb-3 text-center">
                    {FINAL_CRAFTING_REWARDS.toLocaleString()}
                  </div>
                  <div className="text-gray-300 uppercase tracking-wider font-medium text-sm text-center">
                    CRAFTING REWARDS
                  </div>
                </div>
                
                <div className="bg-stone-800 rounded-xl p-6 border border-blue-500/30 hover:border-blue-500/50 transition-colors">
                  <div className="text-3xl md:text-4xl font-bold text-blue-400 mb-3 text-center">
                    {FINAL_BREEDING_REWARDS.toLocaleString()}
                  </div>
                  <div className="text-gray-300 uppercase tracking-wider font-medium text-sm text-center">
                    BREEDING REWARDS
                  </div>
                </div>
                
                <div className="bg-stone-800 rounded-xl p-6 border border-purple-500/30 hover:border-purple-500/50 transition-colors">
                  <div className="text-3xl md:text-4xl font-bold text-purple-400 mb-3 text-center">
                    {FINAL_HEAL_IAP_REWARDS.toLocaleString()}
                  </div>
                  <div className="text-gray-300 uppercase tracking-wider font-medium text-sm text-center">
                    HEAL IAP REWARDS
                  </div>
                </div>
              </div>
              
              {/* Total section */}
              <div className="text-center pt-6 border-t border-stone-700/50">
                <div className="text-lg text-gray-300 mb-4 font-medium">Total Additional Rewards:</div>
                <div className="text-4xl md:text-5xl font-bold text-yellow-400">
                  {(FINAL_CRAFTING_REWARDS + FINAL_BREEDING_REWARDS + FINAL_HEAL_IAP_REWARDS).toLocaleString()} $COCK
                </div>
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
}
