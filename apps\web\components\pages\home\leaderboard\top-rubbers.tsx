import Feathers from "@/components/shared/icons/feathers";
import { GradientAvatar } from "@/components/shared/web3-avatar";
import { Leaderboard } from "@/types/api.types";
import {
  truncateAddress,
  truncateRonAddressCustom,
} from "@/utils/truncate-address";

export default function TopRubbers({
  leaderboard,
}: {
  leaderboard: Leaderboard[];
}) {
  return (
    <div className="flex flex-col h-full p-1 w-full rounded-lg bg-zinc-950">
      <div className="space-y-0">
        {leaderboard &&
          leaderboard.length != 0 &&
          leaderboard.map((item, index) => (
            <div
              key={index}
              className={`
          flex items-center justify-between text-white p-3
          ${index !== leaderboard.length - 1 ? "border-b border-zinc-900" : ""}
          ${index === 0 ? "rounded-t-lg" : ""}
          ${index === leaderboard.length - 1 ? "rounded-b-lg" : ""}
        `}
            >
              <div className="flex items-center space-x-3">
                <GradientAvatar address={item.address} />
                <div>
                  <div className="font-medium">
                    {item.rns
                      ? item.rns.length > 18
                        ? truncateRonAddressCustom(item.rns)
                        : item.rns
                      : truncateAddress(item.address)}
                  </div>
                  <div className="text-sm text-gray-400">
                    {item.nftCount} {item.nftCount > 1 ? "Chickens" : "Chicken"}
                  </div>
                </div>
              </div>
              <div className="flex gap-1 items-center text-yellow-400 font-medium">
                <Feathers size={16} />
                {item.totalFeathers}
              </div>
            </div>
          ))}
      </div>
    </div>
  );
}
