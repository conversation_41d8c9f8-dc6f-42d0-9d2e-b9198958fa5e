import { NextResponse } from "next/server";

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const tokenId = searchParams.get("tokenId");

  if (!tokenId) {
    return NextResponse.json(
      { error: "Token ID is required" },
      { status: 400 }
    );
  }

  try {
    const apiUrl =
      process.env.NEXT_PUBLIC_CHICKEN_API_URL ||
      "https://chicken-api-ivory.vercel.app/api";
    const response = await fetch(`${apiUrl}/game/${tokenId}`);
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.log(error);
    return NextResponse.json(
      { error: "Failed to fetch data" },
      { status: 500 }
    );
  }
}
