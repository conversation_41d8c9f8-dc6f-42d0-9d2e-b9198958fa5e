"use client";

import React from "react";
import { SearchBar } from "../search-bar";
import { CraftingItem } from "../crafting-item";
import { Pagination } from "@/components/ui";

export function CraftingGears() {
  const gearItems = [
    {
      name: "Battle Armor",
      requirements: {
        chicken: 200,
        feather: 50,
      },
    },
    {
      name: "Training Weights",
      requirements: {
        chicken: 150,
        feather: 30,
      },
    },
  ];

  return (
    <div className="mt-4">
      <SearchBar />
      <div className="grid grid-cols-4 gap-4 mt-6">
        {gearItems.map((item, index) => (
          <CraftingItem key={index} {...item} />
        ))}
      </div>
      <div className="mt-6">
        <Pagination>
          <Pagination.List>
            <Pagination.Item variant="first" href="#" />
            <Pagination.Item variant="previous" href="#" />
            <Pagination.Item href="#" isCurrent>
              1
            </Pagination.Item>
            <Pagination.Item variant="ellipsis" />
            <Pagination.Item variant="next" href="#" />
            <Pagination.Item variant="last" href="#" />
          </Pagination.List>
        </Pagination>
      </div>
    </div>
  );
}
