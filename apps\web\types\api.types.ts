import { NextRequest, NextResponse } from "next/server";

export interface ApiResponse<T> {
  status: boolean;
  responseCode: number;
  message: string;
  data?: T;
  errors?: string[];
}

export type SIWEResponse = {
  data: {
    statement: string;
    nonce: string;
    chainId: number;
    version: "1";
  };
};

export type DynamicParams = {
  [key: string]: string | string[];
};

export type HandlerFn = (
  req: NextRequest,
  context: { params: DynamicParams }
) => Promise<NextResponse> | NextResponse;

export type VerifyResponse = {
  data: {
    token: string;
    refreshToken: string;
  };
};

export type LeaderboardResponse = {
  status: boolean;
  responseCode: number;
  data: {
    leaderboard: Leaderboard[];
    pagination: {
      currentPage: number;
      totalPages: number;
    };
  };
};

export type Leaderboard = {
  address: string;
  feathers: number;
  withdrawnFeathers: number;
  totalFeathers: number;
  nftCount: number;
  rns: string;
};

export type LeaderboardPos = {
  position: number;
  userStats: Leaderboard;
};
