# Code Style and Conventions

## TypeScript Conventions
- **Interface names**: 'I' prefix + <PERSON><PERSON><PERSON> (IUserProfile)
- **Enum names**: 'E' prefix + PascalCase (ECropStatus)
- **Enum values**: UPPER_CASE for constants
- **Avoid using type 'any'**
- All files are 100% TypeScript

## File Naming
- **kebab-case** for all files
- **Descriptive suffixes**: .dialog.tsx, .types.ts
- **Test files**: .test.ts or .test.tsx
- **Use (folderName)** for route grouping in Next.js

## Component Conventions
- Create reusable, small (<200 lines) components
- Follow Single Responsibility Principle
- Use 'I' prefix for Props interfaces
- Implement prop validation and defaults
- Document complex components with JSDoc
- Handle loading/error/empty states
- Use descriptive names
- Extract common Tailwind classes
- Ensure responsive design
- Avoid passing down props if possible

## Backend Conventions (Adonis.js)
- **Request**: camelCase
- **Response**: snake_case
- Do not update backend code without permission
- Do not transform API response to avoid complexity, use as-is in UI

## Import Organization
- Group imports: React/Next.js first, then third-party, then local imports
- Use absolute imports with @ alias for src directory