"use client";

import Box from "@/components/shared/box";
import {
  Button,
  Disclosure,
  Menu,
  Skeleton,
  Table,
  Tooltip,
} from "@/components/ui";
import {
  IconCircleInfoFill,
  IconDotsVertical,
  IconPencilBox,
  IconPencilBoxFill,
  IconPlus,
  IconTrash,
  IconTrashFill,
} from "justd-icons";
import { useEffect, useState } from "react";
import { DelegateModal } from "./modal";
import useAllocationStore from "@/store/allocation";

const getAllocationFromLegendaryCount = (count: number): number => {
  const allocationMap: { [key: number]: number } = {
    0: 2,
    1: 5,
    2: 12,
    3: 30,
  };
  return allocationMap[count] || 2;
};

export default function DelegateWl() {
  const {
    getAlloc,
    isPending,
    totalAllocation,
    delegates,
    addOrUpdate,
    setSelectedAcct,
    totalChickens,
    breakDown,
  } = useAllocationStore();
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const getDelegatedTotal = (): number => {
    return delegates.reduce((sum, delegate) => sum + delegate.amount, 0);
  };

  const getRemainingSpots = (): number => {
    return totalAllocation - getDelegatedTotal();
  };

  useEffect(() => {
    getAlloc();
  }, []);

  return (
    <>
      <DelegateModal isOpen={isOpen} setOpen={setIsOpen} isUpdate={isUpdate} />
      <div className="relative flex max-w-[700px] w-full mx-auto mt-10 px-4 md:px-2 lg:px-0 font-Poppins">
        <Box>
          <div className="flex flex-col md:px-2 py-4">
            <h1 className="text-3xl font-Arcadia text-primary">
              Legacy Mint Whitelist Delegation
              <span className="ml-2">
                <Tooltip delay={0}>
                  <Tooltip.Trigger aria-label="Delegate tool">
                    <IconCircleInfoFill />
                  </Tooltip.Trigger>
                  <Tooltip.Content>
                    Delegate your whitelist spot to another wallet address. This
                    tool enables you to transfer your whitelist privileges to a
                    wallet address of your choice.
                  </Tooltip.Content>
                </Tooltip>
              </span>
            </h1>
            <div className="flex flex-col md:flex-row md:items-center w-full mt-4 gap-4 md:gap-8">
              {isPending ? (
                <div className="grid grid-cols-3 w-full gap-4 md:gap-8">
                  {Array.from({ length: 3 }).map((_, idx) => (
                    <Skeleton className=" rounded-lg h-[76px]" key={idx} />
                  ))}
                </div>
              ) : (
                <>
                  <div className="flex flex-col p-4 bg-bg rounded-lg w-full">
                    <p className="text-muted-fg text-sm font-medium">
                      Allocated WL Spot
                    </p>
                    <p className="text-2xl text-green-500 font-semibold">
                      {totalAllocation}
                    </p>
                  </div>
                  <div className="flex flex-col p-4 bg-bg rounded-lg w-full">
                    <p className="text-muted-fg text-sm font-medium">
                      Delegated WL Spot
                    </p>
                    <p className="text-2xl text-purple-500 font-semibold">
                      {getDelegatedTotal()}
                    </p>
                  </div>
                  <div className="flex flex-col p-4 bg-bg rounded-lg w-full">
                    <p className="text-muted-fg text-sm font-medium">
                      Remaining WL Spot
                    </p>
                    <p className="text-2xl text-orange-500 font-semibold">
                      {getRemainingSpots()}
                    </p>
                  </div>
                </>
              )}
            </div>
            {breakDown && breakDown.length != 0 && (
              <div className="flex  my-4 gap-2">
                <Disclosure>
                  <Disclosure.Trigger>Allocation Breakdown</Disclosure.Trigger>
                  <Disclosure.Panel>
                    <div className="flex flex-col gap-2">
                      {breakDown?.map((item, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 bg-zinc-950 rounded"
                        >
                          <div className="flex-1">
                            {item.type === "mystic" ? (
                              <span className="font-medium">
                                <strong>{item.quantity}</strong> Mystic Axies
                                <span className="text-sm text-stone-300">
                                  {" "}
                                  ({item.quantity} × 2 allocations)
                                </span>
                              </span>
                            ) : (
                              <span className="font-medium">
                                <strong>{item.quantity}</strong> -{" "}
                                {item.legendaryCount}x Legendary Parts
                                <span className="text-sm text-stone-300">
                                  ({item.quantity} ×{" "}
                                  {getAllocationFromLegendaryCount(
                                    item.legendaryCount || 0
                                  )}{" "}
                                  allocations)
                                </span>
                              </span>
                            )}
                          </div>
                          <div className="font-semibold">
                            {item.totalAllocation} allocations
                          </div>
                        </div>
                      ))}
                    </div>
                  </Disclosure.Panel>
                </Disclosure>
              </div>
            )}

            <div className="flex flex-col mt-10">
              <div className="flex flex-col md:flex-row w-full justify-between md:items-center">
                <h2 className="text-lg font-Poppins  mb-4">
                  Your Delegated WL Spot
                </h2>
                {delegates.length != 0 && (
                  <Button
                    isDisabled={totalAllocation === 0 || totalChickens === 0}
                    onPress={() => {
                      setSelectedAcct({
                        walletAddress: undefined,
                        amount: undefined,
                      });
                      setIsOpen(true);
                      setIsUpdate(false);
                    }}
                    size="small"
                  >
                    Add Delegatee
                  </Button>
                )}
              </div>

              <div className="overflow-x-hidden mt-4 ">
                {isPending ? (
                  <div className="flex flex-col gap-2 w-full h-full">
                    {Array.from({ length: 4 }).map((_, idx) => (
                      <Skeleton className="w-full h-12 rounded-md" key={idx} />
                    ))}
                  </div>
                ) : delegates.length != 0 ? (
                  <>
                    <div className="flex flex-col md:hidden">
                      {delegates.map((item, idx) => (
                        <div
                          className="bg-zinc-950 border border-zinc-900 rounded-lg p-4 mb-2"
                          key={idx}
                        >
                          <div className="flex flex-col gap-2">
                            <div>
                              <p className="text-sm text-muted-fg">
                                Wallet Address
                              </p>
                              <p className="text-sm break-all">
                                {item.walletAddress}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-fg">Amount</p>
                              <p>{item.amount}</p>
                            </div>
                            <div className="flex justify-end gap-3 mt-2 pt-2 border-t border-zinc-800"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="hidden md:flex">
                      <Table aria-label="Whitelist Spot">
                        <Table.Header className="bg-zinc-950 border-b border-zinc-900">
                          <Table.Column isRowHeader>
                            Wallet Address
                          </Table.Column>
                          <Table.Column>Amount</Table.Column>
                          <Table.Column />
                          <Table.Body items={delegates}>
                            {(item) => (
                              <Table.Row id={item.walletAddress}>
                                <Table.Cell>{item.walletAddress}</Table.Cell>
                                <Table.Cell>{item.amount}</Table.Cell>
                                <Table.Cell></Table.Cell>
                              </Table.Row>
                            )}
                          </Table.Body>
                        </Table.Header>
                      </Table>
                    </div>
                  </>
                ) : (
                  <div className="flex flex-col items-center justify-center gap-2">
                    <p className="text-muted-fg">
                      Spread the love, delegate WL Spot!
                    </p>

                    <Button
                      isDisabled={totalAllocation === 0 || totalChickens === 0}
                      onPress={() => {
                        setSelectedAcct({
                          walletAddress: undefined,
                          amount: undefined,
                        });
                        setIsOpen(true);
                        setIsUpdate(false);
                      }}
                      size="small"
                    >
                      <IconPlus /> Add Delegatee
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Box>
      </div>
    </>
  );
}
