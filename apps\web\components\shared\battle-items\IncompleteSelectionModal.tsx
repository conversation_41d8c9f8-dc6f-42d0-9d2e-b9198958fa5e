"use client";

import React from "react";
import { Alert<PERSON><PERSON><PERSON>, Swords, X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import useMatchmakingStore from "@/store/match-making";

interface IncompleteSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export const IncompleteSelectionModal: React.FC<IncompleteSelectionModalProps> = ({
  isOpen,
  onClose,
  onConfirm
}) => {
  const { selectedBattleItems } = useMatchmakingStore();

  if (!isOpen) return null;

  const incompleteSlots = selectedBattleItems
    .map((item, index) => item === -1 ? index + 1 : null)
    .filter(Boolean) as number[];

  const hasSelectedItems = selectedBattleItems.some(item => item !== -1);

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-[60] p-4 backdrop-blur-sm">
      <div
        className="bg-gray-800 border border-gray-700 rounded-lg shadow-xl max-w-md w-full animate-fadeIn"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-yellow-900 to-yellow-800 p-4 border-b border-gray-700 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-yellow-400 mr-2" />
              <h3 className="text-xl font-bold text-white">Incomplete Selection</h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors p-1"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="mb-6">
            <p className="text-gray-200 mb-4">
              You haven't selected battle items for all rounds:
            </p>
            
            <div className="bg-gray-700/50 rounded-lg p-4 mb-4">
              <ul className="space-y-2">
                {incompleteSlots.map((round) => (
                  <li key={round} className="flex items-center text-yellow-300">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                    Round {round} - No item selected
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-blue-900/30 border border-blue-700/50 rounded-lg p-3">
              <p className="text-blue-200 text-sm">
                {hasSelectedItems ? (
                  <>
                    <span className="font-medium">Note:</span> Empty rounds will be skipped automatically. 
                    You'll fight with the items you've selected for other rounds.
                  </>
                ) : (
                  <>
                    <span className="font-medium">Fighting without items:</span> You'll enter battle 
                    with no special abilities or advantages.
                  </>
                )}
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              appearance="outline"
              className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700"
              onPress={onClose}
            >
              <X className="h-4 w-4 mr-2" />
              Select Items
            </Button>
            <Button
              intent="warning"
              className="flex-1 bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800"
              onPress={onConfirm}
            >
              <Swords className="h-4 w-4 mr-2" />
              Start Battle Anyway
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};