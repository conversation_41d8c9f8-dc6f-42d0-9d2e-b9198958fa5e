import { ReadonlyRequestCookies } from "next/dist/server/web/spec-extension/adapters/request-cookies";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

// Constants
const API_BASE_URL = process.env.HONO_API_ENDPOINT;
const JWT_COOKIE_NAME = "jwt";
const REFRESH_TOKEN_COOKIE_NAME = "refresh_token";

const COOKIE_CONFIG = {
  sameSite: "strict" as const,
  path: "/",
  secure: process.env.NODE_ENV === "production",
  httpOnly: false,
} as const;

const COOKIE_EXPIRY = {
  jwt: 24 * 60 * 60, // 1 day
  refreshToken: 7 * 24 * 60 * 60, // 7 days
} as const;

// Types
interface ApiResponse<T = any> {
  status: boolean;
  responseCode: number;
  message: string;
  data?: T;
  errors?: string[];
}

// API client functions
async function fetchApi(endpoint: string, options: RequestInit = {}) {
  return fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      "content-type": "application/json",
      ...options.headers,
    },
    cache: "no-store",
  });
}

async function getMe(jwt: string) {
  return fetchApi("/api/me", {
    method: "GET",
    headers: { authorization: `Bearer ${jwt}` },
  });
}

async function refreshToken(refreshToken: string) {
  return fetchApi("/api/auth/refresh", {
    method: "POST",
    body: JSON.stringify({ refreshToken }),
  });
}

// Response helpers
function createErrorResponse(
  status: number,
  message: string,
  errors?: string[]
): NextResponse<ApiResponse> {
  return NextResponse.json(
    {
      status: false,
      responseCode: status,
      message,
      errors,
    },
    { status }
  );
}

function createSuccessResponse(
  data: any,
  message: string = "Success"
): NextResponse<ApiResponse> {
  return NextResponse.json({
    status: true,
    responseCode: 200,
    message,
    data,
  });
}

// Cookie helpers
function setAuthCookies(
  response: NextResponse,
  { token, refreshToken }: { token: string; refreshToken: string }
) {
  response.cookies.set(JWT_COOKIE_NAME, token, {
    ...COOKIE_CONFIG,
    maxAge: COOKIE_EXPIRY.jwt,
  });

  response.cookies.set(REFRESH_TOKEN_COOKIE_NAME, refreshToken, {
    ...COOKIE_CONFIG,
    maxAge: COOKIE_EXPIRY.refreshToken,
  });
}

function clearAuthCookies(cookieStore: ReadonlyRequestCookies) {
  cookieStore.delete(JWT_COOKIE_NAME);
  cookieStore.delete(REFRESH_TOKEN_COOKIE_NAME);
}

// Main authentication handler
async function handleAuthentication(cookieStore: ReadonlyRequestCookies) {
  const jwt = cookieStore.get(JWT_COOKIE_NAME)?.value;
  const refreshTokenValue = cookieStore.get(REFRESH_TOKEN_COOKIE_NAME)?.value;

  if (!jwt) {
    if (!refreshTokenValue) {
      // Return a more user-friendly error message when no tokens are present
      return createErrorResponse(401, "Authentication required", [
        "Please connect your wallet to access this feature",
      ]);
    }
  }

  try {
    // Try with current JWT
    if (jwt) {
      const meResponse = await getMe(jwt);
      if (meResponse.ok) {
        const data = await meResponse.json();
        return createSuccessResponse(data);
      }
    }

    if (refreshTokenValue) {
      // Try refreshing token
      const refreshResponse = await refreshToken(refreshTokenValue);

      if (!refreshResponse.ok) {
        throw new Error("Token refresh failed");
      }

      const { data: refreshData } = await refreshResponse.json();
      const newMeResponse = await getMe(refreshData.token);

      if (!newMeResponse.ok) {
        throw new Error("Authentication failed after token refresh");
      }

      const meData = await newMeResponse.json();

      const response = createSuccessResponse(
        meData,
        "Successfully refreshed the token"
      );

      setAuthCookies(response, {
        token: refreshData.token,
        refreshToken: refreshData.refreshToken,
      });

      await new Promise((resolve) => setTimeout(resolve, 1000));

      return response;
    }
    throw new Error("Authentication failed.");
  } catch (error) {
    console.error("Authentication error:", error);

    clearAuthCookies(cookieStore);

    // Return a more user-friendly error message
    return createErrorResponse(401, "Authentication failed", [
      "Your session has expired or is invalid. Please reconnect your wallet.",
    ]);
  }
}

// Main handler
export async function GET(request: NextRequest) {
  const cookieStore = await cookies();
  return handleAuthentication(cookieStore);
}
