import { handleError } from "@/utils/handle-error";
import { create } from "zustand";
import CornToCookieAbi from "@/abi/CornToCookie.abi.json";
import {
  Address,
  erc1155Abi,
  erc20Abi,
  formatEther,
  parseEventLogs,
} from "viem";
import { toast } from "sonner";
import useAuthStore from "./auth";
import { ronin, saigon } from "viem/chains";

const CHAIN_ID = Number(process.env.NEXT_PUBLIC_CHAINDID || "2021");
const COCK_TOKEN_ADDRESS = process.env.NEXT_PUBLIC_COCK_CONTRACT as Address;
const FEATHERS_TOKEN_ADDRESS = process.env
  .NEXT_PUBLIC_FEATHERS_CONTRACT as Address;
const RESOURCES_ADDRESS = process.env.NEXT_PUBLIC_GAMEITEMS_CONTRACT as Address;
const CORN_CRAFTING = process.env.NEXT_PUBLIC_VARIABLE_CRAFTING as Address;
const chain = CHAIN_ID === 2020 ? ronin : saigon;

interface CornAllocation {
  [cornId: number]: number;
}

type Recipe = {
  type: bigint;
  address: string;
  amount: bigint;
  tokenId: bigint;
  tokenType?: number; // Add this to match the interface used in main component
  tokenAddress?: string; // Add this alias for consistency
};

type CornToCookieState = {
  isPending: boolean;
  isFetchingCraftable: boolean;
  craftableCookies: {
    recipes: Recipe[];
    tokenId: bigint;
    acceptedResourceIds: bigint[]; // New field for per-recipe accepted resource IDs
  }[];
};

type Actions = {
  craftCookie: (
    tokenId: number,
    amount: number,
    referralCode: string,
    cornAllocation?: CornAllocation
  ) => Promise<{ tokenIds: bigint[]; amounts: bigint[] } | undefined>;
  getCraftableCookies: () => Promise<void>;
  approveCock: (amt: number) => Promise<void>;
  approveFeathers: () => Promise<void>;
  approveResources: () => Promise<void>;
};

type StoreState = CornToCookieState & Actions;

const initialState = {
  isPending: false,
  craftableCookies: [],
  isFetchingCraftable: false,
};

const useCornToCookieStore = create<StoreState>()((set, get) => {
  return {
    ...initialState,
    getCraftableCookies: async () => {
      set({ isFetchingCraftable: true });
      try {
        const stateContext = window.stateContext;
        if (!stateContext || !stateContext.address) {
          return;
        }
        const { address, publicClient } = stateContext;

        // Call the updated batchGetRecipes function which now returns both recipes and acceptedResourceIds
        const batchResult = await publicClient.readContract({
          address: process.env.NEXT_PUBLIC_VARIABLE_CRAFTING as Address,
          abi: CornToCookieAbi,
          functionName: "batchGetRecipes",
          args: [[0, 1, 2, 3, 4, 5, 6]], // Extended to cover more possible items
        });

        // The new contract returns [recipesData, acceptedResourceIdsArray]
        const [recipesData, acceptedResourceIdsArray] = batchResult as [
          Array<{
            exists: boolean;
            isActive: boolean;
            materials: Array<{
              amount: bigint;
              tokenAddress: string;
              tokenId: bigint;
              tokenType: number;
            }>;
          }>,
          Array<bigint[]>,
        ];

        // Transform recipes data to match new craftableCookies structure
        const craftableCookies = recipesData
          .map((recipe, index) => {
            // Only include recipes that exist and are active
            if (!recipe.exists || !recipe.isActive) {
              return null;
            }

            // Transform materials into Recipe format with both interfaces
            const recipes: Recipe[] = recipe.materials.map((material) => ({
              type: BigInt(material.tokenType),
              address: material.tokenAddress,
              amount: material.amount,
              tokenId: material.tokenId,
              // Add aliases for consistency with main component
              tokenType: material.tokenType,
              tokenAddress: material.tokenAddress,
            }));

            return {
              recipes: recipes,
              tokenId: BigInt(index),
              acceptedResourceIds: acceptedResourceIdsArray[index] || [], // Include accepted resource IDs for this recipe
            };
          })
          .filter(
            (
              item
            ): item is {
              recipes: Recipe[];
              tokenId: bigint;
              acceptedResourceIds: bigint[];
            } => item !== null
          );

        set({ craftableCookies });
      } catch (error) {
        handleError(error);
        set({ craftableCookies: [] });
      } finally {
        set({ isFetchingCraftable: false });
      }
    },

    craftCookie: async (
      tokenId,
      amount,
      referralCode = "0x",
      cornAllocation
    ) => {
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot craft cookie", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const { checkApprovals, fetchBalances } = useAuthStore.getState();

      try {
        await ConnectRecentWallet();

        if (!address || !walletClient) {
          toast.error("Cannot craft cookie", {
            description: "Wallet not connected",
            position: "top-right",
          });
          return;
        }

        set({ isPending: true });

        // Find the recipe for this item
        const recipe = get().craftableCookies.find(
          (cookie) => Number(cookie.tokenId) === tokenId
        );

        if (!recipe) {
          toast.error("Cannot craft cookie", {
            description: "Recipe not found",
            position: "top-right",
          });
          return;
        }

        // Check if this recipe has ERC1155_ANY_RESOURCE materials (tokenType === 2)
        const hasAnyResourceMaterial = recipe.recipes.some(
          (r) => r.tokenType === 2
        );

        // Check required approvals for each material in the recipe
        let needsResourcesApproval = false;
        let needsFeathersApproval = false;
        let totalCockRequired = 0n;

        // Validate corn allocation against accepted resource IDs if using any resource materials
        if (hasAnyResourceMaterial && cornAllocation) {
          const acceptedResourceIdsSet = new Set(
            recipe.acceptedResourceIds.map((id) => Number(id))
          );

          // Validate that all provided corn IDs are in the accepted list
          for (const [cornIdStr, cornAmount] of Object.entries(
            cornAllocation
          )) {
            const cornId = Number(cornIdStr);
            if (cornAmount > 0 && !acceptedResourceIdsSet.has(cornId)) {
              toast.error("Invalid resource", {
                description: `Resource ID ${cornId} is not accepted for this recipe`,
                position: "top-right",
              });
              return;
            }
          }
        }

        // Determine what approvals are needed based on materials
        if (hasAnyResourceMaterial && cornAllocation) {
          // For recipes with ERC1155_ANY_RESOURCE materials and corn allocation
          const cornEntries = Object.entries(cornAllocation).filter(
            ([_, amount]) => amount > 0
          );

          // Check if corn resources need approval (assuming they're ERC1155 from RESOURCES contract)
          if (cornEntries.length > 0) {
            needsResourcesApproval = true;
          }

          // Check other materials in the recipe (non-ERC1155_ANY_RESOURCE)
          const otherMaterials = recipe.recipes.filter(
            (r) => r.tokenType !== 2
          );
          for (const material of otherMaterials) {
            const requiredAmount = material.amount * BigInt(amount);

            if (material.tokenType === 0) {
              // ERC20 token
              if (
                material.address.toLowerCase() ===
                COCK_TOKEN_ADDRESS?.toLowerCase()
              ) {
                totalCockRequired += requiredAmount;
              }
            } else if (material.tokenType === 1) {
              // ERC1155 token
              if (
                material.address.toLowerCase() ===
                RESOURCES_ADDRESS?.toLowerCase()
              ) {
                needsResourcesApproval = true;
              } else if (
                material.address.toLowerCase() ===
                FEATHERS_TOKEN_ADDRESS?.toLowerCase()
              ) {
                needsFeathersApproval = true;
              }
            }
          }
        } else {
          // For regular recipes without ERC1155_ANY_RESOURCE materials, check all materials
          for (const material of recipe.recipes) {
            const requiredAmount = material.amount * BigInt(amount);

            if (material.tokenType === 0) {
              // ERC20 token
              if (
                material.address.toLowerCase() ===
                COCK_TOKEN_ADDRESS?.toLowerCase()
              ) {
                totalCockRequired += requiredAmount;
              }
            } else if (material.tokenType === 1) {
              // ERC1155 token
              if (
                material.address.toLowerCase() ===
                RESOURCES_ADDRESS?.toLowerCase()
              ) {
                needsResourcesApproval = true;
              } else if (
                material.address.toLowerCase() ===
                FEATHERS_TOKEN_ADDRESS?.toLowerCase()
              ) {
                needsFeathersApproval = true;
              }
            }
          }
        }

        // Get current approval states from auth store
        const {
          isResourcesApprovedForAllCookieCrafting,
          isFeathersApprovedForAllCookieCrafting,
          cockAllowanceCookieCrafting,
        } = useAuthStore.getState();

        // Get approval functions
        const { approveResources, approveFeathers, approveCock } = get();

        // Handle approvals in sequence
        if (
          needsResourcesApproval &&
          !isResourcesApprovedForAllCookieCrafting
        ) {
          toast.info("Resources approval required", {
            description: "Approving resources for crafting...",
            position: "top-right",
          });
          await approveResources();
        }

        if (needsFeathersApproval && !isFeathersApprovedForAllCookieCrafting) {
          toast.info("Feathers approval required", {
            description: "Approving feathers for crafting...",
            position: "top-right",
          });
          await approveFeathers();
        }

        if (
          totalCockRequired > 0n &&
          cockAllowanceCookieCrafting < totalCockRequired
        ) {
          toast.info("COCK approval required", {
            description: "Approving COCK tokens for crafting...",
            position: "top-right",
          });
          await approveCock(Number(totalCockRequired));
        }

        const r =
          referralCode === undefined || !referralCode || referralCode === ""
            ? "0x"
            : referralCode;

        let resourceIds: number[] = [];
        let resourceAmounts: number[] = [];

        // Handle resource allocation for ERC1155_ANY_RESOURCE materials
        if (hasAnyResourceMaterial && cornAllocation) {
          // Filter out zero amounts and validate against accepted resource IDs
          const cornEntries = Object.entries(cornAllocation).filter(
            ([cornIdStr, cornAmount]) => {
              const cornId = Number(cornIdStr);
              return (
                cornAmount > 0 &&
                recipe.acceptedResourceIds.some((id) => Number(id) === cornId)
              );
            }
          );

          // Add corn allocations to the arrays
          cornEntries.forEach(([cornId, cornAmount]) => {
            resourceIds.push(Number(cornId));
            resourceAmounts.push(cornAmount); // Scale by the crafting amount
          });
        }

        // Use the craft function from the ABI
        const { request } = await publicClient.simulateContract({
          address: CORN_CRAFTING,
          abi: CornToCookieAbi,
          functionName: "craft",
          args: [
            BigInt(tokenId), // outputTokenId
            BigInt(amount), // amount
            resourceIds, // resourceIds (for ERC1155_ANY_RESOURCE materials)
            resourceAmounts, // resourceAmounts (for ERC1155_ANY_RESOURCE materials)
            r, // referralCode
          ],
          account: address,
        });

        // Execute the transaction
        const hash = await walletClient.writeContract(request);

        toast.info("Crafting transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-center",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          // Refresh balances after successful craft
          await Promise.all([
            checkApprovals(isConnected, publicClient, address),
            fetchBalances(isConnected, publicClient, address),
          ]);

          // Parse event logs to get crafted items info
          const logs = parseEventLogs({
            abi: CornToCookieAbi,
            eventName: "ItemCrafted", // Based on your ABI
            logs: receipt.logs,
          });

          if (logs.length > 0) {
            // Extract the relevant data from the event
            const eventData = logs[0] as any;
            return {
              tokenIds: [eventData.args.outputTokenId],
              amounts: [eventData.args.amount],
            };
          } else {
            // If no specific event, return the expected result
            return {
              tokenIds: [BigInt(tokenId)],
              amounts: [BigInt(amount)],
            };
          }
        } else {
          toast.error("Crafting transaction failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-center",
          });
          throw new Error("Crafting transaction failed");
        }
      } catch (error) {
        handleError(error);
      } finally {
        set({ isPending: false });
      }
    },

    approveResources: async () => {
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot approve resources", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const { checkApprovals } = useAuthStore.getState();

      try {
        await ConnectRecentWallet();

        if (!address || !walletClient) {
          toast.error("Cannot approve ERC1155", {
            description: "Wallet not connected",
            position: "top-right",
          });
          return;
        }

        set({ isPending: true });
        toast.info("Preparing approval transaction...", {
          description: "Setting approval for all resources tokens",
          position: "top-right",
        });

        // Set approval for all for ERC1155 token
        const hash = await walletClient.writeContract({
          address: RESOURCES_ADDRESS,
          abi: erc1155Abi,
          functionName: "setApprovalForAll",
          args: [CORN_CRAFTING, true],
          chain,
          account: address,
        });

        toast.info("Approval transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-right",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          toast.success("ERC1155 approval successful", {
            description:
              "Successfully approved all resources tokens for spending",
            position: "top-right",
          });

          // Update the approval status after successful approval
          await checkApprovals(isConnected, publicClient, address);
        } else {
          toast.error("ERC1155 approval failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-right",
          });
        }
      } catch (error) {
        handleError(error);
      } finally {
        set({ isPending: false });
      }
    },

    approveFeathers: async () => {
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot approve ERC1155", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const { checkApprovals } = useAuthStore.getState();

      try {
        await ConnectRecentWallet();

        if (!address || !walletClient) {
          toast.error("Cannot approve ERC1155", {
            description: "Wallet not connected",
            position: "top-right",
          });
          return;
        }

        set({ isPending: true });
        toast.info("Preparing approval transaction...", {
          description: "Setting approval for all Feathers tokens",
          position: "top-right",
        });

        // Set approval for all for ERC1155 token
        const hash = await walletClient.writeContract({
          address: FEATHERS_TOKEN_ADDRESS,
          abi: erc1155Abi,
          functionName: "setApprovalForAll",
          args: [CORN_CRAFTING, true],
          chain,
          account: address,
        });

        toast.info("Approval transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-right",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          toast.success("ERC1155 approval successful", {
            description:
              "Successfully approved all Feathers tokens for spending",
            position: "top-right",
          });

          // Update the approval status after successful approval
          await checkApprovals(isConnected, publicClient, address);
        } else {
          toast.error("ERC1155 approval failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-right",
          });
        }
      } catch (error) {
        handleError(error);
      } finally {
        set({ isPending: false });
      }
    },

    approveCock: async (amt) => {
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot approve ERC20", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const { checkApprovals } = useAuthStore.getState();

      try {
        await ConnectRecentWallet();

        if (!address || !walletClient) {
          toast.error("Cannot approve ERC20", {
            description: "Wallet not connected",
            position: "top-right",
          });
          return;
        }

        set({ isPending: true });

        const amtWithPrice = BigInt(amt);

        toast.info("Preparing approval transaction...", {
          description: `Approving ${formatEther(amtWithPrice)} COCK tokens for spending`,
          position: "top-right",
        });

        const { request } = await publicClient.simulateContract({
          address: COCK_TOKEN_ADDRESS,
          abi: erc20Abi,
          functionName: "approve",
          args: [CORN_CRAFTING, amtWithPrice],
          chain,
          account: address,
        });

        const hash = await walletClient.writeContract(request);

        toast.info("Approval transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-right",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          toast.success("ERC20 approval successful", {
            description: `Approved ${formatEther(amtWithPrice)} COCK tokens for spending`,
            position: "top-right",
          });

          // Update the allowance after successful approval
          await checkApprovals(isConnected, publicClient, address);
        } else {
          toast.error("ERC20 approval failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-right",
          });
        }
      } catch (error) {
        handleError(error);
      } finally {
        set({ isPending: false });
      }
    },
  };
});

export default useCornToCookieStore;
