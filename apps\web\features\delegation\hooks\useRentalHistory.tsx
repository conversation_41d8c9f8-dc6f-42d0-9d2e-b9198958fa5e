"use client";

import { useState, useMemo, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  IRentalWithMetadata,
  IRentalHistoryResponse,
  IRentalHistoryFilters,
  IRental,
  IRentalApiResponse,
} from "../types/delegation.types";
import {
  DelegationAPI,
  transformRentalApiResponse,
} from "../api/delegation.api";
import useChickenMetadata from "@/features/breeding/tab/breeding/hooks/useChickenMetadata";
import { IChickenMetadata } from "@/lib/types/chicken.types";
import { useStateContext } from "@/providers/app/state";

// Helper function to create rental with real metadata
const createRentalWithRealMetadata = (
  rental: IRental,
  metadataMap: Record<number, IChickenMetadata>
): IRentalWithMetadata => {
  const metadata = metadataMap[rental.chickenTokenId];

  // Extract daily feathers and legendary count from metadata attributes
  const dailyFeathersAttr = metadata?.attributes.find(
    (attr) => attr.trait_type === "Daily Feathers"
  );
  const legendaryCountAttr = metadata?.attributes.find(
    (attr) => attr.trait_type === "Legendary Count"
  );

  const dailyFeathers = dailyFeathersAttr?.value
    ? typeof dailyFeathersAttr.value === "string"
      ? parseInt(dailyFeathersAttr.value)
      : Number(dailyFeathersAttr.value)
    : 0;

  const legendaryCount = legendaryCountAttr?.value
    ? typeof legendaryCountAttr.value === "string"
      ? parseInt(legendaryCountAttr.value)
      : Number(legendaryCountAttr.value)
    : 0;

  return {
    ...rental,
    chickenMetadata: metadata,
    dailyFeathers,
    legendaryCount,
  };
};

// Real API function
const fetchRentalHistory = async (
  page: number = 1,
  pageSize: number = 10
): Promise<IRentalHistoryResponse> => {
  return await DelegationAPI.getRentalHistory(page, pageSize);
};

export function useRentalHistory(pageSize: number = 12) {
  const { address, isConnected } = useStateContext();
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch rental history
  const {
    data: historyResponse,
    isLoading: isLoadingHistory,
    error,
    refetch,
  } = useQuery({
    queryKey: ["rental-history", address, currentPage, pageSize],
    queryFn: () => fetchRentalHistory(currentPage, pageSize),
    enabled: !!address && isConnected,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });

  // Extract token IDs from rental history events for metadata fetching
  const tokenIds = useMemo(() => {
    if (!historyResponse?.data?.data) return [];
    return historyResponse.data.data
      .map((event) => {
        // Get token ID from nested rental or event data (API uses snake_case)
        const rental = event.rental as IRentalApiResponse | undefined;
        const eventData = event.event_data || {};
        return rental?.chicken_token_id || 0;
      })
      .filter((id) => id > 0);
  }, [historyResponse?.data?.data]);

  // Fetch chicken metadata for all events
  const {
    metadataMap,
    isLoading: isLoadingMetadata,
    error: metadataError,
  } = useChickenMetadata(tokenIds);

  // Combine event data with metadata - extract rental from events
  const historyWithMetadata = useMemo(() => {
    if (!historyResponse?.data?.data || !metadataMap) return [];

    return historyResponse.data.data
      .map((event) => event.rental) // Extract rental from event
      .filter((rental): rental is IRentalApiResponse => !!rental) // Filter out null rentals
      .map((rental) => transformRentalApiResponse(rental))
      .map((rental) => createRentalWithRealMetadata(rental, metadataMap));
  }, [historyResponse?.data?.data, metadataMap]);

  // Pagination helpers
  const pagination = useMemo(() => {
    if (!historyResponse?.data?.meta) return null;

    const meta = historyResponse.data.meta;
    return {
      currentPage: meta.currentPage,
      totalPages: meta.lastPage,
      totalItems: meta.total,
      itemsPerPage: meta.perPage,
      hasNextPage: meta.nextPageUrl !== null,
      hasPreviousPage: meta.previousPageUrl !== null,
    };
  }, [historyResponse?.data?.meta]);

  // Navigation functions
  const goToPage = (page: number) => {
    if (pagination && page >= 1 && page <= pagination.totalPages) {
      setCurrentPage(page);
    }
  };

  const goToNextPage = () => {
    if (pagination?.hasNextPage) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (pagination?.hasPreviousPage) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToFirstPage = () => {
    setCurrentPage(1);
  };

  const goToLastPage = () => {
    if (pagination) {
      setCurrentPage(pagination.totalPages);
    }
  };

  // Combined loading state
  const isLoading = isLoadingHistory || isLoadingMetadata;

  return {
    // Data
    history: historyWithMetadata,
    pagination,

    // Loading states
    isLoading,

    // Error states
    error: error || metadataError,

    // Actions
    refetch,

    // Pagination controls
    currentPage,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    goToFirstPage,
    goToLastPage,
  };
}

// Enhanced rental history hook with filtering and comprehensive event data
export function useRentalHistoryEnhanced(
  filters: IRentalHistoryFilters = {},
  pageSize: number = 12
) {
  const { address, isConnected } = useStateContext();
  const [currentPage, setCurrentPage] = useState(filters.page || 1);
  const [activeFilters, setActiveFilters] =
    useState<IRentalHistoryFilters>(filters);

  // Update filters when props change
  useEffect(() => {
    setActiveFilters(filters);
    if (filters.page && filters.page !== currentPage) {
      setCurrentPage(filters.page);
    }
  }, [filters]); // Remove currentPage from dependencies to prevent infinite loop

  // Fetch enhanced rental history
  const {
    data: historyResponse,
    isLoading: isLoadingHistory,
    error,
    refetch,
  } = useQuery({
    queryKey: [
      "rental-history-enhanced",
      address,
      currentPage,
      pageSize,
      activeFilters,
    ],
    queryFn: () =>
      DelegationAPI.getRentalHistoryEnhanced({
        ...activeFilters,
        page: currentPage,
        pageSize,
      }),
    enabled: !!address && isConnected,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });

  // Extract unique token IDs from rental history events for metadata fetching
  const tokenIds = useMemo(() => {
    if (!historyResponse?.data?.data) return [];
    const uniqueTokenIds = new Set(
      historyResponse.data.data
        .map((event: any) => {
          // Try to get token ID from nested rental data or event_data (API uses snake_case)
          const rental = event.rental as IRentalApiResponse | undefined;
          const eventData = event.event_data || {};
          return rental?.chicken_token_id || 0;
        })
        .filter((id: number) => id > 0) // Filter out invalid IDs
    );
    return Array.from(uniqueTokenIds) as number[];
  }, [historyResponse?.data?.data]);

  // Fetch chicken metadata for all events
  const {
    metadataMap,
    isLoading: isLoadingMetadata,
    error: metadataError,
  } = useChickenMetadata(tokenIds);

  // Combine event data with metadata and extract rental data from nested rental object
  const eventsWithMetadata = useMemo(() => {
    if (!historyResponse?.data?.data || !metadataMap) return [];

    return historyResponse.data.data.map((event: any) => {
      // Extract rental data from nested rental object and event_data as fallback (API uses snake_case)
      const rental = event.rental as IRentalApiResponse | undefined;
      const eventData = event.event_data || {};

      return {
        ...event,
        // Use rental data if available, otherwise fallback to event_data
        chicken_token_id: rental?.chicken_token_id || 0,
        owner_address: rental?.owner_address || eventData.ownerAddress || null,
        renter_address:
          rental?.renter_address || eventData.renterAddress || null,
        ronin_price: rental?.ronin_price || eventData.originalPrice || null,
        rental_period:
          rental?.rental_period || eventData.rentalDuration || null,
        reward_distribution:
          rental?.reward_distribution ||
          eventData.originalTerms?.rewardDistribution ||
          null,
        delegated_task:
          rental?.delegated_task ||
          eventData.originalTerms?.delegatedTask ||
          null,
        shared_reward_amount:
          rental?.shared_reward_amount ||
          eventData.originalTerms?.sharedRewardAmount ||
          null,
        chickenMetadata: metadataMap[rental?.chicken_token_id || 0] || null,
      };
    });
  }, [historyResponse?.data?.data, metadataMap]);

  // Pagination helpers
  const pagination = useMemo(() => {
    if (!historyResponse?.data?.meta) return null;

    const meta = historyResponse.data.meta;
    return {
      currentPage: meta.current_page,
      totalPages: meta.last_page,
      totalItems: meta.total,
      itemsPerPage: meta.per_page,
      hasNextPage: meta.next_page_url !== null,
      hasPreviousPage: meta.previous_page_url !== null,
    };
  }, [historyResponse?.data?.meta]);

  // Navigation functions
  const goToPage = (page: number) => {
    if (pagination && page >= 1 && page <= pagination.totalPages) {
      setCurrentPage(page);
    }
  };

  const goToNextPage = () => {
    if (pagination?.hasNextPage) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (pagination?.hasPreviousPage) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToFirstPage = () => {
    setCurrentPage(1);
  };

  const goToLastPage = () => {
    if (pagination) {
      setCurrentPage(pagination.totalPages);
    }
  };

  // Filter update function
  const updateFilters = (newFilters: Partial<IRentalHistoryFilters>) => {
    setActiveFilters((prev) => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Combined loading state
  const isLoading = isLoadingHistory || isLoadingMetadata;

  return {
    // Data
    events: eventsWithMetadata,
    pagination,

    // Loading states
    isLoading,

    // Error states
    error: error || metadataError,

    // Actions
    refetch,
    updateFilters,

    // Pagination controls
    currentPage,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    goToFirstPage,
    goToLastPage,

    // Current filters
    filters: activeFilters,
  };
}
