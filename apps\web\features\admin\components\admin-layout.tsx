"use client";

import { Button, cn } from "@/components/ui";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAdmin } from "../lib/use-admin";

interface IAdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: IAdminLayoutProps) {
  const { isAdmin, isLoading } = useAdmin();
  const pathname = usePathname();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Loading...</h2>
          <p>Verifying admin access</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="mb-4">
            You do not have permission to access this area.
          </p>
          <Button intent="primary">
            <Link href="/">Return to Home</Link>
          </Button>
        </div>
      </div>
    );
  }

  const navItems = [
    { name: "Dashboard", href: "/admin" },
    { name: "Breeding Cooldowns", href: "/admin/breeding-cooldowns" },
    { name: "Breeding Fees", href: "/admin/breeding-fees" },
  ];

  return (
    <div className="min-h-screen bg-[#121212] text-white">
      {/* Header */}
      <header className="bg-[#1E1E1E] border-b border-[#333] py-4 px-6">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-bold">Admin Panel</h1>
          <Button intent="secondary" size="small">
            <Link href="/">Back to Site</Link>
          </Button>
        </div>
      </header>

      <div className="flex flex-col min-h-[calc(100vh_-_68.8px)] md:flex-row">
        {/* Sidebar */}
        <aside className="w-full md:w-64 bg-[#1E1E1E] border-r border-[#333] p-4">
          <nav>
            <ul className="space-y-2">
              {navItems.map((item) => (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className={cn(
                      "block px-4 py-2 rounded-md transition-colors",
                      pathname === item.href
                        ? "bg-primary text-primary-fg font-medium"
                        : "hover:bg-[#333] text-gray-300"
                    )}
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </aside>

        {/* Main content */}
        <main className="flex-1 p-6">{children}</main>
      </div>
    </div>
  );
}
