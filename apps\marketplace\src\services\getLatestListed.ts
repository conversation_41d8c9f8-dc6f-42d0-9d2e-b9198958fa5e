import axios from "axios";
import { env } from "../utils/env.js";
import { Address } from "viem";

interface Order {
  startedAt: number;
}

type ResponseData = {
  erc721Tokens: {
    total: number;
    results: TokenData[];
  };
};

interface TokenData {
  tokenAddress: string;
  tokenId: string;
  owner: string;
  order: Order;
}

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

async function getListedNfts(
  from: number = 0,
  contractAddress: Address
): Promise<ResponseData> {
  const graphqlEndpoint = env.MARKETPLACE_GRAPHQL_ENDPOINT;
  const data = {
    operationName: "GetERC721TokensList",
    variables: {
      from,
      auctionType: "Sale",
      size: 50,
      sort: "PriceAsc",
      rangeCriteria: [],
      tokenAddress: contractAddress,
    },
    query: `query GetERC721TokensList($tokenAddress: String, $auctionType: AuctionType, $from: Int!, $size: Int!, $sort: SortBy, $rangeCriteria: [RangeSearchCriteria!]) {
      erc721Tokens(tokenAddress: $tokenAddress, auctionType: $auctionType, from: $from, size: $size, sort: $sort, rangeCriteria: $rangeCriteria) {
        total
        results {
          tokenAddress
          tokenId
          owner
          name
          image
          order {
            currentPrice
            paymentToken
            startedAt
          }
        }
      }
    }`,
  };

  const headers = {
    "Content-Type": "application/json",
    "x-api-key": env.SKYMAVIS_API_KEY,
  };

  const response = await axios.post<{ data: ResponseData }>(
    graphqlEndpoint,
    data,
    { headers }
  );
  return response.data.data;
}

export async function listedNfts() {
  const openOrders: { id: string; address: string; startedAt: number }[] = [];

  const legacyAddress = env.LEGACY_CONTRACT as Address;
  const genesisAddress = env.CHICKEN_CONTRACT as Address;
  const contractAddresses = [genesisAddress, legacyAddress];
  const DELAY_BETWEEN_REQUESTS = 2000;

  try {
    for (const contractAddress of contractAddresses) {
      let from = 0;
      while (true) {
        const fetchNft = await getListedNfts(from, contractAddress);

        // Process current batch
        fetchNft.erc721Tokens.results.forEach((item) => {
          openOrders.push({
            id: item.tokenId,
            address: item.owner,
            startedAt: item.order.startedAt,
          });
        });

        // If we got less results than requested, we're done
        if (fetchNft.erc721Tokens.results.length < 50) {
          break;
        }

        // Update from for next batch
        from += 50;

        // Add delay before next request
        await delay(DELAY_BETWEEN_REQUESTS);
      }
    }

    return openOrders;
  } catch (error) {
    console.error("Error fetching NFTs:", error);
    throw error;
  }
}
