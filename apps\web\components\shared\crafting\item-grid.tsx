"use client";
import { Badge, cn } from "@/components/ui";
import useFoodCraftingStore from "@/store/food-crafting";
import useCornToCookieStore from "@/store/corn-to-cookie";
import { useCallback } from "react";

interface CraftingItem {
  id: number;
  name: string;
  image: string;
  craftable: boolean;
  item?: boolean;
}

interface ItemGridProps {
  items: CraftingItem[];
  selectedItem: number | null;
  onSelectItem: (index: number) => void;
  balances: Record<number, bigint>;
  className?: string;
  craftable?: boolean;
  setSelectedItemId: (index: number) => void;
}

export default function ItemGrid({
  setSelectedItemId,
  items,
  selectedItem,
  onSelectItem,
  balances,
  className,
}: ItemGridProps) {
  const { craftableItems } = useFoodCraftingStore();
  const { craftableCookies } = useCornToCookieStore();

  // Helper function to get recipe for an item
  const getRecipeForItem = useCallback(
    (itemId: number) => {
      return craftableItems.find(
        (recipe) => recipe.tokenId === itemId && recipe.exists
      );
    },
    [craftableItems]
  );

  // Helper function to check if item is craftable via corn system
  const getCornRecipeForItem = useCallback(
    (itemId: number) => {
      return (
        craftableCookies.find((cookie) => Number(cookie.tokenId) === itemId) ||
        null
      );
    },
    [craftableCookies]
  );

  return (
    <div
      className={cn(
        "grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-4 xl:grid-cols-5 gap-2 sm:gap-3 p-2",
        className
      )}
    >
      {items.map((item, index) => {
        const balance = balances[item.id]?.toString() || "0";
        return (
          <div
            key={index}
            className={`relative group rounded-lg overflow-hidden transition-all duration-200 cursor-pointer ${
              selectedItem === index
                ? "ring-2 ring-primary scale-105 shadow-lg shadow-primary/20"
                : "hover:ring-1 hover:ring-primary/50 hover:scale-102"
            }`}
            onClick={() => {
              onSelectItem(index);
              setSelectedItemId(item.id);
            }}
          >
            <div className="aspect-square bg-stone-800/80 p-2 flex items-center justify-center">
              <img
                src={item.image}
                alt={item.name}
                className="w-full h-full object-contain transition-transform group-hover:scale-110"
              />
            </div>
            <div className="p-2 bg-stone-800/90">
              <p className="text-sm font-medium text-white truncate">
                {item.name}
              </p>
              <div className="flex justify-between items-center mt-1">
                {(() => {
                  const recipe = getRecipeForItem(item.id);
                  const cornRecipe = getCornRecipeForItem(item.id);
                  const isRandomCookie = item.id === 100000;

                  if (isRandomCookie && item.craftable) {
                    // Random cookie - always show as craftable if marked as such
                    return (
                      <Badge className="text-xs" intent="success">
                        Craftable
                      </Badge>
                    );
                  } else if (recipe || cornRecipe) {
                    // Has either regular recipe or corn recipe
                    return (
                      <Badge className="text-xs" intent="success">
                        Craftable
                      </Badge>
                    );
                  } else {
                    // No recipe available
                    return (
                      <Badge className="text-xs" intent="secondary">
                        No Recipe
                      </Badge>
                    );
                  }
                })()}
                {item.item && (
                  <span className="text-xs text-white/70 font-medium">
                    x{balance}
                  </span>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
