"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, Checkbox, Tooltip } from "ui";
import { cn } from "@/utils/classes";
import Image from "next/image";
import {
  IChickenWithRewards,
  IChickenSelection,
  ERewardType,
  IGameRewardItem,
} from "../types/inventory.types";
import { rewardMetadataMap } from "../mock/mock-data";

interface IChickenRewardsTableProps {
  chickens: IChickenWithRewards[];
  selectedChickens: IChickenSelection;
  onToggleChickenSelection: (chickenId: string) => void;
  onSelectAllChickens: () => void;
  onDeselectAllChickens: () => void;
  onChickenClick: (chickenId: string) => void;
  selectedChickenId: string | null;
  onClaimChicken: (chickenId: string) => void;
  className?: string;
}

/**
 * ChickenRewardsTable Component
 *
 * Clean table layout similar to ninuno rewards system
 * More efficient use of space than the grid layout
 */
export const ChickenRewardsTable: React.FC<IChickenRewardsTableProps> = ({
  chickens,
  selectedChickens,
  onToggleChickenSelection,
  onSelectAllChickens,
  onDeselectAllChickens,
  onChickenClick,
  selectedChickenId,
  onClaimChicken,
  className,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const itemsPerPage = 5;

  const selectedCount = Object.values(selectedChickens).filter(Boolean).length;
  const totalCount = chickens.length;
  const isAllSelected = totalCount > 0 && selectedCount === totalCount;
  const isIndeterminate = selectedCount > 0 && selectedCount < totalCount;

  // Pagination logic
  const totalPages = Math.ceil(chickens.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentChickens = chickens.slice(startIndex, endIndex);

  // Get reward type badge color
  const getTypeColor = (type: ERewardType) => {
    switch (type) {
      case ERewardType.CRYSTAL:
        return "bg-blue-600/30 text-blue-200 border border-blue-400/40";
      case ERewardType.SHARD:
        return "bg-purple-600/30 text-purple-200 border border-purple-400/40";
      case ERewardType.CORN:
        return "bg-amber-600/30 text-amber-200 border border-amber-400/40";
      default:
        return "bg-stone-600/30 text-stone-200 border border-stone-400/40";
    }
  };

  const formatLastUpdated = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const toggleRowExpansion = (chickenId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(chickenId)) {
      newExpanded.delete(chickenId);
    } else {
      newExpanded.add(chickenId);
    }
    setExpandedRows(newExpanded);
  };

  return (
    <div
      className={cn(
        "bg-stone-900/50 rounded-lg border border-amber-400/20 p-6",
        className
      )}
    >
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-3">
          <div>
            <h2 className="text-xl font-semibold text-amber-200 mb-1">
              Your Chickens
            </h2>
            <p className="text-sm text-stone-400">
              Select chickens to claim their accumulated rewards
            </p>
          </div>
        </div>
        {/* Selection Status Row */}
        <div className="p-3 bg-stone-800/40 rounded-lg border border-stone-600/30">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div className="flex items-center gap-2 sm:gap-4">
              <span className="text-sm font-medium text-stone-300">
                Selection:
              </span>
              <span className="text-sm font-semibold text-amber-400">
                {selectedCount} of {totalCount} chickens
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-stone-400">
                {selectedCount === 0
                  ? "No chickens selected"
                  : selectedCount === totalCount
                    ? "All chickens selected"
                    : `${totalCount - selectedCount} more to select`}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-hidden rounded-lg border border-stone-600/30">
        {/* Table Header - Hidden on mobile */}
        <div className="hidden md:block bg-stone-800/60 px-6 py-4 border-b border-stone-600/30">
          <div className="grid grid-cols-10 gap-4 items-center">
            <div className="col-span-1 flex items-center gap-2">
              <Checkbox
                isSelected={isAllSelected}
                isIndeterminate={isIndeterminate}
                onChange={
                  isAllSelected ? onDeselectAllChickens : onSelectAllChickens
                }
                className="scale-110"
              />
              <Tooltip delay={0}>
                <Tooltip.Trigger aria-label="Follow My Twitter">
                  <div className="w-3 h-3 bg-amber-500/20 z-30 rounded-full flex items-center justify-center cursor-help">
                    <span className="text-xs text-amber-400">?</span>
                  </div>
                </Tooltip.Trigger>
                <Tooltip.Content>
                  <p className="mb-1 font-medium">Selection and actions:</p>
                  <p className="mb-1">
                    ✓ <span className="text-amber-400">Checkbox</span>: Select
                    for bulk actions
                  </p>
                  <p>
                    👆 <span className="text-blue-400">Details</span>: Toggle
                    row details
                  </p>
                </Tooltip.Content>
              </Tooltip>
            </div>
            <div className="col-span-4">
              <span className="text-sm font-semibold text-stone-300">
                Chicken
              </span>
            </div>
            <div className="col-span-3">
              <span className="text-sm font-semibold text-stone-300">
                Accumulated Rewards
              </span>
            </div>
            <div className="col-span-2">
              <span className="text-sm font-semibold text-stone-300">
                Actions
              </span>
            </div>
          </div>
        </div>

        {/* Mobile Header */}
        <div className="md:hidden bg-stone-800/60 px-4 py-3 border-b border-stone-600/30 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Checkbox
              isSelected={isAllSelected}
              isIndeterminate={isIndeterminate}
              onChange={
                isAllSelected ? onDeselectAllChickens : onSelectAllChickens
              }
              className="scale-110"
            />
            <span className="text-sm font-semibold text-stone-300">
              Select All
            </span>
          </div>
          <div className="text-xs text-stone-400">
            {selectedCount} / {totalCount} selected
          </div>
        </div>

        {/* Table Body */}
        <div className="bg-stone-900/40">
          {currentChickens.map((chicken) => (
            <div
              key={chicken.chickenId}
              className={cn(
                "px-4 md:px-6 py-4 border-b border-stone-600/20 hover:bg-stone-800/40 transition-all duration-200 cursor-pointer relative",
                selectedChickenId === chicken.chickenId &&
                  "bg-amber-500/10 border-amber-400/30 ring-1 ring-amber-400/30",
                selectedChickens[chicken.chickenId] &&
                  "bg-blue-500/5 border-blue-400/20"
              )}
              onClick={() => onChickenClick(chicken.chickenId)}
            >
              {selectedChickenId === chicken.chickenId && (
                <div className="absolute left-0 top-0 bottom-0 w-1 bg-amber-400/60"></div>
              )}

              {/* Desktop Layout */}
              <div className="hidden md:grid grid-cols-10 gap-4 items-center">
                {/* Checkbox */}
                <div className="col-span-1">
                  <div
                    onClick={(e) => e.stopPropagation()}
                    className="group relative p-1 rounded hover:bg-blue-500/10 transition-colors"
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        onToggleChickenSelection(chicken.chickenId);
                      }
                    }}
                    aria-label={`Select ${chicken.name} for bulk actions`}
                  >
                    <Checkbox
                      isSelected={selectedChickens[chicken.chickenId] || false}
                      onChange={() =>
                        onToggleChickenSelection(chicken.chickenId)
                      }
                      className="scale-110"
                    />
                    {selectedChickens[chicken.chickenId] && (
                      <div
                        className="absolute -top-1 -right-1 w-2 h-2 bg-blue-400 rounded-full"
                        aria-hidden="true"
                      ></div>
                    )}
                  </div>
                </div>

                {/* Chicken Info */}
                <div className="col-span-4 flex items-center gap-3">
                  <div className="w-12 h-12 rounded-lg overflow-hidden border border-stone-600/40 flex-shrink-0">
                    <Image
                      src={chicken.image}
                      alt={chicken.name}
                      width={48}
                      height={48}
                      quality={100}
                      unoptimized
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <h3 className="font-semibold text-amber-200 text-sm">
                      {chicken.name}
                    </h3>
                    <p className="text-xs text-stone-400">
                      ID: {chicken.chickenId}
                    </p>
                  </div>
                </div>

                {/* Rewards */}
                <div className="col-span-3">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-lg font-bold text-amber-400">
                      {chicken.totalRewards}
                    </span>
                    <span className="text-xs text-stone-400">
                      total rewards
                    </span>
                    {/* <span className="text-xs text-stone-500">
                      ({chicken.rewards.length} unclaimed)
                    </span> */}
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {Object.entries(chicken.rewardsByType).map(
                      ([type, quantity]) => (
                        <span
                          key={type}
                          className={cn(
                            "px-1.5 py-0.5 rounded text-xs font-medium",
                            getTypeColor(type as ERewardType)
                          )}
                        >
                          {quantity} {type}
                        </span>
                      )
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="col-span-2 flex items-center gap-2">
                  <Button
                    size="extra-small"
                    appearance="plain"
                    onPress={() => toggleRowExpansion(chicken.chickenId)}
                    className="text-amber-400 hover:text-amber-300 bg-stone-800 hover:bg-stone-700 border border-stone-600"
                  >
                    {expandedRows.has(chicken.chickenId)
                      ? "Hide details"
                      : "View details"}
                  </Button>
                </div>
              </div>

              {/* Mobile Layout */}
              <div className="md:hidden">
                <div className="flex items-center gap-3 mb-3">
                  <div
                    onClick={(e) => e.stopPropagation()}
                    className="group relative p-1 rounded hover:bg-blue-500/10 transition-colors"
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        onToggleChickenSelection(chicken.chickenId);
                      }
                    }}
                    aria-label={`Select ${chicken.name} for bulk actions`}
                  >
                    <Checkbox
                      isSelected={selectedChickens[chicken.chickenId] || false}
                      onChange={() =>
                        onToggleChickenSelection(chicken.chickenId)
                      }
                      className="scale-110"
                    />
                    {selectedChickens[chicken.chickenId] && (
                      <div
                        className="absolute -top-1 -right-1 w-2 h-2 bg-blue-400 rounded-full"
                        aria-hidden="true"
                      ></div>
                    )}
                  </div>
                  <div className="w-10 h-10 rounded-lg overflow-hidden border border-stone-600/40 flex-shrink-0">
                    <Image
                      src={chicken.image}
                      alt={chicken.name}
                      width={40}
                      height={40}
                      quality={100}
                      unoptimized
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-amber-200 text-sm">
                      {chicken.name}
                    </h3>
                    <p className="text-xs text-stone-400">
                      ID: {chicken.chickenId}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-amber-400">
                      {chicken.totalRewards}
                    </div>
                    <div className="text-xs text-stone-400">rewards</div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-1">
                    {Object.entries(chicken.rewardsByType).map(
                      ([type, quantity]) => (
                        <span
                          key={type}
                          className={cn(
                            "px-1.5 py-0.5 rounded text-xs font-medium",
                            getTypeColor(type as ERewardType)
                          )}
                        >
                          {type} {quantity}
                        </span>
                      )
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="extra-small"
                      appearance="plain"
                      onPress={() => toggleRowExpansion(chicken.chickenId)}
                      className="text-amber-400 hover:text-amber-300 bg-stone-800 hover:bg-stone-700 border border-stone-600 px-2"
                    >
                      {expandedRows.has(chicken.chickenId)
                        ? "Hide details"
                        : "View details"}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Expandable Details Section */}
              {expandedRows.has(chicken.chickenId) && (
                <div className="px-6 py-6 bg-stone-800/20 border-t border-stone-600/20">
                  {/* Detailed Breakdown - Full Width */}
                  <div className="max-w-2xl mx-auto">
                    <h4 className="text-sm font-semibold text-amber-200 mb-4 text-center">
                      Reward Details
                    </h4>
                    <div className="space-y-3 max-h-64 overflow-y-auto custom-scrollbar">
                      {(() => {
                        // Group rewards by metadata for display
                        const groupedRewards = new Map<
                          number,
                          { metadata: any; rewards: IGameRewardItem[] }
                        >();

                        chicken.rewards.forEach((reward) => {
                          const metadata = rewardMetadataMap.get(
                            reward.rewardId
                          );
                          if (metadata) {
                            if (!groupedRewards.has(reward.rewardId)) {
                              groupedRewards.set(reward.rewardId, {
                                metadata,
                                rewards: [],
                              });
                            }
                            groupedRewards
                              .get(reward.rewardId)!
                              .rewards.push(reward);
                          }
                        });

                        return Array.from(groupedRewards.entries()).map(
                          ([rewardId, { metadata, rewards }]) => {
                            const totalQuantity = rewards.reduce(
                              (sum, reward) => sum + (reward.quantity || 1),
                              0
                            );

                            return (
                              <div
                                key={rewardId}
                                className="flex items-center justify-between p-3 bg-stone-800/60 rounded-lg border border-stone-600/30 hover:bg-stone-800/80 transition-colors"
                              >
                                <div className="flex items-center gap-3">
                                  <div className="w-8 h-8 flex items-center justify-center bg-stone-700/50 rounded-lg border border-stone-600/40">
                                    <Image
                                      src={metadata.image}
                                      alt={metadata.name}
                                      width={24}
                                      height={24}
                                      quality={100}
                                      unoptimized
                                      className="w-full h-full object-contain"
                                    />
                                  </div>
                                  <div>
                                    <span className="text-sm font-semibold text-amber-200">
                                      {metadata.name}
                                    </span>
                                    {/* <p className="text-xs text-stone-400 mt-0.5">
                                      {metadata.description}
                                    </p> */}
                                  </div>
                                </div>
                                <div className="bg-amber-500/90 text-amber-900 px-3 py-1.5 rounded-full text-sm font-bold">
                                  {totalQuantity}
                                </div>
                              </div>
                            );
                          }
                        );
                      })()}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Empty State */}
        {chickens.length === 0 && (
          <div className="bg-stone-900/40 px-6 py-12 text-center">
            <div className="animate-pulse">
              <span className="text-4xl mb-3 block">🐔</span>
            </div>
            <h3 className="text-lg font-medium text-amber-200 mb-2">
              No chickens with rewards
            </h3>
            <p className="text-sm text-stone-400">
              Your chickens haven't earned any rewards yet.
            </p>
            <p className="text-sm text-stone-400 mt-1">
              Play battles to earn rewards for your chickens!
            </p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-4 mt-6">
          <Button
            size="small"
            appearance="plain"
            onPress={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
            isDisabled={currentPage === 1}
            className="text-stone-300 hover:text-amber-200"
          >
            Previous
          </Button>

          <span className="text-sm text-stone-300">
            Page {currentPage} of {totalPages}
          </span>

          <Button
            size="small"
            appearance="plain"
            onPress={() =>
              setCurrentPage((prev) => Math.min(totalPages, prev + 1))
            }
            isDisabled={currentPage === totalPages}
            className="text-stone-300 hover:text-amber-200"
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
};
