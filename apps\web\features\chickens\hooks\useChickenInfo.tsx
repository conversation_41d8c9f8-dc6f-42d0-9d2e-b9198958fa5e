"use client";

import { useMemo } from "react";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import { useChickenTokenIds } from "./useChickenTokenIds";
import useChickenMetadata from "@/features/breeding/tab/breeding/hooks/useChickenMetadata";
import useChickenGenes from "@/features/breeding/tab/breeding/hooks/useChickenGenes";
import { useBulkBattleStats } from "@/features/battle/hooks/useBattleStats";
import { useChickenBreedCountWithConfig } from "@/features/breeding/tab/breeding/hooks/useChickenBreedCount";
import { useChickenCooldownWithConfig } from "@/features/breeding/tab/breeding/hooks/useChickenCooldown";
import { DelegationAPI } from "@/features/delegation/api/delegation.api";
import { useQuery } from "@tanstack/react-query";
import { fetchChickens } from "@/features/breeding/tab/breeding/hooks/useChickens";
import { chickenBulkFetcherAddress } from "@/providers/web3/abi/chicken-bulk-fetcher";
import {
  IChickenInfo,
  IChickenInfoResponse,
  IChickenInfoOptions,
  EChickenType,
  IChickenRentalStatus,
  IChickenBattleStats,
  IChickenDataMaps,
} from "../types/chicken-info.types";
import { IAttribute } from "@/lib/types/chicken.types";

/**
 * Default options for chicken info fetching
 */
const DEFAULT_OPTIONS: IChickenInfoOptions = {
  includeMetadata: true,
  includeGenes: true,
  includeBattleStats: true,
  includeBreedCount: true,
  includeCooldown: true,
  includeRentalStatus: true,
  enableAutoRefresh: false,
  refreshInterval: 5 * 60 * 1000, // 5 minutes
};

/**
 * Helper function to determine chicken type from metadata
 */
const getChickenTypeFromMetadata = (metadata: any): EChickenType => {
  const typeAttribute = metadata?.attributes?.find(
    (attr: IAttribute) => attr.trait_type === "Type"
  );

  // Handle both string and array formats
  let typeValue: string;
  if (Array.isArray(typeAttribute?.value)) {
    typeValue = typeAttribute.value[0] as string;
  } else {
    typeValue = typeAttribute?.value as string;
  }

  switch (typeValue?.toLowerCase()) {
    case "ordinary":
      return EChickenType.ORDINARY;
    case "legacy":
      return EChickenType.LEGACY;
    case "genesis":
      return EChickenType.GENESIS;
    default:
      return EChickenType.ORDINARY; // Default fallback
  }
};

/**
 * Helper function to determine rental status from rental data
 */
const getChickenRentalStatusFromData = (
  rental: any | null
): IChickenRentalStatus => {
  if (!rental) {
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  }

  if (rental.status === 0) {
    return {
      isAvailable: false,
      rentalStatus: "listed",
      statusLabel: "Listed in Rental Market",
    };
  } else if (rental.status === 1) {
    const roninPrice = rental.roninPrice || rental.ronin_price;
    const isDelegation = roninPrice === "0" || roninPrice === 0;

    if (isDelegation) {
      return {
        isAvailable: false,
        rentalStatus: "delegated",
        statusLabel: "Already Delegated",
      };
    } else {
      return {
        isAvailable: false,
        rentalStatus: "rented",
        statusLabel: "Already Rented",
      };
    }
  } else if (rental.status === 2 || rental.status === 3) {
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  }

  return {
    isAvailable: true,
    rentalStatus: "available",
    statusLabel: "Available",
  };
};

/**
 * Helper function to check if chicken is on cooldown
 */
const isChickenOnCooldown = (
  battleStats?: IChickenBattleStats,
  cooldownTime?: number
): boolean => {
  // Check battle state cooldowns
  if (
    battleStats?.state === "faint" ||
    battleStats?.state === "dead" ||
    battleStats?.state === "breeding"
  ) {
    return true;
  }

  // Check breeding cooldown
  if (cooldownTime && cooldownTime * 1000 > Date.now()) {
    return true;
  }

  return false;
};

/**
 * Main hook for fetching comprehensive chicken information
 */
export const useChickenInfo = (
  address?: Address,
  options: Partial<IChickenInfoOptions> = {}
): IChickenInfoResponse => {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };

  // Check if bulk fetcher is available
  const isBulkFetcherAvailable = !!chickenBulkFetcherAddress;

  // Fetch chicken token IDs using bulk fetcher if available
  const {
    tokenIdsByType,
    isLoading: tokenIdsLoading,
    error: tokenIdsError,
  } = useChickenTokenIds(isBulkFetcherAvailable ? address : undefined);

  // Fallback to GraphQL approach if bulk fetcher is not available
  const graphqlChickenQuery = useQuery({
    queryKey: ["chickens", address],
    queryFn: () => fetchChickens(address!),
    enabled: !isBulkFetcherAvailable && !!address,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchInterval: false,
  });

  // Determine which token IDs to use
  const allTokenIds = useMemo(() => {
    if (isBulkFetcherAvailable) {
      return tokenIdsByType.all;
    } else if (graphqlChickenQuery.data) {
      return graphqlChickenQuery.data
        .map((chicken) => Number(chicken.tokenId))
        .sort((a, b) => a - b);
    }
    return [];
  }, [isBulkFetcherAvailable, tokenIdsByType.all, graphqlChickenQuery.data]);

  // Fetch metadata if enabled
  const metadataQuery = useChickenMetadata(
    mergedOptions.includeMetadata ? allTokenIds : []
  );

  // Fetch genes if enabled
  const genesQuery = useChickenGenes(
    mergedOptions.includeGenes ? allTokenIds : []
  );

  // Fetch battle stats if enabled
  const battleStatsQuery = useBulkBattleStats(
    mergedOptions.includeBattleStats ? allTokenIds : []
  );

  // Fetch breed counts if enabled
  const breedCountQuery = useChickenBreedCountWithConfig(
    mergedOptions.includeBreedCount ? allTokenIds : []
  );

  // Fetch cooldown info if enabled
  const cooldownQuery = useChickenCooldownWithConfig(
    mergedOptions.includeCooldown ? allTokenIds : []
  );

  // Fetch rental status if enabled
  const rentalStatusQuery = useQuery({
    queryKey: ["chickenRentalStatuses", allTokenIds],
    queryFn: async () => {
      if (allTokenIds.length === 0) return {};

      try {
        const response = await DelegationAPI.getChickenRentalsBulk(allTokenIds);
        return response.data;
      } catch (error) {
        console.error("Failed to fetch chicken rental statuses:", error);
        return {};
      }
    },
    enabled: mergedOptions.includeRentalStatus && allTokenIds.length > 0,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchInterval: mergedOptions.enableAutoRefresh
      ? mergedOptions.refreshInterval
      : false,
  });

  // Create rental status map
  const rentalStatusMap = useMemo(() => {
    const statusMap: Record<number, IChickenRentalStatus> = {};
    if (rentalStatusQuery.data) {
      allTokenIds.forEach((tokenId) => {
        const rental = rentalStatusQuery.data![tokenId];
        statusMap[tokenId] = getChickenRentalStatusFromData(rental);
      });
    } else {
      allTokenIds.forEach((tokenId) => {
        statusMap[tokenId] = {
          isAvailable: true,
          rentalStatus: "available",
          statusLabel: "Available",
        };
      });
    }
    return statusMap;
  }, [rentalStatusQuery.data, allTokenIds]);

  // Create data maps
  const dataMaps: IChickenDataMaps = useMemo(
    () => ({
      metadataMap: metadataQuery.metadataMap || {},
      genesMap: genesQuery.genesMap || {},
      battleStatsMap: battleStatsQuery.data || {},
      breedCountMap: breedCountQuery.breedCountMap || {},
      cooldownMap: cooldownQuery.cooldownMap || {},
      rentalStatusMap,
    }),
    [
      metadataQuery.metadataMap,
      genesQuery.genesMap,
      battleStatsQuery.data,
      breedCountQuery.breedCountMap,
      cooldownQuery.cooldownMap,
      rentalStatusMap,
    ]
  );

  // Transform data into comprehensive chicken info
  const chickens: IChickenInfo[] = useMemo(() => {
    return allTokenIds.map((tokenId) => {
      const metadata = dataMaps.metadataMap[tokenId];
      const genes = dataMaps.genesMap[tokenId];
      const battleStats = dataMaps.battleStatsMap[tokenId];
      const breedCount = dataMaps.breedCountMap[tokenId];
      const cooldownTime = dataMaps.cooldownMap[tokenId];
      const rentalStatus = dataMaps.rentalStatusMap[tokenId];

      // Extract attributes from metadata
      const dailyFeathersAttribute = metadata?.attributes?.find(
        (attr: IAttribute) => attr.trait_type === "Daily Feathers"
      );
      const levelAttribute = metadata?.attributes?.find(
        (attr: IAttribute) => attr.trait_type === "Level"
      );

      // Calculate win rate
      const totalBattles =
        (battleStats?.wins || 0) + (battleStats?.losses || 0);
      const winRate =
        totalBattles > 0
          ? Math.round(((battleStats?.wins || 0) / totalBattles) * 100)
          : 0;

      // Determine chicken type
      const type = getChickenTypeFromMetadata(metadata);

      // Check cooldown status
      const isOnCooldown = isChickenOnCooldown(battleStats, cooldownTime);

      // Determine final availability
      const isAvailable = rentalStatus?.isAvailable && !isOnCooldown;

      return {
        tokenId,
        type,
        image:
          metadata?.image ||
          `https://chicken-api-ivory.vercel.app/api/image/${tokenId}.png`,
        metadata,
        genes,
        battleStats,
        level: Number(levelAttribute?.value) || battleStats?.level || 1,
        winRate,
        breedCount:
          breedCount ||
          Number(
            metadata?.attributes?.find(
              (attr: IAttribute) => attr.trait_type === "Breed Count"
            )?.value
          ) ||
          0,
        cooldownInfo: {
          cooldownTime: cooldownTime || 0,
          isOnCooldown,
          remainingTime: cooldownTime
            ? Math.max(0, cooldownTime * 1000 - Date.now())
            : 0,
        },
        dailyFeathers: Number(dailyFeathersAttribute?.value) || 0,
        rentalStatus,
        isAvailable,
        isOnCooldown,
        isDead: battleStats?.state === "dead",
        isFaint: battleStats?.state === "faint",
        isBreeding: battleStats?.state === "breeding",
      };
    });
  }, [allTokenIds, dataMaps]);

  // Group chickens by type
  const chickensByType = useMemo(() => {
    const ordinary = chickens.filter(
      (chicken) => chicken.type === EChickenType.ORDINARY
    );
    const legacy = chickens.filter(
      (chicken) => chicken.type === EChickenType.LEGACY
    );
    const genesis = chickens.filter(
      (chicken) => chicken.type === EChickenType.GENESIS
    );

    return { ordinary, legacy, genesis };
  }, [chickens]);

  // Determine loading state
  const isLoading =
    (isBulkFetcherAvailable
      ? tokenIdsLoading
      : graphqlChickenQuery.isLoading) ||
    (mergedOptions.includeMetadata && metadataQuery.isLoading) ||
    (mergedOptions.includeGenes && genesQuery.isLoading) ||
    (mergedOptions.includeBattleStats && battleStatsQuery.isLoading) ||
    (mergedOptions.includeBreedCount && breedCountQuery.isLoading) ||
    (mergedOptions.includeCooldown && cooldownQuery.isLoading) ||
    (mergedOptions.includeRentalStatus && rentalStatusQuery.isLoading) ||
    false;

  // Determine error state
  const error =
    (isBulkFetcherAvailable ? tokenIdsError : graphqlChickenQuery.error) ||
    metadataQuery.error ||
    genesQuery.error ||
    battleStatsQuery.error ||
    breedCountQuery.breedCountQuery.error ||
    cooldownQuery.cooldownQuery.error ||
    rentalStatusQuery.error;

  return {
    chickens,
    chickensByType,
    totalCount: chickens.length,
    isLoading,
    error: error as Error | null,
  };
};

/**
 * Hook for fetching chicken info for the connected wallet
 */
export const useConnectedWalletChickenInfo = (
  options?: Partial<IChickenInfoOptions>
) => {
  const { address, isConnected } = useStateContext();

  // Prevent SSR issues by only running on client
  const isClient = typeof window !== "undefined";

  return useChickenInfo(isConnected && isClient ? address : undefined, options);
};
