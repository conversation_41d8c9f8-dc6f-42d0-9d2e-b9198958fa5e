import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    domains: [
      "sabong-saga-images.s3.ap-southeast-1.amazonaws.com",
      "chicken-api-ivory.vercel.app",
      "sabong-saga-resources.s3.ap-southeast-1.amazonaws.com",
    ],
  },
  output: "standalone",
  /* config options here */
  reactStrictMode: true,
  compiler: {
    // Enable the new JSX transform
    reactRemoveProperties: process.env.NODE_ENV === "production",
    removeConsole: process.env.NODE_ENV === "production",
  },
};

export default nextConfig;
