"use client";

import React, { useState, useEffect } from "react";

// Helper to convert PHT (UTC+8) to UTC
function getTargetUTCTime(hourPHT: number, minutePHT: number) {
  let hourUTC = hourPHT - 8;
  if (hourUTC < 0) hourUTC += 24;
  return { hourUTC, minuteUTC: minutePHT };
}

export function Maintenance() {
  // Read from env, fallback to 23:00 if not set
  const targetHourPHT = parseInt(
    process.env.NEXT_PUBLIC_MAINTENANCE_HOUR_PHT ?? "23",
    10
  );
  const targetMinutePHT = parseInt(
    process.env.NEXT_PUBLIC_MAINTENANCE_MINUTE_PHT ?? "0",
    10
  );

  const [timeLeft, setTimeLeft] = useState<string>("00:00:00");

  useEffect(() => {
    const { hourUTC, minuteUTC } = getTargetUTCTime(
      targetHourPHT,
      targetMinutePHT
    );

    const calculateTimeLeft = (): string => {
      const now = new Date();
      const target = new Date(now);

      target.setUTCHours(hourUTC, minuteUTC, 0, 0);

      // If current time is past the target, set target to next day
      if (now.getTime() > target.getTime()) {
        target.setUTCDate(target.getUTCDate() + 1);
      }

      const difference = target.getTime() - now.getTime();
      if (difference <= 0) return "00:00:00";

      const hours = Math.floor(difference / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      return `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    };

    setTimeLeft(calculateTimeLeft());

    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, [targetHourPHT, targetMinutePHT]);

  return (
    <div
      className="flex flex-col min-h-screen items-center overflow-hidden bg-cover bg-center bg-no-repeat"
      style={{ backgroundImage: "url('/images/BG.png')" }}
    >
      <img
        src="/images/ss-logo.png"
        alt="logo"
        className="h-[120px] md:h-[320px] w-auto mt-24"
      />
      <p className="font-bold font-Arcadia text-4xl md:text-6xl text-[#f7cb42]">
        {timeLeft}
      </p>
      <p className="text-sm font-Poppins md:text-base lg:text-lg mt-4 px-4 text-center max-w-md">
        {"We're currently performing maintenance. Please check back soon."}
      </p>
    </div>
  );
}
