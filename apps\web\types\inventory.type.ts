export type InventoryType =
  | "my-chickens"
  | "delegated-out"
  | "feathers"
  | "items";

export interface BaseInventoryItem {
  id: string;
  type: InventoryType;
  name: string;
  image: string;
  amount: number;
}

export interface ChickenInventoryItem extends BaseInventoryItem {
  type: "my-chickens" | "delegated-out";
  tokenId: string;
  attributes?: {
    Type?: string[];
  };
}

export interface FeatherInventoryItem extends BaseInventoryItem {
  type: "feathers";
  tokenId: number;
  isLegendary: boolean;
  contractAddress: string;
}

export interface ItemInventoryItem extends BaseInventoryItem {
  type: "items";
  tokenId: number;
  description?: string;
  effects?: string[];
  disliked?: string[];
  craftable?: boolean;
}

export type InventoryItem =
  | ChickenInventoryItem
  | FeatherInventoryItem
  | ItemInventoryItem;

export interface InventoryTab {
  id: InventoryType;
  label: string;
  icon: React.ReactNode;
  count: number;
}

export interface InventorySearchFilters {
  query: string;
  type?: InventoryType;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}
