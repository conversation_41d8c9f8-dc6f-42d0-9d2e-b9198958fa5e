"use client";

import { hookstate, useHookstate } from "@hookstate/core";
import { devtools } from "@hookstate/devtools";
import { Key } from "react-aria-components";
import { IBreedingPair, IParentData } from "../types/breeding.types";
import { useStateContext } from "@/providers/app/state";
import useChickens from "./useChickens";
import { useOptimizedChickensForBreeding } from "./useOptimizedChickensForBreeding";
import { Address } from "viem";

const initialState = {
  savedReferralCode: "",
  breedOption: "manual" as Key,
  manualBreedingPair: {
    id: 1,
    parent1: 0,
    parent2: 0,
    breedingItem: 0,
    parent1Data: undefined as IParentData | undefined,
    parent2Data: undefined as IParentData | undefined,
  } as IBreedingPair,
  massBreedingPairs: [
    {
      id: 1,
      parent1: 0,
      parent2: 0,
      breedingItem: 0,
      parent1Data: undefined as IParentData | undefined,
      parent2Data: undefined as IParentData | undefined,
    },
  ] as IBreedingPair[],
  dialog: {
    chickenSelection: false,
    breed: {
      isOpen: false,
      type: "success",
      title: "",
      description: "",
    },
  },
};

const breedingState = hookstate(
  initialState,
  devtools({ key: "breedingState" })
);

export const useBreeding = () => {
  const state = useHookstate(breedingState);
  const { address } = useStateContext();
  const chickensQuery = useChickens(address as Address);

  const handleBreedingPairChange = (newPairs: IBreedingPair[]) => {
    if (state.breedOption.value === "manual") {
      if (newPairs[0]) {
        state.manualBreedingPair.set(newPairs[0]);
      }
    } else {
      state.massBreedingPairs.set(newPairs);
    }
  };

  const getSelectedChickenIds = () => {
    if (state.breedOption.value === "manual") {
      const pair = state.manualBreedingPair.get();
      return [pair.parent1, pair.parent2].filter((id) => id !== 0);
    } else {
      return state.massBreedingPairs.get().reduce((acc: number[], pair) => {
        if (pair.parent1) acc.push(pair.parent1);
        if (pair.parent2) acc.push(pair.parent2);
        return acc;
      }, []);
    }
  };

  const getAvailableChickenCount = () => {
    const selectedIds = getSelectedChickenIds();
    const totalChickens = chickensQuery.chickens.length;
    return totalChickens - selectedIds.length;
  };

  const areAllChickensSelected = () => {
    return getAvailableChickenCount() === 0;
  };

  const isPreviousPairIncomplete = () => {
    const previousPair =
      state.massBreedingPairs.get()[state.massBreedingPairs.length - 1];
    return !previousPair?.parent1 || !previousPair?.parent2;
  };

  return {
    state,
    handleBreedingPairChange,
    getSelectedChickenIds,
    getAvailableChickenCount,
    areAllChickensSelected,
    isPreviousPairIncomplete,
    ...chickensQuery,
  };
};

/**
 * Hook for optimized breeding chicken selection with contract-based loading
 */
export const useOptimizedBreeding = () => {
  const state = useHookstate(breedingState);

  // Use the optimized chicken loading hook
  const optimizedChickensQuery = useOptimizedChickensForBreeding("", {
    pageSize: 20,
    filterType: "all",
    sortBy: "tokenId",
    sortOrder: "asc",
  });

  const getSelectedChickenIds = () => {
    if (state.breedOption.value === "manual") {
      const pair = state.manualBreedingPair.get();
      return [pair.parent1, pair.parent2].filter((id) => id !== 0);
    } else {
      return state.massBreedingPairs.get().reduce((acc: number[], pair) => {
        if (pair.parent1) acc.push(pair.parent1);
        if (pair.parent2) acc.push(pair.parent2);
        return acc;
      }, []);
    }
  };

  const getAvailableChickenCount = () => {
    const selectedIds = getSelectedChickenIds();
    const totalChickens = optimizedChickensQuery.stats.total;
    return totalChickens - selectedIds.length;
  };

  const areAllChickensSelected = () => {
    return getAvailableChickenCount() === 0;
  };

  const isPreviousPairIncomplete = () => {
    const previousPair =
      state.massBreedingPairs.get()[state.massBreedingPairs.length - 1];
    return !previousPair?.parent1 || !previousPair?.parent2;
  };

  const handleBreedingPairChange = (newPairs: IBreedingPair[]) => {
    if (state.breedOption.value === "manual") {
      if (newPairs[0]) {
        state.manualBreedingPair.set(newPairs[0]);
      }
    } else {
      state.massBreedingPairs.set(newPairs);
    }
  };

  return {
    state,
    handleBreedingPairChange,
    getSelectedChickenIds,
    getAvailableChickenCount,
    areAllChickensSelected,
    isPreviousPairIncomplete,
    ...optimizedChickensQuery,
  };
};

export default breedingState;
