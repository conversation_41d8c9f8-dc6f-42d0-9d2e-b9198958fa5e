"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";
import { chain } from "@/providers/web3/web3-provider";
import { rentalAbi } from "@/providers/web3/abi/rental-abi";
import { DelegationAPI } from "../api/delegation.api";

/**
 * Interface for bulk unlisting request
 */
interface IBulkUnlistRequest {
  rentalIds: number[];
}

/**
 * Interface for bulk unlisting response from API
 */
interface IBulkUnlistResponse {
  status: number;
  message: string;
  data: Array<{
    rentalId: number;
    success: boolean;
    message?: string;
    data?: {
      rentalId: number;
      chickenTokenId: number;
      signature: string;
    };
  }>;
}

/**
 * Hook for handling bulk chicken unlisting blockchain transactions
 * Uses the unlistChickenForRentBulk contract function for efficiency
 */
export const useBulkUnlistChickenForRent = () => {
  const { publicClient, walletClient, address, Disconnect } = useStateContext();
  const { blockchainQuery } = useBlockchain();
  const queryClient = useQueryClient();

  const [isUnlisting, setIsUnlisting] = useState(false);
  const [progress, setProgress] = useState<{
    current: number;
    total: number;
    status: string;
  }>({ current: 0, total: 0, status: "" });

  // Get rental contract address from blockchain config
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  /**
   * Execute bulk unlisting process:
   * 1. Call API to get signatures for all rentals
   * 2. Execute single blockchain transaction for all unlisting operations
   * 3. Handle success/error states
   */
  const executeBulkUnlisting = async (rentalIds: number[]) => {
    try {
      if (!address || !walletClient) {
        toast.error("Cannot unlist chickens", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return { success: false, error: "Wallet not connected" };
      }

      if (!rentalAddress) {
        toast.error("Cannot unlist chickens", {
          description: "Rental contract not configured",
          position: "top-right",
        });
        return { success: false, error: "Rental contract not configured" };
      }

      if (rentalIds.length === 0) {
        toast.error("No chickens selected", {
          description: "Please select chickens to unlist",
          position: "top-right",
        });
        return { success: false, error: "No chickens selected" };
      }

      setIsUnlisting(true);
      setProgress({ current: 0, total: 4, status: "Preparing..." });

      // Step 1: Get signatures from API for all rentals
      toast.info("Preparing bulk unlisting...", {
        description: `Getting signatures for ${rentalIds.length} chickens`,
        position: "top-center",
      });

      // Call the bulk cancellation API (we'll create this)
      const bulkCancelResponse =
        await DelegationAPI.cancelRentalBulk(rentalIds);

      if (bulkCancelResponse.status !== 1 || !bulkCancelResponse.data) {
        throw new Error("Failed to get unlisting signatures from server");
      }

      // Filter successful responses
      const successfulUnlistings = bulkCancelResponse.data.filter(
        (result) => result.success && result.data
      );

      if (successfulUnlistings.length === 0) {
        throw new Error("No valid unlisting signatures received");
      }

      // Prepare arrays for contract call
      const contractRentalIds = successfulUnlistings.map((item) =>
        BigInt(item.data!.rentalId)
      );
      const chickenTokenIds = successfulUnlistings.map((item) =>
        BigInt(item.data!.chickenTokenId)
      );
      const signatures = successfulUnlistings.map(
        (item) => item.data!.signature as `0x${string}`
      );

      setProgress({
        current: 1,
        total: 4,
        status: "Simulating transaction...",
      });

      // Step 2: Simulate the bulk transaction
      toast.info("Simulating transaction...", {
        description: "Validating bulk unlisting parameters",
        position: "top-center",
      });

      await publicClient.simulateContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "unlistChickenForRentBulk",
        args: [contractRentalIds, chickenTokenIds, signatures],
        chain,
        account: address,
      });

      // Step 3: Estimate gas for the transaction
      const gasEstimate = await publicClient.estimateContractGas({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "unlistChickenForRentBulk",
        args: [contractRentalIds, chickenTokenIds, signatures],
        account: address,
      });

      setProgress({
        current: 2,
        total: 4,
        status: "Executing transaction...",
      });

      // Step 4: Execute the bulk unlisting transaction
      toast.info("Unlisting chickens on blockchain...", {
        description: `Please confirm the transaction for ${successfulUnlistings.length} chickens`,
        position: "top-center",
      });

      const hash = await walletClient.writeContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "unlistChickenForRentBulk",
        args: [contractRentalIds, chickenTokenIds, signatures],
        gas: gasEstimate + BigInt(100000), // Add buffer for bulk operation
        chain,
        account: address,
      });

      setProgress({
        current: 3,
        total: 4,
        status: "Confirming transaction...",
      });

      // Step 5: Wait for transaction confirmation
      toast.info("Confirming bulk transaction...", {
        description: "Waiting for blockchain confirmation",
        position: "top-center",
      });

      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        confirmations: 1,
      });

      if (receipt.status === "success") {
        toast.success("Chickens unlisted successfully!", {
          description: `${successfulUnlistings.length} chickens have been removed from the marketplace`,
          position: "top-center",
        });

        // Invalidate relevant queries to refresh data with proper sequencing
        setTimeout(async () => {
          // First, invalidate and refetch chickenTokenIds (blockchain data)
          await queryClient.invalidateQueries({
            queryKey: ["chickenTokenIds", "legacy", address],
          });
          await queryClient.invalidateQueries({
            queryKey: ["chickenTokenIds", "genesis", address],
          });

          // Then invalidate rental-related queries
          queryClient.invalidateQueries({ queryKey: ["available-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals", address] });
          queryClient.invalidateQueries({
            queryKey: ["chickenRentalStatuses"],
          });
        }, 2000);

        setProgress({
          current: 4,
          total: 4,
          status: "Complete",
        });

        return {
          success: true,
          hash,
          receipt,
          successfulCount: successfulUnlistings.length,
          totalRequested: rentalIds.length,
          failedCount: rentalIds.length - successfulUnlistings.length,
        };
      } else {
        throw new Error("Transaction failed");
      }
    } catch (error: any) {
      console.error("Bulk unlisting error:", error);

      const errorMessage = error?.message || "Unknown error occurred";
      toast.error("Bulk unlisting failed", {
        description: errorMessage,
        position: "top-center",
      });

      return { success: false, error: errorMessage };
    } finally {
      setIsUnlisting(false);
      setProgress({ current: 0, total: 0, status: "" });
    }
  };

  // Mutation for React Query integration
  const bulkUnlistMutation = useMutation({
    mutationFn: executeBulkUnlisting,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Bulk unlist mutation error:", error);
    },
  });

  return {
    executeBulkUnlisting,
    bulkUnlistMutation,
    isUnlisting,
    progress,
    rentalAddress,
  };
};
