"use client";

import { Button } from "@/components/ui";
import { none, State } from "@hookstate/core";
import { useState } from "react";
import { useOptimizedBreeding } from "../hooks/useBreeding";
import { useSelectedChickenGenes } from "../hooks/useSelectedChickenGenes";
import { IBreedingPair, IParentData } from "../types/breeding.types";
import BreedingCard from "./breeding-card";
import { OptimizedChickenSelectionDialog } from "./optimized-chicken-selection-dialog";

interface BreedingPairProps {
  pair: State<IBreedingPair> | undefined;
  index?: number;
}

type ParentType = "parent1" | "parent2";

export default function BreedingPair({ pair, index = 0 }: BreedingPairProps) {
  const { state } = useOptimizedBreeding();
  const [selectedParent, setSelectedParent] = useState<ParentType | null>(null);

  // Get all selected chickens for genes loading
  const getAllSelectedChickens = () => {
    const allSelected: number[] = [];

    // Add chickens from current pair
    if (pair?.parent1.value) allSelected.push(pair.parent1.value);
    if (pair?.parent2.value) allSelected.push(pair.parent2.value);

    // Add chickens from other pairs in mass breeding
    if (state.massBreedingPairs) {
      state.massBreedingPairs.forEach((p) => {
        if (p.parent1.value) allSelected.push(p.parent1.value);
        if (p.parent2.value) allSelected.push(p.parent2.value);
      });
    }

    // Add manual breeding pair if different from current pair
    if (state.breedOption.value === "manual" && state.manualBreedingPair) {
      if (state.manualBreedingPair.parent1.value)
        allSelected.push(state.manualBreedingPair.parent1.value);
      if (state.manualBreedingPair.parent2.value)
        allSelected.push(state.manualBreedingPair.parent2.value);
    }

    return [...new Set(allSelected)]; // Remove duplicates
  };

  // Load genes only for selected chickens
  const { getGenesForChicken } = useSelectedChickenGenes(
    getAllSelectedChickens()
  );

  const handleOpenDialog = (parentType: ParentType) => {
    setSelectedParent(parentType);
    state.dialog.chickenSelection.set(true);
  };

  const handleSelectChicken = (chicken: IParentData) => {
    if (!pair) return;

    // Ensure the chicken data includes genes
    const chickenWithGenes = {
      ...chicken,
      genes: chicken.genes || getGenesForChicken(chicken.tokenId),
    };

    if (selectedParent === "parent1") {
      pair.merge({
        parent1: chicken.tokenId,
        parent1Data: chickenWithGenes,
      });
    } else if (selectedParent === "parent2") {
      pair.merge({
        parent2: chicken.tokenId,
        parent2Data: chickenWithGenes,
      });
    }
    state.dialog.chickenSelection.set(false);
  };

  // Get all selected chickens from other pairs
  const getSelectedChickens = () => {
    if (!pair) return [];

    const selectedChickens = [pair.parent1.value, pair.parent2.value].filter(
      Boolean
    ) as number[];

    if (state.massBreedingPairs) {
      state.massBreedingPairs.map((p, i) => {
        if (i !== index) {
          if (p.parent1.value) selectedChickens.push(p.parent1.value);
          if (p.parent2.value) selectedChickens.push(p.parent2.value);
        }
      });
    }

    return [...new Set(selectedChickens)];
  };

  const handleRemove = () => {
    pair?.set(none);
  };

  return (
    <>
      <div className="relative flex flex-col md:flex-row justify-center items-center gap-6 md:gap-12 lg:gap-20 xl:gap-32">
        {index > 0 && (
          <Button
            className="absolute top-0 right-0 z-10"
            intent="danger"
            appearance="solid"
            size="small"
            onPress={handleRemove}
            aria-label="Remove breeding pair"
          >
            ✕
          </Button>
        )}

        <BreedingCard
          title="PARENT 1"
          parentData={pair?.parent1Data as State<IParentData>}
          isReversed
          onClick={() => handleOpenDialog("parent1")}
        />

        <div className="hidden sm:invisible md:block">
          <div className="flex flex-col items-center text-center gap-2">
            <span className="text-white text-sm">OPTIONAL</span>
            <div className="bg-[#191C21] w-full rounded-lg p-3">
              <span className="text-gray-400">BREEDING ITEM</span>
            </div>
          </div>
        </div>

        <BreedingCard
          title="PARENT 2"
          parentData={pair?.parent2Data as State<IParentData>}
          onClick={() => handleOpenDialog("parent2")}
        />
      </div>

      <OptimizedChickenSelectionDialog
        isOpen={state.dialog.chickenSelection.value}
        onOpenChange={(v) => state.dialog.chickenSelection.set(v)}
        onSelect={handleSelectChicken}
        selectedChickens={getSelectedChickens()}
      />
    </>
  );
}
