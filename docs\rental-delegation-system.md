# Rental & Delegation System Documentation

## Overview

The Sabong Saga breeding backend includes a comprehensive rental and delegation system that allows chicken owners to delegate their chickens to other players for various tasks and rewards. This system enables monetization of chicken assets through rental fees and flexible reward distribution mechanisms.

## Core Concepts

### Delegation vs Rental

- **Delegation**: The process of temporarily transferring chicken usage rights to another player
- **Rental**: The commercial aspect where chickens can be rented for a fee (RON tokens)
- **Free Delegation**: Direct delegation to a specific address without payment (roninPrice = 0)
- **Paid Rental**: Creating a marketplace listing where anyone can rent the chicken for a fee

## System Components

### Models

#### Rental Model (`app/Models/Rental.ts`)

The central model that manages all rental and delegation data.

**Key Properties:**

- `chickenTokenId`: The NFT token ID being delegated
- `ownerAddress`: Original chicken owner's blockchain address
- `renterAddress`: Delegatee's blockchain address (null for marketplace listings)
- `roninPrice`: Rental fee in wei (0 for free delegation)
- `insurancePrice`: Optional insurance fee in wei
- `rentalPeriod`: Duration in seconds
- `rentedAt`: Timestamp when delegation started
- `expiresAt`: Timestamp when delegation expires
- `status`: Current rental status (see RentalStatus enum)

**Reward Distribution Configuration:**

- `rewardDistribution`: How breeding/daily rewards are distributed
- `gameRewardDistribution`: How gameplay rewards are distributed
- `delegatedTask`: Which tasks the delegatee can perform
- `sharedRewardAmount`: Percentage for shared rewards (1-100)
- `rubStreakBenefactor`: Who benefits from rub streak bonuses
- `legendaryFeatherBenefactor`: Who benefits from legendary feather rewards

#### Enums

```typescript
// Rental Status
enum RentalStatus {
  AVAILABLE = 0, // Listed and available for rent
  RENTED = 1, // Currently being rented/delegated
  EXPIRED = 2, // Rental period has ended
  CANCELLED = 3, // Cancelled by owner
  PENDING = 4, // Awaiting blockchain confirmation
}

// Reward Distribution Types
enum RewardDistributionType {
  DELEGATOR_ONLY = 1, // All rewards go to owner
  DELEGATEE_ONLY = 2, // All rewards go to renter
  SHARED = 3, // Rewards split based on sharedRewardAmount
}

// Game Reward Distribution
enum GameRewardDistributionType {
  DELEGATOR_ONLY = 1, // Game rewards to owner only
  DELEGATEE_ONLY = 2, // Game rewards to renter only
}

// Delegated Tasks
enum DelegatedTaskType {
  DAILY_RUB = 1, // Only daily rub tasks
  GAMEPLAY = 2, // Only gameplay tasks
  BOTH = 3, // Both daily rub and gameplay
}

// Benefactor Types - UNUSED
enum RubStreakBenefactorType {
  DELEGATOR = 1, // Owner gets rub streak benefits
  DELEGATEE = 2, // Renter gets rub streak benefits
}
// UNUSED
enum LegendaryFeatherBenefactorType {
  DELEGATOR = 1, // Owner gets legendary feather benefits
  DELEGATEE = 2, // Renter gets legendary feather benefits
}
```

### Controllers

#### RentalsController (`app/Controllers/Http/RentalsController.ts`)

**Main Endpoints:**

1. **POST /rentals/create** - Create a new rental/delegation
2. **POST /rentals/create-bulk** - Create multiple rentals at once
3. **GET /rentals/available** - List available rentals with filtering
4. **POST /rentals/rent** - Rent an available chicken
5. **GET /rentals/my-rentals** - View user's rental history
6. **GET /rentals/history** - View rental transaction history
7. **POST /rentals/cancel** - Cancel a rental
8. **POST /rentals/cancel-bulk** - Cancel multiple rentals

**Public Endpoints:**

- **GET /rentals/chicken/:chickenTokenId** - Get rental info for specific chicken
- **POST /rentals/chickens-info-bulk** - Get rental info for multiple chickens
- **GET /rentals/chickens-by-wallet** - Get chickens rented by a wallet

## API Usage Examples

### Creating a Free Delegation

```json
POST /rentals/create
{
  "chickenTokenId": 12345,
  "roninPrice": "0",
  "rentalPeriod": 86400,
  "renterAddress": "******************************************",
  "rewardDistribution": 3,
  "gameRewardDistribution": 1,
  "delegatedTask": 3,
  "sharedRewardAmount": 70,
  "rubStreakBenefactor": 1,
  "legendaryFeatherBenefactor": 2
}
```

### Creating a Paid Rental Listing

```json
POST /rentals/create
{
  "chickenTokenId": 12345,
  "roninPrice": "1000000000000000000",
  "rentalPeriod": 86400,
  "insurancePrice": "100000000000000000",
  "rewardDistribution": 1,
  "gameRewardDistribution": 1,
  "delegatedTask": 3,
  "rubStreakBenefactor": 1,
  "legendaryFeatherBenefactor": 1
}
```

### Filtering Available Rentals

```
GET /rentals/available?page=1&pageSize=20&minPrice=0.1&maxPrice=5&delegatedTask=3&sortBy=ronin_price&sortOrder=asc
```

## Blockchain Integration

### Event Processing

The system monitors blockchain events through `RentalEventService`:

- **ChickenListedForRent**: When a chicken is listed for rental
- **ChickenRented**: When a chicken is successfully rented
- **ChickenUnlistedForRent**: When a rental is cancelled
- **InsuranceClaimed**: When insurance is claimed

### Event Processing Flow

1. Events are detected by `RentalEventService.watchRentalEvents()`
2. Events are queued using Bull for processing
3. `ProcessRentalEventJob` handles individual events
4. Database records are updated accordingly
5. `RentalHistoryEvent` records are created for audit trail

## Validation

### CreateRentalValidator

Validates rental creation requests with rules for:

- Required fields based on rental type
- Shared reward amount validation (1-100% when using SHARED distribution)
- Renter address requirement for free delegations

### RentChickenValidator

Simple validation for rental ID when renting a chicken.

## Database Schema

### Rentals Table

- Primary rental/delegation records
- Tracks ownership, pricing, duration, and reward configuration
- Status management for rental lifecycle

### Rental Events Table

- Raw blockchain event data
- Processing status tracking
- Retry mechanism for failed events

### Rental History Events Table

- Human-readable audit trail
- Event descriptions and metadata
- Historical record of all rental activities

## Security Considerations

1. **Authentication**: All rental operations require valid user authentication
2. **Ownership Verification**: System verifies chicken ownership before allowing rentals
3. **Signature Validation**: Blockchain signatures are validated for rental transactions
4. **Status Checks**: Prevents double-renting and invalid state transitions
5. **Insurance**: Optional insurance mechanism for high-value rentals

## Error Handling

Common error scenarios and responses:

- **401 Unauthorized**: User not authenticated
- **400 Bad Request**: Invalid parameters or chicken not found
- **409 Conflict**: Chicken already rented or invalid status
- **500 Internal Server Error**: Blockchain or database errors

## Performance Considerations

- Pagination for rental listings
- Indexed database queries for efficient filtering
- Bulk operations for managing multiple rentals
- Event processing queue to handle blockchain load
- Caching strategies for frequently accessed data

## Future Enhancements

- Advanced filtering and search capabilities
- Rental marketplace with bidding system
- Automated renewal mechanisms
- Enhanced reward distribution algorithms
- Integration with additional game mechanics
