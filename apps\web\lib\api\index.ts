"use client";

import axios from "axios";
import { API_ENDPOINTS } from "./api-helper";
import globalState from "../store";
import { parseCookies } from "nookies";

const instance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BREEDING_API_URL || "",
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Request interceptor with token logic
instance.interceptors.request.use((config) => {
  if (typeof window !== "undefined") {
    const cookies = parseCookies();
    const token = cookies?.jwt;

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }
  return config;
});

// Response interceptor with refresh token logic
instance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // If error is 401 and we haven't tried to refresh yet
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        globalState.loadingMessage.set("Refreshing token");
        globalState.loading.set(true);

        // Try to refresh the token
        const response = await fetch(API_ENDPOINTS.me);
        const data = await response.json();

        if (data.status) {
          globalState.loadingMessage.set("Token refreshed");

          // Get the new token from cookies
          const cookies = parseCookies();
          const newToken = cookies?.jwt;

          if (newToken) {
            // Retry the original request with new token from cookies
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return instance(originalRequest);
          }
        }
      } catch (refreshError) {
        // If refresh fails, logout
        window.stateContext?.Disconnect();
        return Promise.reject(refreshError);
      } finally {
        globalState.loading.set(false);
      }
    }

    return Promise.reject(error);
  }
);

export default instance;
