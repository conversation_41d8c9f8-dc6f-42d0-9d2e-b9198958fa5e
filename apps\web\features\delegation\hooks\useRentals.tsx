"use client";

import { useState, useMemo, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  IRentalWithMetadata,
  IAvailableRentalsResponse,
  IRental,
  IRentalFilters,
  IRentalApiFilters,
  RENTAL_FILTER_DEFAULTS,
} from "../types/delegation.types";
import { DelegationAPI } from "../api/delegation.api";
import { useRentChicken } from "./useRentChicken";
import useChickenMetadata from "@/features/breeding/tab/breeding/hooks/useChickenMetadata";
import { IChickenMetadata } from "@/lib/types/chicken.types";
import { Address } from "viem";

// Utility function to convert UI filters to API filters
const convertFiltersToApiFilters = (
  filters: IRentalFilters,
  searchQuery: string
): IRentalApiFilters => {
  const apiFilters: IRentalApiFilters = {};

  // Add search query
  if (searchQuery.trim()) {
    apiFilters.search = searchQuery.trim();
  }

  // Add price range (only if not default values)
  if (filters.priceRange.min > RENTAL_FILTER_DEFAULTS.PRICE_RANGE.MIN) {
    apiFilters.minPrice = filters.priceRange.min;
  }
  if (filters.priceRange.max !== RENTAL_FILTER_DEFAULTS.PRICE_RANGE.MAX) {
    apiFilters.maxPrice = filters.priceRange.max;
  }

  // Add duration range (only if not default values)
  if (filters.durationRange.min > RENTAL_FILTER_DEFAULTS.DURATION_RANGE.MIN) {
    apiFilters.minDuration = filters.durationRange.min;
  }
  if (filters.durationRange.max !== RENTAL_FILTER_DEFAULTS.DURATION_RANGE.MAX) {
    apiFilters.maxDuration = filters.durationRange.max;
  }

  // Add feather reward distribution filter
  if (filters.featherRewardDistribution.length > 0) {
    apiFilters.featherRewardDistribution =
      filters.featherRewardDistribution.join(",");
  }

  // Add game reward distribution filter
  if (filters.gameRewardDistribution.length > 0) {
    apiFilters.gameRewardDistribution =
      filters.gameRewardDistribution.join(",");
  }

  // Add delegated task filter
  if (filters.delegatedTask.length > 0) {
    apiFilters.delegatedTask = filters.delegatedTask.join(",");
  }

  // Add sorting
  const sortByMapping: Record<string, IRentalApiFilters["sortBy"]> = {
    price: "ronin_price",
    duration: "rental_period",
    created: "created_at",
    tokenId: "chicken_token_id",
  };

  apiFilters.sortBy =
    (sortByMapping[filters.sortBy] as IRentalApiFilters["sortBy"]) ||
    "created_at";
  apiFilters.sortOrder = filters.sortOrder;

  return apiFilters;
};

// Real API functions
const fetchAvailableRentals = async (
  page = 1,
  pageSize = 10,
  filters?: IRentalApiFilters
): Promise<IAvailableRentalsResponse> => {
  return await DelegationAPI.listAvailableRentals(page, pageSize, filters);
};

// Helper function to create rental with real metadata
const createRentalWithRealMetadata = (
  rental: IRental,
  metadataMap: Record<number, IChickenMetadata>
): IRentalWithMetadata => {
  const metadata = metadataMap[rental.chickenTokenId];

  // Extract daily feathers and legendary count from metadata attributes
  const dailyFeathersAttr = metadata?.attributes.find(
    (attr) => attr.trait_type === "Daily Feathers"
  );
  const legendaryCountAttr = metadata?.attributes.find(
    (attr) => attr.trait_type === "Legendary Count"
  );

  const dailyFeathers = dailyFeathersAttr?.value
    ? typeof dailyFeathersAttr.value === "string"
      ? parseInt(dailyFeathersAttr.value)
      : Number(dailyFeathersAttr.value)
    : 0;

  const legendaryCount = legendaryCountAttr?.value
    ? typeof legendaryCountAttr.value === "string"
      ? parseInt(legendaryCountAttr.value)
      : Number(legendaryCountAttr.value)
    : 0;

  return {
    ...rental,
    chickenMetadata: metadata,
    dailyFeathers,
    legendaryCount,
  };
};

export function useRentals(filters?: IRentalFilters, searchQuery?: string) {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12);
  const { executeRentChicken, isRenting: isRentingBlockchain } =
    useRentChicken();

  // Convert UI filters to API filters
  const apiFilters = useMemo(() => {
    if (!filters) return undefined;
    return convertFiltersToApiFilters(filters, searchQuery || "");
  }, [filters, searchQuery]);

  // Fetch available rentals
  const {
    data: rentalsResponse,
    isLoading: isLoadingRentals,
    error,
    refetch,
  } = useQuery({
    queryKey: ["available-rentals", currentPage, pageSize, apiFilters],
    queryFn: () => fetchAvailableRentals(currentPage, pageSize, apiFilters),
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });

  // Extract token IDs from rental data for metadata fetching
  const tokenIds = useMemo(() => {
    return (
      rentalsResponse?.data.data.map((rental) => rental.chickenTokenId) || []
    );
  }, [rentalsResponse?.data.data]);

  // Fetch chicken metadata for all rentals
  const {
    metadataMap,
    isLoading: isLoadingMetadata,
    error: metadataError,
  } = useChickenMetadata(tokenIds);

  // Convert rentals to include real metadata
  const rentalsWithMetadata: IRentalWithMetadata[] = useMemo(() => {
    if (!rentalsResponse?.data.data) return [];

    return rentalsResponse.data.data.map((rental) =>
      createRentalWithRealMetadata(rental, metadataMap)
    );
  }, [rentalsResponse?.data.data, metadataMap]);

  // Combined loading state
  const isLoading = isLoadingRentals || isLoadingMetadata;

  // Note: Rent chicken mutation is now handled by useRentChicken hook
  // which includes full blockchain interaction

  // Pagination helpers
  const totalPages = rentalsResponse?.data.meta.lastPage || 1;
  const hasNextPage = currentPage < totalPages;
  const hasPreviousPage = currentPage > 1;

  const goToNextPage = useCallback(() => {
    if (hasNextPage) {
      setCurrentPage((prev) => prev + 1);
    }
  }, [hasNextPage]);

  const goToPreviousPage = useCallback(() => {
    if (hasPreviousPage) {
      setCurrentPage((prev) => prev - 1);
    }
  }, [hasPreviousPage]);

  const goToPage = useCallback(
    (page: number) => {
      if (page >= 1 && page <= totalPages) {
        setCurrentPage(page);
      }
    },
    [totalPages]
  );

  // Rent a chicken with full blockchain interaction
  const handleRentChicken = async (rental: IRentalWithMetadata) => {
    try {
      const result = await executeRentChicken(
        rental.id,
        rental.ownerAddress as Address
      );
      return result?.success || false;
    } catch {
      return false;
    }
  };

  return {
    // Data
    rentals: rentalsWithMetadata,
    meta: rentalsResponse?.data.meta,

    // Loading states
    isLoading,
    isRenting: isRentingBlockchain,

    // Error states
    error: error || metadataError,

    // Actions
    refetch,
    rentChicken: handleRentChicken,

    // Pagination
    currentPage,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    goToNextPage,
    goToPreviousPage,
    goToPage,

    // Utilities
    reset: useCallback(() => {
      setCurrentPage(1);
    }, []),
    resetToFirstPage: useCallback(() => {
      setCurrentPage(1);
    }, []),
  };
}
