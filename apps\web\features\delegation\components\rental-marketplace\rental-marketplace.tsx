"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "ui";
import { RefreshCw, TrendingUp, Clock, Users } from "lucide-react";
import { IRentalWithMetadata } from "../../types/delegation.types";
import { RentalGrid } from "./rental-grid";
import { RentChickenDialog } from "./rent-chicken-dialog";
import { useStateContext } from "@/providers/app/state";

export function RentalMarketplace() {
  const { address } = useStateContext();
  const [selectedRental, setSelectedRental] =
    useState<IRentalWithMetadata | null>(null);
  const [showRentDialog, setShowRentDialog] = useState(false);

  const handleRentChicken = (rental: IRentalWithMetadata) => {
    setSelectedRental(rental);
    setShowRentDialog(true);
  };

  const handleConfirmRent = async (rental: IRentalWithMetadata) => {
    // The rental logic is now handled within the RentalGrid component
    setShowRentDialog(false);
    setSelectedRental(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">
          Rental Marketplace
        </h1>
        <p className="text-gray-400">
          Discover chickens available for rent from other players
        </p>
      </div>

      {/* Rental Grid */}
      <RentalGrid currentUserAddress={address} />

      {/* Rent Chicken Dialog */}
      <RentChickenDialog
        isOpen={showRentDialog}
        onOpenChange={setShowRentDialog}
        rental={selectedRental}
        onConfirmRent={handleConfirmRent}
        loading={false}
      />
    </div>
  );
}
