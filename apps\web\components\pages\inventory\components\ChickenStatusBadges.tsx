import { Heart } from "lucide-react";

interface ChickenStatusBadgesProps {
  tokenId: string;
  isFavorite: boolean;
  isImmortal: boolean;
  chickenType?:
    | "owned"
    | "delegated-to-me"
    | "rented-to-me"
    | "delegated-out"
    | "rented-out"
    | "listed-in-market";
  onToggleFavorite: (e: React.MouseEvent, tokenId: string) => void;
}

export function ChickenStatusBadges({
  tokenId,
  isFavorite,
  isImmortal,
  chickenType,
  onToggleFavorite,
}: ChickenStatusBadgesProps) {
  return (
    <>
      {/* Favorite heart button - overlay on upper right of image */}
      <button
        className={`absolute top-2 right-2 p-1 ${
          isFavorite
            ? "bg-red-500 text-black"
            : "bg-black bg-opacity-60 text-white hover:bg-red-600"
        } rounded-full transition-colors z-30`}
        onClick={(e) => onToggleFavorite(e, tokenId)}
        aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
        title={isFavorite ? "Remove from favorites" : "Add to favorites"}
      >
        <Heart className={`h-4 w-4 ${isFavorite ? "fill-black" : ""}`} />
      </button>

      {/* Immortal badge - positioned to avoid recovery timer conflict */}
      {isImmortal && (
        <div className="absolute top-2 right-10 bg-gradient-to-r from-yellow-400 to-amber-500 text-black text-xs font-bold px-2 py-1 rounded-full z-30 shadow-lg">
          ⚡
        </div>
      )}

      {/* Delegation/Rental badges - repositioned for better visibility */}
      {chickenType === "delegated-to-me" && (
        <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full z-30 shadow-lg">
          Delegated to me
        </div>
      )}

      {chickenType === "rented-to-me" && (
        <div className="absolute top-2 left-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full z-30 shadow-lg">
          Rented to me
        </div>
      )}

      {chickenType === "delegated-out" && (
        <div className="absolute top-2 left-2 bg-purple-500 text-white text-xs font-bold px-2 py-1 rounded-full z-30 shadow-lg">
          Delegated out
        </div>
      )}

      {chickenType === "rented-out" && (
        <div className="absolute top-2 left-2 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-full z-30 shadow-lg">
          Rented out
        </div>
      )}

      {chickenType === "listed-in-market" && (
        <div className="absolute top-2 left-2 bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full z-30 shadow-lg">
          Listed for rent
        </div>
      )}
    </>
  );
}
