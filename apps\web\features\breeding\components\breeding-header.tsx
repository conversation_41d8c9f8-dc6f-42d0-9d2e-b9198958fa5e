"use client";

import TokenBalance from "@/components/shared/token-balance";
import WalletBalances from "@/components/shared/wallet-balances";
import useTokens from "@/lib/hooks/useTokens";

interface IBreedingHeaderProps {
  title?: string;
}

export function BreedingHeader({ title = "Breeding" }: IBreedingHeaderProps) {
  const tokens = useTokens();

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-6 mb-4 md:mb-6">
      <h1 className="font-bold font-Arcadia text-primary text-3xl md:text-4xl">
        {title}
      </h1>

      <WalletBalances>
        <TokenBalance
          iconSrc={tokens.feather.iconImage}
          alt="Feathers"
          balance={tokens.feather.formatted.toLocaleString() || 0}
        />
        <TokenBalance
          iconSrc={tokens.cock.iconImage}
          alt="Token"
          balance={
            tokens.cock.formatted.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }) || "0.00"
          }
        />
      </WalletBalances>
    </div>
  );
}
