import {
  IRewardItem,
  ERewardType,
  IInventorySelection,
} from "../types/inventory.types";

/**
 * Mock data for Rewards Inventory
 */

// Reward metadata map - quantities come from API
export const rewardMetadataMap = new Map<number, Omit<IRewardItem, "quantity">>(
  [
    // Crystals
    [
      9,
      {
        id: "9",
        name: "Red Crystal",
        type: ERewardType.CRYSTAL,
        image: "/images/rewards/RED CRYSTAL.png",
        description:
          "Born from the droppings of chickens during the fiery Liwayway sunrise, Red Crystals are said to pulse with the energy of new beginnings. Their vibrant hue and warmth make them essential for nurturing and accelerating the growth of eggs, turning the mundane into the miraculous.",
      },
    ],
    [
      10,
      {
        id: "10",
        name: "<PERSON> Crystal",
        type: ERewardType.CRYSTAL,
        image: "/images/rewards/BUE CRYSTAL.png",
        description:
          "Once ordinary chicken droppings, these rare blue crystals are formed when a chicken's waste is exposed to moonlight and mountain dew. Farmers believe they hold the essence of life's cycles—transmuting the humble into the extraordinary. Blue Crystals are prized for their mysterious power to influence the very fabric of chicken genetics.",
      },
    ],

    // Corn
    [
      11,
      {
        id: "11",
        name: "Shiny Corn",
        type: ERewardType.CORN,
        image: "/images/rewards/SHINY CORN.png",
        description:
          "A rare, radiant kernel that gleams with an enchanting light. Shiny Corn holds a secret power that can grant chickens a fleeting touch of immortality, making them untouchable for a precious few hours.",
      },
    ],
    [
      12,
      {
        id: "12",
        name: "Red Corn",
        type: ERewardType.CORN,
        image: "/images/rewards/RED CORN.png",
        description:
          "A fiery, crimson kernel bursting with spicy energy. Chickens who eat Red Corn are said to feel their fighting spirit ignite, ready to charge into battle.",
      },
    ],
    [
      13,
      {
        id: "13",
        name: "Blue Corn",
        type: ERewardType.CORN,
        image: "/images/rewards/BLUE CORN.png",
        description:
          "A cool, sapphire-hued grain that radiates calm and resilience. Blue Corn is prized for its ability to fortify a chicken's defenses and steady their nerves.",
      },
    ],
    [
      14,
      {
        id: "14",
        name: "Yellow Corn",
        type: ERewardType.CORN,
        image: "/images/rewards/YELLOW CORN.png",
        description:
          "A bright, sunlit kernel packed with pure speed. Chickens who snack on Yellow Corn are known to dash and dart with lightning-fast reflexes.",
      },
    ],
    [
      15,
      {
        id: "15",
        name: "Orange Corn",
        type: ERewardType.CORN,
        image: "/images/rewards/ORANGE CORN.png",
        description:
          "A vibrant, zesty grain that crackles with raw cockrage. Orange Corn fills chickens with a wild, unstoppable energy, fueling their most intense outbursts.",
      },
    ],
    [
      16,
      {
        id: "16",
        name: "Violet Corn",
        type: ERewardType.CORN,
        image: "/images/rewards/VIOLET CORN.png",
        description:
          "A rare, mystical kernel shimmering with deep purple hues. Violet Corn awakens a chicken's ferocity, sharpening their instincts and unleashing their wild side.",
      },
    ],
    [
      17,
      {
        id: "17",
        name: "Green Corn",
        type: ERewardType.CORN,
        image: "/images/rewards/GREEN CORN.png",
        description:
          "A lush, emerald grain brimming with life. Green Corn enhances vitality and vigor, helping chickens from even the toughest battles.",
      },
    ],
    [
      18,
      {
        id: "18",
        name: "Black Corn",
        type: ERewardType.CORN,
        image: "/images/rewards/BLACK CORN.png",
        description:
          "A shadowy, obsidian kernel that seems to absorb the light around it. Black Corn grants chickens uncanny evasiveness, letting them slip through danger unseen.",
      },
    ],

    // Shards
    [
      19,
      {
        id: "19",
        name: "Shiny Shard",
        type: ERewardType.SHARD,
        image: "/images/rewards/SHINY SHARD.png",
        description:
          "A dazzling, iridescent shard that glimmers with every movement",
      },
    ],
    [
      20,
      {
        id: "20",
        name: "Vitalis Shard",
        type: ERewardType.SHARD,
        image: "/images/rewards/VITALIS SHARD.png",
        description:
          "A vibrant green shard brimming with pure life force. Said to restore health and vigor to any chicken who holds it.",
      },
    ],
    [
      21,
      {
        id: "21",
        name: "Aegis Shard",
        type: ERewardType.SHARD,
        image: "/images/rewards/AEGIS SHARD.png",
        description:
          "A deep blue shard that radiates a protective aura. It's prized for its ability to shield chickens from harm.",
      },
    ],
    [
      22,
      {
        id: "22",
        name: "Fervor Shard",
        type: ERewardType.SHARD,
        image: "/images/rewards/FERVOR SHARD.png",
        description:
          "A fiery red shard pulsing with raw, aggressive energy. Warriors seek it to ignite their fighting spirit.",
      },
    ],
    [
      23,
      {
        id: "23",
        name: "Gale Shard",
        type: ERewardType.SHARD,
        image: "/images/rewards/GALE SHARD.png",
        description:
          "A sleek, yellow shard that hums with restless energy. It grants chickens the swiftness of lightning.",
      },
    ],
    [
      24,
      {
        id: "24",
        name: "Shade Shard",
        type: ERewardType.SHARD,
        image: "/images/rewards/SHADE SHARD.png",
        description:
          "A smoky, shadowy shard that flickers in the light. It's believed to help chickens slip through danger unseen.",
      },
    ],
    [
      25,
      {
        id: "25",
        name: "Surge Shard",
        type: ERewardType.SHARD,
        image: "/images/rewards/SURGE SHARD.png",
        description:
          "A crackling orange shard, hot to the touch. It's formed from the pent-up energy of the most spirited chickens.",
      },
    ],
    [
      26,
      {
        id: "26",
        name: "Savage Shard",
        type: ERewardType.SHARD,
        image: "/images/rewards/SAVAGE SHARD.png",
        description:
          "A jagged, violet shard that pulses with untamed energy. It's said to sharpen a chicken's instincts and unleash their primal aggression.",
      },
    ],

    // Essences
    [
      27,
      {
        id: "27",
        name: "Red Essence",
        type: ERewardType.ESSENCE,
        image: "/images/rewards/RED ESSENCE.png",
        description:
          "Distilled from the heart of the sunrise, Red Essence crackles with untamed vigor.",
      },
    ],
    [
      28,
      {
        id: "28",
        name: "Blue Essence",
        type: ERewardType.ESSENCE,
        image: "/images/rewards/BLUE ESSENCE.png",
        description:
          "Drawn from the tranquil depths of moonlit springs, Blue Essence radiates a calming, protective energy.",
      },
    ],
    [
      29,
      {
        id: "29",
        name: "Yellow Essence",
        type: ERewardType.ESSENCE,
        image: "/images/rewards/YELLOW ESSENCE.png",
        description:
          "Captured from the fleeting brilliance of midday lightning, Yellow Essence thrums with restless motion.",
      },
    ],
    [
      30,
      {
        id: "30",
        name: "Orange Essence",
        type: ERewardType.ESSENCE,
        image: "/images/rewards/ORANGE ESSENCE.png",
        description:
          "Distilled from the fiery core of ancient embers, Orange Essence radiates with wild, unpredictable energy.",
      },
    ],
    [
      31,
      {
        id: "31",
        name: "Violet Essence",
        type: ERewardType.ESSENCE,
        image: "/images/rewards/VIOLET ESSENCE.png",
        description:
          "Forged in the twilight between day and night, Violet Essence shimmers with enigmatic power.",
      },
    ],
    [
      32,
      {
        id: "32",
        name: "Black Essence",
        type: ERewardType.ESSENCE,
        image: "/images/rewards/BLACK ESSENCE.png",
        description:
          "Born from the rare eclipse shadow, Black Essence absorbs all light and sound.",
      },
    ],
    [
      33,
      {
        id: "33",
        name: "Green Essence",
        type: ERewardType.ESSENCE,
        image: "/images/rewards/GREEN ESSENCE.png",
        description:
          "Harvested from dew-soaked meadows at dawn, Green Essence pulses with the vitality of new growth.",
      },
    ],
  ]
);

// Mock reward items for backward compatibility (will be replaced by API data)
export const mockRewardItems: IRewardItem[] = Array.from(
  rewardMetadataMap.entries()
).map(([id, metadata]) => ({
  ...metadata,
  quantity: 0,
}));

// Mock inventory selection
export const mockInventorySelection: IInventorySelection = {};
