"use client";

import TokenBalance from "@/components/shared/token-balance";
import WalletBalances from "@/components/shared/wallet-balances";
import useTokens from "@/lib/hooks/useTokens";
import React, { useEffect } from "react";
import { Tabs } from "ui";
import { NinunoRewardsTab } from "./ninuno-rewards-tab";
import { RewardsInventoryTab } from "../../inventory/components/rewards-inventory-tab";
import useClaimRewardStore from "@/store/claim-rewards";

/**
 * RewardsDashboard Component
 *
 * The main dashboard component with tabbed interface for different reward types.
 * Includes Ninuno Rewards and Rewards Inventory functionality.
 */
export const RewardsDashboard: React.FC = () => {
  const tokens = useTokens();
  const { canClaimFunc, ableToClaimInFunc } = useClaimRewardStore();

  useEffect(() => {
    canClaimFunc();
    ableToClaimInFunc();
  }, []);
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex justify-between items-start">
        <div className="">
          <h1 className="text-3xl font-bold mb-2">Rewards</h1>
          <p className="text-muted-fg">
            Manage your rewards - track earnings, claim items, and view history
          </p>
        </div>

        <WalletBalances>
          <TokenBalance
            iconSrc={tokens.feather.iconImage}
            alt="Feathers"
            balance={tokens.feather.formatted.toLocaleString() || 0}
          />
          <TokenBalance
            iconSrc={tokens.cock.iconImage}
            alt="Token"
            balance={
              tokens.cock.formatted.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              }) || "0.00"
            }
          />
        </WalletBalances>
      </div>

      <Tabs aria-label="Rewards Dashboard" className="mt-6">
        <Tabs.List>
          <Tabs.Tab id="inventory">Game Rewards</Tabs.Tab>
          <Tabs.Tab id="ninuno">Ninuno Rewards</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel id="ninuno">
          <NinunoRewardsTab />
        </Tabs.Panel>

        <Tabs.Panel id="inventory">
          <RewardsInventoryTab />
        </Tabs.Panel>
      </Tabs>
    </div>
  );
};
