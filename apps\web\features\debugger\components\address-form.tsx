"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/field";
import React from "react";

interface IAddressFormProps {
  inputAddress: string;
  setInputAddress: (value: string) => void;
  handleSubmit: (e: React.FormEvent) => void;
}

export default function AddressForm({
  inputAddress,
  setInputAddress,
  handleSubmit,
}: IAddressFormProps): React.ReactNode {
  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6">
      <form
        onSubmit={handleSubmit}
        className="flex flex-col md:flex-row md:items-end gap-4"
      >
        <div className="flex-1">
          <label
            htmlFor="address"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Wallet Address
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="flex items-center gap-3">
              <Input
                id="address"
                type="text"
                value={inputAddress}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setInputAddress(e.target.value)
                }
                placeholder="Enter wallet address (0x...)"
                className="flex-1 pl-10 border border-gray-300 rounded-lg text-gray-700 focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all"
              />
              <Button
                type="submit"
                size="medium"
                intent="primary"
                className="px-6 py-2 h-10 transition-all"
                isDisabled={!inputAddress.trim()}
              >
                <div className="flex items-center space-x-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span>Load Data</span>
                </div>
              </Button>
            </div>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Enter a wallet address to view all chickens and eggs owned by this
            address
          </p>
        </div>
      </form>
    </div>
  );
}
