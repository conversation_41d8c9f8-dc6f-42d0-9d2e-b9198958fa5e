import { BattleItem } from "@/types/battle-items.types";

export const BATTLE_ITEMS: BattleItem[] = [
  {
    id: 42,
    name: "<PERSON><PERSON>",
    description: "Deals 20% more damage on jump attacks",
    image: "/images/battle_items/TARI.png",
  },
  {
    id: 43,
    name: "Double Edged Sword",
    description:
      "Reduce defense by 20%, increase attack by the amount reduced from defense",
    image: "/images/battle_items/DOUBLE EDGE SWORD.png",
  },
  {
    id: 44,
    name: "Chamomile Tea",
    description: "Your attacks generate 20% less cockrage for your opponent",
    image: "/images/battle_items/CHAMOMILE TEA.png",
  },
  {
    id: 45,
    name: "Hot Kettle",
    description: "Attacks received generate 20% more cockrage",
    image: "/images/battle_items/HOT KETTLE.png",
  },
  {
    id: 46,
    name: "Side Mirror",
    description: "Reflect 20% of the damage received",
    image: "/images/battle_items/SIDE MIRROR.png",
  },
  {
    id: 47,
    name: "<PERSON>",
    description: "When both chickens faint, win the round",
    image: "/images/battle_items/JOKER.png",
  },
  {
    id: 48,
    name: "Steel Plate",
    description:
      "Reduce speed by 20%, increase defense by the amount reduced from speed",
    image: "/images/battle_items/STEEL PLATE.png",
  },
  {
    id: 49,
    name: "Shuriken",
    description:
      "Double evasion, removes your chicken's evasion cap for the whole round",
    image: "/images/battle_items/SHURIKEN.png",
  },
  {
    id: 50,
    name: "Agimat",
    description: "Evade a fatal attack once per round",
    image: "/images/battle_items/AGIMAT.png",
  },
  {
    id: 51,
    name: "Eye of the Tiger",
    description:
      "Reduce cockrage by 20%, increase ferocity by the amount reduced from cockrage",
    image: "/images/battle_items/EYE OF THE TIGER.png",
  },
  {
    id: 52,
    name: "Eyepatch",
    description:
      "Critical attacks penetrate 30% of the target's defense & cannot be evaded",
    image: "/images/battle_items/EYE PATCH.png",
  },
  {
    id: 53,
    name: "Garlic Belt",
    description: "Opponent chicken cannot heal nor regenerate health",
    image: "/images/battle_items/GARLIC BELT.png",
  },
  {
    id: 54,
    name: "Dracula's Teeth",
    description: "Heal 25% of damage dealt",
    image: "/images/battle_items/DRACULA_S TEETH.png",
  },
  {
    id: 55,
    name: "Four Leaf Clover",
    description: "Add 6.9% chance to critically hit",
    image: "/images/battle_items/FOUR LEAF CLOVER.png",
  },
  {
    id: 56,
    name: "Antigravity",
    description: "Chickens can't jump",
    image: "/images/battle_items/ANTI GRAVITY.png",
  },
  {
    id: 57,
    name: "Liwayway's Feather",
    description: "Double a random cookie buff",
    image: "/images/battle_items/LIWAYWAY_S FEATHER.png",
  },
  {
    id: 58,
    name: "Takipsilim's Feather",
    description: "Neutralize random enemy cookie buff",
    image: "/images/battle_items/TAKIPSILIM_S FEATHER.png",
  },
  {
    id: 59,
    name: "Sugar Cubes",
    description:
      " Increase speed by 42% but reduce speed by 10% for each successful attack.",
    image: "/images/battle_items/SUGAR CUBES.png",
  },
  {
    id: 60,
    name: "Concentration Talisman",
    description: "Your attacks cannot be interrupted",
    image: "/images/battle_items/CONCENTRATION TALISMAN.png",
  },
];

export const getBattleItemById = (id: number): BattleItem | undefined => {
  return BATTLE_ITEMS.find((item) => item.id === id);
};

export const getBattleItemsByIds = (ids: number[]): BattleItem[] => {
  return ids.map((id) => getBattleItemById(id)).filter(Boolean) as BattleItem[];
};
