"use client";

import CraftButton from "@/components/shared/crafting/craft-button";
import ItemGrid from "@/components/shared/crafting/item-grid";
import QuantitySelector from "@/components/shared/crafting/quantity-selector";
import AppNavbar from "@/components/shared/navbar";
import TokenBalance from "@/components/shared/token-balance";
import WalletBalances from "@/components/shared/wallet-balances";
import { Badge, Button, Input } from "@/components/ui";
import useAuthStore from "@/store/auth";
import useFoodCraftingStore from "@/store/food-crafting";
import useCornToCookieStore from "@/store/corn-to-cookie";
import useRandomCraftingStore from "@/store/random-crafting";
import { delay } from "@/utils/delay";
import { useCallback, useEffect, useMemo, useState } from "react";
import { formatEther } from "viem";
import CraftingAnim from "./crafting-anim";
import { CraftingTab, filterItemsByTab } from "./tab";
import { MaterialRequirements } from "./material-requirements";
import { CraftingBadges } from "./crafting-badges";
import { CraftingMethodTabs } from "./crafting-method-tabs";

// Import utilities
import {
  CornAllocation,
  ItemData,
  getRecipeForItem,
  getCornRecipeForItem,
  isType2CornRecipe,
  getAcceptedResourceIds,
  isType2RandomPool,
  getAcceptedResourceIdsForRandomPool,
  initializeCornAllocation,
  getRandomCraftingPoolPrice,
  getTotalAllocated,
  getType2Requirements,
  getType2RequirementsForRandomPool,
} from "@/utils/crafting-helpers";

import {
  validateCrafting,
  getValidationErrorMessage,
  ValidationContext,
} from "@/utils/crafting-validation";

import items from "@/data/crafting_data.json";

// Type assertion for items to ensure type safety
const typedItems = items as ItemData[];

interface CraftResult {
  tokenIds: bigint[];
  amounts: bigint[];
}

export default function CraftingPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedItem, setSelectedItem] = useState<number | null>(0);
  const [quantity, setQuantity] = useState(1);
  const [referralCode, setReferralCode] = useState<string | null>(null);
  const [selectedItemId, setSelectedItemId] = useState<number | null>(100000);
  const [craftResult, setCraftResult] = useState<CraftResult | null>(null);
  const [showCraftPopup, setShowCraftPopup] = useState(false);
  const [show, setShow] = useState(false);
  const [craftingMethod, setCraftingMethod] = useState<"recipe" | "corn">(
    "recipe"
  );
  const [cornAllocation, setCornAllocation] = useState<CornAllocation>({});

  // Store hooks
  const { feathers, cock, legendaryFeathers } = useAuthStore();
  const {
    getCraftableCookies,
    craftableCookies,
    craftCookie,
    isPending: isPendingCraftCookieWCorn,
    isFetchingCraftable,
  } = useCornToCookieStore();
  const {
    randomCockPrice,
    randomFeatherPrice,
    isPending,
    foodBalances,
    craftItems,
    craftableItems,
    fetchCraftingRecipes,
    fetchingRecipes,
    approvingCock,
    approvingFeathers,
  } = useFoodCraftingStore();
  const {
    getCraftingPools,
    craftingPools,
    craftFromPool,
    isPending: isPendingRandomCrafting,
    isFetchingPools,
  } = useRandomCraftingStore();

  // Filtered items based on active tab
  const filteredItems = useMemo(() => {
    return filterItemsByTab(typedItems, activeTab);
  }, [activeTab]);

  useEffect(() => {
    fetchCraftingRecipes();
    getCraftableCookies();
    getCraftingPools();
  }, []);

  // Reset selected item when switching tabs if the selected item is not in the new filter
  useEffect(() => {
    if (selectedItem !== null && selectedItem >= 0) {
      const currentItem = typedItems[selectedItem];
      if (currentItem && !filteredItems.includes(currentItem)) {
        setSelectedItem(null);
        setSelectedItemId(null);
      }
    }
  }, [activeTab, filteredItems, selectedItem]);

  // Update the selectedItem index to work with filtered items
  const handleSelectItem = useCallback(
    (filteredIndex: number) => {
      const selectedFilteredItem = filteredItems[filteredIndex];
      if (selectedFilteredItem) {
        // Find the original index in the full items array
        const originalIndex = typedItems.findIndex(
          (item) => item.id === selectedFilteredItem.id
        );
        setSelectedItem(originalIndex);
        setSelectedItemId(selectedFilteredItem.id);
      }
    },
    [filteredItems]
  );

  // Update corn allocation when quantity or item changes
  useEffect(() => {
    if (selectedItemId && selectedItem !== null) {
      const item = typedItems[selectedItem];

      // Handle random items
      if (item?.isRandom) {
        if (isType2RandomPool(0, craftingPools)) {
          const acceptedIds = getAcceptedResourceIdsForRandomPool(
            0,
            craftingPools
          );
          const newAllocation = initializeCornAllocation(acceptedIds);
          setCornAllocation(newAllocation);
        }
      } else if (craftingMethod === "corn") {
        // Handle regular corn crafting
        const cornRecipe = getCornRecipeForItem(
          selectedItemId,
          craftableCookies
        );
        if (cornRecipe && isType2CornRecipe(cornRecipe)) {
          const acceptedIds = getAcceptedResourceIds(cornRecipe);
          const newAllocation = initializeCornAllocation(acceptedIds);
          setCornAllocation(newAllocation);
        }
      }
    }
  }, [
    selectedItemId,
    selectedItem,
    quantity,
    craftingMethod,
    typedItems,
    craftingPools,
    craftableCookies,
  ]);

  // Get pricing from random crafting pool (pool 0)
  const randomCraftingPoolPrice = useMemo(() => {
    return getRandomCraftingPoolPrice(craftingPools, 0);
  }, [craftingPools]);

  // Validation context
  const validationContext: ValidationContext = useMemo(
    () => ({
      cock,
      feathers: Number(feathers),
      legendaryFeathers: Number(legendaryFeathers),
      foodBalances,
      quantity,
    }),
    [cock, feathers, legendaryFeathers, foodBalances, quantity]
  );

  // Main validation logic
  const enoughBalance = useMemo(() => {
    if (selectedItem === null) return false;
    const item = typedItems[selectedItem];
    if (!item) return false;

    return validateCrafting(
      item,
      craftingMethod,
      craftableItems,
      craftableCookies,
      craftingPools,
      cornAllocation,
      validationContext,
      randomCockPrice,
      randomFeatherPrice
    );
  }, [
    selectedItem,
    typedItems,
    craftingMethod,
    craftableItems,
    craftableCookies,
    craftingPools,
    cornAllocation,
    validationContext,
    randomCockPrice,
    randomFeatherPrice,
  ]);

  // Additional validation checks using same logic as error messages
  const shouldDisableCraftButton = useMemo(() => {
    if (selectedItem === null) return true;
    const item = typedItems[selectedItem];
    if (!item) return true;

    // If basic validation fails, disable button
    if (!enoughBalance) return true;

    // Additional checks for over-allocation specifically
    if (item.isRandom && isType2RandomPool(0, craftingPools)) {
      const required = getType2RequirementsForRandomPool(0, craftingPools);
      const totalAllocated = getTotalAllocated(cornAllocation);
      const totalNeeded = required ? required * quantity : 0;
      
      // Disable if over-allocated or under-allocated
      if (totalAllocated !== totalNeeded) return true;
    }

    // Check for variable/corn crafting over-allocation
    if (craftingMethod === "corn") {
      const cornRecipe = getCornRecipeForItem(item.id, craftableCookies);
      if (cornRecipe && isType2CornRecipe(cornRecipe)) {
        const required = getType2Requirements(cornRecipe);
        const totalAllocated = getTotalAllocated(cornAllocation);
        const totalNeeded = required ? required * quantity : 0;
        
        // Disable if over-allocated or under-allocated
        if (totalAllocated !== totalNeeded) return true;
      }
    }

    return false;
  }, [
    selectedItem,
    typedItems,
    enoughBalance,
    craftingMethod,
    craftableCookies,
    craftingPools,
    cornAllocation,
    quantity,
  ]);

  // Enhanced resource allocation change handler
  const handleVariableResourceAllocationChange = useCallback(
    (resourceId: number, amount: number) => {
      let acceptedIds: number[] = [];

      // Check if it's a random item or regular variable crafting
      if (selectedItem !== null && typedItems[selectedItem]?.isRandom) {
        acceptedIds = getAcceptedResourceIdsForRandomPool(0, craftingPools);
      } else {
        const variableRecipe = getCornRecipeForItem(
          selectedItemId!,
          craftableCookies
        );
        acceptedIds = getAcceptedResourceIds(variableRecipe);
      }

      // Only allow allocation for accepted resource IDs
      if (!acceptedIds.includes(resourceId)) {
        return; // Silently ignore allocation for non-accepted resources
      }

      const maxBalance = Number(foodBalances[resourceId] || 0n);
      const validAmount = Math.min(Math.max(0, amount), maxBalance);

      // Preserve scroll position
      const scrollElement = document.getElementById("crafting-content");
      const scrollTop = scrollElement?.scrollTop || 0;

      setCornAllocation((prev) => ({
        ...prev,
        [resourceId]: validAmount,
      }));

      // Restore scroll position after state update
      setTimeout(() => {
        if (scrollElement) {
          scrollElement.scrollTop = scrollTop;
        }
      }, 0);
    },
    [
      selectedItem,
      selectedItemId,
      typedItems,
      foodBalances,
      craftingPools,
      craftableCookies,
    ]
  );

  useEffect(() => {
    if (typeof window !== "undefined") {
      setReferralCode(localStorage.getItem("referralCode"));
    }
  }, []);

  // Reset crafting method when item changes
  useEffect(() => {
    if (selectedItemId) {
      const recipe = getRecipeForItem(selectedItemId, craftableItems);
      const variableRecipe = getCornRecipeForItem(
        selectedItemId,
        craftableCookies
      );

      // If only variable recipe available, switch to variable method
      if (!recipe && variableRecipe) {
        setCraftingMethod("corn");
      } else {
        // Default to recipe method if available, otherwise variable
        setCraftingMethod(recipe ? "recipe" : "corn");
      }
    } else {
      setCraftingMethod("recipe");
    }
    setCornAllocation({});
  }, [selectedItem, selectedItemId, craftableItems, craftableCookies]);

  // Crafting handlers
  const craftRandomCookie = useCallback(async () => {
    try {
      const returnedData = await craftFromPool(
        0,
        quantity,
        referralCode ?? "",
        cornAllocation
      );
      if (
        returnedData &&
        returnedData.amounts != null &&
        returnedData.tokenIds
      ) {
        setShow(true);
        setCraftResult(returnedData);
        await delay(5000);
        setShow(false);
        await delay(1000);
        setShowCraftPopup(true);
      }
    } catch (error) {
      console.error("Random cookie crafting failed:", error);
    }
  }, [quantity, craftFromPool, referralCode, cornAllocation]);

  const craftItemHandler = useCallback(async () => {
    try {
      if (selectedItemId != null) {
        const returnedData = await craftItems(
          selectedItemId,
          quantity,
          referralCode ?? ""
        );

        if (
          returnedData &&
          returnedData.amounts != null &&
          returnedData.tokenIds
        ) {
          setShow(true);
          setCraftResult(returnedData);
          await delay(5000);
          setShow(false);
          await delay(1000);
          setShowCraftPopup(true);
        }
      }
    } catch (error) {
      console.error("Item crafting failed:", error);
    }
  }, [craftItems, quantity, selectedItemId, referralCode]);

  const craftVariableItemHandler = useCallback(async () => {
    try {
      if (selectedItemId != null) {
        const returnedData = await craftCookie(
          selectedItemId,
          quantity,
          referralCode ?? "",
          cornAllocation
        );
        if (
          returnedData &&
          returnedData.amounts != null &&
          returnedData.tokenIds
        ) {
          setShow(true);
          setCraftResult(returnedData);
          await delay(5000);
          setShow(false);
          await delay(1000);
          setShowCraftPopup(true);
        }
      }
    } catch (error) {
      console.error("Variable crafting failed:", error);
    }
  }, [craftCookie, quantity, selectedItemId, referralCode, cornAllocation]);

  const handleQuantityChange = (value: number) => {
    setQuantity(value);
  };

  const incrementQuantity = () =>
    setQuantity((prev) => (prev < 100 ? prev + 1 : prev));
  const decrementQuantity = () =>
    setQuantity((prev) => (prev > 1 ? prev - 1 : 1));

  // Helper function to get tab name
  const getTabName = (tabIndex: number): string => {
    const tabNames = ["all", "food", "battle", "breeding", "adventure"];
    return tabNames[tabIndex] || "unknown";
  };

  return (
    <div className="min-h-screen relative bg-gradient-to-b from-stone-900 via-black to-stone-900 overflow-hidden">
      <CraftingAnim show={show} />

      <div className="sticky top-0 z-20 bg-stone-900/80 backdrop-blur-sm border-b border-primary/10">
        <AppNavbar />
      </div>

      <div className="relative max-w-[1680px] w-full mx-auto mt-3 sm:mt-4 lg:mt-6 px-3 sm:px-4 md:px-6 lg:px-8 pb-[30vh] lg:pb-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-3 sm:mb-4 lg:mb-6">
          <h1 className="font-bold mb-2 md:mb-0 font-Arcadia text-primary text-2xl sm:text-3xl md:text-4xl">
            Crafting
          </h1>

          <div className="flex flex-col sm:flex-row gap-3 items-center">
            <div className="flex gap-2">
              <Button
                appearance="outline"
                size="small"
                onPress={() =>
                  window.open(
                    "https://marketplace.roninchain.com/collections/sabong-saga-game-items",
                    "_blank"
                  )
                }
                className="flex items-center gap-2"
              >
                <span>Buy Feathers</span>
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
              </Button>
              <Button
                appearance="outline"
                size="small"
                onPress={() =>
                  window.open(
                    "https://marketplace.roninchain.com/collections/sabong-saga-resources",
                    "_blank"
                  )
                }
                className="flex items-center gap-2"
              >
                <span>Buy Resources</span>
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
              </Button>
            </div>

            <WalletBalances>
              <TokenBalance
                iconSrc="/images/feathers.png"
                alt="Feathers"
                balance={Number(feathers).toLocaleString() || "0"}
              />
              <TokenBalance
                iconSrc="/images/legendary-feathers.png"
                alt="Legendary Feathers"
                balance={Number(legendaryFeathers).toLocaleString() || "0"}
              />
              <TokenBalance
                iconSrc="/images/COCK_TOKEN_BLUE.webp"
                alt="Token"
                balance={
                  Number(formatEther(cock)).toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  }) || "0.00"
                }
              />
            </WalletBalances>
          </div>
        </div>

        <div className="flex gap-2 w-full items-center mb-3 sm:mb-4 lg:mb-6">
          <CraftingTab activeTab={activeTab} setActiveTab={setActiveTab} />
        </div>

        <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
          <div className="w-full lg:w-3/5 bg-stone-900/50 rounded-lg border border-primary/5 p-2 sm:p-3 lg:p-4">
            <h2 className="text-sm sm:text-base lg:text-lg font-medium text-primary/80 mb-2 lg:mb-3">
              Hench Table
              {activeTab > 0 && (
                <span className="text-xs text-white/60 ml-2">
                  ({filteredItems.length} {getTabName(activeTab)} items)
                </span>
              )}
            </h2>

            <div className="lg:max-h-[550px] lg:overflow-y-auto pr-1 lg:pr-2 custom-scrollbar">
              {fetchingRecipes || isFetchingCraftable || isFetchingPools ? (
                <div className="flex items-center justify-center py-12">
                  <div className="flex flex-col items-center gap-3">
                    <div className="w-8 h-8 border-2 border-primary/20 border-t-primary animate-spin rounded-full"></div>
                    <p className="text-sm text-primary/70">
                      Loading recipes...
                    </p>
                  </div>
                </div>
              ) : (
                <ItemGrid
                  items={filteredItems}
                  selectedItem={
                    selectedItem !== null
                      ? filteredItems.findIndex(
                          (item) => item.id === typedItems[selectedItem]?.id
                        )
                      : null
                  }
                  setSelectedItemId={setSelectedItemId}
                  onSelectItem={handleSelectItem}
                  balances={foodBalances}
                />
              )}
            </div>
          </div>

          <div className="w-full lg:w-2/5 lg:relative">
            {selectedItem !== null &&
            selectedItem >= 0 &&
            selectedItemId != null &&
            selectedItem < items.length ? (
              <div className="fixed bottom-0 left-0 right-0 lg:relative lg:bottom-auto bg-stone-900 rounded-t-xl lg:rounded-lg border border-primary/10 p-3 sm:p-4 lg:p-4 shadow-lg lg:shadow-none h-[40vh] lg:h-auto overflow-hidden z-30 lg:z-auto">
                {/* Item Header */}
                <div className="flex items-center gap-2 sm:gap-3 mb-3">
                  <img
                    src={typedItems[selectedItem]?.image}
                    alt={typedItems[selectedItem]?.name}
                    className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 object-contain bg-stone-800/50 rounded p-1 flex-shrink-0"
                  />

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-1 mb-1">
                      <h2 className="text-sm sm:text-base font-bold text-primary/90 leading-tight truncate flex-1">
                        {typedItems[selectedItem]?.name}
                      </h2>

                      <div className="flex gap-1 flex-wrap">
                        {typedItems[selectedItem] && (
                          <CraftingBadges
                            item={typedItems[selectedItem]}
                            recipe={getRecipeForItem(
                              selectedItemId!,
                              craftableItems
                            )}
                            cornRecipe={getCornRecipeForItem(
                              selectedItemId!,
                              craftableCookies
                            )}
                            enoughBalance={enoughBalance}
                            enoughRecipeMaterials={
                              enoughBalance && craftingMethod === "recipe"
                            }
                            isCornCraftingValid={
                              enoughBalance && craftingMethod === "corn"
                            }
                          />
                        )}

                        {selectedItem !== undefined &&
                          typedItems[selectedItem]?.item &&
                          typedItems[selectedItem]?.id !== undefined && (
                            <Badge
                              intent="secondary"
                              className="text-xs px-1 py-0.5 whitespace-nowrap"
                            >
                              {(() => {
                                const id = typedItems[selectedItem]?.id;
                                return id !== undefined
                                  ? foodBalances[id]?.toString() || "0"
                                  : "0";
                              })()}
                            </Badge>
                          )}
                      </div>
                    </div>

                    <p className="text-xs text-white/70 mt-0.5 line-clamp-1 lg:line-clamp-2">
                      {typedItems[selectedItem]?.description}
                    </p>
                  </div>
                </div>

                {/* Item Details Section */}
                {(() => {
                  const item = typedItems[selectedItem];
                  if (!item || item.id === 100000) return null;

                  return (
                    <div className="hidden lg:block">
                      <div className="h-px w-full bg-gradient-to-r from-transparent via-primary/30 to-transparent my-2"></div>

                      <div className="mb-3 py-2">
                        <div className="flex gap-4">
                          {item?.effect && item.effect.length > 0 && (
                            <div className="bg-stone-800/30 p-3 rounded-lg w-full">
                              <p className="font-medium text-primary/80 mb-1">
                                Effects:
                              </p>
                              <ul className="space-y-1">
                                {item.effect.map((effect, idx) => (
                                  <li
                                    key={idx}
                                    className="text-sm text-white/70 flex items-start"
                                  >
                                    <span className="text-primary mr-1">•</span>{" "}
                                    {effect}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {item?.disliked && item.disliked.length > 0 && (
                            <div className="bg-stone-800/30 p-3 rounded-lg w-full">
                              <p className="font-medium text-primary/80 mb-1">
                                Disliked by:
                              </p>
                              <ul className="space-y-1">
                                {item.disliked.map((instinct, idx) => (
                                  <li
                                    key={idx}
                                    className="text-sm text-white/70 flex items-start"
                                  >
                                    <span className="text-red-400 mr-1">•</span>{" "}
                                    {instinct}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })()}

                <div className="h-px w-full bg-gradient-to-r from-transparent via-primary/30 to-transparent my-2"></div>

                {/* Enhanced Crafting Section */}
                <div
                  className="h-[calc(100%-120px)] lg:h-auto overflow-y-auto scroll-smooth"
                  id="crafting-content"
                >
                  {(() => {
                    const item = typedItems[selectedItem];
                    if (!item) return null;

                    // Special case for items with isRandom = true
                    if (item.isRandom) {
                      return (
                        item.craftable && (
                          <div className="bg-stone-800/50 rounded p-2 lg:p-3 border border-primary/10">
                            <p className="font-medium text-primary/80 mb-2 text-xs lg:text-sm">
                              Requirements
                            </p>

                            {isFetchingPools ? (
                              <div className="flex items-center justify-center py-8">
                                <div className="flex flex-col items-center gap-2">
                                  <div className="w-6 h-6 border-2 border-primary/20 border-t-primary animate-spin rounded-full"></div>
                                  <p className="text-xs text-primary/60">
                                    Loading pool data...
                                  </p>
                                </div>
                              </div>
                            ) : (
                              <MaterialRequirements
                                item={item}
                                craftingPools={craftingPools}
                                quantity={quantity}
                                cornAllocation={cornAllocation}
                                foodBalances={foodBalances}
                                cock={cock}
                                feathers={Number(feathers)}
                                legendaryFeathers={Number(legendaryFeathers)}
                                onAllocationChange={
                                  handleVariableResourceAllocationChange
                                }
                              />
                            )}

                            <div className="space-y-2 lg:space-y-3 mb-2 lg:mb-3">
                              <div className="flex items-center justify-between gap-2 lg:gap-3">
                                <p className="text-white/80 text-xs lg:text-sm">
                                  Quantity:
                                </p>
                                <QuantitySelector
                                  quantity={quantity}
                                  onChange={handleQuantityChange}
                                  onIncrement={incrementQuantity}
                                  onDecrement={decrementQuantity}
                                  max={100}
                                />
                              </div>

                              <div className="space-y-1 lg:space-y-2">
                                <p className="text-muted-fg text-xs lg:text-sm">
                                  Creator code (optional):
                                </p>
                                <Input
                                  value={referralCode ?? ""}
                                  onChange={(e) =>
                                    setReferralCode(e.target.value)
                                  }
                                  className="border border-border w-full rounded-md text-xs lg:text-sm"
                                  placeholder="Enter creator code"
                                />
                              </div>
                            </div>

                            {!enoughBalance && (
                              <p className="text-red-400 text-xs lg:text-sm flex items-center gap-1 lg:gap-2 mb-2 lg:mb-3">
                                <span className="inline-block">⚠️</span>
                                {getValidationErrorMessage(
                                  item,
                                  craftingMethod,
                                  craftingPools,
                                  cornAllocation,
                                  validationContext,
                                  craftableCookies
                                )}
                              </p>
                            )}

                            <CraftButton
                              isPending={
                                isPendingRandomCrafting ||
                                approvingCock ||
                                approvingFeathers
                              }
                              isDisabled={
                                shouldDisableCraftButton ||
                                isPendingRandomCrafting ||
                                approvingCock ||
                                approvingFeathers
                              }
                              onPress={craftRandomCookie}
                              className="w-full"
                            />
                            {(approvingCock || approvingFeathers) && (
                              <p className="text-xs text-primary/70 text-center mt-2">
                                {approvingCock && "Approving COCK tokens..."}
                                {approvingFeathers && "Approving Feathers..."}
                              </p>
                            )}
                          </div>
                        )
                      );
                    }

                    // For regular items, check both crafting methods
                    const recipe = getRecipeForItem(item.id, craftableItems);
                    const cornRecipe = getCornRecipeForItem(
                      item.id,
                      craftableCookies
                    );
                    const hasBothMethods = recipe && cornRecipe;

                    // If no recipes at all, show message
                    if (!recipe && !cornRecipe) {
                      return (
                        <div className="bg-stone-800/50 rounded p-2 lg:p-3 border border-primary/10 text-center">
                          <p className="text-white/60 text-xs lg:text-sm">
                            No crafting recipe available for this item
                          </p>
                        </div>
                      );
                    }

                    return (
                      <div className="bg-stone-800/50 rounded p-2 lg:p-3 border border-primary/10">
                        {/* Show tabs if both methods are available */}
                        <CraftingMethodTabs
                          hasRecipe={!!recipe}
                          hasCornRecipe={!!cornRecipe}
                          activeMethod={craftingMethod}
                          onMethodChange={setCraftingMethod}
                        />

                        <p className="font-medium text-primary/80 mb-2 lg:mb-3 text-xs lg:text-sm">
                          Requirements{" "}
                          {hasBothMethods &&
                            `(${craftingMethod === "recipe" ? "Standard" : "Variable"} Method)`}
                        </p>

                        {(fetchingRecipes && craftingMethod === "recipe") ||
                        (isFetchingCraftable && craftingMethod === "corn") ? (
                          <div className="flex items-center justify-center py-8">
                            <div className="flex flex-col items-center gap-2">
                              <div className="w-6 h-6 border-2 border-primary/20 border-t-primary animate-spin rounded-full"></div>
                              <p className="text-xs text-primary/60">
                                Loading{" "}
                                {craftingMethod === "recipe"
                                  ? "recipe"
                                  : "crafting"}{" "}
                                data...
                              </p>
                            </div>
                          </div>
                        ) : (
                          <MaterialRequirements
                            item={item}
                            craftingMethod={craftingMethod}
                            recipe={recipe}
                            cornRecipe={cornRecipe}
                            craftingPools={craftingPools}
                            quantity={quantity}
                            cornAllocation={cornAllocation}
                            foodBalances={foodBalances}
                            cock={cock}
                            feathers={Number(feathers)}
                            legendaryFeathers={Number(legendaryFeathers)}
                            onAllocationChange={
                              handleVariableResourceAllocationChange
                            }
                          />
                        )}

                        <div className="space-y-2 lg:space-y-3 mb-2 lg:mb-3">
                          <div className="flex items-center justify-between gap-2 lg:gap-3">
                            <p className="text-white/80 text-xs lg:text-sm">
                              Quantity:
                            </p>
                            <QuantitySelector
                              quantity={quantity}
                              onChange={handleQuantityChange}
                              onIncrement={incrementQuantity}
                              onDecrement={decrementQuantity}
                              max={100}
                            />
                          </div>

                          <div className="space-y-1 lg:space-y-2">
                            <p className="text-muted-fg text-xs lg:text-sm">
                              Creator code (optional):
                            </p>
                            <Input
                              value={referralCode ?? ""}
                              onChange={(e) => setReferralCode(e.target.value)}
                              className="border border-border w-full rounded-md text-xs lg:text-sm"
                              placeholder="Enter creator code"
                            />
                          </div>
                        </div>

                        {!enoughBalance && (
                          <p className="text-red-400 text-xs lg:text-sm flex items-center gap-1 lg:gap-2 mb-2 lg:mb-3">
                            <span className="inline-block">⚠️</span>
                            {getValidationErrorMessage(
                              item,
                              craftingMethod,
                              craftingPools,
                              cornAllocation,
                              validationContext,
                              craftableCookies
                            )}
                          </p>
                        )}

                        {/* Show appropriate craft button based on method */}
                        <CraftButton
                          isPending={isPending || isPendingCraftCookieWCorn}
                          isDisabled={
                            shouldDisableCraftButton ||
                            isPending ||
                            isPendingCraftCookieWCorn
                          }
                          onPress={
                            craftingMethod === "recipe"
                              ? craftItemHandler
                              : craftVariableItemHandler
                          }
                          className="w-full"
                        />
                      </div>
                    );
                  })()}
                </div>
              </div>
            ) : (
              <div className="fixed bottom-0 left-0 right-0 lg:relative lg:bottom-auto bg-stone-900/30 rounded-t-lg lg:rounded-lg border border-dashed border-primary/20 p-3 sm:p-4 h-[40vh] lg:h-full flex flex-col items-center justify-center text-center z-30 lg:z-auto">
                <span className="text-xl lg:text-3xl mb-2">🔍</span>
                <h3 className="text-sm lg:text-base font-medium text-primary/70">
                  Select an item
                </h3>
                <p className="text-xs text-white/50 mt-1">
                  Choose an item from the list to view details and craft
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Improved crafting result popup */}
      {showCraftPopup && craftResult && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/80 backdrop-blur-md z-50 p-4">
          <div className="bg-gradient-to-b from-stone-800 to-stone-900 rounded-xl shadow-2xl max-w-3xl w-full border border-primary/30 overflow-hidden">
            <div className="bg-primary/10 p-4 flex items-center justify-between">
              <h2 className="text-2xl font-Arcadia text-primary">
                Crafting Results
              </h2>
              <Button
                intent="danger"
                onPress={() => {
                  setShowCraftPopup(false);
                  setCraftResult(null);
                }}
                className="w-8 h-8 rounded-full"
              >
                <span>×</span>
              </Button>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-6">
                {(() => {
                  const combinedResults: Record<
                    number,
                    { tokenId: bigint; amount: bigint; item?: any }
                  > = {};

                  craftResult.tokenIds.forEach((tokenId, index) => {
                    const itemId = Number(tokenId) % 9;
                    const amount = craftResult.amounts[index] || 1n;

                    if (combinedResults[itemId]) {
                      combinedResults[itemId].amount += amount;
                    } else {
                      combinedResults[itemId] = {
                        tokenId,
                        amount,
                        item: items.find((i: any) => i.id === itemId),
                      };
                    }
                  });

                  return Object.values(combinedResults).map((result, index) => (
                    <div
                      key={index}
                      className="flex flex-col items-center bg-stone-800/70 p-4 rounded-lg border border-primary/20 hover:border-primary/40 transition-all"
                    >
                      <div className="relative mb-3">
                        <div className="absolute inset-0 bg-primary/10 rounded-full animate-pulse opacity-30"></div>
                        <img
                          src={result.item?.image || "/images/cookie-1.jpg"}
                          alt={result.item?.name || "Cookie"}
                          className="w-20 h-20 object-contain relative z-10"
                        />
                      </div>

                      <span className="text-primary font-medium text-center">
                        {result.item?.name || `Cookie #${result.tokenId}`}
                      </span>

                      <span className="text-white/70 text-sm mt-1 bg-stone-700/50 px-2 py-0.5 rounded-full">
                        x{result.amount.toString()}
                      </span>
                    </div>
                  ));
                })()}
              </div>

              <div className="flex justify-center">
                <Button
                  intent="primary"
                  onPress={() => {
                    setShowCraftPopup(false);
                    setCraftResult(null);
                  }}
                  className="px-8 py-2 font-medium"
                >
                  Continue Crafting
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
