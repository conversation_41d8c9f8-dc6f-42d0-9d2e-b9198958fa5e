"use client";

import { useState, useEffect } from "react";
import { Button } from "ui";
import { ChickenCache, ICacheStats } from "../utils/chicken-cache";

interface ICacheDebugProps {
  className?: string;
}

/**
 * Cache Debug Component
 * Shows cache statistics and provides cache management controls
 * Useful for development and debugging cache performance
 */
export function CacheDebug({ className = "" }: ICacheDebugProps) {
  const [stats, setStats] = useState<ICacheStats | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  const refreshStats = () => {
    const cacheStats = ChickenCache.getCacheStats();
    setStats(cacheStats);
  };

  const clearAllCaches = () => {
    ChickenCache.clearAllCaches();
    refreshStats();
  };

  const clearMetadataCache = () => {
    ChickenCache.clearMetadataCache();
    refreshStats();
  };

  const clearRentalCache = () => {
    ChickenCache.clearRentalCache();
    refreshStats();
  };

  useEffect(() => {
    if (isVisible) {
      refreshStats();
      // Refresh stats every 5 seconds when visible
      const interval = setInterval(refreshStats, 5000);
      return () => clearInterval(interval);
    }
  }, [isVisible]);

  const formatTimestamp = (timestamp: number) => {
    if (timestamp === 0) return "Never";
    return new Date(timestamp).toLocaleString();
  };

  const formatAge = (timestamp: number) => {
    if (timestamp === 0) return "N/A";
    const ageMs = Date.now() - timestamp;
    const ageMinutes = Math.floor(ageMs / (1000 * 60));
    const ageHours = Math.floor(ageMinutes / 60);

    if (ageHours > 0) {
      return `${ageHours}h ${ageMinutes % 60}m ago`;
    } else {
      return `${ageMinutes}m ago`;
    }
  };

  if (!isVisible) {
    return (
      <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
        <Button
          size="small"
          appearance="outline"
          onPress={() => setIsVisible(true)}
          className="bg-black/80 text-white border-gray-600 hover:bg-black/90"
        >
          Cache Debug
        </Button>
      </div>
    );
  }

  return (
    <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
      <div className="bg-black/90 text-white p-4 rounded-lg border border-gray-600 min-w-80 max-w-md">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-lg font-semibold">Cache Debug</h3>
          <Button
            size="small"
            appearance="plain"
            onPress={() => setIsVisible(false)}
            className="text-gray-400 hover:text-white"
          >
            ✕
          </Button>
        </div>

        {stats && (
          <div className="space-y-3">
            {/* Cache Statistics */}
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-2">
                Statistics
              </h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-gray-400">Metadata:</span>
                  <span className="ml-1 text-white">{stats.metadataCount}</span>
                </div>
                <div>
                  <span className="text-gray-400">Rental:</span>
                  <span className="ml-1 text-white">{stats.rentalCount}</span>
                </div>
                <div className="col-span-2">
                  <span className="text-gray-400">Cache Size:</span>
                  <span className="ml-1 text-white">{stats.cacheSize}</span>
                </div>
              </div>
            </div>

            {/* Cache Age */}
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-2">
                Cache Age
              </h4>
              <div className="text-xs space-y-1">
                <div>
                  <span className="text-gray-400">Oldest:</span>
                  <span className="ml-1 text-white">
                    {formatAge(stats.oldestEntry)}
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">Newest:</span>
                  <span className="ml-1 text-white">
                    {formatAge(stats.newestEntry)}
                  </span>
                </div>
              </div>
            </div>

            {/* Cache Actions */}
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-2">
                Actions
              </h4>
              <div className="space-y-2">
                <Button
                  size="small"
                  appearance="outline"
                  onPress={refreshStats}
                  className="w-full text-xs bg-transparent border-gray-600 text-white hover:bg-gray-800"
                >
                  Refresh Stats
                </Button>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    size="small"
                    appearance="outline"
                    onPress={clearMetadataCache}
                    className="text-xs bg-transparent border-blue-600 text-blue-400 hover:bg-blue-900/30"
                  >
                    Clear Metadata
                  </Button>
                  <Button
                    size="small"
                    appearance="outline"
                    onPress={clearRentalCache}
                    className="text-xs bg-transparent border-purple-600 text-purple-400 hover:bg-purple-900/30"
                  >
                    Clear Rental
                  </Button>
                </div>
                <Button
                  size="small"
                  appearance="outline"
                  onPress={clearAllCaches}
                  className="w-full text-xs bg-transparent border-red-600 text-red-400 hover:bg-red-900/30"
                >
                  Clear All Caches
                </Button>
              </div>
            </div>

            {/* Performance Tips */}
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-2">
                Performance
              </h4>
              <div className="text-xs text-gray-400 space-y-1">
                <div>
                  Cache Hit Rate:{" "}
                  {stats.metadataCount > 0 ? "Good" : "Building..."}
                </div>
                <div>
                  {stats.metadataCount > 100 && (
                    <span className="text-green-400">
                      ✓ Cache is well populated
                    </span>
                  )}
                  {stats.metadataCount <= 100 && stats.metadataCount > 0 && (
                    <span className="text-yellow-400">
                      ⚡ Cache is building up
                    </span>
                  )}
                  {stats.metadataCount === 0 && (
                    <span className="text-gray-400">○ Cache is empty</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {!stats && (
          <div className="text-center text-gray-400 py-4">
            <div className="animate-spin w-4 h-4 border-2 border-gray-600 border-t-white rounded-full mx-auto mb-2"></div>
            Loading cache stats...
          </div>
        )}
      </div>
    </div>
  );
}

// Export for development use
export default CacheDebug;
