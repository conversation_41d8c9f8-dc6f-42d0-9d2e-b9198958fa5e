"use client";

import { useQuery } from "@tanstack/react-query";
import { Address } from "viem";
import { IChickenGQL } from "../types/chicken-gql.types";
import { useChickenBreedCountWithConfig } from "./useChickenBreedCount";
import useChickenMetadataBatch from "./useChickenMetadataBatch";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import useChickenGenes from "./useChickenGenes";
import { useHookstate } from "@hookstate/core";
import useHookstateDebounce from "@/lib/hooks/useHookstateDebounce";
import { useChickenCooldownWithConfig } from "./useChickenCooldown";

const fetchChickens = async (address: Address) => {
  const response = await fetch(`/api/proxy/ronin-gql?address=${address}`);
  if (!response.ok) {
    throw new Error("Failed to fetch chickens");
  }
  const data = await response.json();
  return data.tokens as IChickenGQL[];
};

const ITEMS_PER_PAGE = 10;

const useChickens = (address: Address) => {
  const chickenQuery = useQuery({
    queryKey: ["chickens", address],
    queryFn: () => fetchChickens(address),
    enabled: !!address,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [loadedChickenIds, setLoadedChickenIds] = useState<number[]>([]);
  const sortOrder = useHookstate<"lowest" | "highest">("lowest");

  // Filter chickens and eggs
  // Note: Currently, those chickens with null attributes will be filtered out and won't appear in either the chickens or eggs arrays.
  const chickens = chickenQuery.isSuccess
    ? chickenQuery.data.filter(
        (chicken) =>
          chicken.attributes?.Type && !chicken.attributes.Type.includes("Egg")
      )
    : [];
  const eggs = chickenQuery.isSuccess
    ? chickenQuery.data.filter((chicken) =>
        chicken.attributes?.Type?.includes("Egg")
      )
    : [];

  const chickensNullAttributes = chickenQuery.isSuccess
    ? chickenQuery.data.filter((chicken) => !chicken.attributes?.Type)
    : [];

  // Extract token IDs from chickens and eggs
  const allChickenTokenIds = chickens.map((chicken) => Number(chicken.tokenId));
  const allEggTokenIds = eggs.map((egg) => Number(egg.tokenId));

  // Search
  const searchQuery = useHookstate("");
  const debouncedSearchQuery = useHookstateDebounce(searchQuery, 500);

  // Get chicken IDs based on search or pagination
  const currentPageChickenIds = debouncedSearchQuery.get().trim()
    ? allChickenTokenIds.filter((tokenId) =>
        tokenId.toString().includes(debouncedSearchQuery.get())
      )
    : allChickenTokenIds.slice(
        (currentPage - 1) * ITEMS_PER_PAGE,
        currentPage * ITEMS_PER_PAGE
      );

  // Calculate pagination info
  const totalPages = Math.ceil(chickens.length / ITEMS_PER_PAGE);
  const hasMore = currentPage < totalPages;

  // Use the accumulated loaded IDs for display and queries
  let paginatedChickenTokenIds = debouncedSearchQuery.get().trim()
    ? currentPageChickenIds // For search, just use the filtered results
    : loadedChickenIds.length > 0
      ? loadedChickenIds // Use accumulated IDs when available
      : currentPageChickenIds; // Fall back to current page IDs on initial load

  // Apply sorting based on sortOrder
  paginatedChickenTokenIds = [...paginatedChickenTokenIds].sort((a, b) => {
    if (sortOrder.value === "lowest") {
      return a - b;
    } else {
      return b - a;
    }
  });

  const loadedChickens = paginatedChickenTokenIds.length;

  // Load more function that accumulates IDs
  const loadMore = () => {
    if (hasMore) {
      setCurrentPage((prev) => prev + 1);
      // Add new IDs to our accumulated list, avoiding duplicates
      const nextPageIds = allChickenTokenIds.slice(
        currentPage * ITEMS_PER_PAGE,
        (currentPage + 1) * ITEMS_PER_PAGE
      );
      setLoadedChickenIds((prev) => {
        // Create a Set to ensure uniqueness
        const uniqueIds = new Set([...prev, ...nextPageIds]);
        return Array.from(uniqueIds);
      });
    }
  };

  // Fetch breed counts from the contract
  const breedCountQuery = useChickenBreedCountWithConfig(
    paginatedChickenTokenIds
  );

  // Fetch metadata for chickens
  const metadataQuery = useChickenMetadataBatch(paginatedChickenTokenIds);

  // Fetch genes for chickens
  const genesQuery = useChickenGenes(paginatedChickenTokenIds);

  const cooldownQuery = useChickenCooldownWithConfig(paginatedChickenTokenIds);

  // Display error toast if any of the queries fail
  useEffect(() => {
    if (chickenQuery.isError) {
      console.error("Chicken query error:", chickenQuery.error);
      toast.error("Failed to fetch chickens");
    }
    if (breedCountQuery.isError) {
      console.error("Breed count query error:", breedCountQuery.error);
      toast.error("Failed to fetch breed counts");
    }
    if (metadataQuery.isError) {
      console.error("Metadata query error:", metadataQuery.error);
      toast.error("Failed to fetch metadata");
    }
    if (genesQuery.isError) {
      console.error("Genes query error:", genesQuery.error);
      toast.error("Failed to fetch genes");
    }
    if (cooldownQuery.isError) {
      console.error("Cooldown query error:", cooldownQuery.error);
      toast.error("Failed to fetch cooldown");
    }
  }, [
    chickenQuery.isError,
    chickenQuery.error,
    breedCountQuery.isError,
    breedCountQuery.error,
    metadataQuery.isError,
    metadataQuery.error,
    genesQuery.isError,
    genesQuery.error,
    cooldownQuery.isError,
    cooldownQuery.error,
  ]);

  useEffect(() => {
    if (debouncedSearchQuery.get().trim() !== "") {
      setCurrentPage(1);
      // Clear accumulated IDs when searching
      setLoadedChickenIds([]);
      return;
    }
  }, [debouncedSearchQuery]);

  // Initialize loaded IDs with first page when data is first loaded
  useEffect(() => {
    if (
      chickenQuery.isSuccess &&
      loadedChickenIds.length === 0 &&
      !debouncedSearchQuery.get().trim() &&
      currentPageChickenIds.length > 0 // Add this check
    ) {
      // Use a callback to ensure we're not depending on stale values
      setLoadedChickenIds(() => [...currentPageChickenIds]);
    }
  }, [
    chickenQuery.isSuccess,
    // Remove currentPageChickenIds from dependencies
    loadedChickenIds.length,
    debouncedSearchQuery,
  ]);

  return {
    chickensNullAttributes,
    chickens,
    eggs,
    allChickenTokenIds,
    allEggTokenIds,
    chickenQuery,

    // Pagination
    searchQuery,
    debouncedSearchQuery,
    hasMore,
    loadMore,
    currentPage,
    totalPages,
    loadedChickens,
    paginatedChickenTokenIds,
    sortOrder,
    breedCountsMap: breedCountQuery.breedCountQuery.isSuccess
      ? breedCountQuery.breedCountMap
      : [],
    metadataMap: metadataQuery.metadataQuery.isSuccess
      ? metadataQuery.metadataMap
      : [],
    genesMap: genesQuery.genesQuery.isSuccess ? genesQuery.genesMap : [],
    cooldownMap: cooldownQuery.cooldownQuery.isSuccess
      ? cooldownQuery.cooldownMap
      : [],
    isOnCooldown: cooldownQuery.isOnCooldown,
    getRemainingCooldown: cooldownQuery.getRemainingCooldown,

    // Loading and error states
    loading:
      chickenQuery.isFetching ||
      breedCountQuery.isLoading ||
      metadataQuery.isLoading ||
      genesQuery.isLoading ||
      cooldownQuery.isLoading,
    error:
      chickenQuery.error ||
      breedCountQuery.error ||
      metadataQuery.error ||
      genesQuery.error ||
      cooldownQuery.error,
    isError:
      chickenQuery.isError ||
      breedCountQuery.isError ||
      metadataQuery.isError ||
      genesQuery.isError ||
      cooldownQuery.isError,
    refetch: chickenQuery.refetch,
  };
};

export { fetchChickens };
export default useChickens;
