import { Context } from "hono";
import { StatusCode } from "hono/utils/http-status";
import { ApiResponse } from "../types";

export const sendResponse = <T>(c: Context, response: ApiResponse<T>) => {
  return c.json(response, response.responseCode as StatusCode);
};
export class ResponseHelper {
  static success<T>(data?: T, message: string = "Success"): ApiResponse<T> {
    return {
      status: true,
      responseCode: 200,
      data,
      message,
    };
  }

  static created<T>(
    data?: T,
    message: string = "Created successfully"
  ): ApiResponse<T> {
    return {
      status: true,
      responseCode: 201,
      data,
      message,
    };
  }

  static badRequest(
    message: string = "Bad request",
    errors?: string[] | Record<string, string[]>
  ): ApiResponse<null> {
    return {
      status: false,
      responseCode: 400,
      message,
      errors,
    };
  }

  static unauthorized(message: string = "Unauthorized"): ApiResponse<null> {
    return {
      status: false,
      responseCode: 401,
      message,
    };
  }

  static forbidden(message: string = "Forbidden access"): ApiResponse<null> {
    return {
      status: false,
      responseCode: 403,
      message,
    };
  }

  static invalidToken(
    message: string = "Invalid or expired token"
  ): ApiResponse<null> {
    return {
      status: false,
      responseCode: 403,
      message,
      errors: ["Token validation failed"],
    };
  }

  static notFound(message: string = "Resource not found"): ApiResponse<null> {
    return {
      status: false,
      responseCode: 404,
      message,
    };
  }

  static serverError(
    message: string = "Internal server error"
  ): ApiResponse<null> {
    return {
      status: false,
      responseCode: 500,
      message,
      errors: ["An unexpected error occurred"],
    };
  }

  static validationError(errors: Record<string, string[]>): ApiResponse<null> {
    return {
      status: false,
      responseCode: 422,
      message: "Validation failed",
      errors,
    };
  }
}
