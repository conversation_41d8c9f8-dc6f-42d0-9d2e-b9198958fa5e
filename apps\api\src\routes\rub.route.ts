import { Hono } from "hono";
import { env } from "process";
import { createAuthMiddleware } from "../middlewares/auth";
import { ableToRub, rub } from "../controllers/daily-rub";
const auth = createAuthMiddleware({
  jwtSecret: env.JWT_SECRET,
});

const rubRoute = new Hono();

rubRoute.post("/daily-rub", auth.protect, rub);
rubRoute.get("/able-to-rub", auth.protect, ableToRub);

export default rubRoute;
