[{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "poolId", "type": "uint256"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ErrDuplicateTokenIdInPool", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "required", "type": "uint256"}, {"internalType": "uint256", "name": "available", "type": "uint256"}], "name": "ErrInsufficientBalance", "type": "error"}, {"inputs": [], "name": "ErrInvalidAmount", "type": "error"}, {"inputs": [], "name": "ErrInvalidMaterials", "type": "error"}, {"inputs": [], "name": "ErrInvalidResourceIds", "type": "error"}, {"inputs": [], "name": "ErrInvalidTokenAddress", "type": "error"}, {"inputs": [], "name": "ErrInvalidWeight", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "poolId", "type": "uint256"}], "name": "ErrNoWeightedOutputsInPool", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "poolId", "type": "uint256"}], "name": "ErrPoolInactive", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "poolId", "type": "uint256"}], "name": "ErrPoolNotFound", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "poolId", "type": "uint256"}, {"internalType": "uint256", "name": "resourceId", "type": "uint256"}], "name": "ErrResourceNotAccepted", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "poolId", "type": "uint256"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ErrTokenNotFound", "type": "error"}, {"inputs": [], "name": "ErrTooManyMaterials", "type": "error"}, {"inputs": [], "name": "ErrTooManyResourceIds", "type": "error"}, {"inputs": [], "name": "ErrTooManyWeightedOutputs", "type": "error"}, {"inputs": [], "name": "ErrTransferFailed", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "ErrUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"}], "name": "PoolActivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"}, {"indexed": false, "internalType": "uint256[]", "name": "tokenIds", "type": "uint256[]"}, {"indexed": false, "internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"indexed": false, "internalType": "uint256", "name": "totalCrafted", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "referralCode", "type": "string"}], "name": "PoolCraftingCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}], "name": "PoolCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"}], "name": "PoolDeactivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "newName", "type": "string"}], "name": "PoolNameUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"}], "name": "PoolUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "PoolWeightedOutputActivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "weight", "type": "uint256"}], "name": "PoolWeightedOutputAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "PoolWeightedOutputDeactivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newWeight", "type": "uint256"}], "name": "PoolWeightedOutputUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "COCK", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FEATHERS", "outputs": [{"internalType": "contract IERC1155Mintable", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_ACCEPTED_RESOURCE_IDS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_CRAFT_AMOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_MATERIALS_PER_POOL", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_WEIGHTED_OUTPUTS_PER_POOL", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PAUSER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REFERRAL", "outputs": [{"internalType": "contract IReferral", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "RESOURCES", "outputs": [{"internalType": "contract IERC1155Mintable", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}, {"internalType": "uint256", "name": "_tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "_weight", "type": "uint256"}], "name": "addWeightedOutputToPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "_poolIds", "type": "uint256[]"}], "name": "batchGetPoolInfo", "outputs": [{"internalType": "string[]", "name": "names", "type": "string[]"}, {"internalType": "bool[]", "name": "existsArray", "type": "bool[]"}, {"internalType": "bool[]", "name": "activeArray", "type": "bool[]"}, {"internalType": "uint256[]", "name": "totalWeights", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "outputCounts", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "_poolIds", "type": "uint256[]"}], "name": "batchGetPools", "outputs": [{"components": [{"components": [{"internalType": "enum SabongSagaRandomCrafting.TokenType", "name": "tokenType", "type": "uint8"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct SabongSagaRandomCrafting.CraftingMaterial[]", "name": "materials", "type": "tuple[]"}, {"internalType": "uint256[]", "name": "acceptedResourceIds", "type": "uint256[]"}, {"internalType": "bool", "name": "exists", "type": "bool"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "totalWeight", "type": "uint256"}], "internalType": "struct SabongSagaRandomCrafting.PoolData[]", "name": "poolsData", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint256[]", "name": "_resourceIds", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "_resourceAmounts", "type": "uint256[]"}, {"internalType": "string", "name": "_referralCode", "type": "string"}], "name": "craftFromPool", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_name", "type": "string"}, {"components": [{"internalType": "enum SabongSagaRandomCrafting.TokenType", "name": "tokenType", "type": "uint8"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct SabongSagaRandomCrafting.CraftingMaterial[]", "name": "_materials", "type": "tuple[]"}, {"internalType": "uint256[]", "name": "_acceptedResourceIds", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "_outputTokenIds", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "_outputWeights", "type": "uint256[]"}], "name": "createPool", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getActivePoolCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getActivePoolIds", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllPoolIds", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}], "name": "getPool", "outputs": [{"components": [{"internalType": "enum SabongSagaRandomCrafting.TokenType", "name": "tokenType", "type": "uint8"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct SabongSagaRandomCrafting.CraftingMaterial[]", "name": "materials", "type": "tuple[]"}, {"internalType": "uint256[]", "name": "acceptedResourceIds", "type": "uint256[]"}, {"internalType": "bool", "name": "exists", "type": "bool"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "totalWeight", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}], "name": "getPoolActiveWeightedOutputCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}], "name": "getPoolActiveWeightedOutputs", "outputs": [{"components": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "weight", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}], "internalType": "struct SabongSagaRandomCrafting.WeightedOutput[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPoolCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "getPoolCraftingCost", "outputs": [{"components": [{"internalType": "enum SabongSagaRandomCrafting.TokenType", "name": "tokenType", "type": "uint8"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct SabongSagaRandomCrafting.CraftingMaterial[]", "name": "materials", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}], "name": "getPoolInfo", "outputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "bool", "name": "exists", "type": "bool"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint256", "name": "totalWeight", "type": "uint256"}, {"internalType": "uint256", "name": "weightedOutputCount", "type": "uint256"}, {"internalType": "uint256", "name": "materialCount", "type": "uint256"}, {"internalType": "uint256", "name": "acceptedResourceCount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}], "name": "getPoolName", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}], "name": "getPoolTotalWeight", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}], "name": "getPoolWeightedOutputCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}], "name": "getPoolWeightedOutputs", "outputs": [{"components": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "weight", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}], "internalType": "struct SabongSagaRandomCrafting.WeightedOutput[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMembers", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_COCK", "type": "address"}, {"internalType": "address", "name": "_RESOURCES", "type": "address"}, {"internalType": "address", "name": "_FEATHERS", "type": "address"}, {"internalType": "address", "name": "_REFERRAL", "type": "address"}, {"internalType": "address", "name": "_FEE_WALLET", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}], "name": "isPoolActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}, {"internalType": "uint256", "name": "_resourceId", "type": "uint256"}], "name": "isPoolResourceAccepted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nextPoolId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "poolExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "poolIds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "poolResourceAccepted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "poolWeightedOutputIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "pools", "outputs": [{"internalType": "uint256", "name": "totalWeight", "type": "uint256"}, {"internalType": "bool", "name": "exists", "type": "bool"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}, {"internalType": "bool", "name": "_active", "type": "bool"}], "name": "setPoolActive", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}, {"internalType": "string", "name": "_newName", "type": "string"}], "name": "setPoolName", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}, {"internalType": "uint256", "name": "_tokenId", "type": "uint256"}, {"internalType": "bool", "name": "_active", "type": "bool"}], "name": "setPoolWeightedOutputActive", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "updateFeeWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}, {"components": [{"internalType": "enum SabongSagaRandomCrafting.TokenType", "name": "tokenType", "type": "uint8"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct SabongSagaRandomCrafting.CraftingMaterial[]", "name": "_materials", "type": "tuple[]"}, {"internalType": "uint256[]", "name": "_acceptedResourceIds", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "_outputTokenIds", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "_outputWeights", "type": "uint256[]"}], "name": "updatePoolConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_poolId", "type": "uint256"}, {"internalType": "uint256", "name": "_tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "_newWeight", "type": "uint256"}], "name": "updatePoolWeightedOutput", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]