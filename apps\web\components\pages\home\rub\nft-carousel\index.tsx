import { ChickX } from "@/components/shared/icons";
import Feathers from "@/components/shared/icons/feathers";
import { Badge } from "@/components/ui";
import { Chickens, ChickensData } from "@/types/chicken.type";
import React, { useState, useCallback, useMemo } from "react";
import { Text } from "react-aria-components";

interface TouchState {
  isSwiping: boolean;
  startX: number;
  currentX: number;
  startY: number;
  currentY: number;
}

const INITIAL_TOUCH_STATE: TouchState = {
  isSwiping: false,
  startX: 0,
  currentX: 0,
  startY: 0,
  currentY: 0,
};

const SWIPE_THRESHOLD = 50;
const CARD_WIDTH = 300;
const TRANSFORM_MULTIPLIER = 0.5;
const Z_OFFSET = 30;
const SCALE_FACTOR = 0.1;
const OPACITY_FACTOR = 0.3;

export const NftCarousel = ({
  chickens,
}: {
  chickens: ChickensData[] | null;
}) => {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const [touchState, setTouchState] = useState<TouchState>(INITIAL_TOUCH_STATE);

  if (!chickens || chickens.length === 0) {
    return (
      <div className="flex flex-col items-center text-stone-700 font-Poppins pb-10">
        <ChickX />
        <Text> No chickens...</Text>
      </div>
    );
  }

  const handleTouchStart = useCallback(
    (
      e: React.TouchEvent<HTMLDivElement> | React.MouseEvent<HTMLDivElement>
    ): void => {
      const clientX = "touches" in e ? e.touches[0]?.clientX : e.clientX;
      const clientY = "touches" in e ? e.touches[0]?.clientY : e.clientY;

      if (clientX === undefined || clientY === undefined) return;

      setTouchState({
        isSwiping: true,
        startX: clientX,
        currentX: 0,
        startY: clientY,
        currentY: 0,
      });
    },
    []
  );

  const handleTouchMove = useCallback(
    (
      e: React.TouchEvent<HTMLDivElement> | React.MouseEvent<HTMLDivElement>
    ): void => {
      if (!touchState.isSwiping) return;

      const clientX = "touches" in e ? e.touches[0]?.clientX : e.clientX;
      const clientY = "touches" in e ? e.touches[0]?.clientY : e.clientY;

      if (clientX === undefined || clientY === undefined) return;

      const currentX = clientX - touchState.startX;
      const currentY = clientY - touchState.startY;

      setTouchState((prev) => ({ ...prev, currentX, currentY }));
    },
    [touchState.isSwiping, touchState.startX, touchState.startY]
  );

  const handleTouchEnd = useCallback((): void => {
    if (!touchState.isSwiping) return;

    const horizontalSwipe =
      Math.abs(touchState.currentX) > Math.abs(touchState.currentY);

    if (horizontalSwipe && Math.abs(touchState.currentX) > SWIPE_THRESHOLD) {
      setActiveIndex((prev) => {
        if (touchState.currentX > 0 && prev > 0) {
          return prev - 1;
        } else if (touchState.currentX < 0 && prev < chickens.length - 1) {
          return prev + 1;
        }
        return prev;
      });
    }

    setTouchState(INITIAL_TOUCH_STATE);
  }, [
    touchState.isSwiping,
    touchState.currentX,
    touchState.currentY,
    chickens.length,
  ]);

  const getPaginationIndexes = useCallback(() => {
    if (chickens.length <= 1) return [0];
    if (chickens.length === 2) return [0, 1];

    // For 3 or more items
    if (activeIndex === 0) {
      return [0, 1, Math.min(2, chickens.length - 1)];
    }
    if (activeIndex === chickens.length - 1) {
      return [
        Math.max(chickens.length - 3, 0),
        chickens.length - 2,
        chickens.length - 1,
      ].filter((index) => index >= 0 && index < chickens.length);
    }
    return [activeIndex - 1, activeIndex, activeIndex + 1];
  }, [activeIndex, chickens.length]);

  const getCardStyle = useCallback(
    (index: number): React.CSSProperties => {
      const offset = index - activeIndex;
      const translateX =
        offset * 40 +
        (touchState.isSwiping ? touchState.currentX * TRANSFORM_MULTIPLIER : 0);

      return {
        transform: `
        translateX(${translateX}px)
        translateZ(${-Math.abs(offset) * Z_OFFSET}px)
        scale(${1 - Math.abs(offset) * SCALE_FACTOR})
      `,
        opacity: 1 - Math.abs(offset) * OPACITY_FACTOR,
        zIndex: chickens.length - Math.abs(offset),
        pointerEvents: index === activeIndex ? "auto" : ("none" as const),
      };
    },
    [activeIndex, touchState.isSwiping, touchState.currentX, chickens.length]
  );

  const canGoBack = activeIndex > 0;
  const canGoForward = activeIndex < chickens.length - 1;

  return (
    <div className="flex flex-col items-center justify-center px-4">
      <div
        className="relative w-[300px] h-[300px]"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleTouchStart}
        onMouseMove={handleTouchMove}
        onMouseUp={handleTouchEnd}
        onMouseLeave={handleTouchEnd}
      >
        {chickens.map((chicken, index) => (
          <div
            key={index}
            className="absolute top-0 left-0 w-full h-full transition-all duration-300"
            style={getCardStyle(index)}
          >
            <div className="w-full h-full bg-bg border rounded-lg shadow-xl p-6 cursor-grab">
              <div className="w-full h-full flex flex-col items-center justify-center relative">
                <img
                  src={chicken?.image}
                  className="h-48 w-auto rounded-md"
                  alt="Chicken"
                />
                <div className="absolute -top-2 flex w-full justify-between items-center">
                  <Badge intent="secondary">#{chicken.tokenId}</Badge>
                  <Badge className="gap-1 items-center">
                    <Feathers size={12} />
                    {chicken.dailyFeathers} /day
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center space-x-3 mt-6">
        {chickens.length > 0 &&
          getPaginationIndexes().map((pageIndex) => (
            <PaginationButton
              key={pageIndex}
              pageIndex={pageIndex}
              activeIndex={activeIndex}
              canGoBack={canGoBack}
              canGoForward={canGoForward}
              onClick={() => setActiveIndex(pageIndex)}
            />
          ))}
      </div>
    </div>
  );
};

interface PaginationButtonProps {
  pageIndex: number;
  activeIndex: number;
  canGoBack: boolean;
  canGoForward: boolean;
  onClick: () => void;
}

const PaginationButton: React.FC<PaginationButtonProps> = ({
  pageIndex,
  activeIndex,
  canGoBack,
  canGoForward,
  onClick,
}) => {
  const isActive = pageIndex === activeIndex;
  const isDisabled =
    (pageIndex < activeIndex && !canGoBack) ||
    (pageIndex > activeIndex && !canGoForward);

  return (
    <button
      onClick={onClick}
      className={`relative w-3 h-3 transform rotate-45 transition-all duration-300 ${
        isActive
          ? "bg-yellow-400"
          : pageIndex < activeIndex && canGoBack
            ? "bg-gray-400 hover:bg-gray-300"
            : pageIndex > activeIndex && canGoForward
              ? "bg-gray-400 hover:bg-gray-300"
              : "bg-gray-600"
      }`}
      disabled={isDisabled}
    >
      {isActive && (
        <>
          <div className="absolute inset-0 bg-yellow-200 opacity-50 animate-ping" />
          <div className="absolute -inset-1 bg-yellow-400 opacity-20 blur-sm animate-pulse" />
        </>
      )}
    </button>
  );
};
