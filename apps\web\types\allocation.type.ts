// Define interfaces
export interface AllocationBreakdown {
  legendaryCount: number;
  quantity: number;
  totalAllocation: number;
  type?: string
}

export interface AllocationData {
  address: string;
  totalChickens: number;
  totalAllocations: number;
  breakdown: AllocationBreakdown[];
  timestamp: Date;
  lastUpdated: Date;
}

export interface DelegateData {
  address: string;
  delegatedBy: string;
  isRemoved: boolean;
  amount: number;
  timestamp: Date;
  lastUpdated: Date;
}


export interface DelegateRequestBody {
  address: string;
  amount: number;
  isRemoved?: boolean;
}