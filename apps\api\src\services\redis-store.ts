// src/services/redis-store.ts
import { Redis, RedisOptions } from "ioredis";
import { NonceData } from "../types";
import { env } from "../env";

export class RedisStore {
  private redis: Redis;
  private readonly NONCE_PREFIX = "eth:nonce:";
  private readonly LOCK_PREFIX = "lock:";
  private readonly NONCE_EXPIRY = 300; // 5 minutes in seconds
  private static instance: RedisStore;

  private readonly acquireLockScript = `
    if redis.call('SET', KEYS[1], ARGV[1], 'NX', 'PX', ARGV[2]) then
      return 1
    else
      return 0
    end
  `;

  private constructor() {
    this.redis = new Redis({
      port: env.REDIS.port,
      host: env.REDIS.host,
      username: env.REDIS.username,
      password: env.REDIS.password,
      db: 0,
    });

    this.redis.on("error", (error) => {
      console.error("Redis connection error:", error);
    });

    this.redis.on("connect", () => {
      console.log("Redis connected successfully");
    });
  }

  public static getInstance(): RedisStore {
    if (!RedisStore.instance) {
      RedisStore.instance = new RedisStore();
    }
    return RedisStore.instance;
  }

  async acquireLock(key: string, ttlMs: number = 5000): Promise<boolean> {
    const lockKey = `${this.LOCK_PREFIX}${key}`;
    const randomValue = Math.random().toString(36).substring(2);

    try {
      const result = await this.redis.eval(
        this.acquireLockScript,
        1,
        lockKey,
        randomValue,
        ttlMs.toString()
      );

      return result === 1;
    } catch (error) {
      console.error("Lock acquisition error:", error);
      return false;
    }
  }

  async releaseLock(key: string): Promise<void> {
    const lockKey = `${this.LOCK_PREFIX}${key}`;
    try {
      await this.redis.del(lockKey);
    } catch (error) {
      console.error("Lock release error:", error);
    }
  }

  async storeNonce(address: string, nonceData: NonceData): Promise<void> {
    const key = this.NONCE_PREFIX + address.toLowerCase();
    try {
      await this.redis.setex(key, this.NONCE_EXPIRY, JSON.stringify(nonceData));
    } catch (error) {
      console.error("Redis store error:", error);
      throw new Error("Failed to store nonce");
    }
  }

  async store(key: string, data: any, expiry?: number): Promise<void> {
    try {
      await this.redis.setex(
        key,
        expiry ?? this.NONCE_EXPIRY,
        JSON.stringify(data)
      );
    } catch (error) {
      console.error("Redis store error:", error);
      throw new Error("Failed to store data");
    }
  }

  async get(key: string): Promise<any> {
    try {
      const data = await this.redis.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error("Redis get error:", error);
      throw new Error("Failed to retrieve data");
    }
  }

  async getNonce(address: string): Promise<NonceData | null> {
    const key = this.NONCE_PREFIX + address.toLowerCase();
    try {
      const data = await this.redis.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error("Redis get error:", error);
      throw new Error("Failed to retrieve nonce");
    }
  }

  async remove(key: string): Promise<void> {
    try {
      await this.redis.del(key);
    } catch (error) {
      console.error("Redis delete error:", error);
      throw new Error("Failed to remove key");
    }
  }

  async removeNonce(address: string): Promise<void> {
    const key = this.NONCE_PREFIX + address.toLowerCase();
    try {
      await this.redis.del(key);
    } catch (error) {
      console.error("Redis delete error:", error);
      throw new Error("Failed to remove nonce");
    }
  }

  async close(): Promise<void> {
    await this.redis.quit();
  }
}

// Helper function to get Redis instance
export const getRedisStore = () => RedisStore.getInstance();
