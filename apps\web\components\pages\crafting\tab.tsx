"use client";

import FriedLeg from "@/components/shared/icons/fried-leg";
import {
  IconCircleInfo,
  IconDotGrid3X3,
  IconShield,
  IconHeart,
  IconMap,
} from "justd-icons";
import { Dispatch, SetStateAction } from "react";
import { buttonStyles, cn, Tooltip } from "@/components/ui";
import { EggIcon, Map, MapIcon, SwordIcon } from "lucide-react";
import { AdventureChickenIcon } from "@/components/shared/icons/chick-adventure";

type ActiveTabProps = {
  activeTab: number;
  setActiveTab: Dispatch<SetStateAction<number>>;
};

// Define tab data with filtering logic
const data = [
  {
    icon: <IconDotGrid3X3 className="h-[24px] w-[24px]" />,
    name: "All Items",
    filter: null, // null means show all items
  },
  {
    icon: <FriedLeg size={24} />,
    name: "Food",
    filter: "food",
  },
  {
    icon: <SwordIcon size={24} />,
    name: "<PERSON>",
    filter: "battle",
  },
  {
    icon: <EggIcon className="h-[24px] w-[24px]" />,
    name: "Breeding",
    filter: "breeding",
  },
  {
    icon: <Map className="h-[24px] w-[24px]" />,
    name: "Adventure",
    filter: "adventure",
  },
];

export function CraftingTab({ activeTab, setActiveTab }: ActiveTabProps) {
  return data.map((itm, idx) => (
    <Tab
      icon={itm.icon}
      name={itm.name}
      active={activeTab === idx}
      key={idx}
      index={idx}
      setActiveTab={setActiveTab}
    />
  ));
}

// Helper function to get current filter
export function getCurrentFilter(activeTab: number): string | null {
  return data[activeTab]?.filter || null;
}

// Helper function to filter items based on active tab
export function filterItemsByTab(items: any[], activeTab: number) {
  const filter = getCurrentFilter(activeTab);

  if (filter === null) {
    // "All Items" - show everything
    return items;
  }

  // Filter by specific type
  return items.filter((item) => item.type === filter);
}

function Tab({
  icon,
  name,
  active,
  index,
  setActiveTab,
}: {
  icon: React.ReactNode;
  name: string;
  active: boolean;
  index: number;
  setActiveTab: Dispatch<SetStateAction<number>>;
}) {
  return (
    <Tooltip delay={0}>
      <Tooltip.Trigger
        aria-label={name}
        className={cn(
          "flex items-center justify-center p-3 rounded-full duration-200 transition-colors outline-none",
          active
            ? "text-black/80 bg-primary "
            : "text-muted-fg bg-stone-800 hover:bg-stone-800/60 "
        )}
      >
        <div onClick={() => setActiveTab(index)}> {icon}</div>
      </Tooltip.Trigger>
      <Tooltip.Content placement="bottom" showArrow={false}>
        <div className="relative">
          <strong className="font-semibold">{name}</strong>
        </div>
      </Tooltip.Content>
    </Tooltip>
  );
}
