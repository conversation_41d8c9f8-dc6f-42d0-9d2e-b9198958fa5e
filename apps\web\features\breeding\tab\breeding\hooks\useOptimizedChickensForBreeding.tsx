"use client";

import { useMemo, useState, useCallback } from "react";
import { useStateContext } from "@/providers/app/state";
import { useChickenTokenIds } from "@/features/chickens/hooks/useChickenTokenIds";
import useChickenMetadataBatch from "./useChickenMetadataBatch";
import { useChickenBreedCountWithConfig } from "./useChickenBreedCount";
import { useChickenCooldownWithConfig } from "./useChickenCooldown";
import { IChickenMetadata } from "@/lib/types/chicken.types";

// Options for breeding chicken selection
interface IBreedingChickenOptions {
  pageSize: number;
  filterType: "all" | "legacy" | "genesis" | "ordinary";
  sortBy: "tokenId" | "breedCount";
  sortOrder: "asc" | "desc";
  enableAutoLoad: boolean;
}

const DEFAULT_BREEDING_OPTIONS: IBreedingChickenOptions = {
  pageSize: 20,
  filterType: "all",
  sortBy: "tokenId",
  sortOrder: "asc",
  enableAutoLoad: false,
};

/**
 * Optimized hook for breeding chicken selection that follows the delegation pattern:
 * 1. Load token IDs from contracts (Genesis/Legacy/Ordinary) - lightweight
 * 2. Apply type filters and search on token IDs - instant filtering
 * 3. Paginate filtered token IDs - only process what's needed
 * 4. Load metadata only for current page token IDs - minimal API calls
 * 5. Load breed counts only for current page token IDs - minimal API calls
 */
export function useOptimizedChickensForBreeding(
  searchQuery: string = "",
  options: Partial<IBreedingChickenOptions> = {}
) {
  const { address, isConnected } = useStateContext();
  const mergedOptions = { ...DEFAULT_BREEDING_OPTIONS, ...options };
  const [currentPage, setCurrentPage] = useState(1);

  // Step 1: Load token IDs from contracts (Genesis/Legacy/Ordinary)
  const {
    tokenIdsByType,
    isLoading: tokenIdsLoading,
    error: tokenIdsError,
  } = useChickenTokenIds(isConnected ? address : undefined);

  // Step 2: Apply type filter on token IDs
  const filteredTokenIds = useMemo(() => {
    const { genesis, legacy, ordinary } = tokenIdsByType;

    switch (mergedOptions.filterType) {
      case "genesis":
        return genesis;
      case "legacy":
        return legacy;
      case "ordinary":
        return ordinary;
      case "all":
      default:
        return [...genesis, ...legacy, ...ordinary];
    }
  }, [tokenIdsByType, mergedOptions.filterType]);

  // Step 3: Apply search filter on token IDs
  const searchFilteredTokenIds = useMemo(() => {
    if (!searchQuery.trim()) {
      return filteredTokenIds;
    }

    const query = searchQuery.toLowerCase();
    return filteredTokenIds.filter((tokenId) =>
      tokenId.toString().includes(query)
    );
  }, [filteredTokenIds, searchQuery]);

  // Step 3.5: Apply sorting to filtered token IDs
  const sortedTokenIds = useMemo(() => {
    const sorted = [...searchFilteredTokenIds];

    if (mergedOptions.sortBy === "tokenId") {
      sorted.sort((a, b) =>
        mergedOptions.sortOrder === "asc" ? a - b : b - a
      );
    }
    // Note: breedCount sorting would require loading all breed counts first,
    // which defeats the purpose of pagination. For now, only tokenId sorting is supported.

    return sorted;
  }, [searchFilteredTokenIds, mergedOptions.sortBy, mergedOptions.sortOrder]);

  // Step 4: Calculate pagination
  const totalCount = sortedTokenIds.length;
  const totalPages = Math.ceil(totalCount / mergedOptions.pageSize);
  const hasMore = currentPage < totalPages;

  // Step 5: Get current page token IDs
  const currentPageTokenIds = useMemo(() => {
    const startIndex = (currentPage - 1) * mergedOptions.pageSize;
    const endIndex = startIndex + mergedOptions.pageSize;
    return sortedTokenIds.slice(startIndex, endIndex);
  }, [sortedTokenIds, currentPage, mergedOptions.pageSize]);

  // Step 6: Load metadata only for current page token IDs
  const metadataQuery = useChickenMetadataBatch(currentPageTokenIds);

  // Step 7: Load breed counts only for current page token IDs
  const breedCountQuery = useChickenBreedCountWithConfig(currentPageTokenIds);

  // Step 8: Load cooldowns only for current page token IDs
  const cooldownQuery = useChickenCooldownWithConfig(currentPageTokenIds);

  // Create maps for efficient lookup
  const metadataMap = metadataQuery.metadataMap || {};
  const breedCountsMap = breedCountQuery.breedCountMap || {};
  const cooldownMap = cooldownQuery.cooldownMap || {};

  // Note: Genes are loaded separately when chickens are selected for breeding
  // to avoid unnecessary API calls for chickens that are just being browsed

  // Step 10: Combine data for current page chickens
  const chickens = useMemo(() => {
    if (!currentPageTokenIds.length) return [];

    return currentPageTokenIds
      .map((tokenId) => {
        const metadata = metadataMap[tokenId] as IChickenMetadata;
        const breedCount = breedCountsMap[tokenId] || 0;
        // genes will be loaded separately when chicken is selected for breeding
        const cooldownTime = cooldownMap[tokenId] || 0;

        // Determine chicken type from token ID ranges
        // Check genesis first since genesis chickens can have any token ID
        let type = "ordinary";
        if (tokenIdsByType.genesis.includes(tokenId)) {
          type = "genesis";
        } else if (tokenIdsByType.legacy.includes(tokenId)) {
          type = "legacy";
        }

        const isOnCooldown = cooldownTime > Date.now() / 1000;

        return {
          tokenId,
          type,
          metadata,
          breedCount,
          cooldownTime,
          isOnCooldown,
          image:
            metadata?.image ||
            `https://chicken-api-ivory.vercel.app/api/image/${tokenId}.png`,
          // genes will be loaded separately when chicken is selected for breeding
        };
      })
      .filter(Boolean);
  }, [
    currentPageTokenIds,
    metadataMap,
    breedCountsMap,
    cooldownMap,
    tokenIdsByType.genesis,
    tokenIdsByType.legacy,
  ]);

  // Pagination functions
  const goToNextPage = useCallback(() => {
    if (hasMore) {
      setCurrentPage((prev) => prev + 1);
    }
  }, [hasMore]);

  const goToPreviousPage = useCallback(() => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  }, [currentPage]);

  const goToPage = useCallback(
    (page: number) => {
      if (page >= 1 && page <= totalPages) {
        setCurrentPage(page);
      }
    },
    [totalPages]
  );

  // Reset page when search or filter changes
  const resetPage = useCallback(() => {
    setCurrentPage(1);
  }, []);

  // Loading states
  const isLoading =
    tokenIdsLoading ||
    metadataQuery.isLoading ||
    breedCountQuery.isLoading ||
    cooldownQuery.isLoading;

  // Error states
  const error =
    tokenIdsError ||
    metadataQuery.error ||
    breedCountQuery.error ||
    cooldownQuery.error;

  // Stats for display
  const stats = useMemo(() => {
    const { genesis, legacy, ordinary } = tokenIdsByType;
    return {
      total: genesis.length + legacy.length + ordinary.length,
      genesis: genesis.length,
      legacy: legacy.length,
      ordinary: ordinary.length,
      filtered: searchFilteredTokenIds.length,
    };
  }, [tokenIdsByType, searchFilteredTokenIds.length]);

  return {
    // Data
    chickens,
    currentPageTokenIds,

    // Pagination
    currentPage,
    totalPages,
    totalCount,
    hasMore,
    goToNextPage,
    goToPreviousPage,
    goToPage,
    resetPage,

    // Loading and error states
    isLoading,
    error,
    isConnected,
    address,

    // Stats
    stats,

    // Filter/sort options
    filterType: mergedOptions.filterType,
    sortBy: mergedOptions.sortBy,
    sortOrder: mergedOptions.sortOrder,

    // Maps for direct access
    metadataMap,
    breedCountsMap,
    cooldownMap,

    // Cooldown helpers
    isOnCooldown: cooldownQuery.isOnCooldown,
    getRemainingCooldown: cooldownQuery.getRemainingCooldown,
  };
}
