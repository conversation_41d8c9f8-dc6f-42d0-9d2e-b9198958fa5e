"use client";

interface ILoadingDots2Props extends React.HTMLAttributes<HTMLDivElement> {
  message?: string;
  dots?: string;
}

export default function LoadingDots2({
  message = "",
  dots = "•",
  ...props
}: ILoadingDots2Props) {
  return (
    <div {...props}>
      {message}
      <span className="dot">{dots}</span>
      <span className="dot">{dots}</span>
      <span className="dot">{dots}</span>
    </div>
  );
}
