[{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "outputTokenId", "type": "uint256"}], "name": "ErrAnyResourceMaterialWithoutAcceptedIds", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "resourceId", "type": "uint256"}], "name": "ErrDuplicateResourceId", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "required", "type": "uint256"}, {"internalType": "uint256", "name": "available", "type": "uint256"}], "name": "ErrInsufficientBalance", "type": "error"}, {"inputs": [], "name": "ErrInvalidAmount", "type": "error"}, {"inputs": [], "name": "ErrInvalidMaterials", "type": "error"}, {"inputs": [], "name": "ErrInvalidResourceIds", "type": "error"}, {"inputs": [], "name": "ErrInvalidTokenAddress", "type": "error"}, {"inputs": [], "name": "ErrMultipleAnyResourceMaterials", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "outputTokenId", "type": "uint256"}], "name": "ErrRecipeInactive", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "outputTokenId", "type": "uint256"}], "name": "ErrRecipeNotFound", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "resourceId", "type": "uint256"}], "name": "ErrResourceIdNotAccepted", "type": "error"}, {"inputs": [], "name": "ErrTooManyMaterials", "type": "error"}, {"inputs": [], "name": "ErrTooManyResourceIds", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "ErrUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "outputTokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "referrer", "type": "string"}], "name": "ItemCrafted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "outputTokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256[]", "name": "newIds", "type": "uint256[]"}], "name": "RecipeAcceptedResourceIdsUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "outputTokenId", "type": "uint256"}], "name": "RecipeActivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "outputTokenId", "type": "uint256"}], "name": "RecipeCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "outputTokenId", "type": "uint256"}], "name": "RecipeDeactivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "outputTokenId", "type": "uint256"}], "name": "RecipeUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256[]", "name": "resourceIds", "type": "uint256[]"}, {"indexed": false, "internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "name": "ResourcesBurned", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "COCK", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FEATHERS", "outputs": [{"internalType": "contract IERC1155Mintable", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_ACCEPTED_RESOURCE_IDS_PER_RECIPE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_MATERIALS_PER_RECIPE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PAUSER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REFERRAL", "outputs": [{"internalType": "contract IReferral", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "RESOURCES", "outputs": [{"internalType": "contract IERC1155Mintable", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "outputTokenId", "type": "uint256"}], "name": "activateRecipe", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256[]", "name": "resourceIds", "type": "uint256[]"}], "name": "batchCheckResourceBalances", "outputs": [{"internalType": "uint256[]", "name": "balances", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "outputTokenIds", "type": "uint256[]"}], "name": "batchGetRecipes", "outputs": [{"components": [{"components": [{"internalType": "enum SabongSagaVariableCrafting.TokenType", "name": "tokenType", "type": "uint8"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct SabongSagaVariableCrafting.CraftingMaterial[]", "name": "materials", "type": "tuple[]"}, {"internalType": "bool", "name": "exists", "type": "bool"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "internalType": "struct SabongSagaVariableCrafting.CraftingRecipe[]", "name": "recipesData", "type": "tuple[]"}, {"internalType": "uint256[][]", "name": "acceptedResourceIdsArray", "type": "uint256[][]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "outputTokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256[]", "name": "resourceIds", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "resourceAmounts", "type": "uint256[]"}, {"internalType": "string", "name": "referralCode", "type": "string"}], "name": "craft", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "outputTokenId", "type": "uint256"}], "name": "deactivateRecipe", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getAllRecipeIds", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "outputTokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "getCraftingCost", "outputs": [{"components": [{"internalType": "enum SabongSagaVariableCrafting.TokenType", "name": "tokenType", "type": "uint8"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct SabongSagaVariableCrafting.CraftingMaterial[]", "name": "materials", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "outputTokenId", "type": "uint256"}], "name": "getRecipe", "outputs": [{"components": [{"internalType": "enum SabongSagaVariableCrafting.TokenType", "name": "tokenType", "type": "uint8"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct SabongSagaVariableCrafting.CraftingMaterial[]", "name": "materials", "type": "tuple[]"}, {"internalType": "bool", "name": "exists", "type": "bool"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256[]", "name": "acceptedResourceIds", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "outputTokenId", "type": "uint256"}], "name": "getRecipeAcceptedResourceIds", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getRecipeCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMembers", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_COCK", "type": "address"}, {"internalType": "address", "name": "_RESOURCES", "type": "address"}, {"internalType": "address", "name": "_FEATHERS", "type": "address"}, {"internalType": "address", "name": "_REFERRAL", "type": "address"}, {"internalType": "address", "name": "_FEE_WALLET", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "outputTokenId", "type": "uint256"}], "name": "isRecipeActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "isRecipeResourceIdAccepted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "recipeAcceptedResourceIds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "recipeExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "recipeIds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "recipes", "outputs": [{"internalType": "bool", "name": "exists", "type": "bool"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "outputTokenId", "type": "uint256"}, {"components": [{"internalType": "enum SabongSagaVariableCrafting.TokenType", "name": "tokenType", "type": "uint8"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct SabongSagaVariableCrafting.CraftingMaterial[]", "name": "materials", "type": "tuple[]"}, {"internalType": "uint256[]", "name": "acceptedResourceIds", "type": "uint256[]"}], "name": "setRecipe", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "outputTokenId", "type": "uint256"}, {"internalType": "uint256[]", "name": "acceptedResourceIds", "type": "uint256[]"}], "name": "setRecipeAcceptedResourceIds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "updateFeeWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]