import { cookies } from "next/headers";

import { NextRequest, NextResponse } from "next/server";
export async function GET(request: NextRequest) {
  const c = await cookies();
  const jwt = c.get("jwt")?.value;
  if (!jwt) {
    return NextResponse.json(
      {
        status: false,
        responseCode: 401,
        message: "Bad request",
      },
      { status: 401 }
    );
  }
  try {
    const res = await fetch(
      `${process.env.HONO_API_ENDPOINT}/api/chickens/able-to-rub`,
      {
        method: "GET",
        headers: {
          "content-type": "application/json",
          authorization: `Bearer ${jwt}`,
        },
        cache: "no-store",
      }
    );

    if (res.ok) {
      const data = await res.json();

      return NextResponse.json(data, { status: 200 });
    }
  } catch (error) {
    return NextResponse.json(
      {
        status: false,
        responseCode: 400,
        message: "Bad request",
        errors: [(error as Error).message],
      },
      { status: 500 }
    );
  }
}
