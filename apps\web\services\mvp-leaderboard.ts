import { LeaderboardType } from "@/types/mvp-leaderboard.type";

export const MvpLeaderboardApi = {
  getMvpLeaderBoard: async (
    type: LeaderboardType,
    page: number,
    limit: number
  ) => {
    const response = await fetch(
      `https://chicken-api-ivory.vercel.app/api/stats/mvp/leaderboard/${type}?page=${page}&limit=${limit}`
    );

    if (!response.ok) {
      throw new Error("Failed to fetch leaderboard data");
    }

    return response.json();
  },
};
