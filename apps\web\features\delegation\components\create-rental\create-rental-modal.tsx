"use client";

import { Modal } from "ui";
import { CreateRental } from "./create-rental";

// Interface for pre-selected chicken data from inventory
interface IPreSelectedChickenData {
  tokenId: number;
  image: string;
  metadata?: any;
  type?: string;
  level?: number;
  chickenStats?: any;
}

interface ICreateRentalModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  preSelectedChickenId?: number | null;
  preSelectedChickenData?: IPreSelectedChickenData | null;
}

export function CreateRentalModal({
  isOpen,
  onOpenChange,
  preSelectedChickenId,
  preSelectedChickenData,
}: ICreateRentalModalProps) {
  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
      <Modal.Content
        size="5xl"
        classNames={{
          content: "max-h-none h-auto",
          overlay: "overflow-y-auto",
        }}
      >
        <Modal.Header>
          <Modal.Title>Delegate Chicken</Modal.Title>
          <Modal.Description>
            Set up delegation terms for your chicken - either list it for rent
            or delegate it directly to someone
          </Modal.Description>
        </Modal.Header>
        <Modal.Body className="pb-12">
          {isOpen && (
            <CreateRental
              preSelectedChickenId={preSelectedChickenId}
              preSelectedChickenData={preSelectedChickenData}
              onSuccess={() => onOpenChange(false)}
            />
          )}
        </Modal.Body>
      </Modal.Content>
    </Modal>
  );
}
