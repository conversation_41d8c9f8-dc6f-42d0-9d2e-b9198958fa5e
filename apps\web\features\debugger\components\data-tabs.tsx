"use client";

import { Tabs } from "@/components/ui/tabs";
import React from "react";
import ReactJson from "react-json-view";

interface IDataTabsProps {
  chickens: any[];
  eggs: any[];
  nullAttributes: any[];
  searchTokenId: string;
  chickensTotal: number;
  eggsTotal: number;
  nullAttributesTotal: number;
}

export default function DataTabs({
  chickens,
  eggs,
  nullAttributes,
  searchTokenId,
  chickensTotal,
  eggsTotal,
  nullAttributesTotal,
}: IDataTabsProps): React.ReactNode {
  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
      <div className="w-full">
        <Tabs aria-label="Chicken Data">
          <Tabs.List className="flex max-w-max bg-gray-50 p-1 rounded-lg mb-4 border-none">
            <Tabs.Tab
              id="chickens"
              className={({ isSelected }) =>
                isSelected
                  ? "flex-1 text-center py-1.5 px-3 bg-white rounded-lg shadow-sm text-primary hover:text-primary font-medium transition-all"
                  : "flex-1 text-center py-1.5 px-3 text-gray-600 rounded-lg hover:text-primary font-medium transition-all"
              }
            >
              <div className="flex translate-y-0.5 items-center justify-center space-x-2">
                <span className="font-medium">Chickens</span>
                <span className="bg-primary/10 text-primary text-xs px-2 py-1 rounded-full">
                  {chickens.length}
                </span>
              </div>
            </Tabs.Tab>
            <Tabs.Tab
              id="eggs"
              className={({ isSelected }) =>
                isSelected
                  ? "flex-1 text-center py-1.5 px-3 bg-white rounded-lg shadow-sm text-primary hover:text-primary font-medium transition-all"
                  : "flex-1 text-center py-1.5 px-3 text-gray-600 rounded-lg hover:text-primary font-medium transition-all"
              }
            >
              <div className="flex translate-y-0.5 items-center justify-center space-x-2">
                <span className="font-medium">Eggs</span>
                <span className="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">
                  {eggs.length}
                </span>
              </div>
            </Tabs.Tab>
            <Tabs.Tab
              id="nullAttributes"
              className={({ isSelected }) =>
                isSelected
                  ? "flex-1 text-center py-1.5 px-3 bg-white rounded-lg shadow-sm text-primary hover:text-primary font-medium transition-all"
                  : "flex-1 text-center py-1.5 px-3 text-gray-600 rounded-lg hover:text-primary font-medium transition-all"
              }
            >
              <div className="flex translate-y-0.5 items-center justify-center space-x-2">
                <span className="font-medium">Null Attributes</span>
                <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                  {nullAttributes.length}
                </span>
              </div>
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel id="chickens">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-700">
                  Chickens Data
                </h2>
                {searchTokenId.trim() && (
                  <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border">
                    Showing {chickens.length} of {chickensTotal} chickens
                  </div>
                )}
              </div>
              <div className="max-h-[60vh] overflow-auto bg-white p-4 rounded-lg border border-gray-300">
                <ReactJson
                  src={chickens}
                  name="chickens"
                  theme="rjv-default"
                  collapsed={1}
                  displayDataTypes={false}
                  enableClipboard={true}
                />
              </div>
            </div>
          </Tabs.Panel>

          <Tabs.Panel id="eggs">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-700">
                  Eggs Data
                </h2>
                {searchTokenId.trim() && (
                  <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border">
                    Showing {eggs.length} of {eggsTotal} eggs
                  </div>
                )}
              </div>
              <div className="max-h-[60vh] overflow-auto bg-white p-4 rounded-lg border">
                <ReactJson
                  src={eggs}
                  name="eggs"
                  theme="rjv-default"
                  collapsed={1}
                  displayDataTypes={false}
                  enableClipboard={true}
                />
              </div>
            </div>
          </Tabs.Panel>

          <Tabs.Panel id="nullAttributes">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-700">
                  Null Attributes Data
                </h2>
                {searchTokenId.trim() && (
                  <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border">
                    Showing {nullAttributes.length} of {nullAttributesTotal}{" "}
                    items
                  </div>
                )}
              </div>
              <div className="max-h-[60vh] overflow-auto bg-white p-4 rounded-lg border">
                <ReactJson
                  src={nullAttributes}
                  name="chickensNullAttributes"
                  theme="rjv-default"
                  collapsed={1}
                  displayDataTypes={false}
                  enableClipboard={true}
                />
              </div>
            </div>
          </Tabs.Panel>
        </Tabs>
      </div>
    </div>
  );
}
