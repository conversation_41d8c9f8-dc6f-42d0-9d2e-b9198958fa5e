import { handleError } from "@/utils/handle-error";
import { create } from "zustand";
import RandomCraftingAbi from "@/abi/RandomCookie.abi.json";
import {
  Address,
  erc1155Abi,
  erc20Abi,
  formatEther,
  parseEventLogs,
} from "viem";
import { toast } from "sonner";
import useAuthStore from "./auth";
import { ronin, saigon } from "viem/chains";

const CHAIN_ID = Number(process.env.NEXT_PUBLIC_CHAINDID || "2021");
const COCK_TOKEN_ADDRESS = process.env.NEXT_PUBLIC_COCK_CONTRACT as Address;
const FEATHERS_TOKEN_ADDRESS = process.env
  .NEXT_PUBLIC_FEATHERS_CONTRACT as Address;
const RESOURCES_ADDRESS = process.env.NEXT_PUBLIC_GAMEITEMS_CONTRACT as Address;
const RANDOM_CRAFTING = process.env
  .NEXT_PUBLIC_RANDOM_CRAFTING_CONTRACT as Address;
const chain = CHAIN_ID === 2020 ? ronin : saigon;

interface CornAllocation {
  [cornId: number]: number;
}

type TokenType = 0 | 1 | 2; // ERC20, ERC1155, ERC1155_ANY_RESOURCE

type CraftingMaterial = {
  tokenType: TokenType;
  tokenAddress: string;
  tokenId: bigint;
  amount: bigint;
};

type WeightedOutput = {
  tokenId: bigint;
  weight: bigint;
  active: boolean;
};

type CraftingPool = {
  materials: CraftingMaterial[];
  acceptedResourceIds: bigint[];
  exists: boolean;
  active: boolean;
  name: string;
  totalWeight: bigint;
  weightedOutputs?: WeightedOutput[]; // Optional, fetched separately if needed
};

type RandomCraftingState = {
  isPending: boolean;
  isFetchingPools: boolean;
  craftingPools: {
    [poolId: number]: CraftingPool;
  };
  activePoolIds: number[];
};

type Actions = {
  craftFromPool: (
    poolId: number,
    amount: number,
    referralCode: string,
    cornAllocation?: CornAllocation
  ) => Promise<{ tokenIds: bigint[]; amounts: bigint[] } | undefined>;
  getCraftingPools: (poolIds?: number[]) => Promise<void>;
  getActivePoolIds: () => Promise<void>;
  getPoolWeightedOutputs: (poolId: number) => Promise<WeightedOutput[]>;
  approveCock: (amt: number) => Promise<void>;
  approveFeathers: () => Promise<void>;
  approveResources: () => Promise<void>;
};

type StoreState = RandomCraftingState & Actions;

const initialState: RandomCraftingState = {
  isPending: false,
  craftingPools: {},
  activePoolIds: [],
  isFetchingPools: false,
};

const useRandomCraftingStore = create<StoreState>()((set, get) => {
  return {
    ...initialState,

    getActivePoolIds: async () => {
      try {
        const stateContext = window.stateContext;
        if (!stateContext || !stateContext.address) {
          return;
        }
        const { publicClient } = stateContext;

        const activePoolIds = await publicClient.readContract({
          address: RANDOM_CRAFTING,
          abi: RandomCraftingAbi,
          functionName: "getActivePoolIds",
          args: [],
        });

        set({
          activePoolIds: (activePoolIds as bigint[]).map((id) => Number(id)),
        });
      } catch (error) {
        handleError(error);
        set({ activePoolIds: [] });
      }
    },

    getCraftingPools: async (poolIds: number[] = [0]) => {
      set({ isFetchingPools: true });
      try {
        const stateContext = window.stateContext;
        if (!stateContext || !stateContext.address) {
          return;
        }
        const { publicClient } = stateContext;

        let targetPoolIds: number[];

        if (poolIds) {
          targetPoolIds = poolIds;
        } else {
          // If no specific pools requested, get all active pools
          await get().getActivePoolIds();
          targetPoolIds = get().activePoolIds;
        }

        if (targetPoolIds.length === 0) {
          set({ craftingPools: {} });
          return;
        }

        // Batch get pool data
        const poolsData = await publicClient.readContract({
          address: RANDOM_CRAFTING,
          abi: RandomCraftingAbi,
          functionName: "batchGetPools",
          args: [targetPoolIds],
        });

        const pools = poolsData as CraftingPool[];

        // Convert to indexed object
        const craftingPools: { [poolId: number]: CraftingPool } = {};

        pools.forEach((pool, index) => {
          const poolId = targetPoolIds[index];

          craftingPools[poolId!] = {
            ...pool,
            // Convert bigint arrays to proper format
            acceptedResourceIds: pool.acceptedResourceIds || [],
          };
        });

        set({ craftingPools });
      } catch (error) {
        handleError(error);
        set({ craftingPools: {} });
      } finally {
        set({ isFetchingPools: false });
      }
    },
    getPoolCraftingConfig: async (
      poolId: number,
      amount: number = 1
    ): Promise<CraftingMaterial[]> => {
      try {
        const stateContext = window.stateContext;
        if (!stateContext || !stateContext.address) {
          return [];
        }
        const { publicClient } = stateContext;

        const craftingCost = await publicClient.readContract({
          address: RANDOM_CRAFTING,
          abi: RandomCraftingAbi,
          functionName: "getPoolCraftingCost",
          args: [BigInt(poolId), BigInt(amount)],
        });

        return craftingCost as CraftingMaterial[];
      } catch (error) {
        handleError(error);
        return [];
      }
    },

    getPoolWeightedOutputs: async (
      poolId: number
    ): Promise<WeightedOutput[]> => {
      try {
        const stateContext = window.stateContext;
        if (!stateContext || !stateContext.address) {
          return [];
        }
        const { publicClient } = stateContext;

        const weightedOutputs = await publicClient.readContract({
          address: RANDOM_CRAFTING,
          abi: RandomCraftingAbi,
          functionName: "getPoolActiveWeightedOutputs",
          args: [BigInt(poolId)],
        });

        return weightedOutputs as WeightedOutput[];
      } catch (error) {
        handleError(error);
        return [];
      }
    },

    craftFromPool: async (
      poolId,
      amount,
      referralCode = "0x",
      cornAllocation
    ) => {
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot craft from pool", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const { checkApprovals, fetchBalances } = useAuthStore.getState();

      try {
        await ConnectRecentWallet();

        if (!address || !walletClient) {
          toast.error("Cannot craft from pool", {
            description: "Wallet not connected",
            position: "top-right",
          });
          return;
        }

        // Get the pool configuration
        const pools = get().craftingPools;
        const pool = pools[poolId];

        if (!pool) {
          // Try to fetch the specific pool
          await get().getCraftingPools([poolId]);
          const updatedPools = get().craftingPools;
          const updatedPool = updatedPools[poolId];

          if (!updatedPool) {
            toast.error("Cannot craft from pool", {
              description: "Pool not found",
              position: "top-right",
            });
            return;
          }
        }

        const poolConfig = pools[poolId] || get().craftingPools[poolId];

        if (!poolConfig) {
          toast.error("Cannot craft from pool", {
            description: "Pool is not active",
            position: "top-right",
          });
          return;
        }

        // Check if this pool has ERC1155_ANY_RESOURCE materials (tokenType === 2)
        const hasAnyResourceMaterial = poolConfig.materials.some(
          (material) => material.tokenType === 2
        );

        // Check required approvals for each material in the pool
        let needsResourcesApproval = false;
        let needsFeathersApproval = false;
        let totalCockRequired = 0n;

        // Validate corn allocation against accepted resource IDs if using any resource materials
        if (hasAnyResourceMaterial && cornAllocation) {
          const acceptedResourceIdsSet = new Set(
            poolConfig.acceptedResourceIds.map((id) => Number(id))
          );

          // Validate that all provided corn IDs are in the accepted list
          for (const [cornIdStr, cornAmount] of Object.entries(
            cornAllocation
          )) {
            const cornId = Number(cornIdStr);
            if (cornAmount > 0 && !acceptedResourceIdsSet.has(cornId)) {
              toast.error("Invalid resource", {
                description: `Resource ID ${cornId} is not accepted for this pool`,
                position: "top-right",
              });
              return;
            }
          }
        }

        // Determine what approvals are needed based on materials
        if (hasAnyResourceMaterial && cornAllocation) {
          // For pools with ERC1155_ANY_RESOURCE materials and corn allocation
          const cornEntries = Object.entries(cornAllocation).filter(
            ([_, amount]) => amount > 0
          );

          // Check if corn resources need approval (assuming they're ERC1155 from RESOURCES contract)
          if (cornEntries.length > 0) {
            needsResourcesApproval = true;
          }

          // Check other materials in the pool (non-ERC1155_ANY_RESOURCE)
          const otherMaterials = poolConfig.materials.filter(
            (material) => material.tokenType !== 2
          );
          for (const material of otherMaterials) {
            const requiredAmount = material.amount * BigInt(amount);

            if (material.tokenType === 0) {
              // ERC20 token
              if (
                material.tokenAddress.toLowerCase() ===
                COCK_TOKEN_ADDRESS?.toLowerCase()
              ) {
                totalCockRequired += requiredAmount;
              }
            } else if (material.tokenType === 1) {
              // ERC1155 token
              if (
                material.tokenAddress.toLowerCase() ===
                RESOURCES_ADDRESS?.toLowerCase()
              ) {
                needsResourcesApproval = true;
              } else if (
                material.tokenAddress.toLowerCase() ===
                FEATHERS_TOKEN_ADDRESS?.toLowerCase()
              ) {
                needsFeathersApproval = true;
              }
            }
          }
        } else {
          // For regular pools without ERC1155_ANY_RESOURCE materials, check all materials
          for (const material of poolConfig.materials) {
            const requiredAmount = material.amount * BigInt(amount);

            if (material.tokenType === 0) {
              // ERC20 token
              if (
                material.tokenAddress.toLowerCase() ===
                COCK_TOKEN_ADDRESS?.toLowerCase()
              ) {
                totalCockRequired += requiredAmount;
              }
            } else if (material.tokenType === 1) {
              // ERC1155 token
              if (
                material.tokenAddress.toLowerCase() ===
                RESOURCES_ADDRESS?.toLowerCase()
              ) {
                needsResourcesApproval = true;
              } else if (
                material.tokenAddress.toLowerCase() ===
                FEATHERS_TOKEN_ADDRESS?.toLowerCase()
              ) {
                needsFeathersApproval = true;
              }
            }
          }
        }

        // Get current approval states from auth store
        const {
          cockAllowanceRandomCrafting,
          isRandomCraftingFeatherApproved,
          isRandomCraftingResourceApproved,
        } = useAuthStore.getState();

        // Get approval functions
        const { approveResources, approveFeathers, approveCock } = get();

        // Handle approvals in sequence
        if (needsResourcesApproval && !isRandomCraftingResourceApproved) {
          toast.info("Resources approval required", {
            description: "Approving resources for crafting...",
            position: "top-right",
          });
          await approveResources();
        }

        if (needsFeathersApproval && !isRandomCraftingFeatherApproved) {
          toast.info("Feathers approval required", {
            description: "Approving feathers for crafting...",
            position: "top-right",
          });
          await approveFeathers();
        }

        if (
          totalCockRequired > 0n &&
          cockAllowanceRandomCrafting < totalCockRequired
        ) {
          toast.info("COCK approval required", {
            description: "Approving COCK tokens for crafting...",
            position: "top-right",
          });
          await approveCock(Number(totalCockRequired));
        }

        const r =
          referralCode === undefined || !referralCode || referralCode === ""
            ? "0x"
            : referralCode;

        let resourceIds: number[] = [];
        let resourceAmounts: number[] = [];

        // Handle resource allocation for ERC1155_ANY_RESOURCE materials
        if (hasAnyResourceMaterial && cornAllocation) {
          // Filter out zero amounts and validate against accepted resource IDs
          const cornEntries = Object.entries(cornAllocation).filter(
            ([cornIdStr, cornAmount]) => {
              const cornId = Number(cornIdStr);
              return (
                cornAmount > 0 &&
                poolConfig.acceptedResourceIds.some(
                  (id) => Number(id) === cornId
                )
              );
            }
          );

          // Add corn allocations to the arrays
          cornEntries.forEach(([cornId, cornAmount]) => {
            resourceIds.push(Number(cornId));
            resourceAmounts.push(cornAmount); // Scale by the crafting amount
          });
        }
        set({ isPending: true });
        // Use the craftFromPool function from the ABI
        const { request } = await publicClient.simulateContract({
          address: RANDOM_CRAFTING,
          abi: RandomCraftingAbi,
          functionName: "craftFromPool",
          args: [
            BigInt(poolId), // poolId
            BigInt(amount), // amount
            resourceIds, // resourceIds (for ERC1155_ANY_RESOURCE materials)
            resourceAmounts, // resourceAmounts (for ERC1155_ANY_RESOURCE materials)
            r, // referralCode
          ],
          account: address,
        });

        // Execute the transaction
        const hash = await walletClient.writeContract(request);

        toast.info("Crafting transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-center",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          // Refresh balances after successful craft
          await Promise.all([
            checkApprovals(isConnected, publicClient, address),
            fetchBalances(isConnected, publicClient, address),
          ]);

          // Parse event logs to get crafted items info
          const logs = parseEventLogs({
            abi: RandomCraftingAbi,
            eventName: "PoolCraftingCompleted",
            logs: receipt.logs,
          });

          if (logs.length > 0) {
            // Extract the relevant data from the event
            const eventData = logs[0] as any;
            return {
              tokenIds: eventData.args.tokenIds,
              amounts: eventData.args.amounts,
            };
          } else {
            // If no specific event, return success indicator
            return {
              tokenIds: [],
              amounts: [],
            };
          }
        } else {
          toast.error("Crafting transaction failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-center",
          });
          throw new Error("Crafting transaction failed");
        }
      } catch (error) {
        handleError(error);
      } finally {
        set({ isPending: false });
      }
    },

    approveResources: async () => {
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot approve resources", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const { checkApprovals } = useAuthStore.getState();

      try {
        await ConnectRecentWallet();

        if (!address || !walletClient) {
          toast.error("Cannot approve ERC1155", {
            description: "Wallet not connected",
            position: "top-right",
          });
          return;
        }

        set({ isPending: true });
        toast.info("Preparing approval transaction...", {
          description: "Setting approval for all resources tokens",
          position: "top-right",
        });

        // Set approval for all for ERC1155 token
        const hash = await walletClient.writeContract({
          address: RESOURCES_ADDRESS,
          abi: erc1155Abi,
          functionName: "setApprovalForAll",
          args: [RANDOM_CRAFTING, true],
          chain,
          account: address,
        });

        toast.info("Approval transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-right",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          toast.success("ERC1155 approval successful", {
            description:
              "Successfully approved all resources tokens for spending",
            position: "top-right",
          });

          // Update the approval status after successful approval
          await checkApprovals(isConnected, publicClient, address);
        } else {
          toast.error("ERC1155 approval failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-right",
          });
        }
      } catch (error) {
        handleError(error);
      } finally {
        set({ isPending: false });
      }
    },

    approveFeathers: async () => {
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot approve ERC1155", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const { checkApprovals } = useAuthStore.getState();

      try {
        await ConnectRecentWallet();

        if (!address || !walletClient) {
          toast.error("Cannot approve ERC1155", {
            description: "Wallet not connected",
            position: "top-right",
          });
          return;
        }

        set({ isPending: true });
        toast.info("Preparing approval transaction...", {
          description: "Setting approval for all Feathers tokens",
          position: "top-right",
        });

        // Set approval for all for ERC1155 token
        const hash = await walletClient.writeContract({
          address: FEATHERS_TOKEN_ADDRESS,
          abi: erc1155Abi,
          functionName: "setApprovalForAll",
          args: [RANDOM_CRAFTING, true],
          chain,
          account: address,
        });

        toast.info("Approval transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-right",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          toast.success("ERC1155 approval successful", {
            description:
              "Successfully approved all Feathers tokens for spending",
            position: "top-right",
          });

          // Update the approval status after successful approval
          await checkApprovals(isConnected, publicClient, address);
        } else {
          toast.error("ERC1155 approval failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-right",
          });
        }
      } catch (error) {
        handleError(error);
      } finally {
        set({ isPending: false });
      }
    },

    approveCock: async (amt) => {
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot approve ERC20", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const { checkApprovals } = useAuthStore.getState();

      try {
        await ConnectRecentWallet();

        if (!address || !walletClient) {
          toast.error("Cannot approve ERC20", {
            description: "Wallet not connected",
            position: "top-right",
          });
          return;
        }

        set({ isPending: true });

        const amtWithPrice = BigInt(amt);

        toast.info("Preparing approval transaction...", {
          description: `Approving ${formatEther(amtWithPrice)} COCK tokens for spending`,
          position: "top-right",
        });

        const { request } = await publicClient.simulateContract({
          address: COCK_TOKEN_ADDRESS,
          abi: erc20Abi,
          functionName: "approve",
          args: [RANDOM_CRAFTING, amtWithPrice],
          chain,
          account: address,
        });

        const hash = await walletClient.writeContract(request);

        toast.info("Approval transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-right",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          toast.success("ERC20 approval successful", {
            description: `Approved ${formatEther(amtWithPrice)} COCK tokens for spending`,
            position: "top-right",
          });

          // Update the allowance after successful approval
          await checkApprovals(isConnected, publicClient, address);
        } else {
          toast.error("ERC20 approval failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-right",
          });
        }
      } catch (error) {
        handleError(error);
      } finally {
        set({ isPending: false });
      }
    },
  };
});

export default useRandomCraftingStore;
