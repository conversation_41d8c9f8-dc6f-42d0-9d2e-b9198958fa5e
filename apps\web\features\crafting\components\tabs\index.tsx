"use client";

import React from "react";
import { Tabs } from "ui";
import { CraftingBreeding } from "./breeding";
import { CraftingFood } from "./food";
import { CraftingGears } from "./gears";
import { CraftingMedicine } from "./medicine";
import { CraftingOthers } from "./others";
import { CraftingVitamins } from "./vitamins";

export function CraftingTabs() {
  return (
    <Tabs aria-label="Crafting">
      <Tabs.List className="border-none font-Poppins">
        <Tabs.Tab
          id="food"
          className={({ isSelected }) =>
            isSelected ? "text-primary hover:text-primary" : ""
          }
          indicatorClassName="bg-primary"
        >
          Food
        </Tabs.Tab>
        <Tabs.Tab
          id="medicine"
          className={({ isSelected }) =>
            isSelected ? "text-primary hover:text-primary" : ""
          }
          indicatorClassName="bg-primary"
        >
          Medicine
        </Tabs.Tab>
        <Tabs.Tab
          id="gears"
          className={({ isSelected }) =>
            isSelected ? "text-primary hover:text-primary" : ""
          }
          indicatorClassName="bg-primary"
        >
          Gears
        </Tabs.Tab>
        <Tabs.Tab
          id="vitamins"
          className={({ isSelected }) =>
            isSelected ? "text-primary hover:text-primary" : ""
          }
          indicatorClassName="bg-primary"
        >
          Vitamins
        </Tabs.Tab>
        <Tabs.Tab
          id="breeding"
          className={({ isSelected }) =>
            isSelected ? "text-primary hover:text-primary" : ""
          }
          indicatorClassName="bg-primary"
        >
          Breeding
        </Tabs.Tab>
        <Tabs.Tab
          id="others"
          className={({ isSelected }) =>
            isSelected ? "text-primary hover:text-primary" : ""
          }
          indicatorClassName="bg-primary"
        >
          Others
        </Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel id="food">
        <CraftingFood />
      </Tabs.Panel>
      <Tabs.Panel id="medicine">
        <CraftingMedicine />
      </Tabs.Panel>
      <Tabs.Panel id="gears">
        <CraftingGears />
      </Tabs.Panel>
      <Tabs.Panel id="vitamins">
        <CraftingVitamins />
      </Tabs.Panel>
      <Tabs.Panel id="breeding">
        <CraftingBreeding />
      </Tabs.Panel>
      <Tabs.Panel id="others">
        <CraftingOthers />
      </Tabs.Panel>
    </Tabs>
  );
}
