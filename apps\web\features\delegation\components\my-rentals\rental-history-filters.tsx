"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Badge } from "ui";
import { Filter, X, Search, Calendar, ChevronDown } from "lucide-react";
import { cn } from "@/utils/classes";
import useDebounce from "@/lib/hooks/useDebounce";
import {
  IRentalHistoryFilters,
  ERentalHistoryEventType,
} from "../../types/delegation.types";

interface IRentalHistoryFiltersProps {
  filters: IRentalHistoryFilters;
  onFiltersChange: (filters: Partial<IRentalHistoryFilters>) => void;
  onClearFilters: () => void;
}

const EVENT_TYPE_OPTIONS = [
  {
    value: ERentalHistoryEventType.LISTED,
    label: "Listed",
    color:
      "bg-green-500/20 text-green-400 border-green-500/30 hover:bg-green-500/30",
  },
  {
    value: ERentalHistoryEventType.RENTED,
    label: "Delegated",
    color:
      "bg-blue-500/20 text-blue-400 border-blue-500/30 hover:bg-blue-500/30",
  },
  {
    value: ERentalHistoryEventType.CANCELLED,
    label: "Cancelled",
    color: "bg-red-500/20 text-red-400 border-red-500/30 hover:bg-red-500/30",
  },
  {
    value: ERentalHistoryEventType.INSURANCE_CLAIMED,
    label: "Insurance Claimed",
    color:
      "bg-purple-500/20 text-purple-400 border-purple-500/30 hover:bg-purple-500/30",
  },
  {
    value: ERentalHistoryEventType.UNLISTED,
    label: "Unlisted",
    color:
      "bg-gray-500/20 text-gray-400 border-gray-500/30 hover:bg-gray-500/30",
  },
];

export default function RentalHistoryFilters({
  filters,
  onFiltersChange,
  onClearFilters,
}: IRentalHistoryFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Local state for inputs
  const [localChickenTokenId, setLocalChickenTokenId] = useState(
    filters.chickenTokenId?.toString() || ""
  );
  const [localActorAddress, setLocalActorAddress] = useState(
    filters.actorAddress || ""
  );

  // Debounced values
  const { debouncedValue: debouncedChickenTokenId } = useDebounce(
    localChickenTokenId,
    500
  );
  const { debouncedValue: debouncedActorAddress } = useDebounce(
    localActorAddress,
    500
  );

  const hasActiveFilters = Object.keys(filters).some(
    (key) => filters[key as keyof IRentalHistoryFilters] !== undefined
  );

  const activeFilterCount = Object.values(filters).filter(
    (value) => value !== undefined && value !== null && value !== ""
  ).length;

  // Sync local state with props when filters change externally
  useEffect(() => {
    setLocalChickenTokenId(filters.chickenTokenId?.toString() || "");
  }, [filters.chickenTokenId]);

  useEffect(() => {
    setLocalActorAddress(filters.actorAddress || "");
  }, [filters.actorAddress]);

  // Apply debounced values to filters
  useEffect(() => {
    const tokenId = debouncedChickenTokenId
      ? parseInt(debouncedChickenTokenId)
      : undefined;
    if (tokenId !== filters.chickenTokenId) {
      onFiltersChange({ chickenTokenId: tokenId });
    }
  }, [debouncedChickenTokenId, filters.chickenTokenId, onFiltersChange]);

  useEffect(() => {
    const address = debouncedActorAddress || undefined;
    if (address !== filters.actorAddress) {
      onFiltersChange({ actorAddress: address });
    }
  }, [debouncedActorAddress, filters.actorAddress, onFiltersChange]);

  // Toggle event type filter
  const toggleEventType = (eventType: ERentalHistoryEventType) => {
    const currentTypes = filters.eventTypes || [];
    const newTypes = currentTypes.includes(eventType)
      ? currentTypes.filter((type) => type !== eventType)
      : [...currentTypes, eventType];

    onFiltersChange({
      eventTypes: newTypes.length > 0 ? newTypes : undefined,
    });
  };

  // Clear specific filter
  const clearFilter = (filterKey: keyof IRentalHistoryFilters) => {
    if (filterKey === "chickenTokenId") {
      setLocalChickenTokenId("");
    } else if (filterKey === "actorAddress") {
      setLocalActorAddress("");
    }
    onFiltersChange({ [filterKey]: undefined });
  };

  return (
    <div className="space-y-6">
      {/* Header with Clear All */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter size={18} className="text-gray-400" />
          <h3 className="text-lg font-medium text-white">Filters</h3>
          {activeFilterCount > 0 && (
            <Badge className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
              {activeFilterCount}
            </Badge>
          )}
        </div>

        {hasActiveFilters && (
          <Button
            intent="secondary"
            size="small"
            onPress={onClearFilters}
            className="flex items-center gap-1 text-red-400 hover:text-red-300"
          >
            <X size={14} />
            Clear All
          </Button>
        )}
      </div>

      {/* Event Type Filter Chips */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-300">
          Event Types
        </label>
        <div className="flex flex-wrap gap-2">
          {EVENT_TYPE_OPTIONS.map((option) => {
            const isSelected =
              filters.eventTypes?.includes(option.value) || false;
            return (
              <button
                key={option.value}
                onClick={() => toggleEventType(option.value)}
                className={cn(
                  "px-3 py-1.5 text-sm font-medium rounded-full border transition-all duration-200",
                  isSelected
                    ? option.color
                        .replace(/hover:bg-\w+-\d+\/\d+/, "")
                        .replace("bg-", "bg-") + " border-current"
                    : "bg-stone-800/50 text-gray-400 border-stone-600 hover:bg-stone-700/50 hover:text-gray-300"
                )}
              >
                {option.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Quick Search */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Chicken Token ID */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-300">
            Chicken Token ID
          </label>
          <div className="relative">
            <TextField
              type="number"
              placeholder="Enter token ID..."
              value={localChickenTokenId}
              onChange={setLocalChickenTokenId}
              prefix={<Search size={16} className="text-gray-400" />}
              className="bg-stone-800/60 border-stone-600"
            />
            {filters.chickenTokenId && (
              <button
                onClick={() => clearFilter("chickenTokenId")}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-red-400 transition-colors"
              >
                <X size={16} />
              </button>
            )}
          </div>
        </div>

        {/* Actor Address */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-300">
            Wallet Address
          </label>
          <div className="relative">
            <TextField
              placeholder="Enter wallet address..."
              value={localActorAddress}
              onChange={setLocalActorAddress}
              prefix={<Search size={16} className="text-gray-400" />}
              className="bg-stone-800/60 border-stone-600"
            />
            {filters.actorAddress && (
              <button
                onClick={() => clearFilter("actorAddress")}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-red-400 transition-colors"
              >
                <X size={16} />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Advanced Filters Toggle */}
      <div className="border-t border-stone-700 pt-4">
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center gap-2 text-sm text-gray-400 hover:text-gray-300 transition-colors"
        >
          <ChevronDown
            size={16}
            className={cn(
              "transition-transform duration-200",
              showAdvanced && "rotate-180"
            )}
          />
          Advanced Filters
        </button>
      </div>

      {/* Advanced Filters Panel */}
      {showAdvanced && (
        <div className="space-y-4 bg-stone-800/30 rounded-lg p-4 border border-stone-700/50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Date From */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                From Date
              </label>
              <div className="relative">
                <TextField
                  type="date"
                  value={filters.dateFrom || ""}
                  onChange={(value) => {
                    onFiltersChange({ dateFrom: value || undefined });
                  }}
                  prefix={<Calendar size={16} className="text-gray-400" />}
                  className="bg-stone-800/60 border-stone-600"
                />
                {filters.dateFrom && (
                  <button
                    onClick={() => clearFilter("dateFrom")}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-red-400 transition-colors"
                  >
                    <X size={16} />
                  </button>
                )}
              </div>
            </div>

            {/* Date To */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                To Date
              </label>
              <div className="relative">
                <TextField
                  type="date"
                  value={filters.dateTo || ""}
                  onChange={(value) => {
                    onFiltersChange({ dateTo: value || undefined });
                  }}
                  prefix={<Calendar size={16} className="text-gray-400" />}
                  className="bg-stone-800/60 border-stone-600"
                />
                {filters.dateTo && (
                  <button
                    onClick={() => clearFilter("dateTo")}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-red-400 transition-colors"
                  >
                    <X size={16} />
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="bg-stone-800/20 rounded-lg p-3 border border-stone-700/50">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-300">
              Active Filters
            </span>
            <span className="text-xs text-gray-400">
              {activeFilterCount} filter{activeFilterCount !== 1 ? "s" : ""}
            </span>
          </div>
          <div className="flex flex-wrap gap-2">
            {filters.eventTypes && filters.eventTypes.length > 0 && (
              <Badge className="bg-blue-600/20 text-blue-400 border border-blue-600/30">
                {filters.eventTypes.length} Event Type
                {filters.eventTypes.length !== 1 ? "s" : ""}
              </Badge>
            )}
            {filters.chickenTokenId && (
              <Badge className="bg-green-600/20 text-green-400 border border-green-600/30">
                Chicken #{filters.chickenTokenId}
              </Badge>
            )}
            {filters.actorAddress && (
              <Badge className="bg-purple-600/20 text-purple-400 border border-purple-600/30">
                Address: {filters.actorAddress.slice(0, 8)}...
              </Badge>
            )}
            {(filters.dateFrom || filters.dateTo) && (
              <Badge className="bg-orange-600/20 text-orange-400 border border-orange-600/30">
                Date Range
              </Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
