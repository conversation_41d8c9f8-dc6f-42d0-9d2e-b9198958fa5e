"use client";

import { useQuery } from "@tanstack/react-query";

const CHICKEN_API_BASE_URL = "https://chicken-api-ivory.vercel.app/api/game";

/**
 * Claim History API Types
 */
export interface IClaimHistoryResponse {
  success: boolean;
  data: {
    claimHistory: {
      _id: string;
      owner: string;
      claims: {
        tokenId: number;
        contractType: string;
        amount: number;
        balanceBefore: number;
        balanceAfter: number;
      }[];
      totalTokenTypes: number;
      claimedAt: string;
      status: "pending" | "completed" | "failed";
      claimMethod: "individual" | "batch";
      contractAddress: string;
      transactionHash: string;
    }[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
      nextOffset: number | null;
      returned: number;
    };
  };
}

/**
 * Fetch claim history from chicken-api-ivory API
 */
export const fetchClaimHistory = async (
  address: string,
  limit = 20,
  offset = 0
): Promise<IClaimHistoryResponse> => {
  const params = new URLSearchParams({
    address,
    limit: limit.toString(),
    offset: offset.toString(),
  });

  const response = await fetch(
    `${CHICKEN_API_BASE_URL}/claim-history?${params.toString()}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch claim history: ${response.statusText}`);
  }

  return response.json();
};

/**
 * Hook to fetch claim history with pagination
 */
export function useClaimHistory(
  address: string | null,
  limit = 20,
  offset = 0
) {
  const claimHistoryQuery = useQuery({
    queryKey: ["claimHistory", address, limit, offset],
    queryFn: () => fetchClaimHistory(address!, limit, offset),
    enabled: !!address,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
  });

  return {
    claimHistory: claimHistoryQuery.data?.data.claimHistory || [],
    pagination: claimHistoryQuery.data?.data.pagination,
    isLoading: claimHistoryQuery.isLoading,
    isError: claimHistoryQuery.isError,
    error: claimHistoryQuery.error,
    refetch: claimHistoryQuery.refetch,
  };
}
