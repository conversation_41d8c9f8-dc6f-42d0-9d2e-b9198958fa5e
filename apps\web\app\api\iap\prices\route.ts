import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get("jwt")?.value;

    if (!token) {
      return NextResponse.json(
        { status: 0, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const response = await fetch(`${process.env.IAP_API_URL}/api/prices`, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!response.ok) {
      const error = await response.json();
      console.log(error);

      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json({ data: data.data }, { status: 200 });
  } catch (error) {
    console.error("Error fetching iap fees:", error);
    return NextResponse.json(
      { status: 0, message: "Internal server error" },
      { status: 500 }
    );
  }
}
