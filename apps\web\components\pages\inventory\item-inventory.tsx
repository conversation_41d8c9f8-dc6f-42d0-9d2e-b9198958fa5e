"use client";

import { useItemInventory } from "@/hooks/useInventoryData";
import { useItemMetadata, ItemMetadata } from "@/hooks/useItemMetadata";
import { Button } from "@/components/ui";
import { ExternalLink, ShoppingCart, Search, ChevronDown, Cookie, Package } from "lucide-react";
import Image from "next/image";
import { useState, useMemo } from "react";

type ItemType = "All" | "Food" | "Material";
type SortBy = "name" | "type" | "amount";
type SortOrder = "asc" | "desc";

interface EnrichedItem {
  tokenId: number;
  balance: number;
  name: string;
  image: string;
  metadata?: ItemMetadata;
}

export default function ItemInventory() {
  const { items: inventoryItems, totalItems } = useItemInventory();
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<ItemType>("All");
  const [sortBy, setSortBy] = useState<SortBy>("name");
  const [sortOrder, setSortOrder] = useState<SortOrder>("asc");
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);
  const [showSortDropdown, setShowSortDropdown] = useState(false);

  // Get token IDs for metadata fetching
  const tokenIds = useMemo(() => inventoryItems.map(item => item.tokenId), [inventoryItems]);
  
  // Fetch metadata for all items
  const { metadata, loading: metadataLoading, error: metadataError } = useItemMetadata(tokenIds);

  // Combine inventory data with metadata
  const enrichedItems = useMemo((): EnrichedItem[] => {
    return inventoryItems.map((item) => {
      const itemMetadata = metadata.find(meta => meta?.tokenId === item.tokenId);
      
      return {
        tokenId: item.tokenId,
        balance: item.balance,
        name: itemMetadata?.name || item.name,
        image: itemMetadata?.image || item.image,
        metadata: itemMetadata,
      };
    });
  }, [inventoryItems, metadata]);

  // Filter and sort items
  const processedItems = useMemo(() => {
    let filtered = enrichedItems;

    // Apply type filter
    if (filterType !== "All") {
      filtered = filtered.filter(item => item.metadata?.type === filterType);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(query) ||
        item.metadata?.description?.toLowerCase().includes(query) ||
        item.tokenId.toString().includes(query)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "type":
          const typeA = a.metadata?.type || "Unknown";
          const typeB = b.metadata?.type || "Unknown";
          comparison = typeA.localeCompare(typeB);
          break;
        case "amount":
          comparison = a.balance - b.balance;
          break;
      }
      
      return sortOrder === "asc" ? comparison : -comparison;
    });

    return filtered;
  }, [enrichedItems, filterType, searchQuery, sortBy, sortOrder]);

  // Get type counts for filter labels
  const typeCounts = useMemo(() => {
    const counts = { All: enrichedItems.length, Food: 0, Material: 0 };
    enrichedItems.forEach(item => {
      if (item.metadata?.type === "Food") counts.Food++;
      else if (item.metadata?.type === "Material") counts.Material++;
    });
    return counts;
  }, [enrichedItems]);

  // Close dropdowns when clicking outside
  const handleClickOutside = () => {
    setShowFilterDropdown(false);
    setShowSortDropdown(false);
  };

  const openMarketplace = () => {
    window.open(
      "https://marketplace.roninchain.com/collections/sabong-saga-resources",
      "_blank"
    );
  };

  if (totalItems === 0) {
    return (
      <div className="text-center p-12">
        <div className="mb-6">
          <div className="mx-auto w-24 h-24 bg-stone-800 rounded-full flex items-center justify-center mb-4">
            <Image
              src="/images/cookie-1.jpg"
              alt="Items"
              width={48}
              height={48}
              className="opacity-50 rounded-full"
            />
          </div>
          <h3 className="text-xl font-medium text-white mb-2">
            No Items Found
          </h3>
          <p className="text-stone-400 mb-6">
            You don't have any items yet. Craft some items or purchase them from
            the marketplace!
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              appearance="outline"
              size="small"
              onPress={() => (window.location.href = "/crafting")}
              className="flex items-center gap-2"
            >
              <span>Go to Crafting</span>
              <ExternalLink className="w-4 h-4" />
            </Button>
            <Button
              appearance="outline"
              size="small"
              onPress={openMarketplace}
              className="flex items-center gap-2"
            >
              <ShoppingCart className="w-4 h-4" />
              <span>Buy Items</span>
              <ExternalLink className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6" onClick={handleClickOutside}>
      {/* Summary Stats */}
      <div className="bg-stone-900/50 rounded-lg p-4 border border-stone-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-white">Total Items</h3>
            <p className="text-stone-400">Combined item count</p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-primary">
              {totalItems.toLocaleString()}
            </p>
            <p className="text-sm text-stone-400">
              {inventoryItems.length} different types
            </p>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="space-y-4">
        {/* Filter and Sort Controls */}
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Type Filter */}
          <div className="relative">
            <button
              className="flex items-center gap-2 bg-stone-700 text-white px-4 py-2 rounded-lg hover:bg-stone-600 transition-colors min-w-[140px]"
              onClick={(e) => {
                e.stopPropagation();
                setShowFilterDropdown(!showFilterDropdown);
              }}
            >
              <Package className="h-4 w-4" />
              <span>Type: {filterType}</span>
              <span className="text-stone-400">({typeCounts[filterType]})</span>
              <ChevronDown className="h-4 w-4" />
            </button>

            {showFilterDropdown && (
              <div className="absolute top-full mt-1 w-48 bg-stone-800 border border-stone-600 rounded-lg shadow-lg overflow-hidden z-40">
                {(Object.keys(typeCounts) as ItemType[]).map((type) => (
                  <button
                    key={type}
                    className={`w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors flex items-center justify-between ${
                      filterType === type ? "bg-stone-700" : ""
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setFilterType(type);
                      setShowFilterDropdown(false);
                    }}
                  >
                    <span className="flex items-center gap-2">
                      {type === "Food" && <Cookie className="h-4 w-4" />}
                      {type === "Material" && <Package className="h-4 w-4" />}
                      {type === "All" && <Search className="h-4 w-4" />}
                      {type}
                    </span>
                    <span className="text-stone-400 text-sm">{typeCounts[type]}</span>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Sort Control */}
          <div className="relative">
            <button
              className="flex items-center gap-2 bg-stone-700 text-white px-4 py-2 rounded-lg hover:bg-stone-600 transition-colors min-w-[140px]"
              onClick={(e) => {
                e.stopPropagation();
                setShowSortDropdown(!showSortDropdown);
              }}
            >
              <span>Sort: {sortBy}</span>
              <span className="text-stone-400">{sortOrder === "asc" ? "↑" : "↓"}</span>
              <ChevronDown className="h-4 w-4" />
            </button>

            {showSortDropdown && (
              <div className="absolute top-full mt-1 w-40 bg-stone-800 border border-stone-600 rounded-lg shadow-lg overflow-hidden z-40">
                {(['name', 'type', 'amount'] as SortBy[]).map((sort) => (
                  <button
                    key={sort}
                    className={`w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors capitalize ${
                      sortBy === sort ? "bg-stone-700" : ""
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSortBy(sort);
                      setShowSortDropdown(false);
                    }}
                  >
                    {sort}
                  </button>
                ))}
                <div className="border-t border-stone-600 my-1"></div>
                <button
                  className="w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
                    setShowSortDropdown(false);
                  }}
                >
                  {sortOrder === "asc" ? "Descending ↓" : "Ascending ↑"}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
            <Search className="h-5 w-5" />
          </div>
          <input
            type="text"
            className="bg-stone-700 text-white pl-10 pr-4 py-2 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Search items..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* Loading State */}
      {metadataLoading && enrichedItems.length > 0 && (
        <div className="bg-blue-900/50 rounded-lg p-3 border border-blue-700">
          <div className="flex items-center gap-2 text-blue-300">
            <div className="h-4 w-4 rounded-full border-2 border-t-transparent border-blue-500 animate-spin"></div>
            <span className="text-sm">Loading item details...</span>
          </div>
        </div>
      )}

      {/* Error State */}
      {metadataError && (
        <div className="bg-red-900/50 rounded-lg p-3 border border-red-700">
          <p className="text-red-300 text-sm">Failed to load some item details. Using fallback data.</p>
        </div>
      )}

      {/* Items Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {processedItems.map((item) => {
          const typeColor = item.metadata?.type === "Food" ? "bg-green-500/20 text-green-400" : 
                           item.metadata?.type === "Material" ? "bg-blue-500/20 text-blue-400" : 
                           "bg-stone-500/20 text-stone-400";
          
          const TypeIcon = item.metadata?.type === "Food" ? Cookie : Package;
          
          return (
            <div
              key={item.tokenId}
              className="bg-stone-800 border border-stone-700 rounded-lg p-4 hover:border-primary/30 transition-all"
            >
              <div className="aspect-square relative mb-3 overflow-hidden rounded-lg bg-stone-700">
                <Image
                  src={item.image}
                  alt={item.name}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                  className="object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/images/cookie-1.jpg"; // Fallback image
                  }}
                />
                
                {/* Type Badge */}
                {item.metadata?.type && (
                  <div className={`absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-medium ${typeColor} flex items-center gap-1`}>
                    <TypeIcon className="h-3 w-3" />
                    {item.metadata.type}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="text-white font-medium text-sm truncate">
                    {item.name}
                  </h4>
                  <span className="bg-primary/20 text-primary text-xs px-2 py-1 rounded-full">
                    {item.balance}
                  </span>
                </div>

                {item.metadata?.description && (
                  <p className="text-stone-400 text-xs line-clamp-2">
                    {item.metadata.description}
                  </p>
                )}

                <div className="flex items-center justify-between text-xs text-stone-500">
                  <span>ID: {item.tokenId}</span>
                  <span>x{item.balance}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* No results message */}
      {processedItems.length === 0 && (searchQuery || filterType !== "All") && (
        <div className="text-center p-8">
          <p className="text-stone-400">
            {searchQuery 
              ? `No items found matching "${searchQuery}"` 
              : `No ${filterType.toLowerCase()} items found`
            }
          </p>
          {(searchQuery || filterType !== "All") && (
            <button
              onClick={() => {
                setSearchQuery("");
                setFilterType("All");
              }}
              className="mt-2 text-primary hover:text-primary/80 text-sm"
            >
              Clear filters
            </button>
          )}
        </div>
      )}

      {/* Quick Actions */}
      <div className="flex flex-col sm:flex-row gap-3">
        <Button
          appearance="outline"
          onPress={() => (window.location.href = "/crafting")}
          className="flex items-center gap-2"
        >
          <span>Go to Crafting</span>
          <ExternalLink className="w-4 h-4" />
        </Button>
        <Button
          appearance="outline"
          onPress={openMarketplace}
          className="flex items-center gap-2"
        >
          <ShoppingCart className="w-4 h-4" />
          <span>Buy More Items</span>
          <ExternalLink className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
