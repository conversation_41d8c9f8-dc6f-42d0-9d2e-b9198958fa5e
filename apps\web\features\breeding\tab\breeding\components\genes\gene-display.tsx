"use client";

import { IconChevronDown, IconChevronUp } from "justd-icons";
import { useState } from "react";
import { GeneCell } from "./gene-cell";
import { IDecodedGene } from "../../types/genes.types";

interface IGeneDisplayProps {
  genes: IDecodedGene;
}

export const GeneDisplay = ({ genes }: IGeneDisplayProps) => {
  const [expanded, setExpanded] = useState(false);

  // Stats display component
  const StatsDisplay = () => (
    <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
      <div className="bg-orange-900/40 p-3 rounded-lg border border-orange-800/50">
        {" "}
        {/* Beak -> Attack */}
        <div className="text-orange-300 text-[10px] mb-1 font-medium">
          Attack
        </div>
        <div className="text-white font-medium">{genes["Innate Attack"]}</div>
      </div>
      <div className="bg-red-900/40 p-3 rounded-lg border border-red-800/50">
        {" "}
        {/* Wings -> Defense */}
        <div className="text-red-300 text-[10px] mb-1 font-medium">Defense</div>
        <div className="text-white font-medium">{genes["Innate Defense"]}</div>
      </div>
      <div className="bg-blue-900/40 p-3 rounded-lg border border-blue-800/50">
        {" "}
        {/* Feet -> Speed */}
        <div className="text-blue-300 text-[10px] mb-1 font-medium">Speed</div>
        <div className="text-white font-medium">{genes["Innate Speed"]}</div>
      </div>
      <div className="bg-green-900/40 p-3 rounded-lg border border-green-800/50">
        {" "}
        {/* Body -> HP */}
        <div className="text-green-300 text-[10px] mb-1 font-medium">
          Health
        </div>
        <div className="text-white font-medium">{genes["Innate Health"]}</div>
      </div>
    </div>
  );

  // Compact view shows only dominant (p) genes
  const CompactView = () => (
    <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
      <div className="bg-[#1A1A2E] p-2 rounded-lg border border-blue-900/30">
        {" "}
        {/* Feet -> Speed */}
        <div className="text-blue-300 text-[10px] mb-1 font-medium">Feet</div>
        <GeneCell value={genes.Feet.p} type="Feet" />
      </div>
      <div className="bg-[#1A1A2E] p-2 rounded-lg border border-purple-900/30">
        {" "}
        {/* Tail -> Evasion */}
        <div className="text-purple-300 text-[10px] mb-1 font-medium">Tail</div>
        <GeneCell value={genes.Tail.p} type="Tail" />
      </div>
      <div className="bg-[#1A1A2E] p-2 rounded-lg border border-green-900/30">
        {" "}
        {/* Body -> HP */}
        <div className="text-green-300 text-[10px] mb-1 font-medium">Body</div>
        <GeneCell value={genes.Body.p} type="Body" />
      </div>
      <div className="bg-[#1A1A2E] p-2 rounded-lg border border-red-900/30">
        {" "}
        {/* Wings -> Defense */}
        <div className="text-red-300 text-[10px] mb-1 font-medium">Wings</div>
        <GeneCell value={genes.Wings.p} type="Wings" />
      </div>
      <div className="bg-[#1A1A2E] p-2 rounded-lg border border-cyan-900/30">
        {" "}
        {/* Eyes -> Ferocity */}
        <div className="text-cyan-300 text-[10px] mb-1 font-medium">Eyes</div>
        <GeneCell value={genes.Eyes.p} type="Eyes" />
      </div>
      <div className="bg-[#1A1A2E] p-2 rounded-lg border border-orange-900/30">
        {" "}
        {/* Beak -> Attack */}
        <div className="text-orange-300 text-[10px] mb-1 font-medium">Beak</div>
        <GeneCell value={genes.Beak.p} type="Beak" />
      </div>
      <div className="bg-[#1A1A2E] p-2 rounded-lg border border-pink-900/30">
        {" "}
        {/* Comb -> Cockrage */}
        <div className="text-pink-300 text-[10px] mb-1 font-medium">Comb</div>
        <GeneCell value={genes.Comb.p} type="Comb" />
      </div>
      <div className="bg-[#1A1A2E] p-2 rounded-lg border border-indigo-900/30">
        {" "}
        {/* Color -> No direct stat */}
        <div className="text-indigo-300 text-[10px] mb-1 font-medium">
          Color
        </div>
        <GeneCell value={genes.Color.p} type="Color" />
      </div>
    </div>
  );

  // Detailed view shows all genes (p, h1, h2, h3)
  const DetailedView = () => {
    // Define all the gene types we want to display
    const geneTypes = [
      { name: "Feet", data: genes.Feet },
      { name: "Tail", data: genes.Tail },
      { name: "Body", data: genes.Body },
      { name: "Wings", data: genes.Wings },
      { name: "Eyes", data: genes.Eyes },
      { name: "Beak", data: genes.Beak },
      { name: "Comb", data: genes.Comb },
      { name: "Color", data: genes.Color },
    ];

    return (
      <div className="space-y-3 overflow-x-auto">
        <div className="grid grid-cols-5 gap-1 text-center bg-[#0F172A] rounded-lg p-2 min-w-[300px] border border-primary/20">
          <div className="text-primary text-[10px] font-medium">Trait</div>
          <div className="text-primary text-[10px] font-medium">P</div>
          <div className="text-primary text-[10px] font-medium">H1</div>
          <div className="text-primary text-[10px] font-medium">H2</div>
          <div className="text-primary text-[10px] font-medium">H3</div>
        </div>

        {geneTypes.map((gene) => {
          // Get color based on gene type - colors match associated stats
          const getGeneColor = () => {
            switch (gene.name) {
              case "Feet": // Speed
                return "border-blue-900/30";
              case "Tail": // Evasion
                return "border-purple-900/30";
              case "Body": // HP
                return "border-green-900/30";
              case "Wings": // Defense
                return "border-red-900/30";
              case "Eyes": // Ferocity
                return "border-cyan-900/30";
              case "Beak": // Attack
                return "border-orange-900/30";
              case "Comb": // Cockrage
                return "border-pink-900/30";
              case "Color": // No direct stat association
                return "border-indigo-900/30";
              default:
                return "border-gray-700/30";
            }
          };

          // Get text color based on gene type - colors match associated stats
          const getGeneTextColor = () => {
            switch (gene.name) {
              case "Feet": // Speed
                return "text-blue-300";
              case "Tail": // Evasion
                return "text-purple-300";
              case "Body": // HP
                return "text-green-300";
              case "Wings": // Defense
                return "text-red-300";
              case "Eyes": // Ferocity
                return "text-cyan-300";
              case "Beak": // Attack
                return "text-orange-300";
              case "Comb": // Cockrage
                return "text-pink-300";
              case "Color": // No direct stat association
                return "text-indigo-300";
              default:
                return "text-gray-300";
            }
          };

          return (
            <div
              key={gene.name}
              className={`grid grid-cols-5 gap-1 text-center min-w-[300px] bg-[#1A1A2E] p-2 rounded-lg border ${getGeneColor()}`}
            >
              <div
                className={`text-[10px] flex items-center justify-center font-medium ${getGeneTextColor()}`}
              >
                {gene.name}
              </div>
              <GeneCell value={gene.data.p} type={`${gene.name} (P)`} />
              <GeneCell value={gene.data.h1} type={`${gene.name} (H1)`} />
              <GeneCell value={gene.data.h2} type={`${gene.name} (H2)`} />
              <GeneCell value={gene.data.h3} type={`${gene.name} (H3)`} />
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="text-xs">
      <div className="flex justify-between items-center bg-[#0F172A] p-2 rounded-lg border border-primary/20 mb-4">
        <div className="text-primary font-medium">Gene Details</div>
        <button
          onClick={() => setExpanded(!expanded)}
          className="text-primary/70 hover:text-primary transition-colors"
        >
          {expanded ? (
            <IconChevronUp className="w-4 h-4" />
          ) : (
            <IconChevronDown className="w-4 h-4" />
          )}
        </button>
      </div>

      <div className="mt-4">
        {!expanded ? <CompactView /> : <DetailedView />}
      </div>

      <div className="mt-4">
        <div className="text-primary font-medium mb-2">Instinct</div>
        <div className="bg-yellow-900/40 p-3 rounded-lg text-center text-yellow-300 font-medium border border-yellow-800/50">
          {genes.Instinct || "-"}
        </div>
      </div>

      {/* Always show stats */}
      <div className="mt-6 mb-4">
        <div className="text-primary font-medium mb-2">Stats</div>
        <StatsDisplay />
      </div>
    </div>
  );
};
