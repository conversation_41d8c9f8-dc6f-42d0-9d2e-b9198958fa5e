"use client";

import { useState, useMemo, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import { useChickenTokenIds } from "./useChickenTokenIds";
import { chickenBulkFetcherAddress } from "@/providers/web3/abi/chicken-bulk-fetcher";
import { DelegationAPI } from "@/features/delegation/api/delegation.api";
import {
  EChickenType,
  IChickenRentalStatus,
  IChickenBattleStats,
} from "../types/chicken-info.types";
import { IAttribute, IChickenMetadata } from "@/lib/types/chicken.types";
import { fetchChickens } from "@/features/breeding/tab/breeding/hooks/useChickens";

// Progressive loading phases
export interface IChickenBasic {
  tokenId: number;
  type: EChickenType;
  image: string;
  level: number;
  isAvailable: boolean;
  rentalStatus: IChickenRentalStatus;
}

export interface IChickenInteractive {
  battleStats: IChickenBattleStats;
  breedCount: number;
  cooldownInfo: {
    isOnCooldown: boolean;
    remainingTime: number;
  };
  dailyFeathers: number;
  winRate: number;
}

export interface IChickenDetailed {
  metadata: IChickenMetadata;
  genes: any;
  fullBattleHistory?: any[];
}

export interface IProgressiveChickenOptions {
  pageSize?: number;
  enableAutoLoad?: boolean;
  preloadNextPage?: boolean;
}

const DEFAULT_OPTIONS: IProgressiveChickenOptions = {
  pageSize: 50,
  enableAutoLoad: true,
  preloadNextPage: true,
};

// Batch size for API calls to prevent timeouts
const BATCH_SIZE = 100;

// Helper function to chunk arrays
function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

// Helper function to determine chicken type from metadata
const getChickenTypeFromMetadata = (metadata: any): EChickenType => {
  const typeAttribute = metadata?.attributes?.find(
    (attr: IAttribute) => attr.trait_type === "Type"
  );

  // Handle both string and array formats
  let typeValue: string;
  if (Array.isArray(typeAttribute?.value)) {
    typeValue = typeAttribute.value[0] as string;
  } else {
    typeValue = typeAttribute?.value as string;
  }

  switch (typeValue?.toLowerCase()) {
    case "ordinary":
      return EChickenType.ORDINARY;
    case "legacy":
      return EChickenType.LEGACY;
    case "genesis":
      return EChickenType.GENESIS;
    default:
      return EChickenType.ORDINARY;
  }
};

// Helper function to determine rental status
const getChickenRentalStatusFromData = (
  rental: any | null
): IChickenRentalStatus => {
  if (!rental) {
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  }

  if (rental.status === 0) {
    return {
      isAvailable: false,
      rentalStatus: "listed",
      statusLabel: "Listed in Rental Market",
    };
  } else if (rental.status === 1) {
    const roninPrice = rental.roninPrice || rental.ronin_price;
    const isDelegation = roninPrice === "0" || roninPrice === 0;

    if (isDelegation) {
      return {
        isAvailable: false,
        rentalStatus: "delegated",
        statusLabel: "Already Delegated",
      };
    } else {
      return {
        isAvailable: false,
        rentalStatus: "rented",
        statusLabel: "Already Rented",
      };
    }
  } else if (rental.status === 2 || rental.status === 3) {
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  }

  return {
    isAvailable: true,
    rentalStatus: "available",
    statusLabel: "Available",
  };
};

// Fetch basic chicken metadata (lightweight)
const fetchBasicChickenMetadata = async (
  tokenIds: number[]
): Promise<Record<number, IChickenBasic>> => {
  if (tokenIds.length === 0) return {};

  try {
    const CHICKEN_API = process.env.NEXT_PUBLIC_CHICKEN_API_URL || "";

    // Fetch basic metadata in chunks
    const chunks = chunkArray(tokenIds, BATCH_SIZE);
    const results = await Promise.allSettled(
      chunks.map(async (chunk) => {
        const response = await fetch(`${CHICKEN_API}/batch`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ ids: chunk }),
        });

        if (!response.ok) throw new Error(`API error: ${response.status}`);
        return response.json();
      })
    );

    // Fetch rental status for all tokens
    const rentalResponse = await DelegationAPI.getChickenRentalsBulk(tokenIds);
    const rentalData = rentalResponse.data || {};

    const basicChickens: Record<number, IChickenBasic> = {};

    results.forEach((result) => {
      if (result.status === "fulfilled") {
        const batchResponse = result.value;
        batchResponse.results?.forEach((item: any) => {
          if (item.status === 200 && item.data) {
            const metadata = item.data;
            const tokenId = item.id;

            // Extract basic info from metadata
            const levelAttribute = metadata.attributes?.find(
              (attr: IAttribute) => attr.trait_type === "Level"
            );

            const type = getChickenTypeFromMetadata(metadata);
            const rental = rentalData[tokenId];
            const rentalStatus = getChickenRentalStatusFromData(rental);

            basicChickens[tokenId] = {
              tokenId,
              type,
              image:
                metadata.image ||
                `https://chicken-api-ivory.vercel.app/api/image/${tokenId}.png`,
              level: Number(levelAttribute?.value) || 1,
              isAvailable: rentalStatus.isAvailable,
              rentalStatus,
            };
          }
        });
      }
    });

    return basicChickens;
  } catch (error) {
    console.error("Failed to fetch basic chicken metadata:", error);
    return {};
  }
};

export const useProgressiveChickenInfo = (
  address?: Address,
  options: Partial<IProgressiveChickenOptions> = {}
) => {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  const [currentPage, setCurrentPage] = useState(1);
  const [loadedPages, setLoadedPages] = useState<Set<number>>(new Set([1]));

  // Check if bulk fetcher is available
  const isBulkFetcherAvailable = !!chickenBulkFetcherAddress;

  // Fetch chicken token IDs
  const {
    tokenIdsByType,
    isLoading: tokenIdsLoading,
    error: tokenIdsError,
  } = useChickenTokenIds(isBulkFetcherAvailable ? address : undefined);

  // Fallback to GraphQL approach if bulk fetcher is not available
  const graphqlChickenQuery = useQuery({
    queryKey: ["chickens", address],
    queryFn: () => fetchChickens(address!),
    enabled: !isBulkFetcherAvailable && !!address,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  // Determine which token IDs to use
  const allTokenIds = useMemo(() => {
    if (isBulkFetcherAvailable) {
      return tokenIdsByType.all;
    } else if (graphqlChickenQuery.data) {
      return graphqlChickenQuery.data
        .map((chicken) => Number(chicken.tokenId))
        .sort((a, b) => a - b);
    }
    return [];
  }, [isBulkFetcherAvailable, tokenIdsByType.all, graphqlChickenQuery.data]);

  // Calculate pagination
  const totalPages = Math.ceil(allTokenIds.length / mergedOptions.pageSize!);
  const hasMore = currentPage < totalPages;

  // Get token IDs for loaded pages
  const loadedTokenIds = useMemo(() => {
    const ids: number[] = [];
    Array.from(loadedPages).forEach((page) => {
      const startIndex = (page - 1) * mergedOptions.pageSize!;
      const endIndex = startIndex + mergedOptions.pageSize!;
      ids.push(...allTokenIds.slice(startIndex, endIndex));
    });
    return ids;
  }, [loadedPages, allTokenIds, mergedOptions.pageSize]);

  // Phase 1: Load basic data for loaded pages
  const basicQuery = useQuery({
    queryKey: ["chickens-basic", address, loadedTokenIds],
    queryFn: () => fetchBasicChickenMetadata(loadedTokenIds),
    enabled: loadedTokenIds.length > 0,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  // Load more pages function
  const loadMore = useCallback(() => {
    if (hasMore) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      setLoadedPages((prev) => new Set([...prev, nextPage]));
    }
  }, [hasMore, currentPage]);

  return {
    // Basic data (always available)
    basicChickens: basicQuery.data || {},
    loadedTokenIds,
    isLoadingBasic: basicQuery.isLoading || tokenIdsLoading,
    errorBasic: basicQuery.error || tokenIdsError,

    // Pagination
    currentPage,
    totalPages,
    hasMore,
    loadMore,
    totalCount: allTokenIds.length,

    // Loading states
    isLoading: basicQuery.isLoading || tokenIdsLoading,
    error: basicQuery.error || tokenIdsError,
  };
};

// Hook for connected wallet
export const useConnectedWalletProgressiveChickenInfo = (
  options?: Partial<IProgressiveChickenOptions>
) => {
  const { address, isConnected } = useStateContext();
  const isClient = typeof window !== "undefined";

  return useProgressiveChickenInfo(
    isConnected && isClient ? address : undefined,
    options
  );
};
