"use client";

import React from "react";
import { SearchBar } from "../search-bar";
import { CraftingItem } from "../crafting-item";
import { Pagination } from "@/components/ui";

export function CraftingFood() {
  const foodItems = [
    {
      name: "Random Cookie",
      requirements: {
        chicken: 100,
        feather: 10,
      },
    },
    {
      name: "Golden Egg",
      requirements: {
        chicken: 200,
        feather: 30,
      },
    },
    {
      name: "Power Sandwich",
      requirements: {
        chicken: 150,
        feather: 20,
      },
    },
    {
      name: "Energy Bar",
      requirements: {
        chicken: 80,
        feather: 15,
      },
    },
    {
      name: "Magic Soup",
      requirements: {
        chicken: 120,
        feather: 25,
      },
    },
    {
      name: "Lucky Meal",
      requirements: {
        chicken: 175,
        feather: 35,
      },
    },
  ];

  return (
    <div className="mt-4">
      <SearchBar />
      <div className="grid grid-cols-4 gap-4 mt-6">
        {foodItems.map((item, index) => (
          <CraftingItem key={index} {...item} />
        ))}
      </div>
      <div className="mt-6">
        <Pagination>
          <Pagination.List>
            <Pagination.Item variant="first" href="#" />
            <Pagination.Item variant="previous" href="#" />
            <Pagination.Item href="#" isCurrent>
              1
            </Pagination.Item>
            <Pagination.Item href="#">2</Pagination.Item>
            <Pagination.Item variant="ellipsis" />
            <Pagination.Item variant="next" href="#" />
            <Pagination.Item variant="last" href="#" />
          </Pagination.List>
        </Pagination>
      </div>
    </div>
  );
}
