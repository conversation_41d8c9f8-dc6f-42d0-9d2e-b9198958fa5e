// utils/processAllocations.mjs
import { parse } from "csv-parse";
import { createObjectCsvWriter } from "csv-writer";
import * as fs from "fs/promises";
import path from "path";
import { fileURLToPath } from "url";
import { createPublicClient, http } from "viem";
import { ronin } from "viem/chains";
import dotenv from "dotenv";
import { dirname } from "path";

// Initialize dotenv
dotenv.config();

// ES Module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const ALLOCATION_MAP = {
  0: 2,
  1: 5,
  2: 12,
  3: 30,
};

// Properly formatted ABI
const CHICKEN_ABI = [
  {
    inputs: [{ name: "owner", type: "address" }],
    name: "balanceOf",
    outputs: [{ name: "", type: "uint256" }],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      { name: "owner", type: "address" },
      { name: "index", type: "uint256" },
    ],
    name: "tokenOfOwnerByIndex",
    outputs: [{ name: "", type: "uint256" }],
    stateMutability: "view",
    type: "function",
  },
];

const publicClient = createPublicClient({
  chain: ronin,
  transport: http(process.env.RONIN_RPC),
});

async function fetchTokenMetadata(tokenId) {
  try {
    const response = await fetch(
      `https://chicken-api-ivory.vercel.app/api/${tokenId}`
    );
    if (!response.ok)
      throw new Error(`Failed to fetch metadata for token ${tokenId}`);
    return await response.json();
  } catch (error) {
    console.error(`Failed to fetch metadata for token ${tokenId}:`, error);
    return null;
  }
}

async function processAddress(address) {
  try {
    console.log(`Processing address: ${address}`);

    const balance = await publicClient.readContract({
      address: process.env.CHICKEN_CONTRACT,
      abi: CHICKEN_ABI,
      functionName: "balanceOf",
      args: [address],
    });

    const balanceNumber = Number(balance);

    if (balanceNumber === 0) {
      return {
        address,
        totalChickens: 0,
        totalAllocations: 0,
        breakdown: [],
        // chickensDetail: [],
        timestamp: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
      };
    }

    const chickens = [];
    const countMap = {};

    for (let i = 0; i < balanceNumber; i++) {
      const tokenId = await publicClient.readContract({
        address: process.env.CHICKEN_CONTRACT,
        abi: CHICKEN_ABI,
        functionName: "tokenOfOwnerByIndex",
        args: [address, BigInt(i)],
      });

      const metadata = await fetchTokenMetadata(tokenId.toString());
      if (!metadata) continue;

      const legendaryCount = Number(
        metadata.attributes.find(
          (attr) => attr.trait_type === "Legendary Count"
        )?.value || 0
      );
      const allocation = ALLOCATION_MAP[legendaryCount] || 2;

      countMap[legendaryCount] = (countMap[legendaryCount] || 0) + 1;
      chickens.push({
        tokenId: tokenId.toString(),
        legendaryCount,
        allocation,
      });
    }

    const breakdown = Object.entries(countMap)
      .sort((a, b) => Number(a[0]) - Number(b[0]))
      .map(([legendaryCount, quantity]) => ({
        legendaryCount: Number(legendaryCount),
        quantity,
        totalAllocation:
          quantity * (ALLOCATION_MAP[Number(legendaryCount)] || 2),
      }));

    const totalAllocations = breakdown.reduce(
      (sum, item) => sum + item.totalAllocation,
      0
    );

    return {
      address,
      totalChickens: chickens.length,
      totalAllocations,
      breakdown,
      // chickensDetail: chickens,
      timestamp: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
    };
  } catch (error) {
    console.error(`Error processing address ${address}:`, error);
    return null;
  }
}

async function processAddressesFromCSV(inputPath, outputPath) {
  try {
    const fileContent = await fs.readFile(inputPath, "utf-8");
    const records = await new Promise((resolve, reject) => {
      parse(
        fileContent,
        {
          columns: true,
          skip_empty_lines: true,
        },
        (err, records) => {
          if (err) reject(err);
          else resolve(records);
        }
      );
    });

    const csvWriter = createObjectCsvWriter({
      path: outputPath,
      header: [
        { id: "address", title: "Address" },
        { id: "totalChickens", title: "Total Chickens" },
        { id: "totalAllocations", title: "Total Allocations" },
        { id: "breakdown", title: "Breakdown" },
        // { id: "chickensDetail", title: "Chickens Detail" },
        { id: "timestamp", title: "Timestamp" },
        { id: "lastUpdated", title: "Last Updated" },
      ],
    });

    const results = [];

    for (const record of records) {
      const result = await processAddress(record.address);
      if (result) {
        const csvRecord = {
          ...result,
          breakdown: JSON.stringify(result.breakdown),
          // chickensDetail: JSON.stringify(result.chickensDetail),
        };
        await csvWriter.writeRecords([csvRecord]);
        results.push(result);
      }
    }

    console.log(`Processing complete. Results saved to ${outputPath}`);
    console.log(`Processed ${results.length} addresses successfully`);
  } catch (error) {
    console.error("Error processing CSV:", error);
    throw error;
  }
}

// Run the script
const inputPath = path.join(process.cwd(), "data", "addresses.csv");
const outputPath = path.join(process.cwd(), "data", "allocations.csv");

processAddressesFromCSV(inputPath, outputPath)
  .then(() => console.log("Processing completed successfully"))
  .catch((error) => {
    console.error("Error:", error);
    process.exit(1);
  });
