import { formatEther } from "viem";

// Types
export interface CornAllocation {
  [cornId: number]: number;
}

export interface RecipeMaterial {
  tokenType: number;
  tokenAddress: string;
  tokenId: bigint;
  amount: bigint;
}

export interface CornMaterial {
  type: bigint;
  address: string;
  amount: bigint;
  tokenId: bigint;
  tokenType?: number;
  tokenAddress?: string;
}

export interface ItemData {
  name: string;
  description: string;
  effect: string[];
  id: number;
  craftable: boolean;
  image: string;
  inventory: number;
  item: boolean;
  disliked: string[];
  type: "food" | "battle" | "breeding" | "adventure";
  rarity?: string;
  isRandom?: boolean;
}

export interface CraftingPool {
  materials: Array<{
    tokenType: number;
    tokenAddress: string;
    tokenId: bigint;
    amount: bigint;
  }>;
  acceptedResourceIds: bigint[];
  exists: boolean;
  active: boolean;
  name: string;
  totalWeight: bigint;
}

// Helper functions for recipe management
export const getRecipeForItem = (
  itemId: number,
  craftableItems: Array<{ tokenId: number; materials: RecipeMaterial[]; exists: boolean }>
) => {
  return craftableItems.find(
    (recipe) => recipe.tokenId === itemId && recipe.exists
  );
};

export const getCornRecipeForItem = (
  itemId: number,
  craftableCookies: Array<{
    recipes: CornMaterial[];
    tokenId: bigint;
    acceptedResourceIds: bigint[];
  }>
) => {
  return (
    craftableCookies.find((cookie) => Number(cookie.tokenId) === itemId) ||
    null
  );
};

// Helper functions for type 2 (corn allocation) recipes
export const isType2CornRecipe = (cornRecipe: any): boolean => {
  if (!cornRecipe || !cornRecipe.recipes) return false;
  return cornRecipe.recipes.some((recipe: any) => recipe.tokenType === 2);
};

export const getType2Requirements = (cornRecipe: any): number | null => {
  if (!cornRecipe || !cornRecipe.recipes) return null;
  const type2Recipe = cornRecipe.recipes.find(
    (recipe: any) => recipe.tokenType === 2
  );
  return type2Recipe ? Number(type2Recipe.amount) : null;
};

export const getAcceptedResourceIds = (cornRecipe: any): number[] => {
  if (!cornRecipe || !cornRecipe.acceptedResourceIds) return [];
  return cornRecipe.acceptedResourceIds.map((id: bigint) => Number(id));
};

// Helper functions for random crafting pools
export const isType2RandomPool = (
  poolId: number = 0,
  craftingPools: Record<number, CraftingPool>
): boolean => {
  const pool = craftingPools[poolId];
  if (!pool || !pool.materials) return false;
  return pool.materials.some((material: any) => material.tokenType === 2);
};

export const getType2RequirementsForRandomPool = (
  poolId: number = 0,
  craftingPools: Record<number, CraftingPool>
): number | null => {
  const pool = craftingPools[poolId];
  if (!pool || !pool.materials) return null;
  const type2Material = pool.materials.find(
    (material: any) => material.tokenType === 2
  );
  return type2Material ? Number(type2Material.amount) : null;
};

export const getAcceptedResourceIdsForRandomPool = (
  poolId: number = 0,
  craftingPools: Record<number, CraftingPool>
): number[] => {
  const pool = craftingPools[poolId];
  if (!pool || !pool.acceptedResourceIds) return [];
  return pool.acceptedResourceIds.map((id: bigint) => Number(id));
};

// Corn allocation utilities
export const initializeCornAllocation = (
  acceptedIds: number[]
): CornAllocation => {
  const allocation: CornAllocation = {};
  acceptedIds.forEach((id) => (allocation[id] = 0));
  return allocation;
};

export const getTotalAllocated = (allocation: CornAllocation): number => {
  return Object.values(allocation).reduce((sum, amount) => sum + amount, 0);
};

export const isCornAllocationValid = (
  allocation: CornAllocation,
  acceptedIds: number[],
  foodBalances: Record<number, bigint>
): boolean => {
  return Object.entries(allocation).every(([idStr, allocated]) => {
    const id = Number(idStr);
    // Check if the resource ID is accepted
    if (!acceptedIds.includes(id)) return allocated === 0;

    const available = Number(foodBalances[id] || 0n);
    return allocated <= available;
  });
};

// Balance checking utilities
export const getCurrentBalance = (
  material: RecipeMaterial | CornMaterial,
  cock: bigint,
  feathers: number,
  legendaryFeathers: number,
  foodBalances: Record<number, bigint>
): bigint => {
  const tokenAddress =
    "tokenAddress" in material ? material.tokenAddress : material.address;
  const tokenType =
    "tokenType" in material ? material.tokenType : Number(material.type);

  if (tokenType === 0) {
    if (
      tokenAddress?.toLowerCase() ===
      process.env.NEXT_PUBLIC_COCK_CONTRACT?.toLowerCase()
    ) {
      return cock;
    }
    return 0n;
  } else {
    if (
      tokenAddress?.toLowerCase() ===
      process.env.NEXT_PUBLIC_FEATHERS_CONTRACT?.toLowerCase()
    ) {
      if (material.tokenId === 1n) {
        return BigInt(feathers);
      }
      if (material.tokenId === 2n) {
        return BigInt(legendaryFeathers);
      }
    }
    const tokenId = Number(material.tokenId);
    return foodBalances[tokenId] || 0n;
  }
};

export const isSufficientMaterial = (
  material: RecipeMaterial | CornMaterial,
  quantity: number,
  cock: bigint,
  feathers: number,
  legendaryFeathers: number,
  foodBalances: Record<number, bigint>
): boolean => {
  const currentBalance = getCurrentBalance(
    material,
    cock,
    feathers,
    legendaryFeathers,
    foodBalances
  );
  const requiredAmount = material.amount * BigInt(quantity);
  return currentBalance >= requiredAmount;
};

export const getMaterialDisplayText = (
  material: RecipeMaterial | CornMaterial,
  quantity: number,
  cock: bigint,
  feathers: number,
  legendaryFeathers: number,
  foodBalances: Record<number, bigint>
): string => {
  const currentBalance = getCurrentBalance(
    material,
    cock,
    feathers,
    legendaryFeathers,
    foodBalances
  );
  const requiredAmount = material.amount * BigInt(quantity || 1);
  const tokenType =
    "tokenType" in material ? material.tokenType : Number(material.type);

  if (tokenType === 0) {
    const currentFormatted = Number(formatEther(currentBalance)).toLocaleString();
    const requiredFormatted = Number(formatEther(requiredAmount)).toLocaleString();
    return `${currentFormatted} / ${requiredFormatted}`;
  } else {
    return `${currentBalance.toString()} / ${requiredAmount.toString()}`;
  }
};

// Pricing utilities
export const getRandomCraftingPoolPrice = (
  craftingPools: Record<number, CraftingPool>,
  poolId: number = 0
) => {
  const pool = craftingPools[poolId];
  if (pool && pool.materials) {
    let cockPrice = 0;
    let featherPrice = 0;

    pool.materials.forEach((material) => {
      if (
        material.tokenType === 0 &&
        material.tokenAddress.toLowerCase() ===
          process.env.NEXT_PUBLIC_COCK_CONTRACT?.toLowerCase()
      ) {
        cockPrice = Number(formatEther(material.amount));
      } else if (
        material.tokenType === 1 &&
        material.tokenAddress.toLowerCase() ===
          process.env.NEXT_PUBLIC_FEATHERS_CONTRACT?.toLowerCase() &&
        material.tokenId === 1n
      ) {
        featherPrice = Number(material.amount);
      }
    });

    return { cockPrice, featherPrice };
  }
  return { cockPrice: 0, featherPrice: 0 };
};

// Support for both crafting methods
export const supportsBothCraftingMethods = (
  itemId: number,
  craftableItems: Array<{ tokenId: number; materials: RecipeMaterial[]; exists: boolean }>,
  craftableCookies: Array<{
    recipes: CornMaterial[];
    tokenId: bigint;
    acceptedResourceIds: bigint[];
  }>
): boolean => {
  const hasRecipe = !!getRecipeForItem(itemId, craftableItems);
  const hasCornRecipe = !!getCornRecipeForItem(itemId, craftableCookies);
  return hasRecipe && hasCornRecipe;
};