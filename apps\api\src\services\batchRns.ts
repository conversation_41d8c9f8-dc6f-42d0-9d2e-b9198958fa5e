// @ts-ignore
import { RNS } from "@roninnetwork/rnsjs";
import { providers } from "ethers";
import { env } from "../env";
import { Address } from "viem";

type RNSNameResponse = {
  name: string;
  address: Address;
};

export const getBatchRns = async (
  address: Address[]
): Promise<RNSNameResponse[]> => {
  try {
    const provider = new providers.JsonRpcProvider(env.RONIN_RPC || env.RPC);
    const RNSInstance = new RNS();
    await RNSInstance.setProvider(provider, 2020);

    const batchCalls = address.map((addr) => RNSInstance.getName.batch(addr));
    const batched = await RNSInstance.batch(...batchCalls);

    const combinedData = batched.map((item: { name: string }, idx: number) => ({
      name: item.name,
      address: address[idx],
    }));

    return combinedData;
  } catch (error) {
    return [];
  }
};
