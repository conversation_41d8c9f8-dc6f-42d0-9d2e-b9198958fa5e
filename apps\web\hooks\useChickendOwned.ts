import { MavisMarketplaceApi } from "@/services/ownedChicken";
import { useQuery, UseQueryResult } from "@tanstack/react-query";
import { isStaging } from "@/lib/constants";
import { IChickenGQL } from "@/features/breeding/tab/breeding/types/chicken-gql.types";
import { mockChickenQueryData } from "@/features/rewards/ninuno/mock/chicken-query-data";

// Define the interface for the API response
export interface IChickenInventoryResponse {
  tokens: IChickenGQL[];
}

export function useChickendOwned(address: string) {
  let chickenQuery = useQuery<IChickenInventoryResponse>({
    queryKey: ["chickensOwned", address], // Include address in the query key
    queryFn: () => MavisMarketplaceApi.getChickensByAddress({ address }),
    enabled: !!address, // Only run the query if address exists
  });

  // Use mock data in Saigon network for specific test address
  if (isStaging) {
    // Transform mock data to match the expected format for inventory
    const mockInventoryData: IChickenInventoryResponse = {
      tokens:
        address === "0xED3f049B1a396495D3b0Bea9A13622CE6E8C2FD7".toLowerCase()
          ? mockChickenQueryData.data || []
          : [],
    };

    // Create a properly typed mock query result
    chickenQuery = {
      // Status
      status: "success",
      isSuccess: true,
      isPending: false,
      isError: false,
      isLoading: false,
      isLoadingError: false,
      isRefetchError: false,

      // Data
      data: mockInventoryData,

      // Error related
      error: null,
      errorUpdatedAt: 0,

      // Fetch status
      fetchStatus: "idle",
      isFetching: false,
      isPaused: false,
      isRefetching: false,
      isInitialLoading: false,

      // Additional properties
      isPlaceholderData: false,
      isStale: false,
      isFetched: true,
      isFetchedAfterMount: true,
      dataUpdatedAt: Date.now(),
      errorUpdateCount: 0,
      failureCount: 0,
      failureReason: null,

      // Functions
      refetch: () =>
        Promise.resolve({} as UseQueryResult<IChickenInventoryResponse, Error>),
      promise: Promise.resolve(mockInventoryData),
    } as UseQueryResult<IChickenInventoryResponse, Error>;
  }

  return chickenQuery;
}
