"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui";

interface ITokenIdFormProps {
  inputTokenId: string;
  setInputTokenId: (value: string) => void;
  handleSubmit: (e: React.FormEvent) => void;
  isLoading?: boolean;
}

export default function TokenIdForm({
  inputTokenId,
  setInputTokenId,
  handleSubmit,
  isLoading = false,
}: ITokenIdFormProps): React.ReactNode {
  return (
    <form
      onSubmit={handleSubmit}
      className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6"
    >
      <div className="flex flex-col md:flex-row md:items-end gap-4">
        <div className="flex-1">
          <label
            htmlFor="tokenId"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Token ID
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              </svg>
            </div>
            <div className="flex items-center gap-3">
              <Input
                id="tokenId"
                type="text"
                placeholder="Enter token ID (e.g., 123)"
                value={inputTokenId}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setInputTokenId(e.target.value)
                }
                className="flex-1 pl-10 border border-gray-300 text-gray-700 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all"
                disabled={isLoading}
              />
              <Button
                type="submit"
                intent="primary"
                size="medium"
                className="px-6 py-2 h-10 transition-all"
                isDisabled={!inputTokenId.trim() || isLoading}
              >
                <div className="flex items-center space-x-2">
                  {isLoading ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      <span>Loading...</span>
                    </>
                  ) : (
                    <>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span>Look Up Owner</span>
                    </>
                  )}
                </div>
              </Button>
            </div>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Enter a token ID to find its owner and contract information
          </p>
        </div>
      </div>
    </form>
  );
}
