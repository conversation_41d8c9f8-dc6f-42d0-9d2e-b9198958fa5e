"use client";

import { extend, hookstate, useHookstate } from "@hookstate/core";
import { devtools } from "@hookstate/devtools";
import merge from "lodash.merge";
import { Address } from "viem";
import { localstored } from "./plugins/localStored";
import { IVerify } from "./types/persist.types";

const initialState = {
  recentConnectorId: "" as string,
  verify: null as IVerify | null,
  isConnected: false,
  address: "" as Address,
  token: "",
  refreshToken: "",
  breeding: {
    loading: false,
    message: `Breeding’s not gae if you say 'no homo' first, OK? 😉`,
  },
};

export const globalStatePersist = hookstate(
  initialState,
  extend(
    localstored({
      key: "globalStatePersist",
      onRestored: (s) => {
        const restored = s.get({ noproxy: true });

        if (s.value) {
          const synced = merge({}, initialState, restored);

          console.log("restored state: ", synced);
          s.set(synced);
        } else {
          console.log("restored state: localstorage is empty");
        }
      },
    }),
    devtools({ key: "globalStatePersist" })
  )
);

export const useGlobalStatePersist = () => useHookstate(globalStatePersist);
