"use client";

import { useQuery } from "@tanstack/react-query";

const CHICKEN_API_BASE_URL = "https://chicken-api-ivory.vercel.app/api/game";

/**
 * Transfer History API Types
 */
export interface ITransferHistoryResponse {
  success: boolean;
  data: {
    transferHistory: {
      _id: string;
      owner: string;
      rewardIds: string[];
      chickenIds: number[];
      tokenId: number;
      quantity: number;
      balanceBefore: number;
      balanceAfter: number;
      transferredAt: string;
      status: "pending" | "completed" | "failed";
    }[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
      nextOffset: number | null;
      returned: number;
    };
  };
}

/**
 * Fetch transfer history from chicken-api-ivory API
 */
export const fetchTransferHistory = async (
  address: string,
  limit = 20,
  offset = 0
): Promise<ITransferHistoryResponse> => {
  const params = new URLSearchParams({
    address,
    limit: limit.toString(),
    offset: offset.toString(),
  });

  const response = await fetch(
    `${CHICKEN_API_BASE_URL}/transfer-history?${params.toString()}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch transfer history: ${response.statusText}`);
  }

  return response.json();
};

/**
 * Hook to fetch transfer history with pagination
 */
export function useTransferHistory(
  address: string | null,
  limit = 20,
  offset = 0
) {
  const transferHistoryQuery = useQuery({
    queryKey: ["transferHistory", address, limit, offset],
    queryFn: () => fetchTransferHistory(address!, limit, offset),
    enabled: !!address,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
  });

  return {
    transferHistory: transferHistoryQuery.data?.data.transferHistory || [],
    pagination: transferHistoryQuery.data?.data.pagination,
    isLoading: transferHistoryQuery.isLoading,
    isError: transferHistoryQuery.isError,
    error: transferHistoryQuery.error,
    refetch: transferHistoryQuery.refetch,
  };
}
