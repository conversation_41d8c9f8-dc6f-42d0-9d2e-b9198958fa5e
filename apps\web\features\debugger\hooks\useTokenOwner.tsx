"use client";

import useBlockchain from "@/lib/hooks/useBlockchain";
import { useContractRead } from "@/lib/hooks/useContractRead";
import { useMemo } from "react";
import { Address } from "viem";

export default function useTokenOwner(tokenId: string | null) {
  const { blockchainQuery } = useBlockchain();
  const tokenIdNumber = tokenId ? Number(tokenId) : null;

  // Determine which contract to use based on token ID threshold
  const contractInfo = useMemo(() => {
    if (!tokenIdNumber || !blockchainQuery.data) return null;

    const isLegacy =
      tokenIdNumber > blockchainQuery.data.chicken_genesis_threshold;

    return {
      address: isLegacy
        ? blockchainQuery.data.chicken_legacy_address
        : blockchainQuery.data.chicken_genesis_address,
      abi: isLegacy
        ? blockchainQuery.data.chicken_legacy_abi
        : blockchainQuery.data.chicken_genesis_abi,
      type: isLegacy ? "Legacy" : "Genesis",
    };
  }, [tokenIdNumber, blockchainQuery.data]);

  // Query the contract for the owner
  const ownerQuery = useContractRead<Address>({
    address: contractInfo?.address,
    abi: contractInfo?.abi,
    functionName: "ownerOf",
    args: tokenIdNumber ? [BigInt(tokenIdNumber)] : undefined,
    enabled: !!tokenIdNumber && !!contractInfo,
  });

  return {
    owner: ownerQuery.data?.toLowerCase(),
    contractType: contractInfo?.type,
    isLoading: ownerQuery.isLoading || blockchainQuery.isLoading,
    isError: ownerQuery.isError,
    error: ownerQuery.error,
  };
}
