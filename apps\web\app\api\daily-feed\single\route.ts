import { NextRequest, NextResponse } from "next/server";
import { privateKeyToAccount } from "viem/accounts";
import { AbiCoder, keccak256, hashMessage } from "ethers";
import { createPublicClient, http, type Address } from "viem";
import { cookies } from "next/headers";
import { CollectionType, StatType } from "@/types/daily-feed.type";
import { ronin } from "viem/chains";
import <PERSON><PERSON><PERSON> from "@/abi/Chicken.abi.json";

const publicClient = createPublicClient({
  chain: ronin,
  transport: http(),
});

interface FeedSignatureRequest {
  feederAddress: string;
  collectionType: CollectionType;
  nftTokenId: number;
  feedItemId: number;
  amount: number;
  debuffStatChoice: StatType;
  nonce: number;
}

interface FeedSignatureResponse {
  signature: string;
  deadline: number;
  feeder: Address;
  affectionPoints: number;
  messageHash: string;
  ethSignedMessageHash: string;
  // Debug info
  encodedData?: string;
  signerAddress?: string;
}

// Configuration - replace with your actual values
const SIGNER_PRIVATE_KEY = process.env
  .DAILY_FEED_SIGNER_WALLET_PK as `0x${string}`;
const SIGNATURE_VALIDITY_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
const API_BASE_URL = process.env.HONO_API_ENDPOINT;
const GENESIS = process.env.GENESIS_CONTRACT as Address;
const LEGACY = process.env.LEGACY_CONTRACT as Address;
const BREEDING_API = process.env.BREEDING_API_URL;

async function fetchApi(endpoint: string, options: RequestInit = {}) {
  return fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      "content-type": "application/json",
      ...options.headers,
    },
    cache: "no-store",
  });
}

// Return true if owner of tokenId
async function ownerOf(tokenId: number) {
  return (await publicClient.readContract({
    address: tokenId <= 2222 ? GENESIS : LEGACY,
    abi: ChickenAbi,
    functionName: "ownerOf",
    args: [BigInt(tokenId)],
  })) as unknown as Address;
}

async function getRentalChicken(tokenId: number) {
  const response = await fetch(`${BREEDING_API}rentals/chicken/${tokenId}`);

  if (!response.ok) {
    throw Error("Cannot fetch rental chicken by id");
  }

  return response.json();
}

// Return the user details
async function getMe(jwt: string) {
  return fetchApi("/api/me", {
    method: "GET",
    headers: { authorization: `Bearer ${jwt}` },
  });
}

/**
 * Calculate affection points based on feed parameters
 * This is a sample calculation - replace with your actual logic
 */
function calculateAffectionPoints(
  collectionType: CollectionType,
  nftTokenId: number,
  feedItemId: number,
  amount: number
): number {
  return 0;
}

/**
 * Create message hash matching the contract's _getFeedMessageHash function
 * From updated contract: uint8(params.collectionType), params.nftTokenId,
 * params.feedItemId, params.affectionPoints, params.amount, uint8(params.debuffStatChoice), nonce, deadline
 */
function createFeedMessageHash(
  collectionType: CollectionType,
  nftTokenId: number,
  feedItemId: number,
  affectionPoints: number,
  amount: number,
  debuffStatChoice: StatType,
  nonce: number,
  deadline: number
): { messageHash: Address; encodedData: string } {
  const abiCoder = new AbiCoder();

  // Updated order from new contract's _getFeedMessageHash:
  // uint8(params.collectionType), params.nftTokenId, params.feedItemId,
  // params.affectionPoints, params.amount, uint8(params.debuffStatChoice), nonce, deadline
  const encoded = abiCoder.encode(
    [
      "uint8", // uint8(params.collectionType)
      "uint256", // params.nftTokenId
      "uint256", // params.feedItemId
      "uint256", // params.affectionPoints
      "uint256", // params.amount
      "uint8", // uint8(params.debuffStatChoice)
      "uint256", // nonce
      "uint256", // deadline
    ],
    [
      collectionType,
      nftTokenId,
      feedItemId,
      affectionPoints,
      amount,
      debuffStatChoice,
      nonce,
      deadline,
    ]
  );

  const messageHash = keccak256(encoded) as Address;

  return {
    messageHash,
    encodedData: encoded,
  };
}

export async function POST(request: NextRequest) {
  try {
    // Authentication check
    const c = await cookies();
    const jwt = c.get("jwt")?.value;
    if (!jwt) {
      return NextResponse.json(
        { status: false, responseCode: 401, message: "Bad request" },
        { status: 401 }
      );
    }

    const resMe = await getMe(jwt);
    if (!resMe.ok) {
      return NextResponse.json(
        { status: false, responseCode: 401, message: "Bad request" },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body: FeedSignatureRequest = await request.json();

    // Streamlined validation
    if (
      !Number.isInteger(body.collectionType) ||
      !Number.isInteger(body.nftTokenId) ||
      !Number.isInteger(body.feedItemId) ||
      !Number.isInteger(body.amount) ||
      !Number.isInteger(body.debuffStatChoice) ||
      !Number.isInteger(body.nonce)
    ) {
      return NextResponse.json(
        { error: "Invalid request parameters" },
        { status: 400 }
      );
    }

    // Validate enum values
    if (
      !Object.values(CollectionType).includes(body.collectionType) ||
      !Object.values(StatType).includes(body.debuffStatChoice)
    ) {
      return NextResponse.json(
        { error: "Invalid enum values" },
        { status: 400 }
      );
    }

    // Get user data and validate ownership
    const me = await resMe.json();
    const feederAddress = me.data.address.toLowerCase();
    const owner = await ownerOf(body.nftTokenId);

    let feeder: Address;
    if (feederAddress === owner.toLowerCase()) {
      feeder = feederAddress;
    } else {
      // Check rental/delegation
      const response = await getRentalChicken(body.nftTokenId);
      if (response.status === 0) {
        return NextResponse.json(
          { status: false, responseCode: 401, message: "Bad request" },
          { status: 401 }
        );
      }

      const { ronin_price, owner_address, renter_address } = response.data;
      // Check if delagated
      if (ronin_price === "0" || ronin_price === 0) {
        if (
          owner_address.toLowerCase() !== feederAddress &&
          renter_address.toLowerCase() !== feederAddress
        ) {
          console.log("here");

          return NextResponse.json(
            { status: false, responseCode: 401, message: "Bad request" },
            { status: 401 }
          );
        }
        feeder = feederAddress as Address;
      } else {
        // Check if rented
        if (renter_address.toLowerCase() !== feederAddress) {
          return NextResponse.json(
            { status: false, responseCode: 401, message: "Bad request" },
            { status: 401 }
          );
        }
        feeder = renter_address;
      }
    }

    // Initialize signer and calculate values
    const signerAccount = privateKeyToAccount(SIGNER_PRIVATE_KEY);
    const deadline =
      Math.floor(Date.now() / 1000) +
      Math.floor(SIGNATURE_VALIDITY_DURATION / 1000);
    const affectionPoints = calculateAffectionPoints(
      body.collectionType,
      body.nftTokenId,
      body.feedItemId,
      body.amount
    );

    // Create and sign message
    const { messageHash, encodedData } = createFeedMessageHash(
      body.collectionType,
      body.nftTokenId,
      body.feedItemId,
      affectionPoints,
      body.amount,
      body.debuffStatChoice,
      body.nonce,
      deadline
    );

    const ethSignedMessageHash = hashMessage(messageHash);
    const signature = await signerAccount.signMessage({
      message: { raw: messageHash as `0x${string}` },
    });
    const response: FeedSignatureResponse = {
      signature,
      deadline,
      affectionPoints,
      messageHash,
      ethSignedMessageHash,
      encodedData,
      feeder,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error generating single feed signature:", error);
    return NextResponse.json(
      { error: "Internal server error", details: (error as Error).message },
      { status: 500 }
    );
  }
}
