"use client";

import { useMemo } from "react";
import { useChickendOwned } from "@/hooks/useChickendOwned";
import { useStateContext } from "@/providers/app/state";
import useAuthStore from "@/store/auth";
import useFoodCraftingStore from "@/store/food-crafting";
import {
  InventoryItem,
  ChickenInventoryItem,
  FeatherInventoryItem,
  ItemInventoryItem,
} from "@/types/inventory.type";
import { useChickenTokenIds } from "@/features/chickens/hooks/useChickenTokenIds";
import { useMyRentals } from "@/features/delegation/hooks/useMyRentals";

interface Chicken {
  tokenId: string;
  image: string;
  attributes?: {
    Type?: string[];
  };
}

export function useInventoryData() {
  const { address } = useStateContext();
  const { feathers, legendaryFeathers } = useAuthStore();
  const { foodBalances } = useFoodCraftingStore();
  const { data: chickenData } = useChickendOwned(address as string);

  // Get token IDs and rental data for accurate count
  const { tokenIdsByType } = useChickenTokenIds(address);
  const { rentedChickens, ownedRentals } = useMyRentals(address);

  // Chicken inventory data - we'll handle this differently for the new structure
  const chickenItems = useMemo((): ChickenInventoryItem[] => {
    if (!chickenData?.tokens) return [];

    return chickenData.tokens.map((chicken: Chicken) => ({
      id: `chicken-${chicken.tokenId}`,
      type: "my-chickens" as const, // Default to my-chickens for now
      name: `Chicken #${chicken.tokenId}`,
      image: chicken.image,
      amount: 1,
      tokenId: chicken.tokenId,
      attributes: chicken.attributes,
    }));
  }, [chickenData]);

  // Feather inventory data
  const featherItems = useMemo((): FeatherInventoryItem[] => {
    const items: FeatherInventoryItem[] = [];

    if (Number(feathers) > 0) {
      items.push({
        id: "feather-regular",
        type: "feathers" as const,
        name: "Regular Feathers",
        image: "/images/feathers.png",
        amount: Number(feathers),
        tokenId: 1,
        isLegendary: false,
        contractAddress: process.env.NEXT_PUBLIC_FEATHERS_CONTRACT || "",
      });
    }

    if (Number(legendaryFeathers) > 0) {
      items.push({
        id: "feather-legendary",
        type: "feathers" as const,
        name: "Legendary Feathers",
        image: "/images/legendary-feathers.png",
        amount: Number(legendaryFeathers),
        tokenId: 2,
        isLegendary: true,
        contractAddress: process.env.NEXT_PUBLIC_FEATHERS_CONTRACT || "",
      });
    }

    return items;
  }, [feathers, legendaryFeathers]);

  // Item inventory data
  const itemItems = useMemo((): ItemInventoryItem[] => {
    const items: ItemInventoryItem[] = [];

    Object.entries(foodBalances).forEach(([tokenId, balance]) => {
      if (Number(balance) > 0) {
        items.push({
          id: `item-${tokenId}`,
          type: "items" as const,
          name: `Food Item #${tokenId}`,
          image: `https://sabong-saga-resources.s3.ap-southeast-1.amazonaws.com/${tokenId}.png`,
          amount: Number(balance),
          tokenId: Number(tokenId),
          craftable: false,
        });
      }
    });

    return items;
  }, [foodBalances]);

  // All inventory items
  const allItems = useMemo((): InventoryItem[] => {
    return [...chickenItems, ...featherItems, ...itemItems];
  }, [chickenItems, featherItems, itemItems]);

  // Counts for tabs - separate my-chickens and delegated-out counts
  const myChickensCount =
    (tokenIdsByType.all?.length || 0) + rentedChickens.length;
  const delegatedOutCount = ownedRentals.length;
  const featherCount = featherItems.reduce((sum, item) => sum + item.amount, 0);
  const itemCount = itemItems.reduce((sum, item) => sum + item.amount, 0);

  return {
    allItems,
    chickenItems,
    featherItems,
    itemItems,
    myChickensCount,
    delegatedOutCount,
    featherCount,
    itemCount,
    // Keep chickenCount for backward compatibility
    chickenCount: myChickensCount,
  };
}

export function useChickenInventory() {
  const { address } = useStateContext();
  const chickenQuery = useChickendOwned(address as string);

  return {
    ...chickenQuery,
    chickens: chickenQuery.data?.tokens || [],
  };
}

export function useFeatherInventory() {
  const { feathers, legendaryFeathers } = useAuthStore();

  return {
    regularFeathers: Number(feathers),
    legendaryFeathers: Number(legendaryFeathers),
    totalFeathers: Number(feathers) + Number(legendaryFeathers),
  };
}

export function useItemInventory() {
  const { foodBalances } = useFoodCraftingStore();

  const items = useMemo(() => {
    return Object.entries(foodBalances)
      .filter(([_, balance]) => Number(balance) > 0)
      .map(([tokenId, balance]) => ({
        tokenId: Number(tokenId),
        balance: Number(balance),
        name: `Food Item #${tokenId}`,
        image: `https://sabong-saga-resources.s3.ap-southeast-1.amazonaws.com/${tokenId}.png`,
      }));
  }, [foodBalances]);

  return {
    items,
    totalItems: items.reduce((sum, item) => sum + item.balance, 0),
  };
}
