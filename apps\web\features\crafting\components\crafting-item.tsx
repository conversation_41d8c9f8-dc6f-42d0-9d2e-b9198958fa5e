"use client";

import { IconMinus, IconPlus } from "justd-icons";
import React, { useState } from "react";
import { <PERSON>ton, Modal } from "ui";

interface CraftingItemProps {
  name: string;
  image?: string;
  requirements: {
    chicken?: number;
    feather?: number;
  };
}

export function CraftingItem({ name, image, requirements }: CraftingItemProps) {
  const [quantity, setQuantity] = useState(10);
  const [isOpen, setIsOpen] = useState(false);

  const handleIncrement = () => {
    setQuantity((prev) => prev + 1);
  };

  const handleDecrement = () => {
    setQuantity((prev) => Math.max(1, prev - 1));
  };

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value >= 1) {
      setQuantity(value);
    }
  };

  return (
    <>
      <div
        onClick={() => setIsOpen(true)}
        className="bg-[#2D2D2D] rounded-xl p-4 aspect-square cursor-pointer hover:bg-[#3D3D3D] transition-colors"
      >
        <div className="aspect-square bg-[#1E1E1E] rounded-lg mb-4">
          {image && (
            <img
              src={image}
              alt={name}
              className="w-full h-full object-cover"
            />
          )}
        </div>
        <div className="text-center">{name}</div>
        <div className="flex justify-center items-center gap-2 mt-2">
          {requirements.chicken && (
            <div className="flex items-center gap-1">
              <div className="mr-2">
                <img
                  className="size-6"
                  src="/images/tokens/chicken-token.png"
                  alt="chicken-token"
                />
              </div>
              <span>{requirements.chicken}</span>
            </div>
          )}
          {requirements.feather && (
            <div className="flex items-center gap-1">
              <div className="mr-2">
                <img
                  className="size-6"
                  src="/images/tokens/feather-token.png"
                  alt="feather-token"
                />
              </div>
              <span>{requirements.feather}</span>
            </div>
          )}
        </div>
      </div>

      <Modal
        isOpen={isOpen}
        onOpenChange={(v) => {
          setIsOpen(v);
        }}
      >
        <Modal.Content size="3xl">
          <div className="flex gap-8 py-8">
            {/* Left side - Image */}
            <div className="w-72 h-72 bg-[#1E1E1E] rounded-lg overflow-hidden">
              {image && (
                <img
                  src={image}
                  alt={name}
                  className="w-full h-full object-cover"
                />
              )}
            </div>

            {/* Right side - Content */}
            <div className="flex-1 space-y-6">
              <div>
                <h2 className="text-xl font-bold uppercase">{name}</h2>
                <p className="text-gray-400 mt-2">
                  Craft a {name.toLowerCase()} for now. You can get Attack
                  Cookie, Defense Cookie, etc randomly.
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <label className="text-gray-200 uppercase font-semibold text-sm">
                    Quantity
                  </label>
                  <div className="flex items-center gap-2 mt-2">
                    <Button
                      intent="secondary"
                      size="square-petite"
                      onPress={handleDecrement}
                      className="bg-[#2D2D2D] hover:bg-[#3D3D3D] text-xl font-bold w-10 h-10"
                    >
                      <IconMinus />
                    </Button>
                    <input
                      type="text"
                      value={quantity}
                      onChange={handleQuantityChange}
                      className="w-16 text-center bg-[#2D2D2D] rounded-md p-2 text-lg"
                    />
                    <Button
                      intent="secondary"
                      size="square-petite"
                      onPress={handleIncrement}
                      className="bg-[#2D2D2D] hover:bg-[#3D3D3D] text-xl font-bold w-10 h-10"
                    >
                      <IconPlus />
                    </Button>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <label className="text-gray-200 font-semibold uppercase text-sm">
                    Cost
                  </label>
                  <div className="flex items-center gap-4 mt-2">
                    {requirements.chicken && (
                      <div className="flex items-center gap-2">
                        <img
                          className="size-6"
                          src="/images/tokens/chicken-token.png"
                          alt="chicken-token"
                        />
                        <span className="text-lg">
                          {requirements.chicken * quantity}
                        </span>
                      </div>
                    )}
                    {requirements.feather && (
                      <div className="flex items-center gap-2">
                        <img
                          className="size-6"
                          src="/images/tokens/feather-token.png"
                          alt="feather-token"
                        />
                        <span className="text-lg">
                          {requirements.feather * quantity}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <Button
                intent="primary"
                size="large"
                className="w-full bg-yellow-400 hover:bg-yellow-500 text-black font-bold text-lg py-4"
                onPress={() => {
                  // Handle craft logic here
                }}
              >
                CRAFT
              </Button>
            </div>
          </div>
        </Modal.Content>
      </Modal>
    </>
  );
}
