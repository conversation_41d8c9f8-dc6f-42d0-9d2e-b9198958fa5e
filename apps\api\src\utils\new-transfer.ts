import { TokenTransfer } from "../types";
import { NFT } from "../types/nfts";

export const checkNewlyTransfer = (
  transferData: { epoch: number; data: TokenTransfer }[],
  nfts: { id: string; address: string }[]
): string[] | null => {
  if (transferData && transferData.length > 0) {
    const ids: string[] = []; // Array to store newly transferred token IDs

    // Extract existing token IDs from the nfts array
    const existingTokenIds = new Set(nfts.map((nft) => nft.id));

    for (const element of transferData) {
      const tokenId = element?.data.tokenId;

      // Check if the tokenId exists and is not already in the nfts array
      if (tokenId && existingTokenIds.has(tokenId)) {
        ids.push(tokenId); // Add the new tokenId to the result array
      }
    }

    return ids.length > 0 ? ids : null; // Return null if no new IDs are found
  }

  return null; // Return null if transferData is empty or undefined
};
