import { Context } from "hono";
import { number, z } from "zod";
import { Response<PERSON><PERSON><PERSON>, sendResponse } from "../utils/response-helper";
import Me from "../models/me";
import Withdraw from "../models/withdraw";
import { getRedisStore, RedisStore } from "../services/redis-store";
import { env } from "../env";
import { Address, isAddress } from "viem";
import { getNfts } from "../services/get-nfts";
import { NFT } from "../types/nfts";
import { getBatchRns } from "../services/batchRns";
import { getTokenBalance } from "../services/get-balanceof";

// Interfaces
interface WithdrawalMap {
  [key: string]: number;
}

interface NftData {
  count: number;
  nfts: NFT[];
}

interface NftMap {
  [key: string]: NftData;
}

interface LeaderboardUser {
  address: string;
  feathers: number;
}

interface NftResult {
  address: string;
  nftCount: number;
  nfts: NFT[];
}

interface CombinedLeaderboardEntry {
  address: string;
  feathers: number;
  withdrawnFeathers: number;
  totalFeathers: number;
  nftCount: number;
  nfts?: NFT[];
  rns?: string | null;
}

interface PaginatedResponse {
  leaderboard: CombinedLeaderboardEntry[];
  pagination: {
    currentPage: number;
    totalPages: number;
  };
}

interface LeaderboardPosition {
  position: number;
  userStats: CombinedLeaderboardEntry;
}

// Validation schemas
const QuerySchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).default("1"),
  limit: z.string().regex(/^\d+$/).transform(Number).default("7"),
});

const AddressParamSchema = z.object({
  address: z.string(),
});

const redisStore = getRedisStore();
// Helper function to calculate total feathers
async function calculateTotalFeathersForAll(): Promise<
  CombinedLeaderboardEntry[]
> {
  const users = await Me.find().select("address feathers").lean();

  const withdrawals = await Withdraw.find().select("address feathers").lean();

  const withdrawalMap = withdrawals.reduce<WithdrawalMap>((acc, withdrawal) => {
    const currentAmount = acc[withdrawal.address] ?? 0;
    acc[withdrawal.address] = currentAmount + (withdrawal.feathers ?? 0);
    return acc;
  }, {});

  const combinedData = users.map((user) => ({
    address: user.address,
    feathers: user.feathers,
    withdrawnFeathers: withdrawalMap[user.address] || 0,
    totalFeathers: user.feathers + (withdrawalMap[user.address] || 0),
    nftCount: 0,
    nfts: [] as NFT[],
  }));

  return combinedData
    .filter((entry) => entry.totalFeathers > 0)
    .sort((a, b) => b.totalFeathers - a.totalFeathers);
}

// Get leaderboard endpoint
export const getLeaderboard = async (c: Context) => {
  const result = QuerySchema.safeParse(c.req.query());

  if (!result.success) {
    return sendResponse(
      c,
      ResponseHelper.badRequest("Invalid query parameters")
    );
  }

  const { page, limit } = result.data;
  const cacheKey = `leaderboard:${page}:${limit}`;

  const cachedData = await redisStore.get(cacheKey);
  if (cachedData) {
    return sendResponse(c, ResponseHelper.success(JSON.parse(cachedData)));
  }

  try {
    const sortedLeaderboard = await calculateTotalFeathersForAll();

    const skip = (page - 1) * limit;
    const paginatedLeaderboard = sortedLeaderboard.slice(skip, skip + limit);

    const addresses = paginatedLeaderboard.map((entry) => entry.address);

    const [nftResults, rnsResults] = await Promise.all([
      Promise.all(
        addresses.map(async (address) => {
          const nfts = await getTokenBalance(address);
          return {
            address,
            nftCount: Number(nfts) as number,
          };
        })
      ),
      getBatchRns(addresses as Address[]),
    ]);
    const nftMap = nftResults.reduce<NftMap>((acc: any, result: any) => {
      acc[result.address] = {
        count: result.nftCount,
        nfts: result.nfts,
      };
      return acc;
    }, {});

    const rnsMap = rnsResults.reduce<Record<string, string>>(
      (acc: { [x: string]: any }, entry: { name: any; address: string }) => {
        if (entry.name) {
          acc[entry.address.toLowerCase()] = entry.name;
        }
        return acc;
      },
      {}
    );

    const finalLeaderboard = paginatedLeaderboard.map((entry) => ({
      address: entry.address,
      feathers: entry.feathers,
      withdrawnFeathers: entry.withdrawnFeathers,
      totalFeathers: entry.totalFeathers,
      nftCount: nftMap[entry.address]?.count || 0,
      nfts: nftMap[entry.address]?.nfts || [],
      rns: rnsMap[entry.address.toLowerCase()] || null,
    }));

    const data: PaginatedResponse = {
      leaderboard: finalLeaderboard,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(sortedLeaderboard.length / limit),
      },
    };

    await redisStore.store(cacheKey, JSON.stringify(data), 300);
    return sendResponse(c, ResponseHelper.success(data));
  } catch (error) {
    console.error("Leaderboard Error:", error);
    return sendResponse(c, ResponseHelper.serverError());
  }
};

// Get leaderboard position endpoint
export const getLeaderboardPosition = async (c: Context) => {
  const result = AddressParamSchema.safeParse(c.req.query());

  if (!result.success) {
    return sendResponse(c, ResponseHelper.badRequest("Invalid address format"));
  }

  const { address } = result.data;
  const normalizedAddress = address.toLowerCase();

  if (!isAddress(address)) {
    return sendResponse(c, ResponseHelper.badRequest("Invalid address"));
  }

  const cacheKey = `leaderboard:address:${normalizedAddress}`;

  const cachedData = await redisStore.get(cacheKey);
  if (cachedData) {
    return sendResponse(c, ResponseHelper.success(JSON.parse(cachedData)));
  }

  try {
    const user = await Me.findOne({ address: normalizedAddress })
      .select("address feathers")
      .lean();

    if (!user) {
      return sendResponse(
        c,
        ResponseHelper.notFound("Address not found in leaderboard")
      );
    }

    const withdrawals = await Withdraw.find({
      address: normalizedAddress,
    })
      .select("feathers")
      .lean();

    const withdrawnFeathers = withdrawals.reduce(
      (sum, withdrawal) => sum + (withdrawal.feathers || 0),
      0
    );

    const nfts = await getTokenBalance(normalizedAddress);
    const totalFeathers = user.feathers + withdrawnFeathers;

    const sortedLeaderboard = await calculateTotalFeathersForAll();
    const rns = await getBatchRns([address]);

    const position =
      sortedLeaderboard.findIndex(
        (entry) => entry.address.toLowerCase() === normalizedAddress
      ) + 1;

    const userStats: CombinedLeaderboardEntry = {
      address: normalizedAddress,
      feathers: user.feathers,
      withdrawnFeathers: withdrawnFeathers,
      totalFeathers: totalFeathers,
      nftCount: Number(nfts) as number,
      rns: rns[0]?.name || null,
    };

    const response: LeaderboardPosition = {
      position,
      userStats,
    };

    await redisStore.store(cacheKey, JSON.stringify(response), 300);
    return sendResponse(c, ResponseHelper.success(response));
  } catch (error) {
    console.error("Leaderboard Position Error:", error);
    return sendResponse(c, ResponseHelper.serverError());
  }
};
