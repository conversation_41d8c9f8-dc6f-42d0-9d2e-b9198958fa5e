"use client";

import React from "react";
import useBreedingFees from "@/lib/hooks/useBreedingFees";
import { State } from "@hookstate/core";
import { IBreedingPair } from "../types/breeding.types";
import { Key } from "react-aria-components";

interface IBreedingCostProps {
  manualBreedingPair?: State<IBreedingPair>;
  breedOption?: Key;
  massBreedingPairs?: State<IBreedingPair[]>;
}

export function BreedingCost({
  manualBreedingPair,
  breedOption = "manual",
  massBreedingPairs,
}: IBreedingCostProps) {
  // Get breed counts from selected chickens
  let parent1BreedCount: number | number[] = 0;
  let parent2BreedCount: number | number[] = 0;

  if (breedOption === "manual" && manualBreedingPair) {
    // For manual breeding, use single breed counts
    parent1BreedCount = manualBreedingPair.parent1Data.value?.breedCount || 0;
    parent2BreedCount = manualBreedingPair.parent2Data.value?.breedCount || 0;
  } else if (
    breedOption === "mass" &&
    massBreedingPairs &&
    massBreedingPairs.length > 0
  ) {
    // For mass breeding, calculate the total cost for all pairs
    // We'll fetch the fees for each pair and sum them up in the hook
    const pairs = massBreedingPairs.get();

    // Create arrays to hold all breed counts
    const parent1BreedCounts: number[] = [];
    const parent2BreedCounts: number[] = [];

    // Collect breed counts from all pairs
    pairs.forEach((pair) => {
      if (pair.parent1Data && pair.parent2Data) {
        parent1BreedCounts.push(pair.parent1Data.breedCount || 0);
        parent2BreedCounts.push(pair.parent2Data.breedCount || 0);
      }
    });

    // Use arrays of breed counts for mass breeding
    if (parent1BreedCounts.length > 0 && parent2BreedCounts.length > 0) {
      parent1BreedCount = parent1BreedCounts;
      parent2BreedCount = parent2BreedCounts;
    }
  }

  // Use the updated hook with breed counts
  const { breedingFeesQuery, cockFee, featherFee } = useBreedingFees(
    parent1BreedCount,
    parent2BreedCount
  );

  const isLoading = breedingFeesQuery.isLoading;
  const isError = breedingFeesQuery.isError;

  return (
    <div className="flex flex-col items-center gap-4">
      <span className="text-white font-bold text-base sm:text-lg">COST</span>
      {isLoading ? (
        <div className="text-white text-sm">Loading...</div>
      ) : isError ? (
        <div className="text-red-500 text-sm">
          Failed to load breeding costs
        </div>
      ) : (
        <div className="flex flex-wrap justify-center gap-4 sm:gap-8 items-center">
          <div className="flex items-center gap-2">
            <div className="mr-1 sm:mr-2">
              <img
                src="/images/tokens/cock-token.webp"
                alt="Cock Token"
                className="w-5 h-5 sm:w-6 sm:h-6"
              />
            </div>
            <span className="text-white text-sm sm:text-base">
              {Math.round(cockFee)}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <img
              src="/images/tokens/feathers-token.png"
              alt="Feather"
              className="w-5 h-5 sm:w-6 sm:h-6"
            />
            <span className="text-white text-sm sm:text-base">
              {featherFee}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
