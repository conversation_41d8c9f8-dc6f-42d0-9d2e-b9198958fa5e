{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint --fix && tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "process-allocations": "node utils/processAllocations.mjs", "upload-mongo": "node utils/uploadToMongo.mjs", "update-mystics": "node utils/updateMysticAllocations.mjs"}, "dependencies": {"@edge-csrf/nextjs": "2.5.3-cloudflare-rc1", "@ethersproject/providers": "5.7.2", "@hookstate/core": "^4.0.1", "@hookstate/devtools": "^4.0.3", "@hookstate/localstored": "^4.0.2", "@next/third-parties": "^15.1.6", "@react-types/overlays": "^3.8.13", "@react-types/shared": "^3.29.0", "@roninnetwork/rnsjs": "^0.2.3", "@sky-mavis/tanto-connect": "^0.0.18", "@sky-mavis/tanto-wagmi": "^0.0.7", "@sky-mavis/waypoint": "^4.0.0", "@tanstack/react-query": "^5.69.0", "@tanstack/react-query-devtools": "^5.69.0", "axios": "^1.7.9", "chart.js": "^4.4.8", "chartjs-plugin-datalabels": "^2.2.0", "clsx": "^2.1.1", "colyseus.js": "^0.16.15", "csrf": "^3.1.0", "csv-parse": "^5.6.0", "csv-writer": "^1.6.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "embla-carousel-react": "^8.5.2", "ethers": "6.13.5", "justd-icons": "^1.10.27", "lodash.merge": "^4.6.2", "lucide-react": "^0.503.0", "mongodb": "^6.13.1", "motion": "^11.15.0", "next": "15.3.1", "next-csrf": "^0.2.1", "next-themes": "^0.4.4", "nookies": "^2.5.2", "nuqs": "^2.4.1", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-aria-components": "^1.5.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-jdenticon": "^1.4.0", "react-json-view": "^1.21.3", "siwe": "1.1.6", "sonner": "^1.7.1", "viem": "^2.28.1", "wagmi": "^2.15.0", "zustand": "^5.0.1"}, "devDependencies": {"@sabongsaga/eslint-config": "workspace:*", "@sabongsaga/typescript-config": "workspace:*", "@testing-library/jest-dom": "^6.6.3", "@types/jest": "^29.5.14", "@types/lodash.merge": "^4.6.9", "@types/react": "18", "@types/react-dom": "18", "jest": "^29.7.0", "postcss": "^8", "tailwind-merge": "^2.6.0", "tailwind-variants": "^0.3.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "tailwindcss-react-aria-components": "^1.2.0", "typescript": "^5"}}