"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";
import { chain } from "@/providers/web3/web3-provider";
import { rentalAbi } from "@/providers/web3/abi/rental-abi";
import { DelegationAPI } from "../api/delegation.api";

/**
 * Interface for bulk cancellation request
 */
interface IBulkCancelRequest {
  rentalIds: number[];
}

/**
 * Interface for bulk cancellation response from API
 */
interface IBulkCancelResponse {
  status: number;
  message: string;
  data: Array<{
    rentalId: number;
    success: boolean;
    message?: string;
    data?: {
      rentalId: number;
      chickenTokenId: number;
      signature: string;
    };
  }>;
}

/**
 * Hook for handling bulk delegation cancellation blockchain transactions
 * Uses the unlistChickenForRentBulk contract function for efficiency
 */
export const useBulkCancelDelegation = () => {
  const { publicClient, walletClient, address, Disconnect } = useStateContext();
  const { blockchainQuery } = useBlockchain();
  const queryClient = useQueryClient();

  const [isCancelling, setIsCancelling] = useState(false);
  const [progress, setProgress] = useState<{
    current: number;
    total: number;
    status: string;
  }>({ current: 0, total: 0, status: "" });

  // Get rental contract address from blockchain config
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  /**
   * Execute bulk cancellation process:
   * 1. Call API to get signatures for all rentals
   * 2. Execute single blockchain transaction for all cancellation operations
   * 3. Handle success/error states
   */
  const executeBulkCancellation = async (rentalIds: number[]) => {
    try {
      if (!address || !walletClient) {
        toast.error("Cannot cancel delegations", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return { success: false, error: "Wallet not connected" };
      }

      if (!rentalAddress) {
        toast.error("Cannot cancel delegations", {
          description: "Rental contract not configured",
          position: "top-right",
        });
        return { success: false, error: "Rental contract not configured" };
      }

      if (rentalIds.length === 0) {
        toast.error("No delegations selected", {
          description: "Please select delegations to cancel",
          position: "top-right",
        });
        return { success: false, error: "No delegations selected" };
      }

      setIsCancelling(true);
      setProgress({ current: 0, total: 4, status: "Preparing..." });

      // Step 1: Get signatures from API for all rentals
      toast.info("Preparing bulk cancellation...", {
        description: `Getting signatures for ${rentalIds.length} delegations`,
        position: "top-center",
      });

      // Call the bulk cancellation API
      const bulkCancelResponse =
        await DelegationAPI.cancelRentalBulk(rentalIds);

      if (bulkCancelResponse.status !== 1 || !bulkCancelResponse.data) {
        throw new Error("Failed to get cancellation signatures from server");
      }

      // Filter successful responses
      const successfulCancellations = bulkCancelResponse.data.filter(
        (result) => result.success && result.data
      );

      if (successfulCancellations.length === 0) {
        throw new Error("No valid cancellation signatures received");
      }

      // Prepare arrays for contract call
      const contractRentalIds = successfulCancellations.map((item) =>
        BigInt(item.data!.rentalId)
      );
      const chickenTokenIds = successfulCancellations.map((item) =>
        BigInt(item.data!.chickenTokenId)
      );
      const signatures = successfulCancellations.map(
        (item) => item.data!.signature as `0x${string}`
      );

      setProgress({
        current: 1,
        total: 4,
        status: "Simulating transaction...",
      });

      // Step 2: Simulate the bulk transaction
      toast.info("Simulating transaction...", {
        description: "Validating bulk cancellation parameters",
        position: "top-center",
      });

      await publicClient.simulateContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "unlistChickenForRentBulk",
        args: [contractRentalIds, chickenTokenIds, signatures],
        chain,
        account: address,
      });

      // Step 3: Estimate gas for the transaction
      const gasEstimate = await publicClient.estimateContractGas({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "unlistChickenForRentBulk",
        args: [contractRentalIds, chickenTokenIds, signatures],
        account: address,
      });

      setProgress({
        current: 2,
        total: 4,
        status: "Executing transaction...",
      });

      // Step 4: Execute the bulk cancellation transaction
      toast.info("Cancelling delegations on blockchain...", {
        description: `Please confirm the transaction for ${successfulCancellations.length} delegations`,
        position: "top-center",
      });

      const hash = await walletClient.writeContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "unlistChickenForRentBulk",
        args: [contractRentalIds, chickenTokenIds, signatures],
        gas: gasEstimate + BigInt(100000), // Add buffer for bulk operation
        chain,
        account: address,
      });

      setProgress({
        current: 3,
        total: 4,
        status: "Confirming transaction...",
      });

      // Step 5: Wait for transaction confirmation
      toast.info("Confirming bulk transaction...", {
        description: "Waiting for blockchain confirmation",
        position: "top-center",
      });

      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        confirmations: 1,
      });

      if (receipt.status === "success") {
        toast.success("Delegations cancelled successfully!", {
          description: `${successfulCancellations.length} delegations have been cancelled`,
          position: "top-center",
        });

        // Invalidate relevant queries to refresh data with proper sequencing
        setTimeout(async () => {
          // First, invalidate and refetch chickenTokenIds (blockchain data)
          await queryClient.invalidateQueries({
            queryKey: ["chickenTokenIds", "legacy", address],
          });
          await queryClient.invalidateQueries({
            queryKey: ["chickenTokenIds", "genesis", address],
          });

          // Then invalidate rental-related queries
          queryClient.invalidateQueries({ queryKey: ["available-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals", address] });
          queryClient.invalidateQueries({
            queryKey: ["chickenRentalStatuses"],
          });
        }, 3000); // Longer delay for cancellation to ensure blockchain state is updated

        setProgress({
          current: 4,
          total: 4,
          status: "Complete",
        });

        return {
          success: true,
          hash,
          receipt,
          successfulCount: successfulCancellations.length,
          totalRequested: rentalIds.length,
          failedCount: rentalIds.length - successfulCancellations.length,
        };
      } else {
        throw new Error("Transaction failed");
      }
    } catch (error: any) {
      console.error("Bulk cancellation error:", error);

      const errorMessage = error?.message || "Unknown error occurred";
      toast.error("Bulk cancellation failed", {
        description: errorMessage,
        position: "top-center",
      });

      return { success: false, error: errorMessage };
    } finally {
      setIsCancelling(false);
      setProgress({ current: 0, total: 0, status: "" });
    }
  };

  // Mutation for React Query integration
  const bulkCancelMutation = useMutation({
    mutationFn: executeBulkCancellation,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Bulk cancel mutation error:", error);
    },
  });

  return {
    executeBulkCancellation,
    bulkCancelMutation,
    isCancelling,
    progress,
    rentalAddress,
  };
};
