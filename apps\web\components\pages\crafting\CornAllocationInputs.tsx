import { Input } from "@/components/ui";

interface CornAllocation {
  [cornId: number]: number;
}

// Corn names mapping
const CORN_NAMES: Record<number, string> = {
  12: "Red Corn",
  13: "Blue Corn",
  14: "Yellow Corn",
  15: "Orange Corn",
  16: "Violet Corn",
  17: "Green Corn",
  18: "Black Corn",
};

export const CornAllocationInputs = ({
  allocation,
  requiredTotal,
  acceptedResourceIds,
  onAllocationChange,
  foodBalances,
  getTotalAllocated,
}: {
  allocation: CornAllocation;
  requiredTotal: number;
  acceptedResourceIds: number[];
  getTotalAllocated: (allocation: CornAllocation) => number;
  foodBalances: Record<number, bigint>;
  onAllocationChange: (cornId: number, amount: number) => void;
}) => {
  const totalAllocated = getTotalAllocated(allocation);
  const remaining = requiredTotal - totalAllocated;

  // Validate that acceptedResourceIds contains valid resources
  if (!acceptedResourceIds || acceptedResourceIds.length === 0) {
    return (
      <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-center">
        <p className="text-red-400 text-sm">
          ⚠️ No accepted resources configured for this recipe
        </p>
        <p className="text-red-300/70 text-xs mt-1">
          Please contact the game administrators
        </p>
      </div>
    );
  }

  // Filter corn IDs to only show accepted ones
  const availableCorns = acceptedResourceIds.filter((id) => CORN_NAMES[id]);

  if (availableCorns.length === 0) {
    return (
      <div className="p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg text-center">
        <p className="text-yellow-400 text-sm">
          ℹ️ No corn resources accepted for this recipe
        </p>
        <p className="text-yellow-300/70 text-xs mt-1">
          This recipe may use other types of resources
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {/* Summary Header */}
      <div className="bg-stone-700/30 p-2 rounded-lg">
        <div className="flex justify-between items-center text-xs lg:text-sm">
          <span className="text-white/80">
            <span className="font-medium">Need:</span>{" "}
            {requiredTotal.toLocaleString()}
          </span>
          <span className="text-white/80">
            <span className="font-medium">Allocated:</span>{" "}
            {totalAllocated.toLocaleString()}
          </span>
          <span
            className={`font-medium ${
              remaining === 0
                ? "text-green-400"
                : remaining < 0
                  ? "text-red-400"
                  : "text-yellow-400"
            }`}
          >
            <span className="text-white/80">Remaining:</span>{" "}
            {remaining.toLocaleString()}
          </span>
        </div>
      </div>

      {/* Accepted Resources Info */}
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-2">
        <p className="text-blue-400 text-xs font-medium mb-1">
          ✓ Accepted Resources ({availableCorns.length} types)
        </p>
        <div className="flex flex-wrap gap-1">
          {availableCorns.map((cornId) => (
            <span
              key={cornId}
              className="text-xs bg-blue-500/20 text-blue-300 px-2 py-0.5 rounded"
            >
              {CORN_NAMES[cornId]} #{cornId}
            </span>
          ))}
        </div>
      </div>

      {/* Compact Corn Allocation Table - Only Accepted Resources */}
      <div className="bg-stone-800/40 rounded-lg overflow-hidden">
        <div className="grid grid-cols-2 gap-1 p-2">
          {availableCorns.map((cornId) => {
            const available = Number(foodBalances[cornId] || 0n);
            const allocated = allocation[cornId] || 0;
            const isValid = allocated <= available;
            const hasBalance = available > 0;

            return (
              <div
                key={cornId}
                className={`flex items-center gap-1 p-1.5 rounded text-xs transition-all ${
                  hasBalance
                    ? "bg-stone-700/30 hover:bg-stone-700/50"
                    : "bg-stone-700/10 opacity-60"
                }`}
              >
                <img
                  src={`https://sabong-saga-resources.s3.ap-southeast-1.amazonaws.com/${cornId}.png`}
                  alt={CORN_NAMES[cornId]}
                  className="w-4 h-4 lg:w-5 lg:h-5 object-contain flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <p className="text-white/80 truncate text-xs font-medium">
                    {CORN_NAMES[cornId]}
                  </p>
                  <p
                    className={`text-xs ${hasBalance ? "text-white/50" : "text-red-400/70"}`}
                  >
                    Balance: {available.toLocaleString()}
                  </p>
                </div>

                {/* Mobile: Increment/Decrement Buttons */}
                <div className="flex items-center gap-0.5 lg:hidden">
                  <button
                    onClick={() =>
                      onAllocationChange(cornId, Math.max(0, allocated - 1))
                    }
                    disabled={allocated <= 0 || !hasBalance}
                    className="w-6 h-6 bg-stone-600/50 hover:bg-stone-600/70 disabled:bg-stone-600/20 disabled:text-white/30 rounded text-white/80 hover:text-white transition-colors flex items-center justify-center text-sm font-bold"
                  >
                    −
                  </button>
                  <span
                    className={`w-8 text-center text-xs font-medium ${
                      isValid ? "text-white/90" : "text-red-400"
                    }`}
                  >
                    {allocated}
                  </span>
                  <button
                    onClick={() =>
                      onAllocationChange(
                        cornId,
                        Math.min(available, allocated + 1)
                      )
                    }
                    disabled={allocated >= available || !hasBalance}
                    className="w-6 h-6 bg-stone-600/50 hover:bg-stone-600/70 disabled:bg-stone-600/20 disabled:text-white/30 rounded text-white/80 hover:text-white transition-colors flex items-center justify-center text-sm font-bold"
                  >
                    +
                  </button>
                </div>

                {/* Desktop: Number Input */}
                <Input
                  type="number"
                  value={allocated}
                  onChange={(e) => {
                    e.preventDefault();
                    const newValue = parseInt(e.target.value) || 0;
                    onAllocationChange(cornId, newValue);
                  }}
                  className={`hidden lg:block w-16 h-7 text-xs text-center ${
                    !isValid ? "border-red-500" : ""
                  }`}
                  min={0}
                  max={available}
                  placeholder="0"
                  disabled={!hasBalance}
                />
              </div>
            );
          })}
        </div>

        {/* Show message if no available corn */}
        {availableCorns.every((id) => Number(foodBalances[id] || 0n) === 0) && (
          <div className="p-3 text-center border-t border-stone-700/50">
            <p className="text-yellow-400 text-xs">
              ⚠️ No accepted corn resources available in your balance
            </p>
            <p className="text-yellow-300/70 text-xs mt-1">
              You need to acquire some corn resources first
            </p>
          </div>
        )}
      </div>

      {/* Quick allocation buttons */}
      <div className="flex gap-1 justify-center">
        <button
          onClick={() => {
            // Clear all allocations for accepted resources only
            availableCorns.forEach((id) => onAllocationChange(id, 0));
          }}
          className="px-2 py-1 text-xs bg-stone-700/50 hover:bg-stone-700/70 rounded text-white/70 hover:text-white transition-colors"
        >
          Clear All
        </button>
        <button
          onClick={() => {
            // Smart auto-fill: prefer corn with higher balances, only use accepted resources
            const availableCornsWithBalance = availableCorns
              .map((id) => ({
                id,
                available: Number(foodBalances[id] || 0n),
              }))
              .filter((corn) => corn.available > 0)
              .sort((a, b) => b.available - a.available);

            // Clear all accepted resources first
            availableCorns.forEach((id) => onAllocationChange(id, 0));

            // Distribute across available accepted corns
            let remaining = requiredTotal;
            for (const corn of availableCornsWithBalance) {
              if (remaining <= 0) break;
              const canAllocate = Math.min(remaining, corn.available);
              if (canAllocate > 0) {
                onAllocationChange(corn.id, canAllocate);
                remaining -= canAllocate;
              }
            }
          }}
          disabled={
            requiredTotal <= 0 ||
            availableCorns.every((id) => Number(foodBalances[id] || 0n) === 0)
          }
          className="px-2 py-1 text-xs bg-primary/20 hover:bg-primary/30 disabled:bg-primary/10 disabled:text-primary/50 rounded text-primary hover:text-white transition-colors"
        >
          Auto-fill
        </button>

        {/* Mobile-specific: Add quick increment buttons */}
        <div className="lg:hidden flex gap-1">
          <button
            onClick={() => {
              // Add 10 to first available accepted corn
              const firstAvailable = availableCorns.find((id) => {
                const available = Number(foodBalances[id] || 0n);
                const current = allocation[id] || 0;
                return available > 0 && current + 10 <= available;
              });
              if (firstAvailable) {
                const current = allocation[firstAvailable] || 0;
                onAllocationChange(firstAvailable, current + 10);
              }
            }}
            disabled={
              !availableCorns.some((id) => {
                const available = Number(foodBalances[id] || 0n);
                const current = allocation[id] || 0;
                return available > 0 && current + 10 <= available;
              })
            }
            className="px-2 py-1 text-xs bg-green-600/20 hover:bg-green-600/30 disabled:bg-green-600/10 disabled:text-green-600/50 rounded text-green-400 hover:text-white transition-colors"
          >
            +10
          </button>
          <button
            onClick={() => {
              // Add 50 to first available accepted corn
              const firstAvailable = availableCorns.find((id) => {
                const available = Number(foodBalances[id] || 0n);
                const current = allocation[id] || 0;
                return available > 0 && current + 50 <= available;
              });
              if (firstAvailable) {
                const current = allocation[firstAvailable] || 0;
                onAllocationChange(firstAvailable, current + 50);
              }
            }}
            disabled={
              !availableCorns.some((id) => {
                const available = Number(foodBalances[id] || 0n);
                const current = allocation[id] || 0;
                return available > 0 && current + 50 <= available;
              })
            }
            className="px-2 py-1 text-xs bg-green-600/20 hover:bg-green-600/30 disabled:bg-green-600/10 disabled:text-green-600/50 rounded text-green-400 hover:text-white transition-colors"
          >
            +50
          </button>
        </div>
      </div>

      {/* Allocation Status */}
      <div className="text-xs text-center">
        {remaining === 0 ? (
          <p className="text-green-400">✓ Perfect allocation!</p>
        ) : remaining > 0 ? (
          <p className="text-yellow-400">
            ⚠️ Need {remaining.toLocaleString()} more
          </p>
        ) : (
          <p className="text-red-400">
            ❌ Over-allocated by {Math.abs(remaining).toLocaleString()}
          </p>
        )}
      </div>

      {/* Warning for non-accepted resources */}
      {Object.entries(allocation).some(([idStr, amount]) => {
        const id = Number(idStr);
        return amount > 0 && !availableCorns.includes(id);
      }) && (
        <div className="p-2 bg-red-500/10 border border-red-500/20 rounded text-red-400 text-xs">
          ⚠️ Warning: You have allocated non-accepted resources. Only the
          following are accepted for this recipe:{" "}
          {availableCorns.map((id) => `#${id}`).join(", ")}
        </div>
      )}
    </div>
  );
};
