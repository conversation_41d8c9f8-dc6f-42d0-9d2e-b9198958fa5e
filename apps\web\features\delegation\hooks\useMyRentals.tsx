"use client";

import { useMemo } from "react";
import {
  useQuery,
  useMutation,
  useQueryClient,
  keepPreviousData,
} from "@tanstack/react-query";
import { toast } from "sonner";
import {
  IRentalWithMetadata,
  IMyRentalsResponse,
  ERentalStatus,
  IRental,
} from "../types/delegation.types";
import { DelegationAPI } from "../api/delegation.api";
import { useCancelDelegation } from "./useCancelDelegation";
import useChickenMetadata from "@/features/breeding/tab/breeding/hooks/useChickenMetadata";
import { IChickenMetadata } from "@/lib/types/chicken.types";
import { useStateContext } from "@/providers/app/state";
import { useChickenDeathVerification } from "./useChickenDeathVerification";

// Real API functions
const fetchMyRentals = async (): Promise<IMyRentalsResponse> => {
  return await DelegationAPI.getMyRentals();
};

// Helper function to create rental with real metadata
const createRentalWithRealMetadata = (
  rental: IRental,
  metadataMap: Record<number, IChickenMetadata>
): IRentalWithMetadata => {
  const metadata = metadataMap[rental.chickenTokenId];

  // Extract daily feathers and legendary count from metadata attributes
  const dailyFeathersAttr = metadata?.attributes.find(
    (attr) => attr.trait_type === "Daily Feathers"
  );
  const legendaryCountAttr = metadata?.attributes.find(
    (attr) => attr.trait_type === "Legendary Count"
  );

  const dailyFeathers = dailyFeathersAttr?.value
    ? typeof dailyFeathersAttr.value === "string"
      ? parseInt(dailyFeathersAttr.value)
      : Number(dailyFeathersAttr.value)
    : 0;

  const legendaryCount = legendaryCountAttr?.value
    ? typeof legendaryCountAttr.value === "string"
      ? parseInt(legendaryCountAttr.value)
      : Number(legendaryCountAttr.value)
    : 0;

  return {
    ...rental,
    chickenMetadata: metadata,
    dailyFeathers,
    legendaryCount,
  };
};

export function useMyRentals(currentUserAddress?: string) {
  const queryClient = useQueryClient();
  const { executeCancelDelegation, isCancelling } = useCancelDelegation();
  const { address } = useStateContext();
  const { verifyChickenDeath } = useChickenDeathVerification();

  // Fetch user's rentals
  const {
    data: myRentalsResponse,
    isLoading: isLoadingRentals,
    error,
    refetch,
  } = useQuery({
    queryKey: ["my-rentals", currentUserAddress],
    queryFn: fetchMyRentals,
    enabled: !!currentUserAddress,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
    placeholderData: keepPreviousData,
  });

  // Extract token IDs from rental data for metadata fetching
  const tokenIds = useMemo(() => {
    if (!myRentalsResponse?.data) return [];

    const allRentals = [
      ...myRentalsResponse.data.ownedRentals,
      ...myRentalsResponse.data.rentedChickens,
      ...myRentalsResponse.data.expiredRentalsWithInsurance,
    ];

    return allRentals.map((rental) => rental.chickenTokenId);
  }, [myRentalsResponse?.data]);

  // Fetch chicken metadata for all rentals
  const {
    metadataMap,
    isLoading: isLoadingMetadata,
    error: metadataError,
  } = useChickenMetadata(tokenIds);

  // Filter expired rentals to only show ones the connected address can claim
  const claimableExpiredRentals = useQuery({
    queryKey: [
      "claimable-expired-rentals",
      currentUserAddress,
      myRentalsResponse?.data?.expiredRentalsWithInsurance,
    ],
    queryFn: async () => {
      if (!myRentalsResponse?.data?.expiredRentalsWithInsurance || !address) {
        return [];
      }

      const expiredRentals = myRentalsResponse.data.expiredRentalsWithInsurance;
      const claimableRentals: IRental[] = [];

      // Check each expired rental for claim eligibility
      for (const rental of expiredRentals) {
        try {
          // Verify chicken death status
          const verification = await verifyChickenDeath(rental.chickenTokenId);

          if (!verification.isVerified) {
            // Skip rentals where death status can't be verified
            continue;
          }

          const isOwner =
            address.toLowerCase() === rental.ownerAddress.toLowerCase();
          const isRenter =
            address.toLowerCase() === rental.renterAddress?.toLowerCase();

          // Determine if connected address can claim based on chicken status
          if (verification.isDead && isOwner) {
            // Chicken died - owner can claim
            claimableRentals.push(rental);
          } else if (!verification.isDead && isRenter) {
            // Chicken survived - renter can claim
            claimableRentals.push(rental);
          }
          // If neither condition is met, the connected address cannot claim
        } catch (error) {
          console.error(
            `Failed to verify death status for chicken ${rental.chickenTokenId}:`,
            error
          );
          // Skip rentals where verification fails
        }
      }

      return claimableRentals;
    },
    enabled:
      !!myRentalsResponse?.data?.expiredRentalsWithInsurance &&
      !!address &&
      !isLoadingMetadata,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });

  // Convert rentals to include real metadata
  const allRentalsWithMetadata: IRentalWithMetadata[] = useMemo(() => {
    if (!myRentalsResponse?.data) return [];

    const allRentals = [
      ...myRentalsResponse.data.ownedRentals,
      ...myRentalsResponse.data.rentedChickens,
      ...myRentalsResponse.data.expiredRentalsWithInsurance,
    ];

    return allRentals.map((rental) =>
      createRentalWithRealMetadata(rental, metadataMap)
    );
  }, [myRentalsResponse?.data, metadataMap]);

  // Separate owned vs rented chickens using API response structure
  const { ownedRentals, rentedChickens, expiredRentalsWithInsurance } =
    useMemo(() => {
      if (!myRentalsResponse?.data) {
        return {
          ownedRentals: [],
          rentedChickens: [],
          expiredRentalsWithInsurance: [],
        };
      }

      const owned = myRentalsResponse.data.ownedRentals.map((rental) =>
        createRentalWithRealMetadata(rental, metadataMap)
      );

      const rented = myRentalsResponse.data.rentedChickens.map((rental) =>
        createRentalWithRealMetadata(rental, metadataMap)
      );

      // Use filtered claimable expired rentals instead of all expired rentals
      const expired = (claimableExpiredRentals.data || []).map((rental) =>
        createRentalWithRealMetadata(rental, metadataMap)
      );

      return {
        ownedRentals: owned,
        rentedChickens: rented,
        expiredRentalsWithInsurance: expired,
      };
    }, [myRentalsResponse?.data, metadataMap, claimableExpiredRentals.data]);

  // Further categorize owned rentals
  const ownedRentalsByStatus = useMemo(() => {
    return {
      active: ownedRentals.filter((r) => r.status === ERentalStatus.RENTED),
      available: ownedRentals.filter(
        (r) => r.status === ERentalStatus.AVAILABLE
      ),
      expired: ownedRentals.filter((r) => r.status === ERentalStatus.EXPIRED),
      cancelled: ownedRentals.filter(
        (r) => r.status === ERentalStatus.CANCELLED
      ),
    };
  }, [ownedRentals]);

  // Categorize rented chickens
  const rentedChickensByStatus = useMemo(() => {
    return {
      active: rentedChickens.filter((r) => r.status === ERentalStatus.RENTED),
      expired: rentedChickens.filter((r) => r.status === ERentalStatus.EXPIRED),
    };
  }, [rentedChickens]);

  // Handle cancel rental with proper contract interaction
  const handleCancelRental = async (rentalId: number) => {
    try {
      // Use the cancel delegation hook which handles both API call and contract interaction
      const result = await executeCancelDelegation(rentalId);

      if (result?.success) {
        return true;
      }
      return false;
    } catch {
      return false;
    }
  };

  // Calculate statistics
  const stats = useMemo(() => {
    const totalEarnings = ownedRentalsByStatus.active.reduce((sum, rental) => {
      return sum + parseFloat(rental.roninPrice) / 1e18;
    }, 0);

    const totalSpent = rentedChickensByStatus.active.reduce((sum, rental) => {
      return sum + parseFloat(rental.roninPrice) / 1e18;
    }, 0);

    return {
      totalOwnedRentals: ownedRentals.length,
      activeOwnedRentals: ownedRentalsByStatus.active.length,
      availableListings: ownedRentalsByStatus.available.length,
      totalRentedChickens: rentedChickens.length,
      activeRentedChickens: rentedChickensByStatus.active.length,
      totalEarnings,
      totalSpent,
      netProfit: totalEarnings - totalSpent,
    };
  }, [
    ownedRentals,
    rentedChickens,
    ownedRentalsByStatus,
    rentedChickensByStatus,
  ]);

  // Combined loading state
  const isLoading =
    isLoadingRentals || isLoadingMetadata || claimableExpiredRentals.isLoading;

  return {
    // Data
    allRentals: allRentalsWithMetadata,
    ownedRentals,
    rentedChickens,
    expiredRentalsWithInsurance,
    ownedRentalsByStatus,
    rentedChickensByStatus,
    stats,

    // Loading states
    isLoading,
    isCancelling,

    // Error states
    error: error || metadataError || claimableExpiredRentals.error,

    // Actions
    refetch,
    cancelRental: handleCancelRental,
  };
}
