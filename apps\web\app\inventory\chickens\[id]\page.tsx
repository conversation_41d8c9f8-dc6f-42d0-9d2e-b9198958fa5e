import CardsLoaders from "@/components/shared/cards-loaders";
import AppNavbar from "@/components/shared/navbar";
import AppInitializer from "@/providers/app/initializer";
import dynamic from "next/dynamic";

const DynamicChickenDetails = dynamic(
  () => import("@/components/pages/inventory/chicken-details"),
  {
    ssr: true,
    loading: () => <CardsLoaders />,
  }
);

export default async function ChickenDetailsPage() {
  return (
    <AppInitializer>
      <div className="min-h-screen relative bg-gradient-to-b from-stone-900 via-black to-stone-900 bg-opacity-85 overflow-hidden">
        <div className="relative z-10">
          <AppNavbar />
        </div>

        <div className="container mx-auto px-4 py-8">
          <DynamicChickenDetails />
        </div>
      </div>
    </AppInitializer>
  );
}
