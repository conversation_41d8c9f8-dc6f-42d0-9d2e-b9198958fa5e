"use client";

import { useMemo } from "react";
import useBlockchain from "@/lib/hooks/useBlockchain";
import useEggInfo from "../../breeding/tab/hatching/hooks/useEggInfo";
import { isEggByTokenId } from "@/lib/utils/chicken-attributes";
import {
  isEggInBreedingPhase,
  isEggInHatchingPhase,
  isEggReadyToHatch,
  isEggHatched,
  getBreedingPhaseRemainingTime,
  getHatchingPhaseRemainingTime,
} from "../../breeding/tab/hatching/utils/egg.utils";

export default function useEggTokenInfo(tokenId: string | null) {
  const { blockchainQuery } = useBlockchain();
  const tokenIdNumber = tokenId ? Number(tokenId) : null;

  // Check if the token is an egg based on its ID
  const isEgg = useMemo(() => {
    if (!tokenIdNumber || !blockchainQuery.data) return false;
    return isEggByTokenId(
      tokenIdNumber,
      blockchainQuery.data.chicken_legacy_threshold
    );
  }, [tokenIdNumber, blockchainQuery.data]);

  // Fetch egg info if it's an egg
  const { eggInfoMap } = useEggInfo(
    isEgg && tokenIdNumber ? [tokenIdNumber] : []
  );

  // Get the egg info for this specific token
  const eggInfo = useMemo(() => {
    if (!isEgg || !tokenIdNumber || !eggInfoMap) return null;
    return eggInfoMap[tokenIdNumber];
  }, [isEgg, tokenIdNumber, eggInfoMap]);

  // Determine egg phase and status
  const eggStatus = useMemo(() => {
    if (!eggInfo) return null;

    const inBreedingPhase = isEggInBreedingPhase(eggInfo);
    const inHatchingPhase = isEggInHatchingPhase(eggInfo);
    const readyToHatch = isEggReadyToHatch(eggInfo);
    const hatched = isEggHatched(eggInfo);

    let phase = "Unknown";
    if (hatched) {
      phase = "Hatched";
    } else if (readyToHatch) {
      phase = "Ready to Hatch";
    } else if (inHatchingPhase) {
      phase = "Hatching Phase";
    } else if (inBreedingPhase) {
      phase = "Breeding Phase";
    }

    // Get time remaining and progress for the current phase
    const timeInfo = inBreedingPhase
      ? getBreedingPhaseRemainingTime(eggInfo)
      : getHatchingPhaseRemainingTime(eggInfo);

    return {
      phase,
      inBreedingPhase,
      inHatchingPhase,
      readyToHatch,
      hatched,
      timeRemaining: timeInfo.remainingSeconds,
      progress: timeInfo.progress,
      parentLeft: eggInfo.chicken_left_token_id,
      parentRight: eggInfo.chicken_right_token_id,
      generation: eggInfo.generation,
      hatchedAt: new Date(eggInfo.hatched_at),
      breedingEndTime: new Date(eggInfo.parent_breeding_state_time * 1000),
      createdAt: new Date(eggInfo.created_at),
      hatched_time: eggInfo.hatched_time,
    };
  }, [eggInfo]);

  return {
    isEgg,
    eggInfo,
    eggStatus,
    isLoading:
      blockchainQuery.isLoading ||
      (isEgg && !eggInfo && tokenIdNumber !== null),
  };
}
