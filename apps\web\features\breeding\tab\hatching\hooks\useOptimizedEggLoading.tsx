"use client";

import { useMemo, useState, useCallback } from "react";
import { useStateContext } from "@/providers/app/state";
import { useChickenTokenIds } from "@/features/chickens/hooks/useChickenTokenIds";
import { useQuery } from "@tanstack/react-query";
import axios from "@/lib/api";
import { IEggInfo } from "../types/egg-info.types";
import { isEggInBreedingPhase, isEggInHatchingPhase } from "../utils/egg.utils";
import useChickenMetadataBatch from "../../breeding/hooks/useChickenMetadataBatch";

// Options for egg loading
interface IOptimizedEggOptions {
  pageSize: number;
  filterPhase: "all" | "breeding" | "hatching";
  sortBy: "tokenId" | "hatchTime";
  sortOrder: "asc" | "desc";
}

const DEFAULT_EGG_OPTIONS: IOptimizedEggOptions = {
  pageSize: 20,
  filterPhase: "all",
  sortBy: "tokenId",
  sortOrder: "asc",
};

/**
 * Optimized hook for egg loading that follows the new pattern:
 * 1. Load token IDs from contracts (Genesis/Legacy/Ordinary) - lightweight
 * 2. Send all token IDs to /ninuno-rewards/view-chicken-info API
 * 3. Filter out those that are not eggs (based on API response)
 * 4. Separate eggs into breeding phase and hatching phase
 * 5. Apply pagination and filtering
 */
export function useOptimizedEggLoading(
  searchQuery: string = "",
  options: Partial<IOptimizedEggOptions> = {}
) {
  const { address, isConnected } = useStateContext();
  const mergedOptions = { ...DEFAULT_EGG_OPTIONS, ...options };
  const [currentPage, setCurrentPage] = useState(1);

  // Step 1: Load token IDs from contracts
  const {
    tokenIdsByType,
    isLoading: tokenIdsLoading,
    error: tokenIdsError,
  } = useChickenTokenIds(isConnected ? address : undefined);

  // Step 2: Get all token IDs (Genesis + Legacy + Ordinary)
  const allTokenIds = useMemo(() => {
    const { genesis, legacy, ordinary } = tokenIdsByType;
    return [...genesis, ...legacy, ...ordinary].sort((a, b) => a - b);
  }, [tokenIdsByType]);

  // Step 3: Send all token IDs to ninuno-rewards API to get chicken info
  const ninunoRewardsQuery = useQuery({
    queryKey: ["ninunoRewards", "allChickens", allTokenIds],
    queryFn: async () => {
      if (!allTokenIds.length) return [];
      
      const { data } = await axios.post("/ninuno-rewards/view-chicken-info", {
        chickenTokenIds: allTokenIds,
      });
      return data.data as IEggInfo[];
    },
    enabled: allTokenIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Step 4: Filter out eggs from the API response
  const allEggTokenIds = useMemo(() => {
    if (!ninunoRewardsQuery.data) return [];
    
    // The API returns data for all chickens, but we only want eggs
    // Eggs are identified by having chicken_left_token_id and chicken_right_token_id
    return ninunoRewardsQuery.data
      .filter((item) => 
        item.chicken_left_token_id && 
        item.chicken_right_token_id && 
        item.is_hatched === 0 // Only unhatched eggs
      )
      .map((egg) => egg.token_id);
  }, [ninunoRewardsQuery.data]);

  // Step 5: Create egg info map for efficient lookup
  const eggInfoMap = useMemo(() => {
    if (!ninunoRewardsQuery.data || !allEggTokenIds.length) return {};
    
    return allEggTokenIds.reduce((map, tokenId) => {
      const eggInfo = ninunoRewardsQuery.data.find(
        (item) => item.token_id === tokenId
      );
      if (eggInfo) {
        map[tokenId] = eggInfo;
      }
      return map;
    }, {} as Record<number, IEggInfo>);
  }, [ninunoRewardsQuery.data, allEggTokenIds]);

  // Step 6: Separate eggs into breeding and hatching phases
  const { breedingPhaseEggs, hatchingPhaseEggs } = useMemo(() => {
    const breeding: number[] = [];
    const hatching: number[] = [];

    allEggTokenIds.forEach((tokenId) => {
      const eggInfo = eggInfoMap[tokenId];
      if (!eggInfo) return;

      if (isEggInBreedingPhase(eggInfo)) {
        breeding.push(tokenId);
      } else if (isEggInHatchingPhase(eggInfo)) {
        hatching.push(tokenId);
      }
    });

    return {
      breedingPhaseEggs: breeding,
      hatchingPhaseEggs: hatching,
    };
  }, [allEggTokenIds, eggInfoMap]);

  // Step 7: Apply phase filter
  const filteredEggTokenIds = useMemo(() => {
    switch (mergedOptions.filterPhase) {
      case "breeding":
        return breedingPhaseEggs;
      case "hatching":
        return hatchingPhaseEggs;
      case "all":
      default:
        return [...breedingPhaseEggs, ...hatchingPhaseEggs];
    }
  }, [breedingPhaseEggs, hatchingPhaseEggs, mergedOptions.filterPhase]);

  // Step 8: Apply search filter
  const searchFilteredEggTokenIds = useMemo(() => {
    if (!searchQuery.trim()) {
      return filteredEggTokenIds;
    }

    const query = searchQuery.toLowerCase();
    return filteredEggTokenIds.filter((tokenId) =>
      tokenId.toString().includes(query)
    );
  }, [filteredEggTokenIds, searchQuery]);

  // Step 9: Apply sorting
  const sortedEggTokenIds = useMemo(() => {
    const sorted = [...searchFilteredEggTokenIds];
    
    if (mergedOptions.sortBy === "tokenId") {
      sorted.sort((a, b) => 
        mergedOptions.sortOrder === "asc" ? a - b : b - a
      );
    } else if (mergedOptions.sortBy === "hatchTime") {
      sorted.sort((a, b) => {
        const eggA = eggInfoMap[a];
        const eggB = eggInfoMap[b];
        if (!eggA || !eggB) return 0;
        
        const timeA = new Date(eggA.hatched_at).getTime();
        const timeB = new Date(eggB.hatched_at).getTime();
        
        return mergedOptions.sortOrder === "asc" ? timeA - timeB : timeB - timeA;
      });
    }
    
    return sorted;
  }, [searchFilteredEggTokenIds, mergedOptions.sortBy, mergedOptions.sortOrder, eggInfoMap]);

  // Step 10: Calculate pagination
  const totalCount = sortedEggTokenIds.length;
  const totalPages = Math.ceil(totalCount / mergedOptions.pageSize);
  const hasMore = currentPage < totalPages;

  // Step 11: Get current page egg token IDs
  const currentPageEggTokenIds = useMemo(() => {
    const startIndex = (currentPage - 1) * mergedOptions.pageSize;
    const endIndex = startIndex + mergedOptions.pageSize;
    return sortedEggTokenIds.slice(startIndex, endIndex);
  }, [sortedEggTokenIds, currentPage, mergedOptions.pageSize]);

  // Step 12: Load metadata only for current page eggs
  const metadataQuery = useChickenMetadataBatch(currentPageEggTokenIds);

  // Pagination functions
  const goToNextPage = useCallback(() => {
    if (hasMore) {
      setCurrentPage((prev) => prev + 1);
    }
  }, [hasMore]);

  const goToPreviousPage = useCallback(() => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  }, [currentPage]);

  const goToPage = useCallback(
    (page: number) => {
      if (page >= 1 && page <= totalPages) {
        setCurrentPage(page);
      }
    },
    [totalPages]
  );

  const resetPage = useCallback(() => {
    setCurrentPage(1);
  }, []);

  // Loading states
  const isLoading =
    tokenIdsLoading ||
    ninunoRewardsQuery.isLoading ||
    metadataQuery.isLoading;

  // Error states
  const error =
    tokenIdsError ||
    ninunoRewardsQuery.error ||
    metadataQuery.error;

  // Stats for display
  const stats = useMemo(() => {
    return {
      total: allEggTokenIds.length,
      breeding: breedingPhaseEggs.length,
      hatching: hatchingPhaseEggs.length,
      filtered: sortedEggTokenIds.length,
    };
  }, [allEggTokenIds.length, breedingPhaseEggs.length, hatchingPhaseEggs.length, sortedEggTokenIds.length]);

  return {
    // Data
    allEggTokenIds,
    breedingPhaseEggs,
    hatchingPhaseEggs,
    currentPageEggTokenIds,
    eggInfoMap,
    metadataMap: metadataQuery.metadataMap || {},

    // Pagination
    currentPage,
    totalPages,
    totalCount,
    hasMore,
    goToNextPage,
    goToPreviousPage,
    goToPage,
    resetPage,

    // Loading and error states
    isLoading,
    error,
    isConnected,
    address,

    // Stats
    stats,

    // Filter options
    filterPhase: mergedOptions.filterPhase,
    sortBy: mergedOptions.sortBy,
    sortOrder: mergedOptions.sortOrder,

    // Raw queries for debugging
    ninunoRewardsQuery,
    metadataQuery,
  };
}
