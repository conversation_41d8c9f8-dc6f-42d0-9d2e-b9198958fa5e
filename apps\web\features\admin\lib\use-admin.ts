"use client";

import { useQuery } from "@tanstack/react-query";
import api from "@/lib/api";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface IUserResponse {
  status: number;
  data: {
    id: number;
    blockchainAddress: string;
    nonce: string;
    is_admin: boolean;
    created_at: string;
    updated_at: string;
  };
}

const fetchUserData = async (): Promise<IUserResponse> => {
  const response = await api.get("/me");
  return response.data;
};

export const useAdmin = (redirectIfNotAdmin = true) => {
  const router = useRouter();
  
  const { data, isLoading, error } = useQuery({
    queryKey: ["userData"],
    queryFn: fetchUserData,
    retry: 1,
  });

  const isAdmin = data?.data?.is_admin || false;

  useEffect(() => {
    // If not loading anymore and either there's an error or user is not admin
    if (!isLoading && redirectIfNotAdmin && (!data || !isAdmin)) {
      router.push("/");
    }
  }, [isLoading, data, isAdmin, router, redirectIfNotAdmin]);

  return {
    isAdmin,
    isLoading,
    userData: data?.data,
    error,
  };
};
