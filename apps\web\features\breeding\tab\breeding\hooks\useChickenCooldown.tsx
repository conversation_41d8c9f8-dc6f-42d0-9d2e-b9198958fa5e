"use client";

import { useQuery } from "@tanstack/react-query";
import React from "react";
import { Address } from "viem";
import { readContract } from "@/lib/blockchain/contractReader";
import { breedingAbi } from "@/providers/web3/abi/breeding-abi";
import useBlockchain from "@/lib/hooks/useBlockchain";

/**
 * Hook to fetch the breeding cooldown times from the breeding contract
 * @param breedingAddress - Address of the breeding contract
 * @param tokenIds - Array of chicken token IDs to get cooldown times for
 * @returns Query result with cooldown times
 */
export function useChickenCooldown(
  breedingAddress: Address | undefined,
  tokenIds: number[] | undefined
) {
  const fetchCooldownTimes = async () => {
    if (!breedingAddress || !tokenIds || tokenIds.length === 0) {
      return [];
    }

    try {
      // Convert tokenIds to BigInt array for contract call
      const bigIntTokenIds = tokenIds.map((id) => BigInt(id));

      // Call the contract function to get cooldown times
      const result = await readContract<bigint[]>({
        address: breedingAddress,
        abi: breedingAbi,
        functionName: "getChickenBreedTimeBatch",
        args: [bigIntTokenIds],
      });

      // Convert BigInt results to numbers (timestamps)
      return result.map((time) => Number(time));
    } catch (error) {
      console.error("Error fetching chicken cooldown times:", error);
      throw error;
    }
  };

  return useQuery({
    queryKey: [
      "readContract",
      "chickenCooldownTimes",
      breedingAddress,
      tokenIds,
    ],
    queryFn: fetchCooldownTimes,
    enabled: !!breedingAddress && !!tokenIds && tokenIds.length > 0,
  });
}

/**
 * Convenience hook that gets the blockchain config first, then fetches cooldown times
 * @param tokenIds - Array of chicken token IDs to get cooldown times for
 * @returns Query result with cooldown times and a mapping of tokenId to cooldown time
 */
export function useChickenCooldownWithConfig(tokenIds: number[]) {
  const { blockchainQuery } = useBlockchain();
  const breedingAddress = blockchainQuery.data?.breeding_address as
    | Address
    | undefined;

  const cooldownQuery = useChickenCooldown(breedingAddress, tokenIds);

  // Create a mapping of tokenId to cooldown time for easier lookup
  const cooldownMap = React.useMemo(() => {
    if (!tokenIds || !cooldownQuery.data) {
      return {};
    }

    return tokenIds.reduce(
      (map, tokenId, index) => {
        map[tokenId] = cooldownQuery.data[index] || 0;
        return map;
      },
      {} as Record<number, number>
    );
  }, [tokenIds, cooldownQuery.data]);

  // Check if a chicken is on cooldown
  const isOnCooldown = React.useCallback(
    (tokenId: number) => {
      const cooldownTime = cooldownMap[tokenId];
      if (!cooldownTime) return false;

      // If cooldown time is in the future, chicken is on cooldown
      return cooldownTime * 1000 > Date.now();
    },
    [cooldownMap]
  );

  // Get remaining cooldown time in seconds
  const getRemainingCooldown = React.useCallback(
    (tokenId: number) => {
      const cooldownTime = cooldownMap[tokenId];
      if (!cooldownTime) return 0;

      const remainingTime = cooldownTime * 1000 - Date.now();
      return remainingTime > 0 ? Math.floor(remainingTime / 1000) : 0;
    },
    [cooldownMap]
  );

  return {
    cooldownQuery,
    cooldownMap,
    isOnCooldown,
    getRemainingCooldown,
    isLoading: blockchainQuery.isLoading || cooldownQuery.isLoading,
    isError: blockchainQuery.isError || cooldownQuery.isError,
    error: blockchainQuery.error || cooldownQuery.error,
  };
}
