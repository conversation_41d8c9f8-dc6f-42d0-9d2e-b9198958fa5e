import { DailyFeathersData, LegendaryFeathersDropResult } from "../types";

// Define the probability distributions for each legendary count
interface ProbabilityDistribution {
  0: number; // Probability of getting 0 items
  1: number; // Probability of getting 1 item
  2: number; // Probability of getting 2 items
  3: number; // Probability of getting 3 items
}

export class LegendaryFeathersDropRandomizer {
  // Probabilities for each legendary count (as percentages)
  private readonly probabilities: Record<
    0 | 1 | 2 | 3,
    ProbabilityDistribution
  > = {
    0: { 0: 100, 1: 0, 2: 0, 3: 0 }, // Legendary Count 0 (guaranteed 0 items)
    1: { 0: 78, 1: 21, 2: 2, 3: 0 }, // Legendary Count 1
    2: { 0: 59, 1: 27, 2: 10, 3: 4 }, // Legendary Count 2
    3: { 0: 40, 1: 33, 2: 19, 3: 8 }, // Legendary Count 3
  };

  /**
   * Determines the number of items dropped based on legendary count and probability distribution.
   * @param legendaryCount The number of legendary items (0, 1, 2, or 3)
   * @returns The number of items dropped (0 to 3)
   */
  private getItemDrop(legendaryCount: 0 | 1 | 2 | 3): number {
    // Get the probability distribution for the given legendary count
    const distribution = this.probabilities[legendaryCount];

    // Generate a random number between 0 and 100
    const random = Math.random() * 100;

    // Calculate cumulative probabilities
    let cumulative = 0;
    for (let items = 0; items <= 3; items++) {
      cumulative += distribution[items as keyof ProbabilityDistribution];
      if (random <= cumulative) {
        return items;
      }
    }

    // Fallback (shouldn't hit this due to probabilities summing to 100)
    return 0;
  }

  /**
   * Processes an array of DailyFeathersData and determines item drops based on legendaryCount.
   * If legendaryCount is undefined, it defaults to 0.
   * @param dataArray Array of DailyFeathersData objects
   * @returns Array of ItemDropResult objects with the number of items dropped for each entry
   */
  public calculateDropsForArray(
    dataArray: DailyFeathersData[]
  ): LegendaryFeathersDropResult[] {
    return dataArray.map((data) => {
      // Default to 0 if legendaryCount is undefined
      const count = data.legendaryCount ?? 0;
      // Ensure the count is within valid range (0-3), otherwise default to 0
      const validCount =
        count >= 0 && count <= 3 ? (count as 0 | 1 | 2 | 3) : 0;
      const itemsDropped = this.getItemDrop(validCount);
      return { ...data, itemsDropped };
    });
  }
}