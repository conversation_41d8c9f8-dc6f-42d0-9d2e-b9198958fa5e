import { Context } from "hono";
import ME from "../models/me";
import <PERSON>ary<PERSON><PERSON><PERSON><PERSON><PERSON> from "../models/legendary-feathers";
import LegendaryFeatherClaim from "../models/legWithdraw";
import WithdrawRequest from "../models/withdraw";
import { ResponseHelper, sendResponse } from "../utils/response-helper";
import { getRedisStore } from "../services/redis-store";

const redisStore = getRedisStore();

export const requestWithdraw = async (c: Context): Promise<Response> => {
  try {
    const user = c.get("user");
    const address = user.address;
    const addrLower = address.toLowerCase();

    const lockAcquired = await redisStore.acquireLock(addrLower);

    if (!lockAcquired) {
      return sendResponse(
        c,
        ResponseHelper.badRequest("Multiple request detected in short time.")
      );
    }

    const userFound = await ME.findOne({ address: addrLower });
    const userLegFound = await LegendaryFeatherMe.findOne({
      address: addrLower,
    });
    const withdrawReq = await WithdrawRequest.find({
      address: addrLower,
      distributed: false,
    });

    const withdrawLegReq = await WithdrawRequest.find({
      address: addrLower,
      distributed: false,
    });
    if (!userFound) {
      return sendResponse(c, ResponseHelper.notFound("User not found!"));
    }

    if (withdrawReq.length != 0 || withdrawLegReq.length != 0) {
      return sendResponse(
        c,
        ResponseHelper.badRequest("User has a pending claim request!")
      );
    }

    const userLegFeathers = userLegFound ? userLegFound.legendaryFeathers : 0;

    if (userFound.feathers < 1 && userLegFeathers < 1) {
      return sendResponse(
        c,
        ResponseHelper.badRequest(
          "User doesn't have enough feathers or legendary feathers to claim!"
        )
      );
    }

    const newReq = new WithdrawRequest({
      address: addrLower,
      feathers: userFound.feathers,
    });

    if (userLegFeathers && userLegFound) {
      const newLegReq = new LegendaryFeatherClaim({
        address: addrLower,
        legendaryFeathers: userLegFeathers,
      });
      userLegFound.legendaryFeathers = 0;
      await userLegFound.save();
      await newLegReq.save();
    }

    await newReq.save();

    userFound.feathers = 0;
    await userFound.save();
    await redisStore.releaseLock(addrLower);

    return sendResponse(
      c,
      ResponseHelper.created(
        { ...newReq, legendaryFeathers: userLegFeathers },
        "Withdrawal request submitted."
      )
    );
  } catch (error) {
    const knownError = error as Error;
    return sendResponse(c, ResponseHelper.serverError(knownError.message));
  }
};
