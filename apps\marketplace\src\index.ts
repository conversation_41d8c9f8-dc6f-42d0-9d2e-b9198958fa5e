import "dotenv/config";
import { serve } from "@hono/node-server";
import { Hono } from "hono";

import { Cron } from "croner";
import { RecordListed } from "./functions/recordListed";
import { MongoDbConnection } from "./services/mongoConnection";
import { env } from "./utils/env";
import { startTransferEventListener } from "./listener/transfer";

function initCronJob() {
  new Cron(
    "*/3 * * * *",
    async () => {
      try {
        await RecordListed();
      } catch (error) {
        console.error("Error in cron job:", error);
      }
    },
    { protect: true }
  );
}

(async () => {
  const app = new Hono();
  const mongodb = new MongoDbConnection();

  await mongodb.connect(env.MONGODB_URI);

  app.get("/", (c) => c.text("Welcome to Sabong Saga!"));

  initCronJob();
  startTransferEventListener()

  serve({ fetch: app.fetch, port: 3004 });
})();
