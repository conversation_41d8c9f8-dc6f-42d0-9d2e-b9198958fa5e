"use client";

import Box from "@/components/shared/box";
import CardsLoaders from "@/components/shared/cards-loaders";
import { Grid, Tooltip } from "@/components/ui";
import { useStateContext } from "@/providers/app/state";
import useAuthStore from "@/store/auth";
import { cn } from "@/utils/classes";
import { IconCircleQuestionmarkFill } from "justd-icons";
import { useCallback, useMemo, useState } from "react";
import { toast } from "sonner";

// Reusable GlowEffect Component
const GlowEffect = () => (
  <>
    <div
      className="absolute inset-0"
      style={{
        filter: "blur(30px)",
        background:
          "radial-gradient(circle, rgba(255,165,0,0.5) 0%, rgba(255,215,0,0.3) 50%, transparent 100%)",
        transform: "scale(1.2)",
        zIndex: -1,
      }}
    />
    <div
      className="absolute inset-0"
      style={{
        filter: "blur(15px)",
        background:
          "radial-gradient(circle, rgba(255,140,0,0.4) 0%, rgba(255,200,0,0.2) 60%, transparent 100%)",
        transform: "scale(1.1)",
        zIndex: -1,
      }}
    />
  </>
);

// Reusable ClaimButton Component
const ClaimButton = ({
  onClick,
  loading,
  disabled,
}: {
  onClick: () => void;
  loading: boolean;
  disabled: boolean;
}) => (
  <button
    onClick={onClick}
    className={cn(
      "w-full md:w-fit px-8 py-3 text-lg font-bold text-black uppercase rounded-lg bg-yellow-400 hover:bg-yellow-500 transition-all duration-300 transform hover:scale-105",
      disabled || loading
        ? "opacity-60 forced-colors:disabled:text-[GrayText] cursor-not-allowed pointer-events-none"
        : ""
    )}
    style={{
      boxShadow:
        "0 0 15px rgba(247, 203, 66, 0.7), 0 0 30px rgba(247, 203, 66, 0.4)",
      textShadow: "0 0 2px rgba(0,0,0,0.2)",
    }}
  >
    {loading ? "Claiming" : "CLAIM"}
  </button>
);

export default function ClaimContent() {
  const { ableToClaim, claimableFeathers, getMe, legendaryClaimableFeathers } =
    useAuthStore();
  const [loading, setIsLoading] = useState(false);
  const { loading: loadingWallet, isConnected } = useStateContext();

  const feather = useMemo(() => {
    return Number(claimableFeathers);
  }, [claimableFeathers]);

  const legendaryFeather = useMemo(() => {
    return Number(legendaryClaimableFeathers);
  }, [legendaryClaimableFeathers]);

  const claim = useCallback(async () => {
    try {
      setIsLoading(true);

      const response = await fetch("/csrf-token");

      if (!response.ok) {
        throw new Error(`API call failed: ${response.statusText}`);
      }

      const { csrfToken } = await response.json();
      const res = await fetch("/api/claim", {
        method: "POST",
        headers: {
          "X-CSRF-Token": csrfToken,
          "Content-Type": "application/json",
        },
      });

      if (res.ok) {
        toast.success("Claim request submitted", {
          description: `You have successfully submitted your claim request. Please wait for a while, and we will send it to your registered wallet.`,
          position: "top-center",
        });
      } else {
        throw Error();
      }
    } catch (error) {
      toast.error(
        "Something went wrong with your claim, please try again later.",
        {
          position: "top-center",
        }
      );
    } finally {
      await getMe();
      setIsLoading(false);
    }
  }, []);
  

  // Loading state
  if (loadingWallet.value) {
    return <CardsLoaders />;
  }

  // Not authenticated state
  if (!isConnected) {
    return (
      <Grid.Item className="relative h-full font-Poppins">
        <Box>
          <Header />
          <FeathersImage />
          <Message feathers={feather} legendaryFeathers={legendaryFeather} />
          <div className="flex w-full items-center justify-center">
            <ClaimButton onClick={() => {}} loading={loading} disabled={true} />
          </div>
        </Box>
        {/* Blur overlay for unauthenticated users */}
        <div className="absolute inset-0 backdrop-blur-md bg-black/50 z-20 rounded-2xl h-full">
          <div className="flex items-center justify-center h-full">
            <p className="text-white text-xl">Please login to get started!</p>
          </div>
        </div>
      </Grid.Item>
    );
  }

  // Authenticated state
  return (
    <Grid.Item className="backdrop-blur-sm bg-opacity-10 h-full">
      <Box>
        <Header />
        <FeathersImage />
        <Message feathers={feather} legendaryFeathers={legendaryFeather} />
        <div className="flex w-full items-center justify-center">
          <ClaimButton
            onClick={claim}
            loading={loading}
            disabled={(feather === 0 && legendaryFeather === 0) || !ableToClaim}
          />
        </div>
      </Box>
    </Grid.Item>
  );
}

// Reusable Header Component
const Header = () => (
  <div className="flex w-full mt-4 gap-2 items-center justify-center">
    <span className="font-bold font-Arcadia text-primary text-2xl">
      Claim Feathers
    </span>
    <Tooltip delay={0}>
      <Tooltip.Trigger aria-label="Claim">
        <IconCircleQuestionmarkFill className="text-primary" />
      </Tooltip.Trigger>
      <Tooltip.Content>You can claim Feathers once daily</Tooltip.Content>
    </Tooltip>
  </div>
);

// Reusable FeathersImage Component
const FeathersImage = () => (
  <div className="flex flex-col items-center justify-center px-4">
    <div className="relative">
      <GlowEffect />
      <img
        src="/images/feathers.png"
        className="h-[300px] w-auto relative"
        aria-label="Feathers logo"
        style={{
          filter:
            "drop-shadow(0 0 15px rgba(255,165,0,0.6)) drop-shadow(0 0 30px rgba(255,215,0,0.4))",
          animation: "pulse 2s infinite",
        }}
      />
    </div>
  </div>
);

// Reusable Message Component
const Message = ({
  feathers,
  legendaryFeathers,
}: {
  feathers: number;
  legendaryFeathers: number;
}) => {
  return (
    <div className="flex flex-col items-center w-full">
      <p className="font-bold text-lg">Claim your rewards!</p>
      {feathers === 0 && legendaryFeathers === 0 ? (
        <p className="text-sm w-full md:w-[80%] text-center text-muted-fg">
          Nothing to claim! You’ve already claimed for today—check your wallet
          and try again tomorrow.
        </p>
      ) : (
        <p className="text-sm w-full md:w-[80%] text-center text-muted-fg">
          You have{" "}
          {feathers > 0 && legendaryFeathers > 0 ? (
            <span className="font-bold text-white">
              {feathers} {feathers > 1 ? "Feathers" : "Feather"}{" "}
              <span className="font-bold text-yellow-400">
                and {legendaryFeathers} Legendary{" "}
                {legendaryFeathers > 1 ? "Feathers" : "Feather"}
              </span>
            </span>
          ) : feathers > 0 ? (
            <span className="font-bold text-white">
              {feathers} {feathers > 1 ? "Feathers" : "Feather"}
            </span>
          ) : (
            <span className="font-bold text-yellow-400">
              {legendaryFeathers} Legendary{" "}
              {legendaryFeathers > 1 ? "Feathers" : "Feather"}
            </span>
          )}{" "}
          ready to claim. Click below to collect your rewards!
        </p>
      )}
    </div>
  );
};
