"use client";

import React, { useEffect, useState } from "react";
import { cn } from "ui";

interface ICooldownTimerProps {
  remainingSeconds: number;
  className?: string;
  onComplete?: () => void;
}

export function CooldownTimer({
  remainingSeconds,
  className,
  onComplete,
}: ICooldownTimerProps) {
  const [timeLeft, setTimeLeft] = useState(remainingSeconds);

  useEffect(() => {
    setTimeLeft(remainingSeconds);
  }, [remainingSeconds]);

  useEffect(() => {
    if (timeLeft <= 0) {
      if (onComplete) onComplete();
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        const newTime = prev - 1;
        if (newTime <= 0 && onComplete) {
          onComplete();
        }
        return newTime > 0 ? newTime : 0;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft, onComplete]);

  // Format time as days, hours, minutes, seconds
  const formatTime = () => {
    if (timeLeft <= 0) return "Ready";

    const days = Math.floor(timeLeft / (24 * 60 * 60));
    const hours = Math.floor((timeLeft % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((timeLeft % (60 * 60)) / 60);
    const seconds = timeLeft % 60;

    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <div className={cn("text-xs font-medium", className)}>
      {formatTime()}
    </div>
  );
}
