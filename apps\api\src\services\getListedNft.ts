import axios from "axios";
import { env } from "../env";
import { Address } from "viem";

interface Order {
  addedAt: number;
  orderStatus: "OPEN" | "CLOSED" | "CANCELLED";
}

type ResponseData = {
  erc721Tokens: {
    total: number;
    results: TokenData[];
  };
};

interface TokenData {
  tokenAddress: string;
  tokenId: string;
  slug: string;
  owner: string;
  name: string;
  order: Order;
}

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

async function getListedNft(
  address: Address,
  from: number = 0,
  size: number = 50,
  contractAddress: Address
): Promise<ResponseData> {
  const graphqlEndpoint = env.MARKETPLACE_GRAPHQL_ENDPOINT;
  const data = {
    operationName: "GetERC721TokensList",
    variables: {
      from,
      auctionType: "Sale",
      owner: address,
      size,
      sort: "PriceAsc",
      rangeCriteria: [],
      tokenAddress: contractAddress,
    },
    query: `query GetERC721TokensList($tokenAddress: String, $slug: String, $owner: String, $auctionType: AuctionType, $criteria: [SearchCriteria!], $from: Int!, $size: Int!, $sort: SortBy, $name: String, $priceRange: InputRange, $rangeCriteria: [RangeSearchCriteria!], $excludeAddress: String) {
      erc721Tokens(
        tokenAddress: $tokenAddress
        slug: $slug
        owner: $owner
        auctionType: $auctionType
        criteria: $criteria
        from: $from
        size: $size
        sort: $sort
        name: $name
        priceRange: $priceRange
        rangeCriteria: $rangeCriteria
        excludeAddress: $excludeAddress
      ) {
        total
        results {
          tokenAddress
          tokenId
          slug
          owner
          name
          order {
            addedAt
            orderStatus
          }
        }
      }
    }`,
  };

  const headers = {
    "Content-Type": "application/json",
    "x-api-key": env.SKYMAVIS_API,
  };

  const response = await axios.post<{ data: ResponseData }>(
    graphqlEndpoint,
    data,
    { headers }
  );
  return response.data.data;
}

export async function listedNft(address: Address) {
  const legacyAddress = env.LEGACY_CONTRACT as Address;
  const genesisAddress = env.CHICKEN_CONTRACT as Address;
  const contractAddresses = [genesisAddress, legacyAddress];
  const openOrders: {
    id: string;
    orderStatus: string;
    tokenAddress: string;
  }[] = [];
  const DELAY_BETWEEN_REQUESTS = 1000; // 1 second delay

  try {
    // Process each contract address
    for (const contractAddress of contractAddresses) {
      let from = 0;
      const size = 50;

      while (true) {
        const fetchNft = await getListedNft(
          address,
          from,
          size,
          contractAddress
        );

        // Process current batch
        fetchNft.erc721Tokens.results.forEach((item) => {
          if (item?.order?.orderStatus === "OPEN") {
            openOrders.push({
              id: item.tokenId,
              orderStatus: item.order.orderStatus,
              tokenAddress: item.tokenAddress,
            });
          }
        });

        // If we got less results than requested, we're done with this contract
        if (fetchNft.erc721Tokens.results.length < size) {
          break;
        }

        // Update from for next batch
        from += size;

        // Add delay before next request
        await delay(DELAY_BETWEEN_REQUESTS);
      }

      // Add delay between processing different contracts
      await delay(DELAY_BETWEEN_REQUESTS);
    }

    return openOrders;
  } catch (error) {
    console.error("Error fetching NFTs:", error);
    throw error;
  }
}
