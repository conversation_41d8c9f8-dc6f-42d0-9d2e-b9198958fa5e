import { SIWEResponse } from "@/types/api.types";
import { Address } from "viem";
import { createSiweMessage, CreateSiweMessageReturnType } from "viem/siwe";

export const prepareSiwe = async (
  address: Address
): Promise<CreateSiweMessageReturnType | null> => {
  try {
    const response = await fetch(`/api/nonce/${address}`);
    if (!response.ok) throw new Error("Failed to fetch nonce");

    const result = (await response.json()) as SIWEResponse;

    return createSiweMessage({
      domain: window.location.hostname,
      address,
      uri: window.location.origin,
      ...result.data,
    });
  } catch (error) {
    return null;
  }
};

// Extract SIWE issuedAt from message
export const extractIssuedAt = (siweMessage: string): string => {
  const issuedAt = siweMessage.match(/Issued At: (.+)/)?.[1];
  if (!issuedAt)
    throw new Error("Failed to extract issuedAt from SIWE message");
  return issuedAt;
};
