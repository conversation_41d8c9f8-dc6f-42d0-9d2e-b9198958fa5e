"use client";

import { Button, type ButtonProps } from "@/components/ui";
import Loading from ".";

interface ILoadingButtonProps extends ButtonProps {
  isLoading?: boolean;
  loadingText?: string;
  children: React.ReactNode;
}

export function LoadingButton({
  isLoading = false,
  loadingText = "Please wait",
  children,
  ...props
}: ILoadingButtonProps) {
  if (isLoading) {
    return (
      <Button isDisabled {...props}>
        <Loading className="mr-2" />
        {loadingText}
      </Button>
    );
  }

  return <Button {...props}>{children}</Button>;
}
