"use client";

import React from "react";
import { Button } from "ui";
import { EViewMode } from "../types/inventory.types";
import { cn } from "@/utils/classes";

interface IViewToggleProps {
  viewMode: EViewMode;
  onViewModeChange: (mode: EViewMode) => void;
  className?: string;
}

/**
 * ViewToggle Component
 *
 * Allows users to switch between aggregated and per-chicken views
 */
export const ViewToggle: React.FC<IViewToggleProps> = ({
  viewMode,
  onViewModeChange,
  className,
}) => {
  return (
    <div
      className={cn(
        "flex items-center gap-1 bg-stone-800/60 rounded-lg p-1.5 border border-stone-600/40",
        className
      )}
    >
      <Button
        size="small"
        appearance={viewMode === EViewMode.AGGREGATED ? "solid" : "plain"}
        onPress={() => onViewModeChange(EViewMode.AGGREGATED)}
        className={cn(
          "text-sm font-medium px-4 py-2 rounded transition-all",
          viewMode === EViewMode.AGGREGATED
            ? "bg-amber-600 text-amber-50 shadow-md"
            : "text-stone-300 hover:text-amber-200 hover:bg-stone-700/50"
        )}
      >
        📊 Aggregated
      </Button>
      <Button
        size="small"
        appearance={viewMode === EViewMode.PER_CHICKEN ? "solid" : "plain"}
        onPress={() => onViewModeChange(EViewMode.PER_CHICKEN)}
        className={cn(
          "text-sm font-medium px-4 py-2 rounded transition-all",
          viewMode === EViewMode.PER_CHICKEN
            ? "bg-amber-600 text-amber-50 shadow-md"
            : "text-stone-300 hover:text-amber-200 hover:bg-stone-700/50"
        )}
      >
        🐔 Per Chicken
      </Button>
    </div>
  );
};
