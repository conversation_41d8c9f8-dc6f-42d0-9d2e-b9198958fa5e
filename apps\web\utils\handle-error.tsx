import { ConnectorErrorType } from "@sky-mavis/tanto-connect";
import { toast } from "sonner";
import { UserRejectedRequestError, ContractFunctionExecutionError } from "viem";

export const handleError = (error: unknown) => {
  if (error instanceof UserRejectedRequestError) {
    toast.error("Transaction rejected", {
      description: "You rejected the transaction in your wallet",
      position: "top-right",
    });
    throw error;
  }
  if (error instanceof ContractFunctionExecutionError) {
    toast.error(error.name, {
      description: error.shortMessage,
      position: "top-right",
    });
    throw error;
  }

  // Handle specific error types
  if (error instanceof Error) {
    if (error.name === ConnectorErrorType.PROVIDER_NOT_FOUND) {
      window.open("https://wallet.roninchain.com", "_blank");
      toast.error("Wallet not found", {
        description: "Please install Ronin Wallet to continue",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("user rejected")) {
      toast.error("Transaction rejected", {
        description: "You rejected the transaction in your wallet",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("insufficient funds")) {
      toast.error("Insufficient funds", {
        description: "You don't have enough funds to complete this transaction",
        position: "top-right",
      });
      throw error;
    }
  } else {
    const err = error as Error;

    // Fallback for non-Error objects
    toast.error(err.name, {
      description: err.message,
      position: "top-right",
    });
    throw err;
  }
};
