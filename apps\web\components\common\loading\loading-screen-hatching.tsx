"use client";

import { cn } from "@/components/ui";
import LoadingDots from "./loading-dots";

interface ILoadingScreenProps {
  message?: string;
  className?: string;
}

export default function LoadingScreenHatching({
  message = "Hatching your egg",
  className = "",
}: ILoadingScreenProps) {
  return (
    <div
      className={cn(
        "fixed inset-0 flex items-center justify-center bg-black/70 backdrop-blur-sm px-4",
        className
      )}
    >
      <div className="">
        <div className="size-[375px] lg:size-[500px]">
          <img
            src="/images/hatching/anim_egg_nest_hatch_01.gif"
            alt="Egg Hatching"
            className="max-w-full h-auto"
          />
        </div>
        <div className="flex justify-center items-center">
          <div className="font-bold font-xl">{message}</div>
          <LoadingDots dots="." />
        </div>
      </div>
    </div>
  );
}
