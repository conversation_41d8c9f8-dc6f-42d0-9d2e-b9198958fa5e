[{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ErrArrayLengthMismatch", "type": "error"}, {"inputs": [], "name": "ErrInsufficientBalance", "type": "error"}, {"inputs": [], "name": "ErrInvalidAmount", "type": "error"}, {"inputs": [], "name": "ErrInvalidCollection", "type": "error"}, {"inputs": [], "name": "ErrInvalidItemConfig", "type": "error"}, {"inputs": [], "name": "ErrInvalidItemType", "type": "error"}, {"inputs": [], "name": "ErrInvalidNonce", "type": "error"}, {"inputs": [], "name": "ErrInvalidSignature", "type": "error"}, {"inputs": [], "name": "ErrInvalidStatChoice", "type": "error"}, {"inputs": [], "name": "ErrItemNotActive", "type": "error"}, {"inputs": [], "name": "ErrMaxStatsReached", "type": "error"}, {"inputs": [], "name": "ErrNoActiveBoostedStats", "type": "error"}, {"inputs": [], "name": "ErrNotAuthorized", "type": "error"}, {"inputs": [], "name": "ErrSignatureExpired", "type": "error"}, {"inputs": [], "name": "ErrStatAlreadyBoosted", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "ErrUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum SabongSagaDailyFeedV2.CollectionType", "name": "collectionType", "type": "uint8"}, {"indexed": true, "internalType": "uint256", "name": "nftTokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256[]", "name": "feedItemIds", "type": "uint256[]"}, {"indexed": false, "internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"indexed": false, "internalType": "uint256", "name": "totalAffectionPoints", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "feeder", "type": "address"}], "name": "BatchFeedCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256[]", "name": "itemIds", "type": "uint256[]"}, {"indexed": false, "internalType": "enum SabongSagaDailyFeedV2.ItemType[]", "name": "itemTypes", "type": "uint8[]"}], "name": "BatchFeedItemsConfigured", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "itemId", "type": "uint256"}, {"indexed": false, "internalType": "enum SabongSagaDailyFeedV2.ItemType", "name": "itemType", "type": "uint8"}], "name": "FeedItemConfigured", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum SabongSagaDailyFeedV2.CollectionType", "name": "collectionType", "type": "uint8"}, {"indexed": true, "internalType": "uint256", "name": "nftTokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "expiryTime", "type": "uint256"}], "name": "ImmortalityGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldValue", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newValue", "type": "uint256"}], "name": "MaxActiveBoostsUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum SabongSagaDailyFeedV2.CollectionType", "name": "collectionType", "type": "uint8"}, {"indexed": true, "internalType": "uint256", "name": "nftTokenId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "feedItemId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "affectionPoints", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "feeder", "type": "address"}], "name": "NFTFed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum SabongSagaDailyFeedV2.CollectionType", "name": "collectionType", "type": "uint8"}, {"indexed": true, "internalType": "uint256", "name": "nftTokenId", "type": "uint256"}, {"indexed": false, "internalType": "enum SabongSagaDailyFeedV2.StatType", "name": "statType", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "expiryTime", "type": "uint256"}], "name": "StatBoosted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum SabongSagaDailyFeedV2.CollectionType", "name": "collectionType", "type": "uint8"}, {"indexed": true, "internalType": "uint256", "name": "nftTokenId", "type": "uint256"}, {"indexed": false, "internalType": "enum SabongSagaDailyFeedV2.StatType", "name": "statType", "type": "uint8"}], "name": "StatDebuffed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum SabongSagaDailyFeedV2.CollectionType", "name": "collectionType", "type": "uint8"}, {"indexed": true, "internalType": "uint256", "name": "nftTokenId", "type": "uint256"}, {"indexed": false, "internalType": "enum SabongSagaDailyFeedV2.StatType", "name": "selectedStat", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "expiryTime", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "feedCount", "type": "uint256"}], "name": "UltimateCookieUsed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "CHICKENS", "outputs": [{"internalType": "contract IERC721", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "CONFIG_SETTER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "GENESIS", "outputs": [{"internalType": "contract IERC721", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PAUSER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "RESOURCES", "outputs": [{"internalType": "contract IERC1155Mintable", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "itemIds", "type": "uint256[]"}, {"internalType": "enum SabongSagaDailyFeedV2.ItemType[]", "name": "itemTypes", "type": "uint8[]"}, {"components": [{"internalType": "enum SabongSagaDailyFeedV2.StatType", "name": "statType", "type": "uint8"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "uint128", "name": "duration", "type": "uint128"}], "internalType": "struct SabongSagaDailyFeedV2.StatBoost[][]", "name": "statBoostsArray", "type": "tuple[][]"}, {"internalType": "uint128[]", "name": "immortalDurations", "type": "uint128[]"}, {"internalType": "enum SabongSagaDailyFeedV2.DebuffType[]", "name": "debuffTypes", "type": "uint8[]"}], "name": "batchConfigureFeedItems", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "itemIds", "type": "uint256[]"}], "name": "batchDisableFeedItems", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "enum SabongSagaDailyFeedV2.CollectionType", "name": "collectionType", "type": "uint8"}, {"internalType": "uint256", "name": "nftTokenId", "type": "uint256"}, {"internalType": "uint256[]", "name": "feedItemIds", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256", "name": "totalAffectionPoints", "type": "uint256"}, {"internalType": "enum SabongSagaDailyFeedV2.StatType[]", "name": "debuffStatChoices", "type": "uint8[]"}], "internalType": "struct SabongSagaDailyFeedV2.BatchFeedParams", "name": "params", "type": "tuple"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "batchFeedNFT", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "itemId", "type": "uint256"}, {"internalType": "enum SabongSagaDailyFeedV2.ItemType", "name": "itemType", "type": "uint8"}, {"components": [{"internalType": "enum SabongSagaDailyFeedV2.StatType", "name": "statType", "type": "uint8"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "uint128", "name": "duration", "type": "uint128"}], "internalType": "struct SabongSagaDailyFeedV2.StatBoost[]", "name": "statBoosts", "type": "tuple[]"}, {"internalType": "uint128", "name": "immortalDuration", "type": "uint128"}, {"internalType": "enum SabongSagaDailyFeedV2.DebuffType", "name": "debuffType", "type": "uint8"}], "name": "configureFeedItem", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "itemId", "type": "uint256"}], "name": "disableFeedItem", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "enum SabongSagaDailyFeedV2.CollectionType", "name": "collectionType", "type": "uint8"}, {"internalType": "uint256", "name": "nftTokenId", "type": "uint256"}, {"internalType": "uint256", "name": "feedItemId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "affectionPoints", "type": "uint256"}, {"internalType": "enum SabongSagaDailyFeedV2.StatType", "name": "debuffStatChoice", "type": "uint8"}], "internalType": "struct SabongSagaDailyFeedV2.FeedParams", "name": "params", "type": "tuple"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "feedNFT", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "getAddressNonce", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeedV2.CollectionType", "name": "collectionType", "type": "uint8"}, {"internalType": "uint256", "name": "nftTokenId", "type": "uint256"}], "name": "getAllNFTStats", "outputs": [{"components": [{"components": [{"internalType": "enum SabongSagaDailyFeedV2.StatType", "name": "statType", "type": "uint8"}, {"internalType": "uint256", "name": "totalValue", "type": "uint256"}, {"internalType": "uint256", "name": "expiryTime", "type": "uint256"}], "internalType": "struct SabongSagaDailyFeedV2.NFTStat[]", "name": "stats", "type": "tuple[]"}, {"internalType": "bool", "name": "isImmortal", "type": "bool"}, {"internalType": "uint256", "name": "immortalExpiryTime", "type": "uint256"}], "internalType": "struct SabongSagaDailyFeedV2.NFTStatsResponse", "name": "response", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "itemId", "type": "uint256"}], "name": "getFeedItemConfig", "outputs": [{"internalType": "enum SabongSagaDailyFeedV2.ItemType", "name": "itemType", "type": "uint8"}, {"components": [{"internalType": "enum SabongSagaDailyFeedV2.StatType", "name": "statType", "type": "uint8"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "uint128", "name": "duration", "type": "uint128"}], "internalType": "struct SabongSagaDailyFeedV2.StatBoost[]", "name": "statBoosts", "type": "tuple[]"}, {"internalType": "uint256", "name": "immortalDuration", "type": "uint256"}, {"internalType": "enum SabongSagaDailyFeedV2.DebuffType", "name": "debuffType", "type": "uint8"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getMaxActiveBoosts", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeedV2.CollectionType", "name": "collectionType", "type": "uint8"}, {"internalType": "uint256", "name": "nftTokenId", "type": "uint256"}], "name": "getNFTStatus", "outputs": [{"internalType": "enum SabongSagaDailyFeedV2.StatType[]", "name": "activeBoostedStats", "type": "uint8[]"}, {"internalType": "uint256[]", "name": "boostAmounts", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "boostExpiryTimes", "type": "uint256[]"}, {"internalType": "uint256", "name": "immortalExpiryTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMembers", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeedV2.CollectionType", "name": "collectionType", "type": "uint8"}, {"internalType": "uint256", "name": "nftTokenId", "type": "uint256"}, {"internalType": "enum SabongSagaDailyFeedV2.StatType", "name": "statType", "type": "uint8"}], "name": "getStatBoost", "outputs": [{"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "expiryTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_RESOURCES", "type": "address"}, {"internalType": "address", "name": "_GENESIS", "type": "address"}, {"internalType": "address", "name": "_CHICKENS", "type": "address"}, {"internalType": "address", "name": "_SIGNER_WALLET", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeedV2.CollectionType", "name": "collectionType", "type": "uint8"}, {"internalType": "uint256", "name": "nftTokenId", "type": "uint256"}], "name": "isNFTImmortal", "outputs": [{"internalType": "bool", "name": "isImmortal", "type": "bool"}, {"internalType": "uint256", "name": "expiryTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxActiveBoosts", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newMaxActiveBoosts", "type": "uint256"}], "name": "setMaxActiveBoosts", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "updateSignerWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]