import { ArrowRightLeft, Clock, Egg, ShoppingCart } from "lucide-react";

interface ChickenOverlaysProps {
  tokenId: string;
  isFaint: boolean;
  isDead: boolean;
  isBreeding: boolean;
  isListed: boolean;
  wasTransferred: boolean;
  isImmortal: boolean;
  recoveryTimers: Record<string, number>;
  breedingTimers: Record<string, number>;
  listedTimers: Record<string, number>;
  transferTimers: Record<string, number>;
  immortalTimers: Record<string, number>;
  formatTime: (seconds: number, type: 'recovery' | 'breeding' | 'listed' | 'transfer' | 'immortal') => string;
}

export function ChickenOverlays({
  tokenId,
  isFaint,
  isDead,
  isBreeding,
  isListed,
  wasTransferred,
  isImmortal,
  recoveryTimers,
  breedingTimers,
  listedTimers,
  transferTimers,
  immortalTimers,
  formatTime,
}: ChickenOverlaysProps) {
  // Priority order: dead → faint → transferred → listed → breeding
  
  if (isDead) {
    return (
      <div className="absolute inset-0 bg-black bg-opacity-80 rounded-lg flex flex-col items-center justify-center z-20">
        <div className="text-red-600 font-bold text-lg mb-2">DEAD</div>
        <div className="text-gray-400 text-sm text-center px-2">
          This chicken has died. Your chicken&apos;s journey has ended, but the
          bond remains forever.
        </div>
      </div>
    );
  }

  if (isFaint) {
    return (
      <div className="absolute inset-0 bg-black bg-opacity-60 rounded-lg flex flex-col items-center justify-center z-20">
        <div className="text-red-400 font-bold text-lg mb-2 flex items-center gap-2">
          {isImmortal && <span className="text-yellow-400">⚡</span>}
          FAINT
        </div>
        <div className="text-white text-sm text-center px-2">
          {recoveryTimers[tokenId] && recoveryTimers[tokenId] > 0
            ? `Recovers in ${formatTime(recoveryTimers[tokenId], 'recovery')}`
            : "Ready to recover!"}
        </div>
      </div>
    );
  }

  if (wasTransferred) {
    return (
      <div className="absolute inset-0 bg-purple-900 bg-opacity-70 rounded-lg flex flex-col items-center justify-center z-20">
        <div className="text-purple-300 font-bold text-lg mb-2 flex items-center gap-2">
          <ArrowRightLeft className="h-6 w-6" />
          TRANSFERRED
        </div>
        <div className="text-white text-sm text-center px-2">
          {transferTimers[tokenId] && transferTimers[tokenId] > 0
            ? `Available in ${formatTime(transferTimers[tokenId], 'transfer')}`
            : "Available now!"}
        </div>
      </div>
    );
  }

  if (isListed) {
    return (
      <div className="absolute inset-0 bg-gradient-to-br from-orange-900/80 to-amber-900/80 backdrop-blur-sm rounded-lg flex flex-col items-center justify-center z-20 border border-orange-500/30">
        <div className="text-orange-300 font-bold text-lg mb-2 flex items-center gap-2">
          <div className="relative">
            <ShoppingCart className="h-6 w-6" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
          </div>
          LISTED
        </div>
        <div className="text-orange-100 text-sm text-center px-2">
          {listedTimers[tokenId] && listedTimers[tokenId] > 0
            ? `Available in ${formatTime(listedTimers[tokenId], 'listed')}`
            : "Available now!"}
        </div>
        <div className="text-orange-200/70 text-xs text-center px-2 mt-1">
          On Marketplace
        </div>
      </div>
    );
  }

  if (isBreeding) {
    return (
      <div className="absolute inset-0 bg-pink-900 bg-opacity-70 rounded-lg flex flex-col items-center justify-center z-20">
        <div className="text-pink-300 font-bold text-lg mb-2 flex items-center gap-2">
          <Egg className="h-6 w-6" />
          BREEDING
        </div>
        <div className="text-white text-sm text-center px-2">
          {breedingTimers[tokenId] && breedingTimers[tokenId] > 0
            ? `Completes in ${formatTime(breedingTimers[tokenId], 'breeding')}`
            : "Breeding complete!"}
        </div>
      </div>
    );
  }

  return null;
}