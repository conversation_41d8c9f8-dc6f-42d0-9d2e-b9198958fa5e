"use client";

import { Loader } from "@/components/ui/loader";
import React from "react";

export default function Loading(): React.ReactNode {
  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6">
      <div className="flex flex-col justify-center items-center py-16">
        <Loader size="large" intent="primary" variant="ring" className="mb-4" />
        <span className="text-gray-600 font-medium">
          Loading chicken data...
        </span>
        <p className="text-gray-500 text-sm mt-2">
          This may take a moment depending on the number of chickens
        </p>
      </div>
    </div>
  );
}
