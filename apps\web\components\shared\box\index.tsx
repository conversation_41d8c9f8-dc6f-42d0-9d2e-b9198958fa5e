import { cn } from "@/utils/classes";
import { memo, PropsWithChildren } from "react";

interface BoxProps extends PropsWithChildren {
  className?: string;
}

const Box = (props: BoxProps) => {
  return (
    <div
      className={cn(
        "flex flex-col rounded-2xl p-6 w-full h-full font-Poppins gap-12 relative min-h-[748px]",
        props.className
      )}
      style={{
        background: "rgba(0, 0, 0, 0.85)",
        border: "2px solid rgba(247, 203, 66, 0.3)", // Faded golden base border
      }}
    >
      {/* Bottom glowing line */}
      <div
        className="absolute bottom-0 left-[20%] w-[60%] h-[2px]"
        style={{
          background:
            "linear-gradient(90deg, transparent, #f7cb42, transparent)",
          boxShadow: "0 0 10px #f7cb42, 0 0 20px #f7cb42",
        }}
      />
      {props.children}
    </div>
  );
};

export default memo(Box);
