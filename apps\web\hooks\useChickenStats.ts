import { useCallback, useState, useRef } from "react";
import { ChickenStats } from "@/types/chicken.type";

export function useChickenStats() {
  const [chickenStats, setChickenStats] = useState<
    Record<string, ChickenStats>
  >({});
  const [statsLoading, setStatsLoading] = useState<Set<string>>(new Set());

  // Use refs to avoid dependency issues in useCallback
  const chickenStatsRef = useRef(chickenStats);
  const statsLoadingRef = useRef(statsLoading);

  // Update refs when state changes
  chickenStatsRef.current = chickenStats;
  statsLoadingRef.current = statsLoading;

  // Helper functions to check chicken states
  const isDead = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    return stats?.state === "dead";
  };

  const isFaint = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    return stats?.state === "faint";
  };

  const isBreeding = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    return stats?.state === "breeding";
  };

  const isListed = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    return stats?.state === "listed";
  };

  const wasTransferredToday = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    return stats?.wasTransferredToday === true;
  };

  const wasListedToday = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    return stats?.wasListedToday === true;
  };

  const isImmortal = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    return stats?.isImmortal === true;
  };

  // Function to fetch stats for multiple chickens in batch with error handling and retry
  const fetchChickenStatsBatch = useCallback(
    async (tokenIds: string[]) => {
      // Filter out already loaded or loading chickens using refs
      const idsToFetch = tokenIds.filter(
        (id) => !chickenStatsRef.current[id] && !statsLoadingRef.current.has(id)
      );

      if (idsToFetch.length === 0) return;

      // Mark all as loading
      setStatsLoading((prev) => new Set([...prev, ...idsToFetch]));

      // Retry logic
      const maxRetries = 3;
      let retries = 0;
      let success = false;

      while (retries < maxRetries && !success) {
        try {
          // Add timeout to prevent hanging requests
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

          const response = await fetch(
            `https://chicken-api-ivory.vercel.app/api/game/batch`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                ids: idsToFetch.map((id) => parseInt(id, 10)),
              }),
              signal: controller.signal,
            }
          );

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`API responded with status: ${response.status}`);
          }

          const data = await response.json();

          // Validate response structure
          if (!data.chickens || !Array.isArray(data.chickens)) {
            throw new Error("Invalid response format from API");
          }

          // Process the batch response
          const newStats: Record<string, ChickenStats> = {};
          const newCooldowns: Record<string, number> = {};
          const newRecoveryTimers: Record<string, number> = {};
          const newBreedingTimers: Record<string, number> = {};
          const newTransferTimers: Record<string, number> = {};
          const newListedTimers: Record<string, number> = {};
          const newImmortalTimers: Record<string, number> = {};

          data.chickens.forEach((chicken: any) => {
            if (chicken && chicken.id) {
              newStats[chicken.id] = {
                hp: chicken.stats?.currentHp ?? 100,
                maxHp: chicken.stats?.hp ?? 100,
                level: chicken.level ?? 1,
                attack: chicken.stats?.attack ?? 0,
                defense: chicken.stats?.defense ?? 0,
                speed: chicken.stats?.speed ?? 0,
                ferocity: chicken.stats?.ferocity ?? 0,
                cockrage: chicken.stats?.cockrage ?? 0,
                evasion: chicken.stats?.evasion ?? 0,
                mmr: chicken.mmr ?? 0,
                hpCooldown: chicken.hpCooldown ?? 0,
                regenRate: chicken.regenRate ?? 1,
                state: chicken.state ?? "normal",
                recoverDate: chicken.recoverDate,
                breedingTime: chicken.breedingTime,
                wasTransferredToday: chicken.wasTransferredToday ?? false,
                wasListedToday: chicken.wasListedToday ?? false,
                isImmortal: chicken.isImmortal ?? false,
                boosters: chicken.boosters || {},
              };

              // Set initial cooldown timer if hpCooldown exists
              if (chicken.hpCooldown && chicken.hpCooldown > 0) {
                newCooldowns[chicken.id] = Math.round(chicken.hpCooldown * 60); // Convert minutes to seconds
              }

              // Set initial recovery timer if chicken is faint and has recovery date
              if (chicken.state === "faint" && chicken.recoverDate) {
                const now = new Date();
                const recoverDate = new Date(chicken.recoverDate);
                const timeRemaining = recoverDate.getTime() - now.getTime();

                if (timeRemaining > 0) {
                  newRecoveryTimers[chicken.id] = Math.ceil(
                    timeRemaining / 1000
                  );
                }
              }

              // Set initial breeding timer if chicken is breeding and has breeding time
              if (chicken.state === "breeding" && chicken.breedingTime) {
                const now = Date.now();
                const breedingEndTime = chicken.breedingTime * 1000;
                const timeRemaining = breedingEndTime - now;

                if (timeRemaining > 0) {
                  newBreedingTimers[chicken.id] = Math.ceil(
                    timeRemaining / 1000
                  );
                }
              }

              // Set initial transfer timer if chicken was transferred today
              if (chicken.wasTransferredToday) {
                // Calculate time until next UTC midnight (00:00:00 UTC)
                const now = new Date();
                const nextMidnight = new Date();
                nextMidnight.setUTCDate(nextMidnight.getUTCDate() + 1);
                nextMidnight.setUTCHours(0, 0, 0, 0);

                const timeRemaining = nextMidnight.getTime() - now.getTime();
                if (timeRemaining > 0) {
                  newTransferTimers[chicken.id] = Math.ceil(
                    timeRemaining / 1000
                  );
                }
              }

              // Set initial listed timer if chicken was listed today
              if (chicken.wasListedToday) {
                // Calculate time until next UTC midnight (00:00:00 UTC)
                const now = new Date();
                const nextMidnight = new Date();
                nextMidnight.setUTCDate(nextMidnight.getUTCDate() + 1);
                nextMidnight.setUTCHours(0, 0, 0, 0);

                const timeRemaining = nextMidnight.getTime() - now.getTime();
                if (timeRemaining > 0) {
                  newListedTimers[chicken.id] = Math.ceil(timeRemaining / 1000);
                }
              }

              // Set initial listed timer if chicken was listed today
              if (chicken.wasListedToday) {
                // Calculate time until next UTC midnight (00:00:00 UTC)
                const now = new Date();
                const nextMidnight = new Date();
                nextMidnight.setUTCDate(nextMidnight.getUTCDate() + 1);
                nextMidnight.setUTCHours(0, 0, 0, 0);

                const timeRemaining = nextMidnight.getTime() - now.getTime();
                if (timeRemaining > 0) {
                  newListedTimers[chicken.id] = Math.ceil(timeRemaining / 1000);
                }
              }

              // Set initial immortal timer if chicken is immortal
              if (chicken.isImmortal) {
                // Calculate time until next UTC midnight (00:00:00 UTC)
                const now = new Date();
                const nextMidnight = new Date();
                nextMidnight.setUTCDate(nextMidnight.getUTCDate() + 1);
                nextMidnight.setUTCHours(0, 0, 0, 0);

                const timeRemaining = nextMidnight.getTime() - now.getTime();
                if (timeRemaining > 0) {
                  newImmortalTimers[chicken.id] = Math.ceil(
                    timeRemaining / 1000
                  );
                }
              }
            }
          });

          setChickenStats((prev) => ({
            ...prev,
            ...newStats,
          }));

          success = true;

          // Return the timer data so it can be used by the calling component
          return {
            newCooldowns,
            newRecoveryTimers,
            newBreedingTimers,
            newTransferTimers,
            newListedTimers,
            newImmortalTimers,
          };
        } catch (error) {
          retries++;
          console.error(
            `Error fetching stats for chickens (attempt ${retries}/${maxRetries}):`,
            error
          );

          // Only wait before retrying if we're going to retry
          if (retries < maxRetries) {
            await new Promise((resolve) => setTimeout(resolve, 1000 * retries)); // Exponential backoff
          }
        }
      }

      // If all retries failed, set default stats
      if (!success) {
        const defaultStats: Record<string, ChickenStats> = {};
        idsToFetch.forEach((id) => {
          defaultStats[id] = {
            hp: 100,
            maxHp: 100,
            level: 1,
            attack: 0,
            defense: 0,
            speed: 0,
            ferocity: 0,
            cockrage: 0,
            evasion: 0,
            mmr: 0,
            hpCooldown: 0,
            regenRate: 1,
            state: "normal",
            boosters: {},
          };
        });

        setChickenStats((prev) => ({
          ...prev,
          ...defaultStats,
        }));
      }

      // Remove from loading set regardless of success/failure
      setStatsLoading((prev) => {
        const newSet = new Set(prev);
        idsToFetch.forEach((id) => newSet.delete(id));
        return newSet;
      });

      return null;
    },
    [] // Remove dependencies to prevent infinite loops
  );

  // Function to refetch single chicken stats using batch API
  const refetchChickenStats = async (tokenId: string) => {
    if (!tokenId) return;

    // Use batch API even for single chicken to maintain consistency
    const response = await fetch(
      `https://chicken-api-ivory.vercel.app/api/game/batch`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ids: [parseInt(tokenId, 10)],
        }),
      }
    );
    if (!response.ok) {
      throw new Error(`Failed to fetch game stats: ${response.status}`);
    }
    const batchData = await response.json();
    const statsData = batchData.chickens?.[0] || {
      wins: 0,
      losses: 0,
      draws: 0,
      level: 1,
      state: "normal",
      stats: {},
    };

    setChickenStats((prev) => {
      const prevChicken = prev[tokenId];
      const newStats: Record<string, ChickenStats> = {};
      newStats[tokenId] = {
        hp: statsData.stats?.currentHp || 100,
        maxHp: prevChicken?.maxHp ?? 100,
        level: prevChicken?.level ?? 1,
        attack: prevChicken?.attack ?? 0,
        defense: prevChicken?.defense ?? 0,
        speed: prevChicken?.speed ?? 0,
        ferocity: prevChicken?.ferocity ?? 0,
        cockrage: prevChicken?.cockrage ?? 0,
        evasion: prevChicken?.evasion ?? 0,
        mmr: prevChicken?.mmr ?? 0,
        hpCooldown: prevChicken?.hpCooldown ?? 0,
        regenRate: prevChicken?.regenRate ?? 1,
        state: prevChicken?.state ?? "normal",
        recoverDate: prevChicken?.recoverDate,
        boosters: prevChicken?.boosters || {},
      };
      return {
        ...prev,
        ...newStats,
      };
    });
  };

  return {
    chickenStats,
    statsLoading,
    setChickenStats,
    setStatsLoading,
    fetchChickenStatsBatch,
    refetchChickenStats,
    isDead,
    isFaint,
    isBreeding,
    isListed,
    wasTransferredToday,
    wasListedToday,
    isImmortal,
  };
}
