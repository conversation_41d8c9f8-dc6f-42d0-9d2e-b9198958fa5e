"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";
import { chain } from "@/providers/web3/web3-provider";
import { rentalAbi } from "@/providers/web3/abi/rental-abi";
import { IBulkCreateRentalResponse } from "../types/delegation.types";
import { useChickenApproval } from "./useChickenApproval";

/**
 * Hook for handling bulk chicken listing blockchain transactions
 * Processes multiple chickens in a single smart contract call
 */
export const useListChickenForRentBulk = () => {
  const { publicClient, walletClient, address, Disconnect } = useStateContext();
  const { blockchainQuery } = useBlockchain();
  const queryClient = useQueryClient();
  const { executeApproval, checkApprovalStatus } = useChickenApproval();

  const [isListing, setIsListing] = useState(false);

  // Get rental contract address from blockchain config
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  /**
   * Execute bulk blockchain listing after API creates rental records
   */
  const executeBulkBlockchainListing = async (
    bulkApiResponse: IBulkCreateRentalResponse
  ) => {
    try {
      if (!address || !walletClient) {
        toast.error("Cannot list chickens", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return { success: false, error: "Wallet not connected" };
      }

      if (!rentalAddress) {
        toast.error("Cannot list chickens", {
          description: "Rental contract not configured",
          position: "top-right",
        });
        return { success: false, error: "Rental contract not configured" };
      }

      // Filter successful rentals from API response
      const successfulRentals = bulkApiResponse.data.filter(
        (result) => result.success && result.data
      );

      if (successfulRentals.length === 0) {
        return {
          success: false,
          error: "No successful rentals to process on blockchain",
        };
      }

      setIsListing(true);

      // Prepare arrays for smart contract call
      const chickenIds: bigint[] = [];
      const rentIds: bigint[] = [];
      const ethPrices: bigint[] = [];
      const insurancePrices: bigint[] = [];
      const rentDurations: bigint[] = [];
      const signatures: `0x${string}`[] = [];

      // Process each successful rental
      for (const result of successfulRentals) {
        if (!result.data) continue;

        const rentalData = result.data;

        chickenIds.push(BigInt(rentalData.chickenTokenId));
        rentIds.push(BigInt(rentalData.rentalId));
        ethPrices.push(BigInt(rentalData.roninPrice));
        insurancePrices.push(
          rentalData.insurancePrice ? BigInt(rentalData.insurancePrice) : 0n
        );
        rentDurations.push(BigInt(rentalData.rentalPeriod));

        // The signature should be provided by the API response
        // Check if signature exists in the rental data
        if (rentalData.signature) {
          signatures.push(rentalData.signature as `0x${string}`);
        } else {
          throw new Error(
            `Missing signature for chicken #${rentalData.chickenTokenId}`
          );
        }
      }

      toast.info("Preparing blockchain transaction...", {
        description: "Checking approvals and estimating gas",
        position: "top-center",
      });

      // Check NFT approvals for all chicken contracts
      // For bulk operations, we'll check approval for the first chicken and assume others need the same
      // This is a simplified approach - in production you might want to check each chicken individually
      const firstChickenId = chickenIds[0] ? Number(chickenIds[0]) : 0;
      if (firstChickenId > 0) {
        const approvalNeeded = !(await checkApprovalStatus(firstChickenId));
        if (approvalNeeded) {
          toast.info("NFT approval required", {
            description:
              "Please approve the rental contract to manage your chickens",
            position: "top-center",
          });

          const approvalResult = await executeApproval(firstChickenId);
          if (!approvalResult.success) {
            throw new Error("NFT approval failed");
          }
        }
      }

      // Simulate the bulk transaction
      toast.info("Simulating transaction...", {
        description: "Validating bulk listing parameters",
        position: "top-center",
      });

      await publicClient.simulateContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "listChickenForRentBulk",
        args: [
          chickenIds,
          rentIds,
          ethPrices,
          insurancePrices,
          rentDurations,
          signatures,
        ],
        chain,
        account: address,
      });

      // Estimate gas for the transaction
      const gasEstimate = await publicClient.estimateContractGas({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "listChickenForRentBulk",
        args: [
          chickenIds,
          rentIds,
          ethPrices,
          insurancePrices,
          rentDurations,
          signatures,
        ],
        account: address,
      });

      // Execute the bulk listing transaction
      toast.info("Listing chickens on blockchain...", {
        description: `Please confirm the transaction for ${successfulRentals.length} chickens`,
        position: "top-center",
      });

      const hash = await walletClient.writeContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "listChickenForRentBulk",
        args: [
          chickenIds,
          rentIds,
          ethPrices,
          insurancePrices,
          rentDurations,
          signatures,
        ],
        gas: gasEstimate + BigInt(100000), // Add buffer for bulk operation
        chain,
        account: address,
      });

      // Wait for transaction confirmation
      toast.info("Confirming bulk transaction...", {
        description: "Waiting for blockchain confirmation",
        position: "top-center",
      });

      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        confirmations: 1,
      });

      if (receipt.status === "success") {
        toast.success("Bulk listing successful!", {
          description: `${successfulRentals.length} chickens listed on blockchain`,
          position: "top-center",
        });

        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ["rentals"] });
        queryClient.invalidateQueries({ queryKey: ["myRentals"] });
        queryClient.invalidateQueries({ queryKey: ["chickensForDelegation"] });

        return {
          success: true,
          hash,
          receipt,
          processedCount: successfulRentals.length,
        };
      } else {
        throw new Error("Transaction failed");
      }
    } catch (error: any) {
      console.error("Bulk blockchain listing error:", error);

      let errorMessage = "Failed to list chickens on blockchain";
      let errorDescription = "Please try again";

      if (error?.message?.includes("User rejected")) {
        errorMessage = "Transaction cancelled";
        errorDescription = "You cancelled the transaction";
      } else if (error?.message?.includes("insufficient funds")) {
        errorMessage = "Insufficient funds";
        errorDescription = "You don't have enough RON for gas fees";
      } else if (error?.message) {
        errorMessage = "Blockchain error";
        errorDescription = error.message;
      }

      toast.error(errorMessage, {
        description: errorDescription,
        position: "top-center",
      });

      return { success: false, error: errorMessage };
    } finally {
      setIsListing(false);
    }
  };

  // Mutation for React Query integration
  const bulkListChickenMutation = useMutation({
    mutationFn: executeBulkBlockchainListing,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Bulk list chicken mutation error:", error);
    },
  });

  return {
    executeBulkBlockchainListing,
    bulkListChickenMutation,
    isListing,
    rentalAddress,
    // Approval functions for external use
    executeApproval,
    checkApprovalStatus,
  };
};
