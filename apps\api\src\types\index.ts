import { MetadataAttribute, NFTMetadata } from "./nfts";

export * from "./token-transfer";

export interface SignMessage {
  version: string;
  chainId: number;
  nonce: string;
  statement: string;
  domain: string;
  address: string;
  uri: string;
}

export interface NonceData {
  nonce: string;
  timestamp: number;
}

export interface AuthConfig {
  jwtSecret: string;
  refreshJwtSecret: string;
  signMessage?: SignMessage;
  expiresIn?: number;
  redisUrl?: string;
  refreshTokenExpiresIn?: number;
}

export interface JWTPayload {
  address: string;
  exp: number;
}

export interface ApiResponse<T> {
  status: boolean;
  responseCode: number;
  data?: T;
  message: string;
  errors?: string[] | Record<string, string[]>;
}

export interface TokenPayload {
  address: string;
  exp: number;
  tokenType: "access" | "refresh";
}

export class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number,
    public errors?: string[] | Record<string, string[]>
  ) {
    super(message);
    this.name = "AppError";
  }
}

export interface DailyFeathersData {
  tokenId: string;
  image?: string;
  dailyFeathers?: number;
  legendaryCount?: number;
  renterAddress?: string;
  ownerAddress?: string;
  delegatedTask?: DelegatedTaskType;
  rewardDistribution?: RewardDistributionType;
  sharedRewardAmount?: number;
  rubStreakBenefactor?: RubStreakBenefactorType;
}

export enum RewardDistributionType {
  DELEGATOR_ONLY = 1,
  DELEGATEE_ONLY = 2,
  SHARED = 3,
}

export enum DelegatedTaskType {
  DAILY_RUB = 1,
  GAMEPLAY = 2,
  BOTH = 3,
}

export enum RubStreakBenefactorType {
  DELEGATOR = 1,
  DELEGATEE = 2,
}

export interface RentedNFT {
  delegatedTask: DelegatedTaskType;
  rewardDistribution: RewardDistributionType;
  sharedRewardAmount: number;
  renterAddress: string;
  ownerAddress: string;
  tokenId: string;
  image?: string;
  dailyFeathers?: number;
  legendaryCount?: number;
  rubStreakBenefactor?: RubStreakBenefactorType;
}

// Interface for result data (extends input with item drop result)
export interface LegendaryFeathersDropResult extends DailyFeathersData {
  itemsDropped: number;
}

export interface BatchMetadataResult {
  id: number;
  data: NFTMetadata;
  status: number;
}

export interface BatchMetadataResponse {
  results: BatchMetadataResult[];
  metadata: {
    processed: number;
    stateUpdates: number;
  };
}

// Helper type for specific attribute values
export type AttributeValue<T extends string> = {
  trait_type: T;
  value: any;
  display_type: string;
};

// Specific attribute types for better type safety
export type BreedingAttribute = AttributeValue<"Breeding"> & { value: boolean };
export type StateAttribute = AttributeValue<"State"> & { value: string };
export type LegendaryCountAttribute = AttributeValue<"Legendary Count"> & {
  value: number;
};
export type DailyFeathersAttribute = AttributeValue<"Daily Feathers"> & {
  value: number;
};

// Union type for all possible attribute types
export type KnownAttributes =
  | BreedingAttribute
  | StateAttribute
  | LegendaryCountAttribute
  | DailyFeathersAttribute
  | AttributeValue<"Feet">
  | AttributeValue<"Tail">
  | AttributeValue<"Body">
  | AttributeValue<"Wings">
  | AttributeValue<"Eyes">
  | AttributeValue<"Beak">
  | AttributeValue<"Comb">
  | AttributeValue<"Generation">
  | AttributeValue<"Gender">
  | AttributeValue<"Instinct">
  | AttributeValue<"Color">
  | AttributeValue<"Type">
  | AttributeValue<"Level">
  | AttributeValue<"Genes">
  | AttributeValue<"Birthdate">
  | AttributeValue<"Grit Attack">
  | AttributeValue<"Grit Defense">
  | AttributeValue<"Grit Speed">
  | AttributeValue<"Grit Health">
  | AttributeValue<"Innate Attack">
  | AttributeValue<"Innate Defense">
  | AttributeValue<"Innate Speed">
  | AttributeValue<"Innate Health">
  | AttributeValue<"Parent 1">
  | AttributeValue<"Parent 2">
  | AttributeValue<"Breed Count">;
