"use client";

import { IChickenMetadata } from "@/lib/types/chicken.types";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useMemo } from "react";

export const CHICKEN_API = process.env.NEXT_PUBLIC_CHICKEN_API_URL || "";

const fetchChickenMetadata = async (tokenId: number) => {
  const { data } = await axios.get(`${CHICKEN_API}/${tokenId}`);
  return data as IChickenMetadata;
};

// Interface for batch API response
interface IBatchApiResponse {
  results: Array<{
    id: number;
    data: IChickenMetadata;
    status: number;
  }>;
  metadata: {
    processed: number;
    stateUpdates: number;
  };
}

/**
 * Fetch metadata for multiple chickens using the batch API
 */
const fetchBatchChickenMetadata = async (
  tokenIds: number[]
): Promise<IChickenMetadata[]> => {
  if (!CHICKEN_API) {
    throw new Error("Chicken API URL not configured");
  }

  if (!tokenIds || tokenIds.length === 0) {
    return [];
  }

  try {
    const { data } = await axios.post(`${CHICKEN_API}/batch`, {
      ids: tokenIds,
    });

    const batchResponse = data as IBatchApiResponse;

    // Extract the metadata from the results array and maintain order
    const metadataMap = new Map<number, IChickenMetadata>();

    // Process successful responses
    batchResponse.results.forEach((result) => {
      if (result.status === 200 && result.data) {
        metadataMap.set(result.id, result.data);
      }
    });

    // Return metadata in the same order as requested tokenIds
    // If a tokenId is missing from the response, we'll skip it (filter out undefined)
    const orderedMetadata = tokenIds
      .map((tokenId) => metadataMap.get(tokenId))
      .filter(
        (metadata): metadata is IChickenMetadata => metadata !== undefined
      );

    return orderedMetadata;
  } catch (error) {
    console.warn(
      "Failed to fetch batch metadata, falling back to individual calls:",
      error
    );

    // Fallback to individual calls if batch fails
    return Promise.all(tokenIds.map(fetchChickenMetadata));
  }
};

const useChickenMetadata = (tokenIds: number[]) => {
  // Memoize the tokenIds to prevent unnecessary re-renders
  const memoizedTokenIds = useMemo(() => {
    if (!tokenIds || tokenIds.length === 0) return [];
    return [...tokenIds].sort((a, b) => a - b); // Sort to ensure consistent array
  }, [tokenIds]);

  const fetchMetadatas = () => {
    if (!CHICKEN_API) {
      throw new Error("Chicken API URL not configured");
    }

    if (!memoizedTokenIds || memoizedTokenIds.length === 0) {
      return [];
    }

    return fetchBatchChickenMetadata(memoizedTokenIds);
  };

  const metadataQuery = useQuery({
    queryKey: ["chickenMetadata", memoizedTokenIds],
    queryFn: fetchMetadatas,
    enabled: !!memoizedTokenIds && memoizedTokenIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchInterval: false,
  });

  // create metadata mapping
  const metadataMap = useMemo(() => {
    if (!metadataQuery.data || !memoizedTokenIds) {
      return {};
    }

    // Create a map from the returned metadata array
    // We need to match metadata back to tokenIds using the edition field
    const map: Record<number, IChickenMetadata> = {};

    metadataQuery.data.forEach((metadata) => {
      if (metadata && metadata.edition) {
        map[metadata.edition] = metadata;
      }
    });

    return map;
  }, [metadataQuery.data, memoizedTokenIds]);

  return {
    metadataQuery,
    metadataMap,
    isLoading: metadataQuery.isLoading || metadataQuery.isFetching,
    error: metadataQuery.error,
    isError: metadataQuery.isError,
  };
};

export { fetchChickenMetadata, fetchBatchChickenMetadata };
export default useChickenMetadata;
