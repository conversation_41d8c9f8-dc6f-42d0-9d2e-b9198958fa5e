"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, TextField } from "ui";
import { Filter, X, ChevronDown } from "lucide-react";
import {
  IRentalFilters,
  ERewardDistributionType,
  EGameRewardDistributionType,
  EDelegatedTaskType,
  REWARD_DISTRIBUTION_LABELS,
  GAME_REWARD_DISTRIBUTION_LABELS,
  DELEGATED_TASK_LABELS,
  RENTAL_FILTER_DEFAULTS,
} from "../../types/delegation.types";

interface IRentalFiltersProps {
  filters: IRentalFilters;
  onFiltersChange: (filters: IRentalFilters) => void;
  onReset: () => void;
  className?: string;
}

export function RentalFilters({
  filters,
  onFiltersChange,
  onReset,
  className,
}: IRentalFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const sortOptions = [
    { value: "price", label: "Price" },
    { value: "duration", label: "Duration" },
    { value: "created", label: "Date Created" },
    { value: "feathers", label: "Daily Feathers" },
    { value: "tokenId", label: "Token ID" },
  ];

  const updateFilters = (updates: Partial<IRentalFilters>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const hasActiveFilters = () => {
    return (
      filters.priceRange.min > RENTAL_FILTER_DEFAULTS.PRICE_RANGE.MIN ||
      filters.priceRange.max !== Number.MAX_SAFE_INTEGER ||
      filters.durationRange.min > RENTAL_FILTER_DEFAULTS.DURATION_RANGE.MIN ||
      filters.durationRange.max !== Number.MAX_SAFE_INTEGER ||
      filters.featherRewardDistribution.length > 0 ||
      filters.gameRewardDistribution.length > 0 ||
      filters.delegatedTask.length > 0
    );
  };

  return (
    <div
      className={`bg-stone-800 border border-stone-700 rounded-lg p-4 ${className}`}
    >
      {/* Filter Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-gray-400" />
          <h3 className="font-semibold text-white">Filters</h3>
          {hasActiveFilters() && (
            <span className="bg-yellow-500 text-black text-xs px-2 py-1 rounded-full">
              Active
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {hasActiveFilters() && (
            <Button
              size="small"
              appearance="outline"
              onPress={onReset}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4" />
              Clear Filters
            </Button>
          )}
          <Button
            size="small"
            appearance="plain"
            onPress={() => setIsExpanded(!isExpanded)}
            className="text-gray-400 hover:text-white"
          >
            <ChevronDown
              className={`w-4 h-4 transition-transform ${isExpanded ? "rotate-180" : ""}`}
            />
          </Button>
        </div>
      </div>

      {/* Quick Sort */}
      <div className="flex items-center gap-2 mb-4">
        <Select
          placeholder="Sort by"
          selectedKey={filters.sortBy}
          onSelectionChange={(key) =>
            updateFilters({ sortBy: key as IRentalFilters["sortBy"] })
          }
          className="flex-1"
        >
          <Select.Trigger />
          <Select.List>
            {sortOptions.map((option) => (
              <Select.Option key={option.value} id={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select.List>
        </Select>
        <Button
          size="small"
          appearance="outline"
          onPress={() =>
            updateFilters({
              sortOrder: filters.sortOrder === "asc" ? "desc" : "asc",
            })
          }
          className="px-3"
        >
          {filters.sortOrder === "asc" ? "↑" : "↓"}
        </Button>
      </div>

      {/* Expanded Filters */}
      {isExpanded && (
        <div className="space-y-4 border-t border-stone-700 pt-4">
          {/* Price Range */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Daily Rate Range (RON/day)
            </label>
            <div className="flex items-center gap-2">
              <TextField
                type="number"
                placeholder="Min"
                value={filters.priceRange.min.toString()}
                onChange={(value) =>
                  updateFilters({
                    priceRange: {
                      ...filters.priceRange,
                      min: parseFloat(value) || 0,
                    },
                  })
                }
                className="flex-1"
              />
              <span className="text-gray-400">to</span>
              <TextField
                type="number"
                placeholder="Max"
                value={
                  filters.priceRange.max === Number.MAX_SAFE_INTEGER
                    ? ""
                    : filters.priceRange.max.toString()
                }
                onChange={(value) =>
                  updateFilters({
                    priceRange: {
                      ...filters.priceRange,
                      max:
                        value === ""
                          ? Number.MAX_SAFE_INTEGER
                          : parseFloat(value) || Number.MAX_SAFE_INTEGER,
                    },
                  })
                }
                className="flex-1"
              />
            </div>
          </div>

          {/* Duration Range */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Duration Range (Days)
            </label>
            <div className="flex items-center gap-2">
              <TextField
                type="number"
                placeholder="Min"
                value={filters.durationRange.min.toString()}
                onChange={(value) =>
                  updateFilters({
                    durationRange: {
                      ...filters.durationRange,
                      min: parseInt(value) || 1,
                    },
                  })
                }
                className="flex-1"
              />
              <span className="text-gray-400">to</span>
              <TextField
                type="number"
                placeholder="Max"
                value={
                  filters.durationRange.max === Number.MAX_SAFE_INTEGER
                    ? ""
                    : filters.durationRange.max.toString()
                }
                onChange={(value) =>
                  updateFilters({
                    durationRange: {
                      ...filters.durationRange,
                      max:
                        value === ""
                          ? Number.MAX_SAFE_INTEGER
                          : parseInt(value) || Number.MAX_SAFE_INTEGER,
                    },
                  })
                }
                className="flex-1"
              />
            </div>
          </div>

          {/* Daily Rub Distribution */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Daily Rub Distribution
            </label>
            <div className="flex flex-wrap gap-2">
              {Object.entries(REWARD_DISTRIBUTION_LABELS).map(
                ([key, label]) => {
                  const value = parseInt(key) as ERewardDistributionType;
                  const isSelected =
                    filters.featherRewardDistribution.includes(value);
                  return (
                    <Button
                      key={key}
                      size="small"
                      appearance={isSelected ? "solid" : "outline"}
                      intent={isSelected ? "primary" : "secondary"}
                      onPress={() => {
                        const newDistribution = isSelected
                          ? filters.featherRewardDistribution.filter(
                              (d) => d !== value
                            )
                          : [...filters.featherRewardDistribution, value];
                        updateFilters({
                          featherRewardDistribution: newDistribution,
                        });
                      }}
                    >
                      {label}
                    </Button>
                  );
                }
              )}
            </div>
          </div>

          {/* Game Reward Distribution */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Game Reward Distribution
            </label>
            <div className="flex flex-wrap gap-2">
              {Object.entries(GAME_REWARD_DISTRIBUTION_LABELS).map(
                ([key, label]) => {
                  const value = parseInt(key) as EGameRewardDistributionType;
                  const isSelected =
                    filters.gameRewardDistribution.includes(value);
                  return (
                    <Button
                      key={key}
                      size="small"
                      appearance={isSelected ? "solid" : "outline"}
                      intent={isSelected ? "primary" : "secondary"}
                      onPress={() => {
                        const newDistribution = isSelected
                          ? filters.gameRewardDistribution.filter(
                              (d) => d !== value
                            )
                          : [...filters.gameRewardDistribution, value];
                        updateFilters({
                          gameRewardDistribution: newDistribution,
                        });
                      }}
                    >
                      {label}
                    </Button>
                  );
                }
              )}
            </div>
          </div>

          {/* Delegated Task */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Delegated Activities
            </label>
            <div className="flex flex-wrap gap-2">
              {Object.entries(DELEGATED_TASK_LABELS).map(([key, label]) => {
                const value = parseInt(key) as EDelegatedTaskType;
                const isSelected = filters.delegatedTask.includes(value);
                return (
                  <Button
                    key={key}
                    size="small"
                    appearance={isSelected ? "solid" : "outline"}
                    intent={isSelected ? "primary" : "secondary"}
                    onPress={() => {
                      const newTasks = isSelected
                        ? filters.delegatedTask.filter((t) => t !== value)
                        : [...filters.delegatedTask, value];
                      updateFilters({ delegatedTask: newTasks });
                    }}
                  >
                    {label}
                  </Button>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
