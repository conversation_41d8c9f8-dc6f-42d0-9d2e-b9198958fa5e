import "dotenv/config";
import { serve } from "@hono/node-server";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { createAuthMiddleware } from "./middlewares/auth";
import { logger } from "hono/logger";
import { MongoDbConnection } from "./services/mongodb";
import { env } from "./env";
import appRoutes from "./routes";
import { startEventListener } from "./listeners/event.listener";
import { redisMiddleware } from "./middlewares/redis";
import { pinoLogger } from "hono-pino";

// function initCronJob() {
//   new Cron("*/10 * * * *", async () => {
//     try {
//       await distributeRewards();
//     } catch (error) {
//       console.error("Error in cron job:", error);
//     }
//   });
// }

(async () => {
  const app = new Hono();
  app.use(
    pinoLogger({
      pino: {
        level: "debug",
      },
    })
  );
  const mongodb = new MongoDbConnection();

  await mongodb.connect(env.MONGODB_URI);
  app.use("*", redisMiddleware);
  app.use("/api/*", cors());
  app.get("/", (c) => c.text("Welcome to Sabong Saga!"));
  app.use(logger());

  // Create auth middleware
  const auth = createAuthMiddleware({
    jwtSecret: env.JWT_SECRET,
  });

  app.route("/api", appRoutes);

  // Auth routes
  app.get("/api/auth/nonce/:address", auth.getNonce);
  app.post("/api/auth/verify", auth.verifySignature);
  app.post("/api/auth/refresh", auth.refreshToken);

  // startEventListener();
  // initCronJob();

  serve({ fetch: app.fetch, port: 3002 });
})();
