import { NextRequest, NextResponse } from "next/server";

const API_URL = process.env.NEXT_PUBLIC_BREEDING_API_URL || "";

// GET /api/admin/breeding-cooldowns
export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get("breeding_token")?.value;
    
    if (!token) {
      return NextResponse.json(
        { status: 0, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const response = await fetch(`${API_URL}/admin/breeding-cooldowns`, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching breeding cooldowns:", error);
    return NextResponse.json(
      { status: 0, message: "Internal server error" },
      { status: 500 }
    );
  }
}
