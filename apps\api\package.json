{"name": "api", "version": "0.1.0", "description": "", "main": "./dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "rm -rf dist && tsup", "start": "node ."}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@sabongsaga/eslint-config": "workspace:*", "@sabongsaga/typescript-config": "workspace:*", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.17.9", "terser": "^5.36.0", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "5.5.4"}, "dependencies": {"@hono/node-server": "^1.13.7", "@roninnetwork/rnsjs": "^0.2.3", "axios": "^1.7.9", "croner": "^9.0.0", "dotenv": "^16.4.7", "ethers": "5.6.9", "hono": "^4.6.14", "hono-pino": "^0.7.0", "ioredis": "^5.4.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.3", "pino": "^9.6.0", "redlock": "5.0.0-beta.2", "siwe": "1.1.6", "viem": "^2.21.57", "zod": "^3.24.1"}, "packageManager": "pnpm@9.0.0"}