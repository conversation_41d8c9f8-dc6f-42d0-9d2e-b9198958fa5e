"use client";

import React from "react";
import Image from "next/image";
import { X } from "lucide-react";
import useMatchmakingStore from "@/store/match-making";
import { getBattleItemById } from "@/data/battle-items";

interface BattleItemSlotProps {
  slotIndex: number;
  className?: string;
}

const BattleItemSlot: React.FC<BattleItemSlotProps> = ({
  slotIndex,
  className = "",
}) => {
  const { selectedBattleItems, clearBattleItem, setBattleItem } =
    useMatchmakingStore();
  const itemId = selectedBattleItems[slotIndex];
  const item =
    itemId !== -1 && itemId !== undefined ? getBattleItemById(itemId) : null;

  const handleClear = () => {
    clearBattleItem(slotIndex);
  };

  const handleSkip = () => {
    setBattleItem(slotIndex, -1);
  };

  return (
    <div className={`${className}`}>
      <div className="bg-gray-800 border-2 border-gray-600 rounded-lg p-3 min-h-[120px] flex flex-col">
        {/* Slot Header */}
        <div className="flex justify-between items-center mb-2">
          <span className="text-yellow-400 font-bold text-sm">
            Round {slotIndex + 1}
          </span>
          {item && (
            <button
              onClick={handleClear}
              className="text-gray-400 hover:text-white transition-colors p-1"
              title="Remove item"
            >
              <X size={16} />
            </button>
          )}
        </div>

        {/* Slot Content */}
        {item ? (
          <div className="flex-1 flex items-center space-x-3">
            <div className="w-12 h-12 bg-gray-700 rounded overflow-hidden flex-shrink-0 relative">
              <Image
                src={item.image}
                alt={item.name}
                fill
                className="object-cover"
                sizes="48px"
              />
            </div>
            <div className="flex-1 min-w-0">
              <h4
                className="text-white font-medium text-sm truncate"
                title={item.name}
              >
                {item.name}
              </h4>
              <p
                className="text-gray-400 text-xs mt-1 overflow-hidden"
                style={{
                  display: "-webkit-box",
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: "vertical" as const,
                }}
              >
                {item.description}
              </p>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex flex-col items-center justify-center text-center">
            <div className="w-12 h-12 bg-gray-700 rounded border-2 border-dashed border-gray-500 flex items-center justify-center mb-2">
              <span className="text-gray-500 text-xs font-bold">?</span>
            </div>
            <button
              onClick={handleSkip}
              className="text-gray-400 hover:text-yellow-400 text-xs font-medium transition-colors"
            >
              Skip Round
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

interface BattleItemSlotsProps {
  className?: string;
}

export const BattleItemSlots: React.FC<BattleItemSlotsProps> = ({
  className = "",
}) => {
  return (
    <div className={`${className}`}>
      <div className="mb-4">
        <h3 className="text-white font-bold text-lg mb-2">Selected Items</h3>
        <p className="text-gray-400 text-sm">
          Items will be used in this order during battle. Click to remove or
          skip rounds.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {[0, 1, 2].map((slotIndex) => (
          <BattleItemSlot key={slotIndex} slotIndex={slotIndex} />
        ))}
      </div>
    </div>
  );
};
