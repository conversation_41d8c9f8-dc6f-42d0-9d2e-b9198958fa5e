"use client";

import { Modal } from "@/components/ui/modal";
import { IconCircleInfo } from "justd-icons";
import React from "react";
import { GeneDisplay } from "./gene-display";
import { IDecodedGene } from "../../types/genes.types";

interface IGeneInfoIconProps {
  genes: IDecodedGene;
}

export const GeneInfoIcon = ({ genes }: IGeneInfoIconProps) => {
  // Use a ref to store the tooltip trigger element
  const triggerRef = React.useRef<HTMLDivElement>(null);
  const [showTooltip, setShowTooltip] = React.useState(false);
  const [tooltipPosition, setTooltipPosition] = React.useState({
    top: 0,
    left: 0,
  });

  // Update tooltip position when it's shown
  React.useEffect(() => {
    if (showTooltip && triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setTooltipPosition({
        top: rect.top - 30, // Position above the icon
        left: rect.left - 50, // Position to the left of the icon
      });
    }
  }, [showTooltip]);

  return (
    <>
      <div onClick={(e: React.MouseEvent) => e.stopPropagation()}>
        <Modal>
          <Modal.Trigger>
            <div
              ref={triggerRef}
              className="size-6 bg-primary text-black hover:bg-primary/70 grid place-items-center rounded-full transition-colors cursor-pointer"
              onMouseEnter={() => setShowTooltip(true)}
              onMouseLeave={() => setShowTooltip(false)}
            >
              <IconCircleInfo className="size-5" />
            </div>
          </Modal.Trigger>
          <Modal.Content size="lg" isDismissable={true}>
            <Modal.Header>
              <Modal.Title>Chicken Genes</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <GeneDisplay genes={genes} />
            </Modal.Body>
          </Modal.Content>
        </Modal>
      </div>

      {/* Render tooltip in a portal to avoid overflow issues */}
      {showTooltip && (
        <div
          className="fixed z-50 bg-[#0F172A] text-primary rounded-lg px-3 py-1.5 text-sm shadow-md border border-primary/30"
          style={{
            top: `${tooltipPosition.top}px`,
            left: `${tooltipPosition.left}px`,
            pointerEvents: "none", // Ensure it doesn't interfere with mouse events
          }}
        >
          <div className="text-xs whitespace-nowrap font-medium">
            View chicken genes
          </div>
        </div>
      )}
    </>
  );
};
