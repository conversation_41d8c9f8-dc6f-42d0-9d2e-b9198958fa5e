"use client";

import { useQuery } from "@tanstack/react-query";
import { useStateContext } from "@/providers/app/state";
import { IGameRewardsResponse, IRewardItem } from "../types/inventory.types";
import { rewardMetadataMap } from "../mock/mock-data";

const GAME_REWARDS_API_URL = process.env.NEXT_PUBLIC_CHICKEN_API_URL
  ? `${process.env.NEXT_PUBLIC_CHICKEN_API_URL}/game/rewards`
  : "https://chicken-api-ivory.vercel.app/api/game/rewards";

/**
 * Hook to fetch game rewards from API and process them with metadata
 */
export const useGameRewards = () => {
  const { address } = useStateContext();

  const { data, isLoading, error, refetch } = useQuery<IRewardItem[]>({
    queryKey: ["gameRewards", address],
    queryFn: async (): Promise<IRewardItem[]> => {
      if (!address) {
        throw new Error("No wallet address available");
      }

      console.log("🔍 Fetching rewards for address:", address);
      const apiUrl = `${GAME_REWARDS_API_URL}?address=${address}`;
      console.log("🌐 API URL:", apiUrl);

      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error(`Failed to fetch rewards: ${response.statusText}`);
      }

      const apiData: IGameRewardsResponse = await response.json();
      console.log("📦 Raw API response:", apiData);

      if (!apiData.success) {
        throw new Error("API returned unsuccessful response");
      }

      // Extract all rewards from all chickens
      const allRewards = Object.values(apiData.data.rewardsByChicken).flat();
      console.log("🎁 All rewards from API:", allRewards);

      // Filter only unclaimed rewards
      const unclaimedRewards = allRewards.filter((reward) => !reward.claimed);
      console.log("🚫 Unclaimed rewards:", unclaimedRewards);

      // Group by rewardId and sum quantities (use API quantity if available, otherwise count occurrences)
      const rewardQuantities = new Map<number, number>();
      unclaimedRewards.forEach((reward) => {
        const currentQuantity = rewardQuantities.get(reward.rewardId) || 0;
        const rewardQuantity = reward.quantity || 1; // Use API quantity or default to 1
        rewardQuantities.set(reward.rewardId, currentQuantity + rewardQuantity);
      });
      console.log(
        "📊 Reward quantities by ID:",
        Array.from(rewardQuantities.entries())
      );

      // Combine with metadata and create final reward items
      const rewardItems: IRewardItem[] = [];

      rewardQuantities.forEach((quantity, rewardId) => {
        const metadata = rewardMetadataMap.get(rewardId);
        console.log(
          `🔍 RewardID ${rewardId}: metadata=${!!metadata}, quantity=${quantity}`
        );
        if (metadata && quantity > 0) {
          rewardItems.push({
            ...metadata,
            quantity,
          });
        }
      });

      console.log("✨ Final reward items:", rewardItems);
      return rewardItems;
    },
    enabled: !!address,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  return {
    rewardItems: data || [],
    isLoading,
    error,
    refetch,
    hasRewards: (data?.length || 0) > 0,
    address, // For debugging
  };
};
