# Suggested Commands

## Development Commands
- `pnpm dev` - Start development server for all apps
- `pnpm build` - Build all apps and packages
- `pnpm lint` - Run linting for all packages
- `pnpm lint -F web` - Run linting specifically for web app
- `pnpm format` - Format code with Prettier

## Web App Specific Commands
- `cd apps/web && pnpm dev` - Start only web app development
- `cd apps/web && pnpm build` - Build only web app
- `cd apps/web && pnpm lint:fix` - Fix linting errors (preferred over build for checking)

## Package Management
- Use **pnpm** instead of npm
- Use package managers for dependency management instead of manually editing package files

## Windows-Specific Commands
- Use `;` instead of `&&` for chaining commands in PowerShell
- Use PowerShell as the terminal environment

## Testing Commands
- Write and run tests to validate code changes
- Suggest testing after making code edits

## Git Commands
- Standard git commands for version control
- `git status` - Check current status
- `git add .` - Stage changes
- `git commit -m "message"` - Commit changes