import axios from "axios";
import { env } from "../env";
import { Address } from "viem";

export const getTokenTransfer = async ({ address }: { address: Address }) => {
  try {
    const { data } = await axios.get(
      `${env.SKYNET_API}/ronin/web3/v2/accounts/${address}/tokens/${env.CHICKEN_CONTRACT}/transfers?limit=200`,
      {
        headers: {
          "X-API-KEY": env.SKYMAVIS_API,
        },
      }
    );
    return data.result.items;
  } catch (error) {
    return null;
  }
};
