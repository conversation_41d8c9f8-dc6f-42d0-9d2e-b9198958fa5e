import { IChickenMetadata } from "@/lib/types/chicken.types";
import { IGenesResponse } from "./genes.types";

export interface IBreedingPair {
  id: number;
  parent1: number;
  parent2: number;
  breedingItem: number;
  parent1Data?: IParentData;
  parent2Data?: IParentData;
}
export interface IParentData {
  tokenId: number;
  image: string;
  breedCount: number;
  metadata: IChickenMetadata;
  genes: IGenesResponse;
}
