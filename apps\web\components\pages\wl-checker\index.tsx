"use client";
import Box from "@/components/shared/box";
import { Button } from "@/components/ui";
import { DelegateData } from "@/types/allocation.type";
import { useMemo, useState } from "react";
import { isAddress } from "viem";

interface AllocationBreakdown {
  type?: "mystic" | "chicken";
  legendaryCount?: number;
  quantity: number;
  totalAllocation: number;
}

interface AllocationData {
  address: string;
  totalChickens: number;
  totalAllocations: number;
  breakdown: AllocationBreakdown[];
  totalDelegated: number;
  delegated: DelegateData[];
}

export default function WLCheckerPage() {
  const [roninAddress, setRoninAddress] = useState<string>();
  const [allocationData, setAllocationData] = useState<AllocationData | null>(
    null
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleCheck = async () => {
    if (!roninAddress) {
      setError("Please enter a Ronin address");
      return;
    }

    if (!isAddress(roninAddress)) {
      setError("Please enter a valid Ronin address");
      return;
    }

    setLoading(true);
    setError(null);
    setAllocationData(null);

    try {
      const response = await fetch(
        `/api/chickens/check?address=${roninAddress}`
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || `API call failed: ${response.statusText}`
        );
      }

      const data: AllocationData = await response.json();
      setAllocationData(data);
    } catch (err) {
      console.error("Error details:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const getAllocationFromLegendaryCount = (count: number): number => {
    const allocationMap: { [key: number]: number } = {
      0: 2,
      1: 5,
      2: 12,
      3: 30,
    };
    return allocationMap[count] || 2;
  };

  const formatNumber = (num: number): string => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const realAlloc = useMemo(() => {
    const alloc = allocationData?.totalAllocations ?? 0;
    const delegated = allocationData?.totalDelegated ?? 0;
    return alloc - delegated;
  }, [allocationData?.totalAllocations, allocationData?.totalDelegated]);

  return (
    <div className="flex flex-col items-center justify-center mt-10 px-4 md:px-2 lg:px-0">
      <Box className="max-w-2xl  ">
        <h1 className="text-3xl font-bold text-center font-Arcadia text-primary mt-1">
          Legacy Mint Allocation Checker
        </h1>

        <div className="space-y-6 ">
          <div>
            <label className="block text-sm font-medium mb-2">
              Ronin Address
            </label>
            <input
              type="text"
              placeholder="Enter Ronin address (0x...)"
              value={roninAddress ?? ""}
              onChange={(e) => setRoninAddress(e.target.value)}
              className="w-full p-3 bg-stone-700 rounded border border-stone-600 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
            />
          </div>
          <Button
            onPress={handleCheck}
            isDisabled={loading || !roninAddress}
            className={"w-full"}
          >
            {loading ? "Checking..." : "Check Allocation"}
          </Button>
          {error && (
            <div className="p-4 bg-red-500/20 border border-red-500 rounded text-red-200">
              {error}
            </div>
          )}
          {loading && (
            <div className="animate-pulse space-y-4">
              <div className="h-24 bg-stone-700/30 rounded"></div>
              <div className="h-12 bg-stone-700/30 rounded w-3/4"></div>
              <div className="h-12 bg-stone-700/30 rounded w-1/2"></div>
            </div>
          )}
          {allocationData && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="p-4 bg-red-500/20 border border-red-500/30 rounded">
                <div className="text-sm text-red-300">Total Chickens</div>
                <div className="text-2xl font-bold">
                  {allocationData.totalChickens}
                </div>
              </div>
              <div className="p-4 bg-green-500/20 border border-green-500/30 rounded">
                <div className="text-sm text-green-300">Total Allocations</div>
                <div className="text-2xl font-bold">{realAlloc}</div>
              </div>
              <div className="p-4 bg-purple-500/20 border border-purple-500/30 rounded">
                <div className="text-sm text-purple-300">Delegated WL Spot</div>
                <div className="text-2xl font-bold">
                  {allocationData.totalDelegated}
                </div>
              </div>
              <div className="p-4 bg-blue-500/20 border border-blue-500/30 rounded">
                <div className="text-sm text-blue-300">Total $RON cost</div>
                <div className="text-2xl font-bold">
                  {formatNumber(Number((realAlloc * 6.942).toFixed(4)))}
                </div>
              </div>
              <div className="p-4 bg-yellow-500/20 border border-yellow-500/30 rounded">
                <div className="text-sm text-yellow-300">Total $COCK cost</div>
                <div className="text-2xl font-bold">
                  {formatNumber(Number((realAlloc * 16969).toFixed(0)))}
                </div>
              </div>
              <div className="p-4 bg-orange-500/20 border border-orange-500/30 rounded">
                <div className="text-sm text-orange-300">
                  Total FEATHERS needed
                </div>
                <div className="text-2xl font-bold">
                  {formatNumber(Number((realAlloc * 120).toFixed(0)))}
                </div>
              </div>
            </div>
          )}
          <div className="mt-6 space-y-4">
            <div className="p-4 bg-stone-700/50 rounded">
              <div className="">
                <h3 className="text-lg font-semibold mb-2">
                  Allocation Breakdown
                </h3>
                <div className="space-y-2">
                  {allocationData?.breakdown.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-stone-600/30 rounded"
                    >
                      <div className="flex-1">
                        {item.type === "mystic" ? (
                          <span className="font-medium">
                            <strong>{item.quantity}</strong> Mystic Axies
                            <span className="text-sm text-stone-300">
                              {" "}
                              ({item.quantity} × 2 allocations)
                            </span>
                          </span>
                        ) : (
                          <span className="font-medium">
                            <strong>{item.quantity}</strong> -{" "}
                            {item.legendaryCount}x Legendary Parts
                            <span className="text-sm text-stone-300">
                              {" "}
                              ({item.quantity} ×{" "}
                              {getAllocationFromLegendaryCount(
                                item.legendaryCount || 0
                              )}{" "}
                              allocations)
                            </span>
                          </span>
                        )}
                      </div>
                      <div className="font-semibold">
                        {item.totalAllocation} allocations
                      </div>
                    </div>
                  ))}
                  {allocationData?.delegated.map((item, idx) => (
                    <div
                      key={idx}
                      className="flex items-center justify-between p-2 bg-stone-600/30 rounded"
                    >
                      <div className="flex-1">
                        <span className="font-medium">
                          <strong>Delegated by</strong> :{" "}
                          <span className="text-sm text-stone-300">
                            {item.delegatedBy
                              ? `${item.delegatedBy.slice(0, 6)}...${item.delegatedBy.slice(item.delegatedBy.length - 8, item.delegatedBy.length)}`
                              : `No wallet address`}
                          </span>
                        </span>
                      </div>
                      <div className="font-semibold">
                        {item.amount} allocations
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Box>
    </div>
  );
}
