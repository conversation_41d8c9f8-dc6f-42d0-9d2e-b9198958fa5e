"use client";

import Box from "@/components/shared/box";
import CardsLoaders from "@/components/shared/cards-loaders";
import { Grid, Skeleton, Tooltip } from "@/components/ui";
import { useStateContext } from "@/providers/app/state";
import {
  LeaderboardResponse,
  Leaderboard as LeaderboardType,
} from "@/types/api.types";
import { cn } from "@/utils/classes";
import { IconCircleQuestionmarkFill, IconRefreshFill } from "justd-icons";
import { memo, useCallback, useEffect, useState } from "react";
import { Text } from "react-aria-components";
import TopRubbers from "./top-rubbers";

// Constants
const ITEMS_PER_PAGE = 10;
const API_ENDPOINTS = {
  LEADERBOARD: "/api/leaderboard",
  POSITION: "/api/leaderboard/position",
} as const;

// Memoized Components
const MemoizedDivider = memo(() => (
  <div
    className="w-full h-[2px] p-0"
    style={{
      background: "linear-gradient(90deg, transparent, #f7cb42, transparent)",
      filter: "brightness(2)",
    }}
  />
));
MemoizedDivider.displayName = "MemoizedDivider";

interface LeaderboardHeaderProps {
  rank: number | "n/a";
  isAuthenticated: boolean;
  isLoading: boolean;
  reload: () => void;
}

const LeaderboardHeader = memo(
  ({ rank, isAuthenticated, isLoading, reload }: LeaderboardHeaderProps) => (
    <div className="flex flex-col w-full mt-4 items-center justify-center">
      <div className="flex gap-2">
        <Text className="font-bold font-Arcadia text-primary text-2xl">
          Top Chicken Tenders
        </Text>
        <Tooltip delay={0}>
          <Tooltip.Trigger aria-label="Leaderboard">
            <IconCircleQuestionmarkFill className="text-primary" />
          </Tooltip.Trigger>
          <Tooltip.Content>
            This leaderboard exists purely for fun and bragging rights. Proceed
            with clout-chasing responsibly!
          </Tooltip.Content>
        </Tooltip>
      </div>
      {isLoading ? (
        <div className="flex flex-col gap-1 items-center">
          <Skeleton className="h-[30px] w-24" />
          <Skeleton className="h-6 w-14" />
        </div>
      ) : (
        <>
          <div className="flex gap-2 items-center">
            <Text
              className={`font-bold text-white text-3xl transition-all duration-300 ${
                !isAuthenticated ? "blur-sm" : ""
              }`}
            >
              {isAuthenticated ? rank : "???"}
            </Text>

            <IconRefreshFill
              onClick={reload}
              className="text-primary h-5 w-5 cursor-pointer"
            />
          </div>

          <Text className={cn("text-muted-fg", !isAuthenticated && "blur-sm")}>
            Your Rank
          </Text>
        </>
      )}
    </div>
  )
);
LeaderboardHeader.displayName = "LeaderboardHeader";

interface PaginationButtonProps {
  pageIndex: number;
  activeIndex: number;
  canGoBack: boolean;
  canGoForward: boolean;
  onClick: () => void;
}

const PaginationButton = memo(
  ({
    pageIndex,
    activeIndex,
    canGoBack,
    canGoForward,
    onClick,
  }: PaginationButtonProps) => {
    const isActive = pageIndex === activeIndex;
    const isDisabled =
      (pageIndex < activeIndex && !canGoBack) ||
      (pageIndex > activeIndex && !canGoForward);

    const baseClasses =
      "relative w-3 h-3 transform rotate-45 transition-all duration-300";
    const colorClasses = isActive
      ? "bg-yellow-400"
      : pageIndex < activeIndex && canGoBack
        ? "bg-gray-400 hover:bg-gray-300"
        : pageIndex > activeIndex && canGoForward
          ? "bg-gray-400 hover:bg-gray-300"
          : "bg-gray-600";

    return (
      <button
        onClick={onClick}
        className={`${baseClasses} ${colorClasses}`}
        disabled={isDisabled}
      >
        {isActive && (
          <>
            <div className="absolute inset-0 bg-yellow-200 opacity-50 animate-ping" />
            <div className="absolute -inset-1 bg-yellow-400 opacity-20 blur-sm animate-pulse" />
          </>
        )}
      </button>
    );
  }
);
PaginationButton.displayName = "PaginationButton";

const MemoizedTopRubbers = memo(
  ({ leaderboard }: { leaderboard: LeaderboardType[] }) => (
    <div className={`transition-all duration-300`}>
      <TopRubbers leaderboard={leaderboard} />
    </div>
  )
);
MemoizedTopRubbers.displayName = "MemoizedTopRubbers";

export default function Leaderboard() {
  const { address, isConnected } = useStateContext();

  const [activeIndex, setActiveIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [pendingRank, setPendingRank] = useState(false);
  const [userRank, setUserRank] = useState<number | "n/a">("n/a");
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardType[]>([]);
  const [totalPages, setTotalPages] = useState(1);
  const [error, setError] = useState<string | null>(null);

  const fetchLeaderboardData = useCallback(async (page: number) => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch(`${API_ENDPOINTS.LEADERBOARD}?page=${page}`);

      if (!response.ok) {
        throw new Error("Failed to fetch leaderboard data");
      }

      const data: LeaderboardResponse = await response.json();

      if (data.status && data.responseCode === 200) {
        setLeaderboardData(data.data.leaderboard ?? []);
        setTotalPages(data.data.pagination.totalPages ?? 1);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchUserRank = useCallback(async () => {
    if (!address) return;

    try {
      setPendingRank(true);
      setError(null);
      const response = await fetch(
        `${API_ENDPOINTS.POSITION}?address=${address}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch user rank");
      }

      const data = await response.json();

      if (data.status && data.responseCode === 200) {
        setUserRank(data.data.position === 0 ? "n/a" : data.data.position);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setPendingRank(false);
    }
  }, [address]);

  const reloadLeaderboard = useCallback(async () => {
    // Reset states
    setLoading(true);
    setError(null);

    // Fetch both leaderboard and user rank
    try {
      await Promise.all([
        fetchLeaderboardData(activeIndex + 1),
        isConnected && fetchUserRank(),
      ]);
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  }, [fetchLeaderboardData, fetchUserRank, activeIndex, isConnected]);

  useEffect(() => {
    fetchLeaderboardData(activeIndex + 1);
  }, [activeIndex, fetchLeaderboardData]);

  useEffect(() => {
    if (isConnected) {
      fetchUserRank();
    }
  }, [fetchUserRank, isConnected]);

  const getPaginationIndexes = useCallback(() => {
    if (totalPages <= 1) return [0];
    if (totalPages === 2) return [0, 1];

    if (activeIndex === 0) {
      return [0, 1, Math.min(2, totalPages - 1)];
    }
    if (activeIndex === totalPages - 1) {
      return [totalPages - 3, totalPages - 2, totalPages - 1];
    }
    return [activeIndex - 1, activeIndex, activeIndex + 1];
  }, [activeIndex, totalPages]);

  const canGoBack = activeIndex > 0;
  const canGoForward = activeIndex < totalPages - 1;

  if (loading) return <CardsLoaders />;

  if (error) {
    return <div className="text-red-500 text-center p-4">{error}</div>;
  }

  return (
    <Grid.Item className="backdrop-blur-sm bg-opacity-10 h-full">
      <Box className="gap-3 font-Poppins justify-between">
        <div className="flex flex-col gap-3">
          <LeaderboardHeader
            rank={userRank}
            isLoading={pendingRank}
            isAuthenticated={isConnected}
            reload={reloadLeaderboard}
          />
          <MemoizedDivider />
          <MemoizedTopRubbers leaderboard={leaderboardData} />
        </div>

        <div className="flex justify-center items-center space-x-3 my-8">
          {getPaginationIndexes().map((pageIndex) => (
            <PaginationButton
              key={pageIndex}
              pageIndex={pageIndex}
              activeIndex={activeIndex}
              canGoBack={canGoBack}
              canGoForward={canGoForward}
              onClick={() => setActiveIndex(pageIndex)}
            />
          ))}
        </div>
      </Box>
    </Grid.Item>
  );
}
