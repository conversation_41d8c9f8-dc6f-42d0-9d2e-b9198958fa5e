"use client";

import { useQuery } from "@tanstack/react-query";
import { readContract, executeMulticall } from "@/lib/blockchain/contractReader";
import { Abi, Address } from "viem";

/**
 * Hook for reading contract data
 */
export function useContractRead<T = unknown>({
  address,
  abi,
  functionName,
  args = [],
  enabled = true,
  queryKey = [],
  staleTime,
  cacheTime,
}: {
  address?: Address;
  abi?: Abi;
  functionName: string;
  args?: unknown[];
  enabled?: boolean;
  queryKey?: unknown[];
  staleTime?: number;
  cacheTime?: number;
}) {
  return useQuery({
    queryKey: ["contractRead", address, functionName, ...args, ...queryKey],
    queryFn: async () => {
      if (!address || !abi) {
        throw new Error("Missing required parameters: address or abi");
      }
      return readContract<T>({ address, abi, functionName, args });
    },
    enabled: enabled && !!address && !!abi,
    staleTime,
    gcTime: cacheTime,
  });
}

/**
 * Hook for executing multicall
 */
export function useMulticall<T = unknown[]>({
  calls,
  enabled = true,
  queryKey = [],
  staleTime,
  cacheTime,
}: {
  calls: {
    address: Address;
    abi: Abi;
    functionName: string;
    args?: unknown[];
  }[];
  enabled?: boolean;
  queryKey?: unknown[];
  staleTime?: number;
  cacheTime?: number;
}) {
  return useQuery({
    queryKey: ["multicall", JSON.stringify(calls), ...queryKey],
    queryFn: async () => {
      if (!calls.length) {
        throw new Error("No calls provided");
      }
      return executeMulticall<T>({ calls });
    },
    enabled: enabled && calls.length > 0 && calls.every(call => !!call.address && !!call.abi),
    staleTime,
    gcTime: cacheTime,
  });
}
