import mongoose from "mongoose";

export class MongoDbConnection {
  public async connect(uri: string): Promise<void> {
    try {
      mongoose.set("strictQuery", false);
      await mongoose.connect(uri);
      console.info("Connected to mongodb successfully!");
    } catch (error) {
      process.exit(1);
    }

    mongoose.connection.on("disconnected", async () => {
      await this.connect(uri);
    });
  }

  public async disconnect(): Promise<void> {
    await mongoose.disconnect();
  }
}
