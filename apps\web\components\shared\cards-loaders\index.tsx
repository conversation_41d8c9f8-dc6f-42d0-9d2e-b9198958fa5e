"use client";

import { Grid } from "@/components/ui";
import Box from "../box";
import { memo } from "react";

const CardLoaders = () => {
  const diamonds = [0, 1, 2];

  return (
    <Grid.Item className="backdrop-blur-sm bg-opacity-10 h-full">
      <Box className="h-full flex justify-center items-center">
        <div className="flex space-x-3">
          {diamonds.map((_, index) => (
            <div key={index} className="relative w-3 h-3 rotate-45">
              {/* Base diamond */}
              <div className="absolute inset-0 bg-gray-400" />
              {/* Ping animation layer */}
              <div
                className="absolute inset-0 bg-yellow-200 opacity-50 animate-ping"
                style={{ animationDelay: `${index * 600}ms` }}
              />
              {/* Pulse animation layer */}
              <div
                className="absolute -inset-1 bg-yellow-400 opacity-40 blur-sm animate-pulse"
                style={{ animationDelay: `${index * 600}ms` }}
              />
            </div>
          ))}
        </div>
      </Box>
    </Grid.Item>
  );
};

export default memo(CardLoaders);
