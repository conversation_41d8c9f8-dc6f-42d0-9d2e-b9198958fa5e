"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, Badge } from "ui";
import { cn } from "@/utils/classes";
import Image from "next/image";
import { 
  IChickenWithRewards, 
  IGameRewardItem, 
  ERewardType 
} from "../types/inventory.types";
import { rewardMetadataMap } from "../mock/mock-data";

interface IPerChickenRewardDetailsProps {
  chicken: IChickenWithRewards | null;
  onClaimChicken: (chickenId: string) => void;
  className?: string;
}

/**
 * PerChickenRewardDetails Component
 * 
 * Shows detailed reward information for a selected chicken
 * Similar to the detail view in the current inventory
 */
export const PerChickenRewardDetails: React.FC<IPerChickenRewardDetailsProps> = ({
  chicken,
  onClaimChicken,
  className,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!chicken) {
    return (
      <div className={cn("bg-stone-900/30 rounded-lg border border-dashed border-amber-400/30 p-6 flex flex-col items-center justify-center text-center", className)}>
        <span className="text-4xl mb-3">🐔</span>
        <h3 className="text-lg font-semibold text-amber-200 mb-2">
          Select a chicken
        </h3>
        <p className="text-sm text-stone-400">
          Choose a chicken from the table to view detailed rewards
        </p>
      </div>
    );
  }

  // Get reward type badge color - improved contrast
  const getTypeColor = (type: ERewardType) => {
    switch (type) {
      case ERewardType.CRYSTAL:
        return "bg-blue-600/30 text-blue-200 border border-blue-400/40";
      case ERewardType.SHARD:
        return "bg-purple-600/30 text-purple-200 border border-purple-400/40";
      case ERewardType.CORN:
        return "bg-amber-600/30 text-amber-200 border border-amber-400/40";
      default:
        return "bg-stone-600/30 text-stone-200 border border-stone-400/40";
    }
  };

  // Group rewards by metadata for display
  const groupedRewards = new Map<number, { metadata: any; rewards: IGameRewardItem[] }>();
  
  chicken.rewards.forEach((reward) => {
    const metadata = rewardMetadataMap.get(reward.rewardId);
    if (metadata) {
      if (!groupedRewards.has(reward.rewardId)) {
        groupedRewards.set(reward.rewardId, { metadata, rewards: [] });
      }
      groupedRewards.get(reward.rewardId)!.rewards.push(reward);
    }
  });

  return (
    <div className={cn("bg-gradient-to-br from-stone-900/90 to-stone-800/90 rounded-lg border border-amber-400/30 backdrop-blur-sm shadow-xl shadow-amber-400/10", className)}>
      {/* Compact Header with Toggle */}
      <div className="flex items-center justify-between p-4 border-b border-amber-400/20">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-gradient-to-br from-stone-700/60 to-stone-800/80 rounded-lg flex items-center justify-center flex-shrink-0 border border-amber-400/30">
            <Image
              src={chicken.image}
              alt={chicken.name}
              width={64}
              height={64}
              quality={100}
              unoptimized
              className="w-full h-full object-contain rounded-lg"
            />
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-bold text-amber-200 mb-1">
              {chicken.name}
            </h3>
            <div className="flex items-center gap-3">
              <div className="bg-amber-500/90 text-amber-900 px-3 py-1 rounded-full text-sm font-bold">
                {chicken.totalRewards} Rewards
              </div>
              <div className="text-xs text-stone-400">
                ID: {chicken.chickenId}
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col items-end gap-2">
          <Button
            intent="primary"
            size="medium"
            onPress={() => onClaimChicken(chicken.chickenId)}
            isDisabled={chicken.totalRewards === 0}
            className="bg-amber-600 hover:bg-amber-500 text-amber-50 font-semibold disabled:bg-stone-600 disabled:text-stone-400"
          >
            Claim All
          </Button>
          <Button
            size="small"
            appearance="plain"
            onPress={() => setIsExpanded(!isExpanded)}
            className="text-amber-400 hover:text-amber-300 text-xs"
          >
            {isExpanded ? "▼ Less Details" : "▶ More Details"}
          </Button>
        </div>
      </div>

      {/* Expandable Content */}
      {isExpanded && (
        <div className="p-4 space-y-4 border-t border-amber-400/20">
          {/* Reward Summary - Compact */}
          <div className="bg-stone-800/40 p-4 rounded-lg border border-stone-600/30">
            <h3 className="font-semibold text-amber-200 mb-3 text-lg">
              Summary
            </h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex justify-between items-center">
                <span className="text-stone-300">Total:</span>
                <div className="bg-amber-500/90 text-amber-900 px-2 py-1 rounded-full text-xs font-bold">
                  {chicken.totalRewards}
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-stone-300">Unclaimed:</span>
                <div className="bg-orange-500/20 text-orange-200 px-2 py-1 rounded-full text-xs font-semibold border border-orange-400/40">
                  {chicken.rewards.length}
                </div>
              </div>
            </div>
          </div>

          {/* Detailed Rewards List - Compact */}
          <div className="bg-stone-800/40 p-4 rounded-lg border border-stone-600/30">
            <h3 className="font-semibold text-amber-200 mb-3 text-lg">
              Detailed Breakdown
            </h3>
            <div className="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
              {Array.from(groupedRewards.entries()).map(([rewardId, { metadata, rewards }]) => {
                const totalQuantity = rewards.reduce((sum, reward) => sum + (reward.quantity || 1), 0);
                
                return (
                  <div key={rewardId} className="flex items-center justify-between p-2 bg-stone-800/60 rounded-lg border border-stone-600/30 hover:bg-stone-800/80 transition-colors">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 flex items-center justify-center bg-stone-700/50 rounded border border-stone-600/40">
                        <Image
                          src={metadata.image}
                          alt={metadata.name}
                          width={24}
                          height={24}
                          quality={100}
                          unoptimized
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <div>
                        <span className="text-sm font-semibold text-amber-200">{metadata.name}</span>
                        <div className="flex items-center gap-2 mt-0.5">
                          <span className={cn("px-1.5 py-0.5 rounded text-xs font-medium", getTypeColor(metadata.type))}>
                            {metadata.type}
                          </span>
                          <span className="text-xs text-stone-400">
                            {rewards.length} rewards
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="bg-amber-500/90 text-amber-900 px-2 py-1 rounded-full text-xs font-bold">
                      {totalQuantity}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};